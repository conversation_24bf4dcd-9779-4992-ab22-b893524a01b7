# 自动驾驶车辆停滞检测和处理逻辑深度分析报告

## 1. 系统概述

### 1.1 核心功能
本项目中的停滞检测系统是一个多层次、多场景的智能监控和处理框架，主要负责：
- 实时监控车辆停滞状态
- 智能识别不同停滞场景
- 触发相应的处理策略
- 自动召唤远程操作员介入

### 1.2 关键组件
- **停滞监控单元** (`StagnantMonitorUnit`): 负责实时检测车辆停滞状态
- **停滞规则单元** (`StagnantRuleUnit`): 负责根据停滞情况触发相应的处理策略
- **呼叫码83** (`EvaluationAlert::ADC_STAGNANT`): 系统检测到车辆停滞后触发的核心警报类型

## 2. 核心文件架构

### 2.1 主要文件及职责

| 文件路径 | 主要职责 | 关键功能 |
|---------|---------|---------|
| `modules/arbitration_monitor/monitor/units/stagnant_monitor_unit.cc` | 停滞状态检测和场景识别 | 实时监控、场景分类、状态更新 |
| `modules/arbitration_monitor/monitor/units/stagnant_monitor_unit.h` | 停滞监控单元头文件 | 定义30种停滞场景枚举 |
| `modules/control_arbitration/strategy/rule_logistic_unit/stagnant_rule_unit.cc` | 停滞规则判断和警报触发 | 规则逻辑、警报触发、抑制机制 |
| `modules/control_arbitration/strategy/rule_logistic_unit/stagnant_rule_unit.h` | 停滞规则单元头文件 | 接口定义、成员变量声明 |
| `modules/control_arbitration/proto/evaluation_alert.proto` | 警报类型定义 | 定义ADC_STAGNANT=83警报类型 |

### 2.2 配置文件

| 配置文件 | 作用 | 关键参数 |
|---------|------|---------|
| `config/module/arbitrationmonitor/monitor_config/arbitration_monitor_master_to_safety_10hz_config.pb.txt` | 停滞检测参数配置 | 速度阈值、距离阈值、场景配置 |
| `config/module/controlarbitration/rule_strategy_config.pb.txt` | 停滞处理策略配置 | 时间阈值、评分规则、召回配置 |

## 3. 停滞检测逻辑详解

### 3.1 停滞监控单元 (StagnantMonitorUnit)

#### 3.1.1 核心检测条件
```protobuf
# 关键阈值配置
max_block_speed_mps: 0.1              # 车辆被认为停滞的最大速度(m/s)
min_block_distance_to_stop_fence: 20  # 距离停车线的最小距离(m)
```

#### 3.1.2 时间计算机制
- **block_time**: 车辆速度低于阈值的持续时间
- **stagnant_time**: 车辆在完全自动驾驶模式下的停滞时间  
- **scenario_duration**: 特定阻塞场景的持续时间

#### 3.1.3 停滞场景分类 (BlockScenario)
系统定义了30种不同的停滞场景：

| 场景类型 | 场景ID | 描述 | 典型触发条件 |
|---------|--------|------|-------------|
| `BLOCK` | 1 | 一般性阻塞 | 1.5秒场景+0.2秒阻塞 |
| `BLOCK_BY_TURN` | 2 | 转弯处阻塞 | 7秒场景+2秒阻塞 |
| `BLOCK_BY_DYNAMIC_OBSTACLE` | 6 | 动态障碍物阻塞 | 10秒场景+7秒阻塞 |
| `BLOCK_IN_JUNCTION` | 9 | 路口内阻塞 | 2秒场景+1秒阻塞 |
| `BLOCK_BY_TRAFFIC_LIGHT` | 23 | 交通灯阻塞 | 特殊处理逻辑 |
| `BLOCK_LONG_TIME` | 24 | 长时间阻塞 | 超过3分钟 |
| `BLOCK_BY_VIRTUAL_STOP` | 29 | 虚拟停车标志阻塞 | 规划模块请求 |

### 3.2 场景特定的停滞判断逻辑

#### 3.2.1 配置驱动的判断机制
```cpp
// 宏定义实现的场景判断逻辑
#define CASE_SCENARIO_STAGNANT(scenario_type)                                   \
  case BlockScenario::scenario_type: {                                          \
    if (!stagnant_scenario_config_.count(#scenario_type)) {                     \
      break;                                                                    \
    }                                                                           \
    for (auto scenario_config : stagnant_scenario_config_.at(#scenario_type)) { \
      if (scenario_duration >= scenario_config.scenario_duration() &&           \
          block_time >= scenario_config.block_time()) {                         \
        is_adc_stagnant = true;                                                 \
        break;                                                                  \
      }                                                                         \
    }                                                                           \
    break;                                                                      \
  }
```

#### 3.2.2 特殊场景处理
```cpp
// 长时间阻塞检测
if (IsBlockedWithoutDecision()) {
  if (block_time >= kMaxBlockTimeInMS) {  // 180000ms = 3分钟
    is_adc_stagnant = true;
    block_scenario_output = BlockScenario::BLOCK_LONG_TIME;
  }
}

// 虚拟停车标志检测
if (planning_message != nullptr && planning_message->planning().call_for_virtual_stop_sign()) {
  is_adc_stagnant = true;
  block_scenario_output = BlockScenario::BLOCK_BY_VIRTUAL_STOP;
}
```

## 4. 停滞规则单元 (StagnantRuleUnit)

### 4.1 触发ADC_STAGNANT的条件

#### 4.1.1 基于场景的直接触发
```cpp
// 来自规划模块的停滞请求
if (metrics_buffer.back()->scene_info().scene() ==
    walle::control_arbitration::SceneInfo::ADC_STAGNANT_FROM_PNC) {
  return EvaluationAlert::ADC_STAGNANT;
}

// 停车排队区域特殊处理
if (metrics_buffer.back()->scene_info().scene() ==
    walle::control_arbitration::SceneInfo::WAITING_IN_PARKING_QUEUE_AREA) {
  if (metrics_buffer.back()->stagnant_time_in_ms() >= kStagnantTimeInParkingQueueAreaInMs) {
    return EvaluationAlert::ADC_STAGNANT;
  }
}
```

#### 4.1.2 基于位置和时间的触发
```protobuf
# 不同位置的停滞时间阈值配置
stagnant_rule_unit_config {
  stagnant_time_on_the_way: 50000     # 路上停滞50秒
  stagnant_time_in_home: 30000        # 家区停滞30秒  
  stagnant_time_in_home_queue: 50000  # 家区排队停滞50秒
}
```

#### 4.1.3 停车排队区域特殊处理
```cpp
// 停车排队区域需要更长时间才触发
const double kStagnantTimeInParkingQueueAreaInMs = 720000; // 12分钟
```

### 4.2 智能抑制逻辑 (防误触发)

#### 4.2.1 时序抑制机制
```cpp
// 避免在特定情况后立即触发的时序常量
const int64_t kMinValidBlockScenenSequencenum = 3000;  // 红灯后抑制3秒
const int64_t kMinQueueSequenceNum = 1000;             // 排队后抑制1秒
const int64_t kMinHomeNotAllowSequenceNum = 3000;      // 家区不允许进入后抑制3秒
```

#### 4.2.2 抑制逻辑实现
```cpp
// 路上停滞的抑制逻辑
bool StagnantRuleUnit::IsAdcStagnantOnTheWay(const MetricsBuffer& metrics_buffer) {
  // 红灯后短时间内不触发
  if (last_red_trafficlight_sequence_id_ != -1 &&
      (sequence_id_ - last_red_trafficlight_sequence_id_) <= kMinValidBlockScenenSequencenum) {
    return false;
  }
  
  // 离开家区后短时间内不触发
  if (last_homearea_sequence_id_ != -1 &&
      (sequence_id_ - last_homearea_sequence_id_) <= kMinValidBlockScenenSequencenum) {
    return false;
  }
  
  // 检查停滞时间是否达到阈值
  if (metrics_buffer.back()->stagnant_time_in_ms() >= stagnant_config_.stagnant_time_on_the_way()) {
    return true;
  }
  return false;
}
```

## 5. 呼叫码83的处理流程

### 5.1 警报触发链路
```
StagnantMonitorUnit::Run()
    ↓ 调用
UpdateBlockTime() → 更新阻塞时间
    ↓ 调用  
SetAdcBlockInfo() → 设置停滞状态和场景
    ↓ 传递给
StagnantRuleUnit::Run()
    ↓ 返回
EvaluationAlert::ADC_STAGNANT (83)
    ↓ 传递给
ArbitrationSystem → 系统仲裁处理
    ↓ 触发
RemoteOperationRequestor → 远程操作请求
```

### 5.2 远程操作请求机制
```cpp
// 设置呼叫码和消息
bool RemoteOperationRequestor::RequestCockpit() {
  exchange::CockpitCall remote_operation_request;
  remote_operation_request.set_timestamp(absl::ToUnixSeconds(now_));
  remote_operation_request.set_vin(vin_);
  remote_operation_request.mutable_emergency()->set_except_event(except_event_);
  remote_operation_request.mutable_emergency()->set_action(ExceptEvent::CALL);
  remote_operation_request.set_source(CockpitCall::CONTROL_ARBITRATION);
  
  // 设置原因码为83 (ADC_STAGNANT)
  remote_operation_request.mutable_emergency()->mutable_extra_info()->set_reason_code(reason_);
  remote_operation_request.mutable_emergency()->mutable_extra_info()->set_msg(
      control_arbitration::EvaluationAlert::Type_Name(reason_)); // "ADC_STAGNANT"
      
  return SendRemoteOperationRequest(remote_operation_request);
}
```

### 5.3 系统状态切换
当ADC_STAGNANT触发时，系统会：

1. **评分调整**: AUTO系统评分降至30分 (baseline: 100)
2. **召回配置**: 设置4.1秒的召回时间阈值
3. **远程请求**: 自动发起远程操作员介入请求
4. **状态切换**: 根据情况切换到TELEOPERATION或UNKNOWN状态

```protobuf
# 策略配置
strategy {
  name: "adc_stagnant"
  system_type: AUTO
  rule_logistic {
    baseline: 100
    rule_score {
      score: 30                           # 触发后评分降至30
      item: STAGNANT
      evaluation_alert_type: ADC_STAGNANT
      recall_time_thresholds {
        key: "ADC_STAGNANT"
        value: 4100                       # 4.1秒召回阈值
      }
    }
  }
}
```

## 6. 关键配置参数总结

### 6.1 基础检测参数
| 参数名 | 数值 | 单位 | 含义 |
|--------|------|------|------|
| `max_block_speed_mps` | 0.1 | m/s | 停滞速度阈值 |
| `min_block_distance_to_stop_fence` | 20 | m | 停车线距离阈值 |
| `kMaxBlockTimeInMS` | 180000 | ms | 最大阻塞时间(3分钟) |

### 6.2 停滞时间阈值
| 场景类型 | 时间阈值 | 单位 | 说明 |
|---------|---------|------|------|
| 路上停滞 | 50000 | ms | 50秒后触发 |
| 家区停滞 | 30000 | ms | 30秒后触发 |
| 家区排队停滞 | 50000 | ms | 50秒后触发 |
| 停车排队区域 | 720000 | ms | 12分钟后触发 |

### 6.3 抑制时间参数
| 抑制类型 | 时间 | 单位 | 说明 |
|---------|------|------|------|
| 红灯后抑制 | 3000 | 序列号差值 | 约3秒 |
| 排队后抑制 | 1000 | 序列号差值 | 约1秒 |
| 家区限制后抑制 | 3000 | 序列号差值 | 约3秒 |

## 7. 系统特点和优势

### 7.1 多层次检测架构
- **物理层**: 基于速度和位置的基础检测
- **场景层**: 针对不同驾驶场景的专门逻辑  
- **时序层**: 考虑历史状态的智能抑制
- **上下文层**: 结合车辆位置和环境信息

### 7.2 配置化设计优势
- 所有关键参数都可通过配置文件调整
- 支持不同场景的差异化配置
- 便于针对不同运营环境进行优化
- 无需修改代码即可调整检测策略

### 7.3 智能抑制机制
- 避免在红灯、排队等正常情况下误触发
- 考虑车辆位置上下文(家区、路上、排队区域)
- 基于时序的动态抑制逻辑
- 多维度的误触发防护

### 7.4 完整的处理链路
- 从检测到警报触发的完整自动化流程
- 自动化的远程操作员召唤机制
- 与整体仲裁系统的深度集成
- 支持多种系统状态切换策略

## 8. 总结

这套停滞检测系统体现了自动驾驶系统在安全性和可靠性方面的精细化设计。通过多维度的检测逻辑、智能化的抑制机制和完整的处理链路，确保在车辆真正遇到无法自主解决的停滞情况时，能够及时、准确地触发人工介入，同时最大程度地避免误触发，保证系统的稳定性和用户体验。

**呼叫码83 (ADC_STAGNANT)** 作为核心警报类型，承载着从底层检测到上层决策的关键信息传递功能，是整个停滞处理系统的核心枢纽。
