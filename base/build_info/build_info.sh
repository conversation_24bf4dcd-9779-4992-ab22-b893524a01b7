#!/bin/bash

package_dir=$(cd `dirname \$0`; pwd)
workdir=`realpath $package_dir`

walle_build_date=`(cd $workdir && date  +'%Y-%m-%d %H:%M:%S')`
walle_git_branch_name=`(cd $workdir && git rev-parse --abbrev-ref HEAD)`
walle_git_commit=`(cd $workdir && git rev-parse --verify HEAD)`
walle_git_commit_owner=`(cd $workdir && git log -1 --pretty=format:'%an - %ae')`
walle_git_latest_tag=`(cd $workdir && git describe --tags --always)`

if [ -z "$walle_version" ]; then
    walle_version='not-specified';
fi

echo "kBuildDate:  \"${walle_build_date}\";"
echo "kBuildVersion: \"${walle_version}\";"
echo "kBuildGitCommit: \"${walle_git_commit}\";"
echo "kBuildGitCommitOwner: \"${walle_git_commit_owner}\";"
echo "kBuildGitBranchName: \"${walle_git_branch_name}\";"
echo "kBuildGitLatestTag: \"${walle_git_latest_tag}\";"