load("//build/bazel_rules:test_rules.bzl", "untrack_test_file")

package(default_visibility = ["//visibility:private"])

genrule(
    name = "private_build_info_pb",
    srcs = ["//:git"],
    outs = ["build_info.pb.txt"],
    cmd = "$(location build_info.sh) > $@",
    local = True,
    tools = [":build_info.sh"],
)

untrack_test_file(
    name = "build_info_pb",
    real = ":private_build_info_pb",
    placeholder = "fake_build_info.pb.txt",
    visibility = ["//visibility:public"],
)
