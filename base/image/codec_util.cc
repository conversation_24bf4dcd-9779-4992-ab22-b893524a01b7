// Copyright @2023 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/image/codec_util.h"

#include <memory>
#include <string>
#include <unordered_map>

#include "glog/logging.h"

#include "base/image/image_codec.h"
#include "base/image/png_codec.h"

#if defined(USE_JETSON_ORIN) && defined(USE_CUDA)
#include "base/image/jetson_jpeg.h"
#include "base/image/jetson_video.h"
#elif defined(USE_DRIVE_ORIN) && defined(USE_CUDA)
#include "base/image/driveos_jpeg_6090.h"
#include "base/image/driveos_video_6090.h"
#elif defined(__x86_64__)
#ifdef HAVE_FFMPEG
#include "base/image/ffmpeg_codec.h"
#endif
#include "base/image/jpeg_codec_cpu.h"
#if defined(USE_CUDA)
#include "base/image/nv_jpeg.h"
#endif
#elif !defined(USE_HORIZON)
#warning This platform does not have supported codec!
#endif

namespace image {

std::unique_ptr<ImageEncoder> CreateImageEncoder(const ImageCodecCreationOptions& options) {
  if (options.codec_type == CodecType::kHevc) {
#ifdef HAVE_FFMPEG
    return internal::CreateHevcEncoder(options);
#endif
  } else if (options.codec_type == CodecType::kJpeg) {
    return internal::CreateJpegEncoder(options);
  } else if (options.codec_type == CodecType::kPng) {
    return std::make_unique<PNGEncoder>();
  }
  return nullptr;
}

std::unique_ptr<ImageDecoder> CreateImageDecoder(const ImageCodecCreationOptions& options) {
  if (options.codec_type == CodecType::kHevc) {
#ifdef HAVE_FFMPEG
    return internal::CreateHevcDecoder(options);
#endif
  } else if (options.codec_type == CodecType::kJpeg) {
    return internal::CreateJpegDecoder(options);
  } else if (options.codec_type == CodecType::kPng) {
    return std::make_unique<PNGDecoder>();
  }
  return nullptr;
}

namespace internal {

std::unique_ptr<ImageEncoder> CreateHevcEncoder(const ImageCodecCreationOptions& options) {
  if (options.device_type == DeviceType::kCuda) {
#if defined(USE_JETSON_ORIN) && defined(USE_CUDA)
    return std::make_unique<JetsonEncoder>(options.device_type);
#elif (defined(USE_DRIVE_ORIN)) && defined(USE_CUDA)
    return std::make_unique<DriveosHevcEncoder>(options.device_type);
#elif defined(__x86_64__) && defined(HAVE_FFMPEG)
    return std::make_unique<FFmpegEncoder>(options.device_type);
#endif
  } else if (options.device_type == DeviceType::kCpu) {
#if defined(__x86_64__) && defined(HAVE_FFMPEG)
    return std::make_unique<FFmpegEncoder>(options.device_type);
#endif
  } else {
    LOG(FATAL) << "Unsupported device type!";
  }
  return nullptr;
}

std::unique_ptr<ImageDecoder> CreateHevcDecoder(const ImageCodecCreationOptions& options) {
  if (options.device_type == DeviceType::kCuda) {
#if defined(USE_JETSON_ORIN) && defined(USE_CUDA)
    return std::make_unique<JetsonDecoder>(options.device_type);
#elif (defined(USE_DRIVE_ORIN)) && defined(USE_CUDA)
    return std::make_unique<DriveosHevcDecoder>(options.device_type);
#elif defined(__x86_64__) && defined(HAVE_FFMPEG)
    return std::make_unique<FFmpegDecoder>(options.device_type);
#endif
  } else if (options.device_type == DeviceType::kCpu) {
#if defined(__x86_64__) && defined(HAVE_FFMPEG)
    return std::make_unique<FFmpegDecoder>(options.device_type);
#endif
  } else {
    LOG(FATAL) << "Unsupported device type!";
  }
  return nullptr;
}

std::unique_ptr<ImageEncoder> CreateJpegEncoder(const ImageCodecCreationOptions& options) {
  if (options.device_type == DeviceType::kCuda) {
#if defined(USE_CUDA) && \
    (defined(__x86_64__) || defined(USE_JETSON_ORIN) || defined(USE_DRIVE_ORIN))
    return std::make_unique<NvJpegEncoder>(options.quality, options.huffman_optimize);
#endif
  } else if (options.device_type == DeviceType::kCpu) {
#if defined(__x86_64__)
    return std::make_unique<JpegEncoderCpu>(options.quality, options.huffman_optimize);
#endif
  } else {
    LOG(FATAL) << "Unsupported device type!";
  }
  LOG(FATAL) << "This platform doesn't have supported JPEG encoder!";
  return nullptr;
}

std::unique_ptr<ImageDecoder> CreateJpegDecoder(const ImageCodecCreationOptions& options) {
  if (options.device_type == DeviceType::kCuda) {
#if defined(USE_CUDA) && \
    (defined(__x86_64__) || defined(USE_JETSON_ORIN) || defined(USE_DRIVE_ORIN))
    return std::make_unique<NvJpegDecoder>();
#endif
  } else if (options.device_type == DeviceType::kCpu) {
#if defined(__x86_64__)
    return std::make_unique<JpegDecoderCpu>();
#endif
  } else {
    LOG(FATAL) << "Unsupported device type!";
  }
  LOG(FATAL) << "This platform doesn't have supported JPEG decoder!";
  return nullptr;
}

}  // namespace internal
}  // namespace image
