// Copyright @2022 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/image/cairo_context.h"

#include "gtest/gtest.h"

namespace image {

TEST(CairoContextTest, BasicTest) {
  CairoContext cairo_context(1000, 1000);
  cairo_context.CairoSetLineWidth(2.0);
  EXPECT_EQ(cairo_context.CairoGetLineWidth(), 2.0);

  Image<uint8_t> image(1000, 1000, 4);
  CairoContext cairo_context1(image.View());
  cairo_context1.CairoSetLineWidth(2.0);
  EXPECT_EQ(cairo_context1.CairoGetLineWidth(), 2.0);
  EXPECT_TRUE(cairo_context1.WriteToFile("/tmp/test.png"));
  EXPECT_FALSE(cairo_context1.WriteToFile("/tmp/__not_exist_dir__/test.png"));
}

}  // namespace image
