// Copyright @2022 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/image/color.h"

#include "gtest/gtest.h"

namespace image {

TEST(ColorTest, BasicTest) {
  Color color(0, 0, 0, 1);
  Color256 color256(color);
  EXPECT_EQ(color256.r, 0);
  EXPECT_EQ(color256.g, 0);
  EXPECT_EQ(color256.b, 0);
  EXPECT_EQ(color256.a, 255);
  Color color1 = color256.ToColor();
  EXPECT_EQ(color, color1);
}

}  // namespace image
