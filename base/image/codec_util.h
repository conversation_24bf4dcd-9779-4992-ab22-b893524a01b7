// Copyright @2023 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <memory>
#include <string>

#include "base/image/flags.h"
#include "base/image/image_codec.h"

namespace image {

struct ImageCodecCreationOptions {
  const CodecType codec_type;
  const DeviceType device_type;
  int quality = 0;
  int huffman_optimize = 0;
};

std::unique_ptr<ImageEncoder> CreateImageEncoder(const ImageCodecCreationOptions& options);

std::unique_ptr<ImageDecoder> CreateImageDecoder(const ImageCodecCreationOptions& options);

// Functions in the `internal` namespace are intended to be used ONLY by the codec factory
// functions. If you want to create a codec instance, use the`CreateImageEncoder` or
// `CreateImageDecoder` respectively.
namespace internal {

std::unique_ptr<ImageEncoder> CreateHevcEncoder(const ImageCodecCreationOptions& options);

std::unique_ptr<ImageDecoder> CreateHevcDecoder(const ImageCodecCreationOptions& options);

std::unique_ptr<ImageEncoder> CreateJpegEncoder(const ImageCodecCreationOptions& options);

std::unique_ptr<ImageDecoder> CreateJpegDecoder(const ImageCodecCreationOptions& options);

}  // namespace internal
}  // namespace image
