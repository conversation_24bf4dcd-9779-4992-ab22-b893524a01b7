// Copyright @2022 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>
//          <PERSON><PERSON> (wuwang<PERSON>@meituan.com)

#pragma once

#include <vector>
#include <string>

#include "cairo/cairo.h"
#include "gtest/gtest.h"

#include "base/common/macros.h"
#include "base/image/color.h"
#include "base/image/image.h"
#include "base/math/circle2d.h"

namespace image {

class CairoContext {
 public:
  using CairoLineCapType = cairo_line_cap_t;
  using CairoLineJoinType = cairo_line_join_t;

  CairoContext(int height, int width);
  virtual ~CairoContext();

  bool WriteToFile(const std::string& file_name) const;
  void CairoShowText(const math::Vector2d& position, const std::string& text, double font_size);
  // line attributes
  void CairoSetLineWidth(double line_width);
  double CairoGetLineWidth();
  void CairoSetLineCap(CairoLineCapType line_cap_type);
  void CairoSetLineJoin(CairoLineJoinType line_join_type);
  void CairoSetDash(const std::vector<double>& dashes, double offset = 0.0);
  // color attributes
  void CairoSetSourceRGBA(const Color& color);
  // draw functions
  void CairoArc(const math::Circle2d& circle, double angle1, double angle2);
  void CairoMoveTo(const math::Vector2d& point);
  void CairoLineTo(const math::Vector2d& point);
  void CairoClosePath(bool close);
  void CairoFill(bool fill);
  void CairoAntialias(bool use_antialias);
  // other
  void CairoStroke();

 private:
  using CairoSurface = cairo_surface_t;
  using Cairo = cairo_t;
  using CairoFormat = cairo_format_t;

  explicit CairoContext(ImageView<uint8_t> image_view);

  CairoSurface* cairo_surface_ = nullptr;  // not owned
  Cairo* cairo_ = nullptr;                 // not owned

  friend class ImagePainter;
  FRIEND_TEST(CairoContextTest, BasicTest);
  DISALLOW_COPY_AND_ASSIGN(CairoContext);
};

}  // namespace image
