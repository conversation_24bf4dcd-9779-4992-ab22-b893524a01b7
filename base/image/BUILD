load("//build/bazel_rules:walle_platform_library.bzl", "walle_platform_library", "walle_platform_select")
load("//third_party/driveos_codec/utils:config.bzl", "NVMEDIA_COPTS")

package(default_visibility = ["//visibility:public"])

cc_library(
    name = "npp_macros",
    hdrs = ["npp_macros.h"],
    deps = ["@glog"],
)

cc_library(
    name = "image_format",
    srcs = ["image_format.cc"],
    hdrs = ["image_format.h"],
    deps = ["@glog"],
)

cc_library(
    name = "image_data",
    srcs = ["image_data.cc"],
    hdrs = ["image_data.h"],
    deps = [
        ":image_format",
        "//base/common:macros",
        "//base/container:array_view",
    ],
)

cc_library(
    name = "flags",
    srcs = ["flags.cc"],
    hdrs = ["flags.h"],
    deps = [
        "@gflags",
    ],
)

walle_platform_library(
    name = "image_convert_npp",
    srcs = ["image_convert.cc"],
    hdrs = ["image_convert.h"],
    platform = "use_cuda",
    deps = [
        "//base/common:macros",
        "//base/container:array_view",
        "//base/cuda:macros",
        "//base/cuda:mirror_buffer",
        "//base/image:npp_macros",
        "@cuda",
    ],
)

walle_platform_library(
    name = "image_convert_dummy",
    srcs = ["image_convert_dummy.cc"],
    hdrs = ["image_convert.h"],
    platform = "no_cuda",
    deps = [
        "//base/common:macros",
        "//base/container:array_view",
    ],
)

cc_library(
    name = "image_convert",
    deps = [
        ":image_convert_dummy",
        ":image_convert_npp",
    ],
)

cc_library(
    name = "image_codec",
    srcs = ["image_codec.cc"],
    hdrs = ["image_codec.h"],
    deps = [
        ":image",
        ":image_data",
        "//base/common:cc_error_code_proto",
        "//base/common:optional",
        "//base/common:status",
    ],
)

walle_platform_library(
    name = "nv_jpeg",
    srcs = [
        "nv_jpeg.cc",
    ],
    hdrs = [
        "nv_jpeg.h",
    ],
    platform = "x86_64_with_cuda",
    deps = [
        ":image_codec",
        "//base/common:status",
        "//base/file:file_util",
        "//base/strings:format",
        "//base/synchronization:mutex",
        "//third_party/nvjpeg:nvjpeg_custom",
    ],
)

walle_platform_library(
    name = "jetson_jpeg",
    srcs = [
        "jetson_jpeg.cc",
    ],
    hdrs = [
        "jetson_jpeg.h",
    ],
    linkopts = [
        "-lnvbuf_utils",
        "-lnvjpeg",
    ],
    platform = "orin_jetson",
    deps = [
        ":image_codec",
        ":image_convert",
        "//base/common:status",
        "//base/cuda:macros",
        "//base/file:file_util",
        "//base/math",
        "//base/strings:format",
        "//base/synchronization:mutex",
        "//third_party/jetson_jpeg:nv_buffer",
        "@cuda",
    ],
)

walle_platform_library(
    name = "driveos_jpeg",
    srcs = [
        "driveos_jpeg_6090.cc",
    ],
    hdrs = [
        "driveos_jpeg_6090.h",
    ],
    copts = NVMEDIA_COPTS,
    linkopts = [
        "-lnvmedia_ijpd_sci",
        "-lnvmedia_ijpe_sci",
        "-lnvscibuf",
        "-lnvscisync",
        "-lnvscicommon",
    ],
    platform = "orin_drive",
    deps = [
        ":image_codec",
        ":image_convert",
        "//base/common:status",
        "//base/cuda:macros",
        "//base/file:file_util",
        "//base/math",
        "//base/strings:format",
        "//base/synchronization:mutex",
        "//third_party/driveos_codec/utils:driveos_codec_6090",
        "@cuda",
    ],
)

cc_library(
    name = "jpeg_codec_cpu",
    srcs = ["jpeg_codec_cpu.cc"],
    hdrs = ["jpeg_codec_cpu.h"],
    linkopts = [
        "-ljpeg",
    ],
    deps = [
        ":image_codec",
        ":image_data",
        "//base/common:cc_error_code_proto",
        "//base/common:macros",
        "//base/common:status",
        "//base/file:file_util",
        "//base/image:image_format",
    ],
)

cc_test(
    name = "jpeg_codec_cpu_test",
    size = "small",
    srcs = ["jpeg_codec_cpu_test.cc"],
    tags = [
        "ci",
        "ci_gpu",
    ],
    deps = [
        ":codec_util",
        ":image_codec",
        ":jpeg_codec_cpu",
        "//base/file:file_util",
        "//base/image:image_test_util",
        "//base/testing:scoped_temp_dir",
        "//base/testing:test_main",
    ],
)

cc_library(
    name = "png_codec",
    srcs = ["png_codec.cc"],
    hdrs = ["png_codec.h"],
    linkopts = [
        "-lpng",
    ],
    deps = [
        ":image_codec",
        "//base/common:status",
        "//base/file:file_util",
    ],
)

walle_platform_library(
    name = "image_thumbnail_npp",
    srcs = ["image_thumbnail.cc"],
    hdrs = ["image_thumbnail.h"],
    platform = "use_cuda",
    deps = [
        ":image_data",
        "//base/common:status",
        "//base/strings:format",
        "@cuda",
    ],
)

walle_platform_library(
    name = "image_thumbnail_dummy",
    srcs = ["image_thumbnail_dummy.cc"],
    hdrs = ["image_thumbnail.h"],
    platform = "no_cuda",
    deps = [
        ":image_data",
        "//base/common:status",
        "//base/strings:format",
    ],
)

cc_library(
    name = "image_thumbnail",
    deps = [
        ":image_thumbnail_dummy",
        ":image_thumbnail_npp",
    ],
)

cc_library(
    name = "image_test_util",
    srcs = ["image_test_util.cc"],
    hdrs = ["image_test_util.h"],
    deps = [
        ":image",
        ":image_data",
        "//base/common:cc_error_code_proto",
        "//base/common:status",
        "//base/file:file_util",
        "//base/file:file_path_util",
        #"@png_archive//:png",
        "@opencv2//:core",
        "@opencv2//:highgui",
        "@opencv2//:imgcodecs",
    ],
)

cc_library(
    name = "ffmpeg_codec",
    srcs = ["ffmpeg_codec.cc"],
    hdrs = ["ffmpeg_codec.h"],
    deps = [
        ":ffmpeg_av_codec",
        ":image_codec",
        ":image_data",
        "//base/common:status",
        "//base/math",
        "//base/strings:format",
        "//base/synchronization:mutex",
    ],
)

walle_platform_library(
    name = "driveos_video",
    srcs = [
        "driveos_video_6090.cc",
    ],
    hdrs = [
        "driveos_video_6090.h",
    ],
    copts = NVMEDIA_COPTS,
    linkopts = [
        "-lnvmedia_ide_sci",
        "-lnvscibuf",
        "-lnvscisync",
        "-lnvscicommon",
        "-lnvmedia_ide_parser",
    ],
    platform = "orin_drive",
    deps = [
        ":image_codec",
        ":image_convert",
        "//base/common:status",
        "//base/cuda:macros",
        "//base/file:file_util",
        "//base/math",
        "//base/strings:format",
        "//base/synchronization:mutex",
        "//third_party/driveos_codec/utils:driveos_codec_6090",
        "@cuda",
    ],
)

walle_platform_library(
    name = "jetson_video",
    srcs = [
        "jetson_video.cc",
    ],
    hdrs = [
        "jetson_video.h",
    ],
    linkopts = [
        "-lv4l2",
        "-lnvbuf_utils",
        "-lnvbufsurface",
        "-lnvbufsurftransform",
    ],
    platform = "orin_jetson",
    deps = [
        ":image_codec",
        ":image_convert",
        ":image_data",
        "//base/common:status",
        "//base/cuda:macros",
        "//base/file:file_util",
        "//base/math",
        "//base/strings:format",
        "//base/synchronization:mutex",
        "//third_party/jetson_video",
        "@cuda",
    ],
)

cc_test(
    name = "png_codec_test",
    size = "small",
    srcs = ["png_codec_test.cc"],
    tags = [
        "ci",
        "ci_cpu",
    ],
    deps = [
        ":image_test_util",
        ":image_util",
        ":png_codec",
        "//base/testing:scoped_temp_dir",
        "//base/testing:test_main",
    ],
)

cc_test(
    name = "jetson_jpeg_codec_test",
    size = "small",
    srcs = ["jetson_jpeg_codec_test.cc"],
    data = [":test_data"],
    tags = [
        "ci",
        "ci_gpu",
    ],
    deps = [
        "//base/file:file_path_util",
        "//base/file:file_util",
        "//base/image:image_util",
        "//base/testing:test_main",
    ],
)

cc_test(
    name = "image_convert_test",
    size = "small",
    srcs = ["image_convert_test.cc"],
    data = ["//base/image/test_data"],
    tags = [
        "ci",
        "ci_gpu",
    ],
    deps = [
        ":image_convert",
        "//base/file:file_util",
        "//base/testing:test_main",
    ],
)

cc_test(
    name = "image_thumbnail_test",
    size = "small",
    srcs = ["image_thumbnail_test.cc"],
    tags = [
        "ci",
        "ci_gpu",
    ],
    deps = [
        ":image_test_util",
        ":image_thumbnail",
        "//base/testing:scoped_temp_dir",
        "//base/testing:test_main",
    ],
)

cc_binary(
    name = "jpeg_codec_benchmark",
    srcs = ["jpeg_codec_benchmark.cc"],
    deps = [
        ":image_test_util",
        "//base/image:codec_util",
        "//base/image:image_codec",
        "@benchmark",
    ],
)

cc_binary(
    name = "driveos_jpeg_codec_benchmark",
    srcs = ["driveos_jpeg_codec_benchmark.cc"],
    deps = [
        ":image_test_util",
        "//base/image:codec_util",
        "//base/image:image_codec",
        "@benchmark",
    ],
)

cc_binary(
    name = "driveos_hevc_codec_benchmark",
    srcs = ["driveos_hevc_codec_benchmark.cc"],
    deps = [
        ":image_test_util",
        "//base/image:codec_util",
        "//base/image:image_codec",
        "@benchmark",
    ],
)

cc_binary(
    name = "image_thumbnail_benchmark",
    srcs = ["image_thumbnail_benchmark.cc"],
    deps = [
        ":image_test_util",
        ":image_thumbnail",
        "@benchmark",
    ],
)

cc_library(
    name = "image",
    hdrs = ["image.h"],
    deps = [
        ":image_data",
        "//base/common:macros",
        "//base/container:utils",
        "//base/image:image_format",
    ],
)

cc_test(
    name = "image_test",
    srcs = ["image_test.cc"],
    tags = [
        "ci",
        "ci_cpu",
    ],
    deps = [
        ":image",
        ":image_test_util",
        "//base/testing:test_main",
    ],
)

cc_test(
    name = "image_view_test",
    srcs = ["image_view_test.cc"],
    tags = [
        "ci",
        "ci_cpu",
    ],
    deps = [
        ":image",
        "//base/testing:test_main",
    ],
)

cc_library(
    name = "image_util",
    srcs = ["image_util.cc"],
    hdrs = ["image_util.h"],
    deps = [
        ":codec_util",
        ":image",
        ":image_codec",
        ":image_data",
        ":jpeg_codec_cpu",
        ":png_codec",
        "//base/common:macros",
        "//base/container:utils",
        "//base/image/proto:cc_image_proto",
        "//base/math:math_util",
        "//base/math:vector",
        "//base/utils:zlib_utils",
        "//common/util/image:image_util",
        "//base/utils:lz4_utils",
        "//base/utils:zstd_utils",
    ] + walle_platform_select(
        "use_cuda",
        ["//base/cuda:tensor"],
        [],
    ),
)

cc_test(
    name = "image_util_test",
    size = "small",
    srcs = ["image_util_test.cc"],
    data = ["//base/image/test_data"],
    tags = [
        "ci",
        "ci_cpu",
    ],
    deps = [
        ":image_test_util",
        ":image_util",
        "//base/testing:test_main",
        "//walle/engine/render:color_table",
        "@com_google_absl//absl/time",
    ],
)

cc_binary(
    name = "image_util_benchmark",
    srcs = ["image_util_benchmark.cc"],
    deps = [
        ":image_util",
        "//base/math:math_util",
        "@benchmark",
    ],
)

cc_library(
    name = "opencv_util",
    hdrs = [
        "opencv_util.h",
    ],
    deps = [
        ":image",
    ] + walle_platform_select(
        "horizon",
        [],
        ["@opencv2//:core"],
    ),
)

cc_test(
    name = "opencv_util_test",
    srcs = ["opencv_util_test.cc"],
    tags = [
        "ci",
        "ci_cpu",
    ],
    deps = [
        ":opencv_util",
        "//base/testing:test_main",
    ],
)

cc_library(
    name = "device_image",
    hdrs = ["device_image.h"],
    deps = [
        ":image",
        "//base/common:macros",
        "//base/cuda:device_buffer",
        "@glog",
    ],
)

cc_test(
    name = "device_image_test",
    srcs = ["device_image_test.cc"],
    tags = [
        "ci",
        "ci_gpu",
    ],
    deps = [
        ":device_image",
        ":device_image_view",
        ":image",
        "//base/testing:test_main",
    ],
)

cc_library(
    name = "device_image_view",
    hdrs = ["device_image_view.h"],
    deps = [
        ":device_image",
        "//base/common:macros",
        "//base/cuda:device_buffer",
    ],
)

cc_test(
    name = "device_image_view_test",
    srcs = ["device_image_view_test.cc"],
    tags = [
        "ci",
        "ci_gpu",
    ],
    deps = [
        ":device_image_view",
        ":image",
        "//base/testing:test_main",
    ],
)

cc_library(
    name = "device_image_utils",
    hdrs = ["device_image_utils.h"],
    deps = [
        ":device_image",
        "//base/common:macros",
        "//base/cuda:device_buffer",
        "//base/cuda/operators:cuda_ops_library",
    ],
)

cc_test(
    name = "device_image_utils_test",
    srcs = ["device_image_utils_test.cc"],
    data = ["//base/image/test_data"],
    tags = [
        "ci",
        "ci_gpu",
    ],
    deps = [
        ":device_image",
        ":device_image_utils",
        ":device_image_view",
        ":image",
        ":image_util",
        ":opencv_util",
        "//base/cuda:tensor",
        "//base/testing:test_main",
        "@opencv2//:imgcodecs",
        "@opencv2//:imgproc",
    ],
)

cc_binary(
    name = "device_image_utils_benchmark",
    srcs = ["device_image_utils_benchmark.cc"],
    data = ["//base/image/test_data"],
    deps = [
        ":device_image",
        ":device_image_utils",
        ":device_image_view",
        ":image",
        ":opencv_util",
        "@benchmark",
        "@gtest",
        "@opencv2//:imgcodecs",
        "@opencv2//:imgproc",
    ],
)

cc_library(
    name = "color",
    hdrs = ["color.h"],
    deps = [
        "@glm",
    ],
)

cc_test(
    name = "color_test",
    srcs = ["color_test.cc"],
    tags = [
        "ci",
        "ci_cpu",
    ],
    deps = [
        ":color",
        "//base/testing:test_main",
    ],
)

cc_library(
    name = "cairo_context",
    srcs = ["cairo_context.cc"],
    hdrs = ["cairo_context.h"],
    linkopts = select({
        "//:orin_jetson": [
            "-lnvjpeg",
        ],
        "//conditions:default": ["-ljpeg"],
    }),
    deps = [
        ":color",
        ":image",
        "//base/container:enum_class_hash",
        "//base/container:utils",
        "//base/math:circle2d",
        "//base/math:vector",
        "@com_google_absl//absl/strings",
        "@org_cairographics_cairo//:cairo",
    ],
)

cc_test(
    name = "cairo_context_test",
    srcs = ["cairo_context_test.cc"],
    tags = [
        "ci",
        "ci_cpu",
    ],
    deps = [
        ":cairo_context",
        "//base/testing:test_main",
    ],
)

cc_library(
    name = "image_painter",
    srcs = ["image_painter.cc"],
    hdrs = ["image_painter.h"],
    deps = [
        ":cairo_context",
        "//base/container:enum_class_hash",
        "//base/math:axis_align_box2d",
        "//base/math:circle2d",
        "//base/math:oriented_box2d",
        "//base/math:polygon2d",
    ],
)

cc_test(
    name = "image_painter_test",
    srcs = ["image_painter_test.cc"],
    data = [
        "//base/image/test_data",
    ],
    #temporarily_offline
    #tags = ["ci"],
    deps = [
        ":image_painter",
        "//base/testing:test_main",
    ],
)

cc_library(
    name = "gif_creator",
    srcs = ["gif_creator.cc"],
    hdrs = ["gif_creator.h"],
    deps = [
        ":image",
        ":image_util",
        "//base/common:macros",
        "//third_party/gif",
    ],
)

cc_test(
    name = "gif_creator_test",
    srcs = ["gif_creator_test.cc"],
    tags = [
        "ci",
        "ci_cpu",
    ],
    deps = [
        ":gif_creator",
        ":image_test_util",
        "//base/file:file_path_util",
        "//base/testing:test_main",
    ],
)

cc_library(
    name = "ffmpeg_av_codec",
    srcs = ["ffmpeg_av_codec.cc"],
    hdrs = ["ffmpeg_av_codec.h"],
    linkopts = [
        "-L/usr/local/lib",
        "-lavutil",
        "-lavcodec",
        "-lswscale",
    ],
    deps = [
        ":image_codec",
        ":image_data",
        "//base/common:status",
        "//base/math",
        "//base/strings:format",
        "//base/synchronization:mutex",
        "@gtest",
    ] + select({
        "//:use_cuda": [
            "//base/cuda:macros",
            "//base/image:npp_macros",
            "@cuda",
        ],
        "//conditions:default": [],
    }),
)

cc_test(
    name = "ffmpeg_av_codec_test",
    size = "small",
    srcs = ["ffmpeg_av_codec_test.cc"],
    data = [":test_data"],
    # TODO(lizhixiang09): run this ut after updating the new docker image, with new ffmpeg
    # tags = [
    #     "ci",
    #     "ci_gpu",
    # ],
    deps = [
        ":ffmpeg_av_codec",
        "//base/file:file_util",
        "//base/testing:test_main",
    ],
)

cc_library(
    name = "codec_util",
    srcs = ["codec_util.cc"],
    hdrs = ["codec_util.h"],
    copts = NVMEDIA_COPTS,
    local_defines = select({
        "//:use_centos_lidar": [],
        "//conditions:default": ["HAVE_FFMPEG"],
    }),
    implementation_deps = [":codec_util_impl"],
    deps = [
        ":flags",
        ":image_codec",
        "//base/image:png_codec",
        "@glog",
    ],
)

cc_library(
    name = "x86_64_codec_util_impl",
    deps = select({
        "//:use_centos_lidar": [
            ":jpeg_codec_cpu",
        ],
        "//conditions:default": [
            ":ffmpeg_codec",
            ":jpeg_codec_cpu",
        ],
    }),
)

cc_library(
    name = "codec_util_impl",
    deps = select({
        "//:x86_64": [
            ":x86_64_codec_util_impl",
        ],
        "//:orin_jetson": [
            ":jetson_jpeg",
            ":jetson_video",
        ],
        "//:orin_drive": [
            ":driveos_jpeg",
            ":driveos_video",
        ],
        "//conditions:default": [],
    }) + select({
        "//:x86_64_with_cuda": [
            ":nv_jpeg",
        ],
        "//conditions:default": [],
    }),
)

cc_test(
    name = "codec_util_test",
    size = "small",
    srcs = ["codec_util_test.cc"],
    copts = NVMEDIA_COPTS,
    tags = [
        "ci",
        "ci_gpu",
    ],
    deps = [
        ":codec_util",
        ":codec_util_impl",
        "//base/image:image_codec",
        "//base/strings:format",
        "//base/testing:test_main",
    ],
)

cc_library(
    name = "ffmpeg_av_encoder",
    srcs = ["ffmpeg_av_encoder.cc"],
    hdrs = ["ffmpeg_av_encoder.h"],
    linkopts = [
        "-L/usr/local/lib",
        "-lavutil",
        "-lavcodec",
        "-lswscale",
    ],
    deps = [
        ":image_codec",
        ":image_data",
        "//base/common:status",
        "//base/math",
        "//base/strings:format",
        "//base/synchronization:mutex",
    ] + select({
        "//:use_cuda": [
            "//base/cuda:macros",
            "//base/image:npp_macros",
            "@cuda",
        ],
        "//conditions:default": [],
    }),
)

cc_test(
    name = "ffmpeg_av_encoder_test",
    size = "small",
    srcs = ["ffmpeg_av_encoder_test.cc"],
    data = [":test_data"],
    linkopts = [
        "-L/usr/local/lib",
        "-lavcodec",
        "-lswscale",
    ],
    # TODO(lizhixiang09): open the ut after adding libx265 in docker
    # tags = [
    #     "ci",
    #     "ci_gpu",
    # ],
    deps = [
        ":ffmpeg_av_encoder",
        ":ffmpeg_codec",
        ":image_data",
        "//base/file:file_util",
        "//base/testing:test_main",
    ],
)
