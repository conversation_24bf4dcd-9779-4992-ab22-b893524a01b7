// Copyright @2022 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>
//          <PERSON><PERSON> (wuwang<PERSON>@meituan.com)

#include "base/image/cairo_context.h"

#include <fontconfig/fontconfig.h>

#include <unordered_map>

#include "absl/strings/match.h"

#include "base/container/enum_class_hash.h"

namespace image {

namespace {
using CairoFormat = cairo_format_t;
using CairoStatus = cairo_status_t;

constexpr char kPNGFileSuffix[] = ".png";

CairoFormat GetImageViewCairoFormat(const ImageView<uint8_t>& image_view) {
  // TODO(wanglong): support other format.
  CHECK_EQ(image_view.num_channels(), 4);
  return CAIRO_FORMAT_ARGB32;
}
}  // namespace

CairoContext::CairoContext(int height, int width) {
  cairo_surface_ = CHECK_NOTNULL(cairo_image_surface_create(CAIRO_FORMAT_ARGB32, width, height));
  cairo_ = CHECK_NOTNULL(cairo_create(cairo_surface_));
}

CairoContext::CairoContext(ImageView<uint8_t> image_view) {
  const CairoFormat cairo_format = GetImageViewCairoFormat(image_view);
  const int stride = cairo_format_stride_for_width(cairo_format, image_view.width());
  CHECK_EQ(stride, image_view.width() * image_view.num_bytes_per_pixel());
  cairo_surface_ = cairo_image_surface_create_for_data(
      image_view.mutable_data(), cairo_format, image_view.width(), image_view.height(), stride);
  CHECK(cairo_surface_ != nullptr);
  cairo_ = CHECK_NOTNULL(cairo_create(cairo_surface_));
}

CairoContext::~CairoContext() {
  cairo_destroy(cairo_);
  cairo_surface_destroy(cairo_surface_);
  // Fix memory leak when using cairo_show_text() following:
  //   https://gitlab.freedesktop.org/cairo/cairo/-/issues/393
  cairo_debug_reset_static_data();
  FcFini();
}

bool CairoContext::WriteToFile(const std::string& file_name) const {
  CHECK(absl::EndsWith(file_name, kPNGFileSuffix)) << "Only support write to png file.";
  const CairoStatus status = cairo_surface_write_to_png(cairo_surface_, file_name.c_str());
  if (status != CAIRO_STATUS_SUCCESS) {
    LOG(ERROR) << "write file faild: " << file_name;
    return false;
  }
  return true;
}

void CairoContext::CairoShowText(const math::Vector2d& position,
                                 const std::string& text,
                                 double font_size) {
  cairo_set_font_size(cairo_, font_size);
  cairo_move_to(cairo_, position.x, position.y);
  cairo_show_text(cairo_, text.c_str());
}

void CairoContext::CairoSetLineWidth(double line_width) {
  cairo_set_line_width(cairo_, line_width);
}

void CairoContext::CairoSetLineCap(CairoContext::CairoLineCapType line_cap) {
  cairo_set_line_cap(cairo_, line_cap);
}

void CairoContext::CairoSetLineJoin(CairoContext::CairoLineJoinType line_join) {
  cairo_set_line_join(cairo_, line_join);
}

void CairoContext::CairoSetDash(const std::vector<double>& dashes, double offset) {
  cairo_set_dash(cairo_, dashes.data(), dashes.size(), offset);
  CHECK(cairo_status(cairo_) != CAIRO_STATUS_INVALID_DASH) << "invalid dash value!";
}

double CairoContext::CairoGetLineWidth() { return cairo_get_line_width(cairo_); }

void CairoContext::CairoSetSourceRGBA(const Color& color) {
  cairo_set_source_rgba(cairo_, color.r, color.g, color.b, color.a);
}

void CairoContext::CairoArc(const math::Circle2d& circle, double angle1, double angle2) {
  cairo_arc(cairo_, circle.center().x, circle.center().y, circle.radius(), angle1, angle2);
}

void CairoContext::CairoMoveTo(const math::Vector2d& point) {
  cairo_move_to(cairo_, point.x, point.y);
}

void CairoContext::CairoLineTo(const math::Vector2d& point) {
  cairo_line_to(cairo_, point.x, point.y);
}

void CairoContext::CairoClosePath(bool close) {
  if (close) {
    cairo_close_path(cairo_);
  }
}

void CairoContext::CairoFill(bool fill) {
  if (fill) {
    cairo_fill(cairo_);
  }
}

void CairoContext::CairoAntialias(bool use_antialias) {
  if (use_antialias) {
    cairo_set_antialias(cairo_, CAIRO_ANTIALIAS_DEFAULT);
  } else {
    cairo_set_antialias(cairo_, CAIRO_ANTIALIAS_NONE);
  }
}

void CairoContext::CairoStroke() { cairo_stroke(cairo_); }

}  // namespace image
