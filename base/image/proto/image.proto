syntax = "proto3";

package image_proto;

// Next id: 5
enum ImageCodecType {
  PNG_CODEC = 0;
  JPEG_CODEC = 1;
  ZLIB_CODEC = 2;
  LZ4_CODEC = 3;
  ZSTD_CODEC = 4;
}

// Next id: 11
enum DataType {
  INT8 = 0;
  INT16 = 1;
  INT32 = 2;
  INT64 = 3;
  UINT8 = 4;
  UINT16 = 5;
  UINT32 = 6;
  UINT64 = 7;
  FLOAT16 = 8;
  FLOAT32 = 9;
  FLOAT64 = 10;
}

// Next id: 3
message EncodeImageData {
  // Use bytes here to avoid utf-8 check.
  bytes data = 1;
  ImageCodecType image_codec_type = 2;
}

// Next id: 8
message Image {
  int32 width = 1;
  int32 height = 2;
  int32 num_channels = 3;
  int32 num_bytes_per_channel = 4;
  DataType data_type = 7;
  oneof image_data {
    bytes raw_image_data = 5;
    EncodeImageData encoded_image_data = 6;
  }
}
