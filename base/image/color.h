// Copyright @2022 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include "glm/glm.hpp"

namespace image {

using Color = glm::dvec4;

class Color256 {
 public:
  static constexpr uint8_t kMaxUint8 = 255;

  Color256() : r(0), g(0), b(0), a(kMaxUint8) {}
  Color256(uint8_t r, uint8_t g, uint8_t b, uint8_t a = kMaxUint8) : r(r), g(g), b(b), a(a) {}

  explicit Color256(const Color& color)
      : r(static_cast<uint8_t>(color.r * kMaxUint8)),
        g(static_cast<uint8_t>(color.g * kMaxUint8)),
        b(static_cast<uint8_t>(color.b * kMaxUint8)),
        a(static_cast<uint8_t>(color.a * kMaxUint8)) {}

  Color ToColor() const {
    return Color(static_cast<double>(r) / kMaxUint8, static_cast<double>(g) / kMaxUint8,
                 static_cast<double>(b) / kMaxUint8, static_cast<double>(a) / kMaxUint8);
  }

  union {
    struct {
      uint8_t r;
      uint8_t g;
      uint8_t b;
      uint8_t a;
    };
    uint8_t elements[4];
  };
};

}  // namespace image
