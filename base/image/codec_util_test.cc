// Copyright @2023 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/image/codec_util.h"

#include "gtest/gtest.h"

#include "base/image/image_codec.h"

#if defined(USE_JETSON_ORIN) && defined(USE_CUDA)
#include "base/image/jetson_jpeg.h"
#include "base/image/jetson_video.h"
#elif defined(USE_DRIVE_ORIN) && defined(USE_CUDA)
#include "base/image/driveos_jpeg_6090.h"
#include "base/image/driveos_video_6090.h"
#elif defined(__x86_64__)
#include "base/image/ffmpeg_codec.h"
#include "base/image/jpeg_codec_cpu.h"
#if defined(USE_CUDA)
#include "base/image/nv_jpeg.h"
#endif
#elif !defined(USE_HORIZON)
#warning This platform doesn't have supported HEVC codec!
#endif

namespace image {

TEST(CodecUtilTest, TestCreateImageEncoder) {
  // Test for supported codec type.
  {
    std::unique_ptr<ImageEncoder> decoder = CreateImageEncoder(ImageCodecCreationOptions{
        .codec_type = CodecType::kJpeg,
        .device_type = DeviceType::kCpu,
        .quality = 75,
        .huffman_optimize = 1,
    });
#if defined(__x86_64__)
    EXPECT_TRUE(dynamic_cast<JpegEncoderCpu*>(decoder.get()) != nullptr);
#else
    EXPECT_EQ(decoder, nullptr);
#endif
  }

  // Test for non-supported codec type.
  {
    const CodecType codec_type = static_cast<CodecType>(-1);
    std::unique_ptr<ImageEncoder> decoder = CreateImageEncoder(ImageCodecCreationOptions{
        .codec_type = codec_type,
        .device_type = DeviceType::kCpu,
        .quality = 75,
        .huffman_optimize = 1,
    });
    EXPECT_EQ(decoder, nullptr);
  }
}

TEST(CodecUtilTest, TestCreateImageDecoder) {
  // Test for supported codec type.
  {
    std::unique_ptr<ImageDecoder> decoder = CreateImageDecoder(ImageCodecCreationOptions{
        .codec_type = CodecType::kJpeg,
        .device_type = DeviceType::kCpu,
        .quality = 75,
        .huffman_optimize = 1,
    });
#if defined(__x86_64__)
    EXPECT_TRUE(dynamic_cast<JpegDecoderCpu*>(decoder.get()) != nullptr);
#else
    EXPECT_EQ(decoder, nullptr);
#endif
  }

  // Test for non-supported codec type.
  {
    const CodecType codec_type = static_cast<CodecType>(-1);
    std::unique_ptr<ImageDecoder> decoder = CreateImageDecoder(ImageCodecCreationOptions{
        .codec_type = codec_type,
        .device_type = DeviceType::kCpu,
        .quality = 75,
        .huffman_optimize = 1,
    });
    EXPECT_EQ(decoder, nullptr);
  }
}

namespace internal {

TEST(CodecUtilTest, TestCreateHevcEncoder) {
  // GPU
  {
    std::unique_ptr<ImageEncoder> codec = CreateHevcEncoder(ImageCodecCreationOptions{
        .device_type = DeviceType::kCuda,
    });

#if defined(USE_JETSON_ORIN) && defined(USE_CUDA)
    EXPECT_NE(dynamic_cast<JetsonEncoder*>(codec.get()), nullptr);
#elif defined(USE_DRIVE_ORIN) && defined(USE_CUDA)
    EXPECT_NE(dynamic_cast<DriveosHevcEncoder*>(codec.get()), nullptr);
#elif defined(__x86_64__)
    EXPECT_NE(dynamic_cast<FFmpegEncoder*>(codec.get()), nullptr);
#else
    EXPECT_EQ(codec, nullptr);
#endif
  }

  // CPU
  {
    std::unique_ptr<ImageEncoder> codec = CreateHevcEncoder(ImageCodecCreationOptions{
        .device_type = DeviceType::kCpu,
    });

#if defined(__x86_64__)
    EXPECT_NE(dynamic_cast<FFmpegEncoder*>(codec.get()), nullptr);
#else
    EXPECT_EQ(codec, nullptr);
#endif
  }

  // Invalid device type.
  {
    const DeviceType device_type = static_cast<DeviceType>(-1);
    EXPECT_DEATH(CreateHevcEncoder(ImageCodecCreationOptions{
                     .device_type = device_type,
                 }),
                 "");
  }
}

TEST(CodecUtilTest, TestCreateHevcDecoder) {
  // GPU
  {
    std::unique_ptr<ImageDecoder> codec = CreateHevcDecoder(ImageCodecCreationOptions{
        .device_type = DeviceType::kCuda,
    });

#if defined(USE_JETSON_ORIN) && defined(USE_CUDA)
    EXPECT_NE(dynamic_cast<JetsonDecoder*>(codec.get()), nullptr);
#elif defined(USE_DRIVE_ORIN) && defined(USE_CUDA)
    EXPECT_NE(dynamic_cast<DriveosHevcDecoder*>(codec.get()), nullptr);
#elif defined(__x86_64__)
    EXPECT_NE(dynamic_cast<FFmpegDecoder*>(codec.get()), nullptr);
#else
    EXPECT_EQ(codec, nullptr);
#endif
  }

  // CPU
  {
    std::unique_ptr<ImageDecoder> codec = CreateHevcDecoder(ImageCodecCreationOptions{
        .device_type = DeviceType::kCpu,
    });

#if defined(__x86_64__)
    EXPECT_NE(dynamic_cast<FFmpegDecoder*>(codec.get()), nullptr);
#else
    EXPECT_EQ(codec, nullptr);
#endif
  }

  // Invalid device type.
  {
    const DeviceType device_type = static_cast<DeviceType>(-1);
    EXPECT_DEATH(CreateHevcDecoder(ImageCodecCreationOptions{
                     .device_type = device_type,
                 }),
                 "");
  }
}

TEST(CodecUtilTest, TestCreateJpegEncoderWithCudaDeviceType) {
  std::unique_ptr<ImageEncoder> encoder = CreateJpegEncoder(ImageCodecCreationOptions{
      .device_type = DeviceType::kCuda,
      .quality = 75,
      .huffman_optimize = 1,
  });

#if defined(USE_CUDA) && \
    (defined(__x86_64__) || defined(USE_JETSON_ORIN) || defined(USE_DRIVE_ORIN))
  EXPECT_NE(encoder, nullptr);
  EXPECT_TRUE(dynamic_cast<NvJpegEncoder*>(encoder.get()) != nullptr);
#else
  EXPECT_EQ(encoder, nullptr);
#endif
}

TEST(CodecUtilTest, TestCreateJpegEncoderWithCpuDeviceType) {
  std::unique_ptr<ImageEncoder> encoder = CreateJpegEncoder(ImageCodecCreationOptions{
      .device_type = DeviceType::kCpu,
      .quality = 75,
      .huffman_optimize = 1,
  });

#if defined(__x86_64__)
  EXPECT_NE(encoder, nullptr);
  EXPECT_TRUE(dynamic_cast<JpegEncoderCpu*>(encoder.get()) != nullptr);
#else
  EXPECT_EQ(encoder, nullptr);
#endif
}

TEST(CodecUtilTest, TestCreateJpegEncoderWithInvalidDeviceType) {
  const DeviceType device_type = static_cast<DeviceType>(-1);
  EXPECT_DEATH(CreateJpegEncoder(ImageCodecCreationOptions{
                   .device_type = device_type,
                   .quality = 75,
                   .huffman_optimize = 1,
               }),
               "");
}

TEST(CodecUtilTest, TestCreateJpegDecoderWithCudaDeviceType) {
  std::unique_ptr<ImageDecoder> decoder = CreateJpegDecoder(ImageCodecCreationOptions{
      .device_type = DeviceType::kCuda,
  });

#if defined(USE_CUDA) && \
    (defined(__x86_64__) || defined(USE_JETSON_ORIN) || defined(USE_DRIVE_ORIN))
  EXPECT_NE(dynamic_cast<NvJpegDecoder*>(decoder.get()), nullptr);
#else
  EXPECT_EQ(decoder, nullptr);
#endif
}

TEST(CodecUtilTest, TestCreateJpegDecoderWithCpuDeviceType) {
  std::unique_ptr<ImageDecoder> decoder = CreateJpegDecoder(ImageCodecCreationOptions{
      .device_type = DeviceType::kCpu,
  });

#if defined(__x86_64__)
  EXPECT_NE(dynamic_cast<JpegDecoderCpu*>(decoder.get()), nullptr);
#else
  EXPECT_EQ(decoder, nullptr);
#endif
}

TEST(CodecUtilTest, TestCreateJpegDecoderWithInvalidDeviceType) {
  const DeviceType device_type = static_cast<DeviceType>(-1);
  EXPECT_DEATH(CreateJpegDecoder(ImageCodecCreationOptions{
                   .device_type = device_type,
               }),
               "");
}

}  // namespace internal
}  // namespace image
