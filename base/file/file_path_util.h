// Copyright @2019 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <string>
#include <vector>

#include <boost/filesystem.hpp>

#include "base/common/optional.h"

namespace file_path {

template <typename ...Args>
std::string Join(const std::string& path1, const Args& ...paths) {
  boost::filesystem::path joined(path1);
  int joins[sizeof...(Args)] =
      { (joined = joined / paths, 0)... };
  (void)joins;
  return joined.string();
}

enum class SystemErrorType {
  SUCCESS = boost::system::errc::success,
  ADDRESS_FAMILY_NOT_SUPPORTED = boost::system::errc::address_family_not_supported,
  ADDRESS_IN_USE = boost::system::errc::address_in_use,
  ADDRESS_NOT_AVAILABLE = boost::system::errc::address_not_available,
  ALREADY_CONNECTED = boost::system::errc::already_connected,
  ARGUMENT_LIST_TOO_LONG = boost::system::errc::argument_list_too_long,
  ARGUMENT_OUT_OF_DOMAIN = boost::system::errc::argument_out_of_domain,
  BAD_ADDRESS = boost::system::errc::bad_address,
  BAD_FILE_DESCRIPTOR = boost::system::errc::bad_file_descriptor,
  BAD_MESSAGE = boost::system::errc::bad_message,
  BROKEN_PIPE = boost::system::errc::broken_pipe,
  CONNECTION_ABORTED = boost::system::errc::connection_aborted,
  CONNECTION_ALREADY_IN_PROGRESS = boost::system::errc::connection_already_in_progress,
  CONNECTION_REFUSED = boost::system::errc::connection_refused,
  CONNECTION_RESET = boost::system::errc::connection_reset,
  CROSS_DEVICE_LINK = boost::system::errc::cross_device_link,
  DESTINATION_ADDRESS_REQUIRED = boost::system::errc::destination_address_required,
  DEVICE_OR_RESOURCE_BUSY = boost::system::errc::device_or_resource_busy,
  DIRECTORY_NOT_EMPTY = boost::system::errc::directory_not_empty,
  EXECUTABLE_FORMAT_ERROR = boost::system::errc::executable_format_error,
  FILE_EXISTS = boost::system::errc::file_exists,
  FILE_TOO_LARGE = boost::system::errc::file_too_large,
  FILENAME_TOO_LONG = boost::system::errc::filename_too_long,
  FUNCTION_NOT_SUPPORTED = boost::system::errc::function_not_supported,
  HOST_UNREACHABLE = boost::system::errc::host_unreachable,
  IDENTIFIER_REMOVED = boost::system::errc::identifier_removed,
  ILLEGAL_BYTE_SEQUENCE = boost::system::errc::illegal_byte_sequence,
  INAPPROPRIATE_IO_CONTROL_OPERATION = boost::system::errc::inappropriate_io_control_operation,
  INTERRUPTED = boost::system::errc::interrupted,
  INVALID_ARGUMENT = boost::system::errc::invalid_argument,
  INVALID_SEEK = boost::system::errc::invalid_seek,
  IO_ERROR = boost::system::errc::io_error,
  IS_A_DIRECTORY = boost::system::errc::is_a_directory,
  MESSAGE_SIZE = boost::system::errc::message_size,
  NETWORK_DOWN = boost::system::errc::network_down,
  NETWORK_RESET = boost::system::errc::network_reset,
  NETWORK_UNREACHABLE = boost::system::errc::network_unreachable,
  NO_BUFFER_SPACE = boost::system::errc::no_buffer_space,
  NO_CHILD_PROCESS = boost::system::errc::no_child_process,
  NO_LINK = boost::system::errc::no_link,
  NO_LOCK_AVAILABLE = boost::system::errc::no_lock_available,
  NO_MESSAGE_AVAILABLE = boost::system::errc::no_message_available,
  NO_MESSAGE = boost::system::errc::no_message,
  NO_PROTOCOL_OPTION = boost::system::errc::no_protocol_option,
  NO_SPACE_ON_DEVICE = boost::system::errc::no_space_on_device,
  NO_STREAM_RESOURCES = boost::system::errc::no_stream_resources,
  NO_SUCH_DEVICE_OR_ADDRESS = boost::system::errc::no_such_device_or_address,
  NO_SUCH_DEVICE = boost::system::errc::no_such_device,
  NO_SUCH_FILE_OR_DIRECTORY = boost::system::errc::no_such_file_or_directory,
  NO_SUCH_PROCESS = boost::system::errc::no_such_process,
  NOT_A_DIRECTORY = boost::system::errc::not_a_directory,
  NOT_A_SOCKET = boost::system::errc::not_a_socket,
  NOT_A_STREAM = boost::system::errc::not_a_stream,
  NOT_CONNECTED = boost::system::errc::not_connected,
  NOT_ENOUGH_MEMORY = boost::system::errc::not_enough_memory,
  NOT_SUPPORTED = boost::system::errc::not_supported,
  OPERATION_CANCELED = boost::system::errc::operation_canceled,
  OPERATION_IN_PROGRESS = boost::system::errc::operation_in_progress,
  OPERATION_NOT_PERMITTED = boost::system::errc::operation_not_permitted,
  OPERATION_NOT_SUPPORTED = boost::system::errc::operation_not_supported,
  OPERATION_WOULD_BLOCK = boost::system::errc::operation_would_block,
  OWNER_DEAD = boost::system::errc::owner_dead,
  PERMISSION_DENIED = boost::system::errc::permission_denied,
  PROTOCOL_ERROR = boost::system::errc::protocol_error,
  PROTOCOL_NOT_SUPPORTED = boost::system::errc::protocol_not_supported,
  READ_ONLY_FILE_SYSTEM = boost::system::errc::read_only_file_system,
  RESOURCE_DEADLOCK_WOULD_OCCUR = boost::system::errc::resource_deadlock_would_occur,
  RESOURCE_UNAVAILABLE_TRY_AGAIN = boost::system::errc::resource_unavailable_try_again,
  RESULT_OUT_OF_RANGE = boost::system::errc::result_out_of_range,
  STATE_NOT_RECOVERABLE = boost::system::errc::state_not_recoverable,
  STREAM_TIMEOUT = boost::system::errc::stream_timeout,
  TEXT_FILE_BUSY = boost::system::errc::text_file_busy,
  TIMED_OUT = boost::system::errc::timed_out,
  TOO_MANY_FILES_OPEN_IN_SYSTEM = boost::system::errc::too_many_files_open_in_system,
  TOO_MANY_FILES_OPEN = boost::system::errc::too_many_files_open,
  TOO_MANY_LINKS = boost::system::errc::too_many_links,
  TOO_MANY_SYMBOLIC_LINK_LEVELS = boost::system::errc::too_many_symbolic_link_levels,
  VALUE_TOO_LARGE = boost::system::errc::value_too_large,
  WRONG_PROTOCOL_TYPE = boost::system::errc::wrong_protocol_type
};

bool Exists(const std::string& file_path);
bool Exists(const std::string& file_path, SystemErrorType* error_code, std::string* error_message);

bool IsDirectory(const std::string& file_path);

std::string GetTempPath();
std::string GetHomePath();

bool isRelativePath(const std::string& file_path);

std::string BaseName(const std::string& path);
base::Optional<std::string> DirName(const std::string& path);
std::string Stem(const std::string& path);

// Return false while path non-existent.
bool Equivalent(const std::string& path1, const std::string& path2);

// Example GenerateUniquePath("walle/%%%%/%%%%");
std::string GenerateUniquePath(const std::string& pattern);

boost::filesystem::perms GetDirectoryPerms775();

bool CreateDirectory(const std::string& path,
                     bool recursive,
                     boost::filesystem::perms perms = file_path::GetDirectoryPerms775());
bool CreateDirectoryIfNotExists(const std::string& path,
                                bool recursive,
                                boost::filesystem::perms perms = file_path::GetDirectoryPerms775());

bool RemovePath(const std::string& path, bool recursive);
bool MoveFile(const std::string& src_path, const std::string& dst_path);
bool CopyFile(const std::string& source_file, const std::string& dest_file);
bool CopyDirectory(const std::string& source_dir,
                   const std::string& dest_dir,
                   boost::filesystem::perms perms = file_path::GetDirectoryPerms775());

std::vector<std::string> ListFiles(const std::string& path,
                                   const std::string& filename_prefix,
                                   const std::string& filename_suffix);

std::vector<std::string> ListDirectories(const std::string& path, const std::string& pattern = "");

std::vector<std::string> GetRecordFilesByNodeName(const std::string& path,
                                                  const std::string& node_name,
                                                  const std::string& filename_suffix);

std::string GetExtensionOfFile(const std::string& file_name);

int64_t CalcDirectoryStorageSize(const std::string& path);
int64_t GetLastWriteTimestampForFile(const std::string& path);
int64_t GetFileSize(const std::string& path);
bool CreateSymbolLink(const std::string& path_based_on_pwd, const std::string& to_path);

}  // namespace file_path
