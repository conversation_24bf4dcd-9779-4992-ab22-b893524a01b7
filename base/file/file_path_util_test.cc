// Copyright @2019 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include <string>

#include "gtest/gtest.h"

#include "base/common/status.h"
#include "base/common/status_test_util.h"
#include "base/file/file_path_util.h"
#include "base/file/file_util.h"
#include "base/testing/scoped_temp_dir.h"

namespace base {

TEST(FilePathUtil, CreateDirectoryAndCopyFile) {
  std::string unique_path =
      file_path::Join(file_path::GetTempPath(), file_path::GenerateUniquePath("%%%%/%%%%"));
  std::string error_message;
  file_path::SystemErrorType error_code;
  EXPECT_FALSE(file_path::Exists(unique_path));
  EXPECT_FALSE(file_path::Exists(unique_path, &error_code, &error_message));
  EXPECT_EQ(error_code, file_path::SystemErrorType::NO_SUCH_FILE_OR_DIRECTORY);
  EXPECT_FALSE(file_path::CreateDirectory(unique_path, false));
  EXPECT_TRUE(file_path::CreateDirectory(unique_path, true));
  EXPECT_TRUE(file_path::Exists(unique_path));
  EXPECT_TRUE(file_path::Exists(unique_path, &error_code, &error_message));
  EXPECT_EQ(error_code, file_path::SystemErrorType::SUCCESS);

  const std::string source_path = file_path::Join(unique_path, "test_proto.bin");
  const std::string dest_path1 = file_path::Join(unique_path, "test_proto.bin.copy");
  const std::string dest_path2 = source_path;

  const std::string kNodeConfigs =
      "value1: 1, "
      "value2: 2.5, "
      "value3: \"test-proto\", ";
  CHECK(WriteContentToFile(source_path, kNodeConfigs));
  EXPECT_TRUE(file_path::CopyFile(source_path, dest_path1));
  EXPECT_TRUE(file_path::CopyFile(source_path, dest_path2));
  std::string contents;
  ReadFileToString(dest_path1, &contents);
  EXPECT_TRUE(contents == kNodeConfigs);
  contents.clear();
  ReadFileToString(dest_path2, &contents);
  EXPECT_TRUE(contents == kNodeConfigs);

  const std::string dest_path3 = file_path::Join(unique_path, "test_proto.bin.move");
  EXPECT_TRUE(file_path::MoveFile(source_path, dest_path3));
  contents.clear();
  ReadFileToString(dest_path3, &contents);
  EXPECT_TRUE(contents == kNodeConfigs);

  base::Optional<std::string> dir_name = file_path::DirName(unique_path);
  EXPECT_TRUE(dir_name != base::none);
  EXPECT_FALSE(file_path::RemovePath(*dir_name, false));
  EXPECT_TRUE(file_path::RemovePath(*dir_name, true));
}

TEST(FilePathUtil, FilePathComponent) {
  EXPECT_TRUE(file_path::DirName("file_path_without_dir") == base::none);
  EXPECT_EQ(file_path::BaseName("/tmp/bbb/aaa.txt"), "aaa.txt");
  EXPECT_EQ(file_path::BaseName("/tmp/bbb"), "bbb");
  EXPECT_NE(file_path::BaseName("/tmp/bbb/"), "bbb");
  EXPECT_EQ(*file_path::DirName("/tmp/bbb/aaa.txt"), "/tmp/bbb");
  EXPECT_EQ(file_path::Stem("/tmp/bbb/aaa.txt"), "aaa");
}

TEST(FilePathUtil, BaseNameForRoot) {
  base::Optional<std::string> base_path = file_path::DirName("/");
  EXPECT_TRUE(base_path == base::none);
}

TEST(FilePathUtil, BaseName) {
  const std::string& file_path = "/a/b/c/d/e";
  const std::string expected_result[] = {
      "/a/b/c/d/e",
      "/a/b/c/d",
      "/a/b/c",
      "/a/b",
      "/a",
      "/",
  };

  base::Optional<std::string> path = file_path;
  for (const base::Optional<std::string>& result : expected_result) {
    ASSERT_TRUE(path != base::none);
    EXPECT_EQ(*path, result);
    path = file_path::DirName(*path);
  }

  EXPECT_TRUE(path == base::none);
}

TEST(FilePathUtil, Equivalent) {
  EXPECT_TRUE(file_path::Equivalent("", "."));
  EXPECT_TRUE(file_path::Equivalent("", "./"));
  EXPECT_TRUE(file_path::Equivalent(".", "./"));
  EXPECT_FALSE(file_path::Equivalent("nonexistent_path", "nonexistent_path/"));
}

TEST(FilePathUtil, ListFiles) {
  // TODO(yanglei)
}

TEST(FileUtil, GetExtensionOfFile) {
  std::string proto_binary_file_name = "test_proto.bin";
  std::string proto_txt_file_name = "test_proto.pb.txt";
  std::string jpeg_file_name = "test_proto.jpg";
  std::string png_file_name = "test_proto.pb.png";
  EXPECT_EQ(file_path::GetExtensionOfFile(proto_binary_file_name), ".bin");
  EXPECT_EQ(file_path::GetExtensionOfFile(proto_txt_file_name), ".txt");
  EXPECT_EQ(file_path::GetExtensionOfFile(jpeg_file_name), ".jpg");
  EXPECT_EQ(file_path::GetExtensionOfFile(png_file_name), ".png");
}

TEST(FilePathUtil, CreateDirectoryPerm) {
  const std::string temp_root = file_path::GetTempPath();
  const std::string temp_path = file_path::Join(temp_root, "file_path_perm", "test");
  LOG(ERROR) << "temp_path:" << temp_path;
  file_path::CreateDirectory(temp_path, true, file_path::GetDirectoryPerms775());
  EXPECT_TRUE(file_path::Exists(temp_path));
  EXPECT_EQ(boost::filesystem::owner_all | boost::filesystem::group_all |
                boost::filesystem::others_read | boost::filesystem::others_exe,
            boost::filesystem::status(temp_path).permissions());
}

}  // namespace base
