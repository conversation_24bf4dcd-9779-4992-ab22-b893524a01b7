// Copyright @2019 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/file/file_util.h"

#include <fcntl.h>
#include <sys/stat.h>

#include <cstdio>
#include <fstream>

#include "absl/strings/str_split.h"
#include "glog/logging.h"
#include "google/protobuf/io/zero_copy_stream_impl.h"
#include "google/protobuf/text_format.h"

#include "base/common/eintr_wrapper.h"
#include "base/common/optional.h"
#include "base/file/file_path_util.h"

namespace base {
namespace {

constexpr int64_t kDefaultFileBlockSize = 128 * 1024LL;  // 128kB

using google::protobuf::Message;
using google::protobuf::TextFormat;

}  // namespace

ScopedFileHandle::ScopedFileHandle(const std::string& file_path, const char* mode) {
  file_ = fopen(file_path.c_str(), mode);
}

ScopedFileHandle::~ScopedFileHandle() {
  if (file_ != nullptr) {
    fclose(file_);
  }
}

FILE* ScopedFileHandle::handle() { return file_; }

bool ReadFileToString(const std::string& file_path, std::string* contents) {
  CHECK(contents != nullptr);
  CHECK(contents->empty());
  ScopedFileHandle scoped_handle(file_path.c_str(), "r");
  FILE* file = scoped_handle.handle();
  if (file == nullptr) {
    PLOG(ERROR) << "Failed open file \"" << file_path << "\" for read.";
    return false;
  }

  constexpr int kBufferSize = 10240;
  char buffer[kBufferSize] = {0};
  while (true) {
    int bytes_read = fread(buffer, 1, sizeof(buffer), file);
    if (bytes_read >= 0) {
      contents->append(buffer, bytes_read);
    }

    if (bytes_read < 0) {
      PLOG(ERROR) << "Error happens during reading file: \"" << file_path << "\"";
      return false;
    }

    if (bytes_read < kBufferSize) {
      return true;
    }
  }

  LOG(FATAL) << "Cannot be there.";
  return false;
}

bool ReadTextProtoFile(const std::string& file_path, Message* message, bool allow_unknown_field) {
  CHECK(message != nullptr);
  std::string contents;
  if (!ReadFileToString(file_path, &contents)) {
    return false;
  }

  if (allow_unknown_field) {
    TextFormat::Parser parser;
    parser.AllowUnknownField(true);
    return parser.ParseFromString(contents, message);
  }

  return TextFormat::ParseFromString(contents, message);
}

bool ReadBinaryProtoFile(const std::string& file_name, Message* message) {
  std::fstream input(file_name, std::ios::in | std::ios::binary);
  if (!input.good()) {
    PLOG(ERROR) << "Failed open file \"" << file_name << "\" for read.";
    return false;
  }
  if (!message->ParseFromIstream(&input)) {
    PLOG(ERROR) << "Failed to parse file \"" << file_name << "\" as binary proto.";
    return false;
  }
  return true;
}

bool ReadProtoFile(const std::string& file_name, Message* message) {
  if (absl::EndsWith(file_name, ".bin")) {
    return ReadBinaryProtoFile(file_name, message);
  } else {
    return ReadTextProtoFile(file_name, message);
  }
}

bool WriteContentToFile(const std::string& file_path,
                        const std::string& content,
                        const char* mode) {
  ScopedFileHandle scoped_handle(file_path.c_str(), mode);
  FILE* file = scoped_handle.handle();
  if (file == nullptr) {
    PLOG(ERROR) << "Failed open file for write: " << file_path;
    return false;
  }
  int status = chmod(file_path.c_str(), S_IRGRP | S_IWGRP | S_IROTH | S_IWOTH | S_IRUSR | S_IWUSR);

  if (status != 0) {
    PLOG(ERROR) << "Failed change file permission: " << file_path;
    return false;
  }

  size_t size = fwrite(content.c_str(), 1, content.length(), file);
  if (size != content.length()) {
    PLOG(ERROR) << "Failed to write content to file " << file_path
                << " length: " << content.length();
    return false;
  }

  return true;
}

bool WriteTextProtoToFile(const std::string& file_path, const Message& message, const char* mode) {
  std::string contents;
  CHECK(TextFormat::PrintToString(message, &contents))
      << "Failed to serialize protobuf: " << message.Utf8DebugString();
  return WriteContentToFile(file_path, contents, mode);
}

bool WriteBinaryProtoToFile(const std::string& file_path, const Message& message) {
  int fd = HANDLE_EINTR(
      open(file_path.c_str(), O_WRONLY | O_CREAT | O_TRUNC, S_IRUSR | S_IWUSR | S_IRGRP | S_IROTH));
  if (fd == -1) {
    PLOG(ERROR) << "Failed to open file: " << file_path;
    return false;
  }
  google::protobuf::io::FileOutputStream output(fd, kDefaultFileBlockSize);
  if (!message.SerializeToZeroCopyStream(&output)) {
    CHECK(output.Close());
    PLOG(ERROR) << "Failed to write protobuf to binary: " << message.Utf8DebugString();
    return false;
  }
  CHECK(output.Close());
  return true;
}

int64_t GetFileSize(const std::string& file_path) {
  struct stat info;
  stat(file_path.c_str(), &info);
  return info.st_size;
}

base::Optional<std::string> TryGenerateTargetPath(const std::string& base_path,
                                                  const std::string& sub_path,
                                                  boost::filesystem::perms perms) {
  if (base_path.empty()) {
    LOG(ERROR) << "Exporter base path can not be empty";
    return base::none;
  }
  const base::Optional<std::string> target_path = file_path::Join(base_path, sub_path);
  if (!target_path.has_value()) {
    LOG(ERROR) << "Target file is not initialized";
    return base::none;
  }
  if (target_path.has_value() && file_path::Exists(target_path.value())) {
    LOG(ERROR) << "Target file " << target_path.value() << " is already exists.";
  }
  const base::Optional<std::string> dir_name = file_path::DirName(target_path.value_or(""));
  if (!dir_name.has_value()) {
    LOG(ERROR) << "Target file " << target_path.value_or("") << " do not have valid dir name";
    return base::none;
  }
  if (!file_path::CreateDirectoryIfNotExists(dir_name.value(), true, perms)) {
    LOG(ERROR) << "CreateDirectoryIfNotExists failed";
    return base::none;
  }
  return target_path.value();
}

}  // namespace base
