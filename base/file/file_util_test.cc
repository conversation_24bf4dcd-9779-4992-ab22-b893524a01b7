// Copyright @2020 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/file/file_util.h"

#include <string>

#include "base/common/status.h"
#include "base/file/proto/test.pb.h"
#include "base/testing/scoped_temp_dir.h"
#include "google/protobuf/text_format.h"
#include "gtest/gtest.h"

namespace base {

TEST(FileUtil, AsciiProtoWriteAndRead) {
  const std::string kNodeConfigs =
      "value1: 1, "
      "value2: 2.5, "
      "value3: \"test-proto\", ";

  base::testing::ScopedTempDir scoped_dir("walle-%%%%");
  LOG(ERROR) << "temp_path: " << scoped_dir.temp_path();
  std::string proto_path = file_path::Join(scoped_dir.temp_path(), "test_proto.pb.txt");
  TestProto test_proto;
  CHECK(google::protobuf::TextFormat::ParseFromString(kNodeConfigs, &test_proto));
  EXPECT_TRUE(WriteTextProtoToFile(proto_path, test_proto));
  TestProto test_proto_read;
  EXPECT_TRUE(ReadTextProtoFile(proto_path, &test_proto_read));
  EXPECT_EQ(test_proto_read.value1(), 1);
  EXPECT_EQ(test_proto_read.value2(), 2.5);
  EXPECT_EQ(test_proto_read.value3(), "test-proto");
}

TEST(FileUtil, BinaryProtoWriteAndRead) {
  const std::string kNodeConfigs =
      "value1: 1, "
      "value2: 2.5, "
      "value3: \"test-proto\", ";

  base::testing::ScopedTempDir scoped_dir("walle-%%%%");
  LOG(ERROR) << "temp_path: " << scoped_dir.temp_path();
  std::string proto_path = file_path::Join(scoped_dir.temp_path(), "test_proto.bin");
  TestProto test_proto;
  CHECK(google::protobuf::TextFormat::ParseFromString(kNodeConfigs, &test_proto));
  EXPECT_TRUE(WriteBinaryProtoToFile(proto_path, test_proto));
  TestProto test_proto_read;
  EXPECT_TRUE(ReadBinaryProtoFile(proto_path, &test_proto_read));
  EXPECT_EQ(test_proto_read.value1(), 1);
  EXPECT_EQ(test_proto_read.value2(), 2.5);
  EXPECT_EQ(test_proto_read.value3(), "test-proto");
}

TEST(FileUtil, ReadAsciiProtoWithUnknownFields) {
  const std::string kNodeConfigsWithUnknownField =
      "value1: 1, "
      "value2: 2.5, "
      "value3: \"test-proto\", "
      "value6: 6, "
      "value4: \"unknown-proto\", "
      "value5: \"value5\", "
      "value7: 7, ";

  base::testing::ScopedTempDir scoped_dir("walle-%%%%");
  LOG(ERROR) << "temp_path: " << scoped_dir.temp_path();
  std::string proto_path = file_path::Join(scoped_dir.temp_path(), "test_proto.pb.txt");
  WriteContentToFile(proto_path, kNodeConfigsWithUnknownField);

  TestProto test_proto_read;
  // Even unknown fields is allowed:
  // 1. The result of function is false;
  // 2. The text content behind the unknown fields will not be parsed and lost in the object;
  EXPECT_FALSE(ReadTextProtoFile(proto_path, &test_proto_read, true));
  EXPECT_EQ(test_proto_read.value1(), 1);
  EXPECT_EQ(test_proto_read.value2(), 2.5);
  EXPECT_EQ(test_proto_read.value3(), "test-proto");
  EXPECT_EQ(test_proto_read.value6(), 6);
  EXPECT_TRUE(test_proto_read.value5().empty());
  EXPECT_EQ(test_proto_read.value7(), 0);
}

}  // namespace base
