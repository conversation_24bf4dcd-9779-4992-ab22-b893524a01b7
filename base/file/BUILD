package(default_visibility = ["//visibility:public"])

cc_library(
    name = "file_path_util",
    srcs = ["file_path_util.cc"],
    hdrs = ["file_path_util.h"],
    linkopts = [
        "-lboost_system",
        "-lboost_filesystem",
    ],
    deps = [
        "//base/common:optional",
        "//base/common:status",
    ],
)

cc_library(
    name = "file_util",
    srcs = ["file_util.cc"],
    hdrs = ["file_util.h"],
    linkopts = [
        "-lboost_system",
        "-lboost_filesystem",
    ],
    deps = [
        ":file_path_util",
        "//base/common:eintr_wrapper",
        "//base/common:macros",
        "//base/common:optional",
        "//base/common:status",
    ],
)

cc_test(
    name = "file_path_util_test",
    size = "small",
    srcs = ["file_path_util_test.cc"],
    tags = [
        "ci",
        "ci_cpu",
    ],
    deps = [
        ":file_path_util",
        ":file_util",
        "//base/common:status_test_util",
        "//base/testing:scoped_temp_dir",
        "//base/testing:test_main",
        "@com_google_absl//absl/strings",
    ],
)

cc_test(
    name = "file_util_test",
    size = "small",
    srcs = ["file_util_test.cc"],
    tags = [
        "ci",
        "ci_cpu",
    ],
    deps = [
        ":file_util",
        "//base/file/proto:cc_test_proto",
        "//base/testing:scoped_temp_dir",
        "//base/testing:test_main",
        "@com_google_absl//absl/strings",
    ],
)
