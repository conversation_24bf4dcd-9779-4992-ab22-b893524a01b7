// Copyright @2019 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <string>

#include <boost/filesystem.hpp>
#include "google/protobuf/message.h"

#include "base/common/macros.h"
#include "base/common/optional.h"
#include "base/file/file_path_util.h"

namespace base {

class ScopedFileHandle {
 public:
  ScopedFileHandle(const std::string& file_path, const char* mode);
  virtual ~ScopedFileHandle();

  FILE* handle();

 private:
  FILE* file_ = nullptr;
};

bool ReadFileToString(const std::string& file_path, std::string* contents);

// If file_name ends with 'bin', read file by ReadBinaryProtoFile. Otherwise, by ReadTextProtoFile
bool ReadProtoFile(const std::string& file_name, google::protobuf::Message* message);
// Be careful to use the `allow_unknown_field` flag unless you know what you're doing,
// It is strongly recommended to read more details about `ReadAsciiProtoWithUnknownFields` in
// current UT.
bool ReadTextProtoFile(const std::string& file_path,
                       google::protobuf::Message* message,
                       bool allow_unknown_field = false);
bool ReadBinaryProtoFile(const std::string& file_name, google::protobuf::Message* message);

bool WriteContentToFile(const std::string& file_path,
                        const std::string& content,
                        const char* mode = "w");
bool WriteTextProtoToFile(const std::string& file_path,
                          const google::protobuf::Message& message,
                          const char* mode = "w");
bool WriteBinaryProtoToFile(const std::string& file_path, const google::protobuf::Message& message);

int64_t GetFileSize(const std::string& file_path);

base::Optional<std::string> TryGenerateTargetPath(const std::string& base_path,
                                                  const std::string& sub_path,
                                                  boost::filesystem::perms perms =
                                                      file_path::GetDirectoryPerms775());

}  // namespace base
