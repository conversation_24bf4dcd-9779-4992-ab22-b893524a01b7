// Copyright @2019 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/file/file_path_util.h"

#include <fcntl.h>
#include <regex>
#include <unistd.h>

#include <boost/algorithm/string.hpp>
#include <boost/filesystem.hpp>

#include "absl/strings/match.h"
#include "glog/logging.h"

#include "base/common/status.h"

namespace file_path {

constexpr char kRecordNameSeperator[] = ".";

bool Exists(const std::string& file_path) {
  boost::filesystem::path path(file_path);
  return ::boost::filesystem::exists(path);
}

bool Exists(const std::string& file_path, SystemErrorType* error_code, std::string* error_message) {
  boost::filesystem::path path(file_path);
  boost::system::error_code ec;
  bool ret = ::boost::filesystem::exists(path, ec);
  *error_message = ec.message();
  *error_code = SystemErrorType(ec.value());
  return ret;
}

bool IsDirectory(const std::string& file_path) {
  boost::filesystem::path path(file_path);
  return ::boost::filesystem::is_directory(path);
}

std::string GetHomePath() {
  const char* home_dir = getenv("HOME");
  if (home_dir && home_dir[0]) {
    return std::string(home_dir);
  }

  return std::string();
}

std::string GetTempPath() {
  const char* test_tmp = getenv("TEST_TMPDIR");
  if (test_tmp && test_tmp[0]) {
    return std::string(test_tmp);
  }
  return boost::filesystem::temp_directory_path().string();
}

bool isRelativePath(const std::string& file_path) {
  boost::filesystem::path path(file_path);
  return path.is_relative();
}

std::string GenerateUniquePath(const std::string& pattern) {
  return boost::filesystem::unique_path(pattern).string();
}

std::string BaseName(const std::string& path) {
  boost::filesystem::path file_path(path);
  return file_path.filename().string();
}

base::Optional<std::string> DirName(const std::string& path) {
  boost::filesystem::path file_path(path);
  if (file_path.has_parent_path()) {
    return file_path.parent_path().string();
  } else {
    return base::None();
  }
}

std::string Stem(const std::string& path) {
  boost::filesystem::path file_path(path);
  return file_path.stem().string();
}

bool Equivalent(const std::string& path1, const std::string& path2) {
  boost::filesystem::path file_path1(path1);
  boost::filesystem::path file_path2(path2);
  if (file_path1.string() == "") {
    file_path1 = ".";
  }
  if (file_path2.string() == "") {
    file_path2 = ".";
  }
  if (!boost::filesystem::exists(file_path1)) {
    VLOG(10) << "Path doesn't exist: " << path1;
    return false;
  }
  if (!boost::filesystem::exists(file_path2)) {
    VLOG(10) << "Path doesn't exist: " << path2;
    return false;
  }
  boost::system::error_code error_code;
  bool ret = boost::filesystem::equivalent(file_path1, file_path2, error_code);
  if (error_code) {
    LOG(ERROR) << "Equivalent path error, reason:" << error_code.message();
    return false;
  }
  return ret;
}

boost::filesystem::perms GetDirectoryPerms775() {
  return boost::filesystem::owner_all |
         boost::filesystem::group_all |
         boost::filesystem::others_read |
         boost::filesystem::others_exe |
         boost::filesystem::add_perms;
}


bool CreateDirectory(const std::string& path, bool recursive, boost::filesystem::perms perms) {
  boost::filesystem::path file_path(path);
  boost::system::error_code error_code;

  if (recursive) {
    if (!boost::filesystem::create_directories(file_path, error_code)) {
      PLOG(ERROR) << "Failed to create path: " << file_path << " " << error_code.message();
      return false;
    }
  } else {
    if (!boost::filesystem::create_directory(file_path, error_code)) {
      PLOG(ERROR) << "Failed to create path: " << file_path << " " << error_code.message();
      return false;
    }
  }
  boost::filesystem::permissions(file_path, perms);
  return true;
}

bool CreateDirectoryIfNotExists(const std::string& path,
                                bool recursive,
                                boost::filesystem::perms perms) {
  if (Exists(path)) {
    return true;
  }

  return CreateDirectory(path, recursive, perms);
}

bool RemovePath(const std::string& path, bool recursive) {
  boost::filesystem::path file_path(path);
  boost::system::error_code error_code;
  if (recursive) {
    boost::filesystem::remove_all(file_path, error_code);
  } else {
    boost::filesystem::remove(file_path, error_code);
  }

  if (!error_code) {
    return true;
  }

  PLOG(ERROR) << "Failed to delete path: " << path << " " << error_code.message();
  return false;
}

bool MoveFile(const std::string& src_path, const std::string& dst_path) {
  if (!CopyFile(src_path, dst_path)) {
    return false;
  }
  if (!RemovePath(src_path, false)) {
    return false;
  }
  return true;
}

std::vector<std::string> ListFiles(const std::string& path,
                                   const std::string& filename_prefix,
                                   const std::string& filename_suffix) {
  std::vector<std::string> files;
  if (path.empty()) {
    return files;
  }
  if (!boost::filesystem::exists(path)) {
    LOG(ERROR) << "Path doesn't exist: " << path;
    return files;
  }
  boost::filesystem::directory_iterator iter(path);
  while (iter != boost::filesystem::directory_iterator()) {
    if ((filename_suffix.empty() || absl::EndsWith(iter->path().string(), filename_suffix)) &&
        (filename_prefix.empty() ||
         absl::StartsWith(iter->path().stem().string(), filename_prefix))) {
      files.emplace_back(iter->path().string());
    }
    ++iter;
  }
  return files;
}

std::vector<std::string> ListDirectories(const std::string& path, const std::string& pattern) {
  std::vector<std::string> directories;
  if (path.empty()) {
    return directories;
  }
  if (!boost::filesystem::exists(path)) {
    LOG(ERROR) << "Path doesn't exist: " << path;
    return directories;
  }

  for (boost::filesystem::directory_iterator iter(path);
       iter != boost::filesystem::directory_iterator(); ++iter) {
    if (!boost::filesystem::is_directory(iter->path())) {
      continue;
    }

    if (!pattern.empty()) {
      std::regex re(pattern);
      if (std::regex_match(BaseName(iter->path().string()), re)) {
        directories.emplace_back(iter->path().string());
      }
    } else {
      directories.emplace_back(iter->path().string());
    }
  }
  return directories;
}

std::vector<std::string> GetRecordFilesByNodeName(const std::string& path,
                                                  const std::string& node_name,
                                                  const std::string& filename_suffix) {
  return ListFiles(path, node_name + kRecordNameSeperator, filename_suffix);
}

bool CopyFile(const std::string& source_file, const std::string& dest_file) {
  if (Equivalent(source_file, dest_file)) {
    return true;
  }
  boost::system::error_code error_code;
  boost::filesystem::copy_file(source_file, dest_file, boost::filesystem::copy_option::overwrite_if_exists,
                               error_code);
  if (!error_code) {
    return true;
  }

  PLOG(ERROR) << "Failed to copy file: " << source_file << " to: " << dest_file << " " << error_code.message();
  return false;
}

bool CopyDirectory(const std::string& source_dir,
                   const std::string& dest_dir,
                   boost::filesystem::perms perms) {
  boost::filesystem::recursive_directory_iterator path_end;
  for (boost::filesystem::recursive_directory_iterator path(source_dir); path != path_end; ++path) {
    if (file_path::IsDirectory(boost::filesystem::path(*path).string())) {
      continue;
    }

    std::string source_file_path = boost::filesystem::path(*path).string();
    std::string dest_file_path;
    boost::replace_first_copy(std::back_inserter(dest_file_path), source_file_path, source_dir, dest_dir);
    auto dir_name = file_path::DirName(dest_file_path);
    if (dir_name != base::none) {
      file_path::CreateDirectoryIfNotExists(*dir_name, true, perms);
    }

    if (!file_path::CopyFile(source_file_path, dest_file_path)) {
      return false;
    }
  }

  if (!file_path::Exists(dest_dir)) {
    LOG(INFO) << "mkdir: " << dest_dir;
    return file_path::CreateDirectoryIfNotExists(dest_dir, true, perms);
  }

  return true;
}

std::string GetExtensionOfFile(const std::string& file_name) {
  std::string extension = boost::filesystem::extension(file_name);
  return extension;
}

int64_t CalcDirectoryStorageSize(const std::string& path) {
  if (!IsDirectory(path)) {
    LOG(ERROR) << path << "is not a directory";
    return -1;
  }
  int64_t dir_size = 0;
  for (const boost::filesystem::directory_entry& f :
       boost::filesystem::recursive_directory_iterator(path)) {
    if (boost::filesystem::is_regular_file(f.path())) {
      dir_size += boost::filesystem::file_size(f.path());
    }
  }
  return dir_size;
}

int64_t GetLastWriteTimestampForFile(const std::string& path) {
  if (!Exists(path)) {
    LOG(ERROR) << path << " doesn't exist";
    return -1;
  }

  return boost::filesystem::last_write_time(path) * 1e9;
}

int64_t GetFileSize(const std::string& path) {
  if (!Exists(path)) {
    LOG(ERROR) << path << " doesn't exist";
    return -1;
  }
  return boost::filesystem::file_size(path);
}

bool CreateSymbolLink(const std::string& path_based_on_pwd, const std::string& to_path) {
  boost::system::error_code error_code;
  boost::filesystem::path from_path =
      boost::filesystem::complete(boost::filesystem::path(path_based_on_pwd));
  boost::filesystem::create_symlink(from_path, to_path, error_code);
  return error_code ? false : true;
}

}  // namespace file_path
