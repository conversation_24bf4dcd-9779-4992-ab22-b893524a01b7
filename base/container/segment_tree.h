// Copyright @2022 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>
//          Zhipeng Hu (<EMAIL>)

#pragma once

#include <vector>

#include "glog/logging.h"

namespace base {
namespace container {

template <typename TreeNode>
class SegmentTree final {
 public:
  template <typename MakeNode, typename JoinNode>
  static std::vector<TreeNode> Build(const MakeNode& make_node, const JoinNode& join_node,
                                     int n_samples, int max_chunk = 1) {
    CHECK_GE(n_samples, 1);
    CHECK_GE(max_chunk, 1);
    const int n = (n_samples + max_chunk - 1) / max_chunk;
    std::vector<TreeNode> nodes(2 * n);
    Build(make_node, join_node, max_chunk, 0, 0, n - 1, &nodes);
    return nodes;
  }

 private:
  template <typename MakeNode, typename JoinNode>
  static void Build(const MakeNode& make_node, const JoinN<PERSON>& join_node, int max_chunk, int index,
                    int start, int end, std::vector<TreeNode>* nodes) {
    if (end - start + 1 <= max_chunk) {
      (*nodes)[index] = make_node(start);
    } else {
      const int mid = start + (end - start) / 2;
      const int l_index = index + 1;
      const int r_index = index + 2 * (mid - start + 1);
      Build(make_node, join_node, max_chunk, l_index, start, mid, nodes);
      Build(make_node, join_node, max_chunk, r_index, mid + 1, end, nodes);
      (*nodes)[index] = join_node((*nodes)[l_index], (*nodes)[r_index]);
    }
  }
};

}  // namespace container
}  // namespace base
