// Copyright @2025 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <algorithm>
#include <deque>
#include <limits>
#include <unordered_map>
#include <utility>
#include <vector>

#include "glog/logging.h"
#include "gtest/gtest.h"

#include "base/common/macros.h"
#include "base/common/optional.h"
#include "base/container/grid_cells.h"
#include "base/math/math_util.h"
#include "base/math/oriented_box2d.h"
#include "base/math/packed_index.h"
#include "base/math/polygon2d.h"
#include "base/math/vector2.h"
#include "base/strings/macros.h"

namespace base {
namespace container {

template <typename T, typename GridCellsContainer = GridCellsHashMap<T>>
class GridIndex final {
 public:
  GridIndex(math::Vector2d p0, math::Vector2d p1, double resolution);
  GridIndex(math::Vector2d p0, math::Vector2d p1, double resolution, T default_value);

  const GridCellsContainer& cells() const { return *cells_; }
  int x_size() const { return cells_->x_size(); }
  int y_size() const { return cells_->y_size(); }

  double min_x() const { return min_x_; }
  double min_y() const { return min_y_; }
  double max_x() const { return max_x_; }
  double max_y() const { return max_y_; }

  double resolution() const { return resolution_; }
  double resolution_inverse() const { return resolution_inverse_; }

  void Clear();

  bool IsInside(const math::Vector2i& index) const;

  const T* Get(const math::Vector2d& p) const;
  const T* Get(double x, double y) const;
  T* GetMutable(const math::Vector2d& p);
  T* GetMutable(double x, double y);

  const T* GetByIndex(const math::Vector2i& index) const;
  T* GetMutableByIndex(const math::Vector2i& index);

  base::Optional<math::Vector2i> GetIndex(double x, double y) const;
  math::Vector2i GetIndexOrDie(double x, double y) const;

  std::vector<const T*> GetAllInRect(const math::Vector2d& p0, const math::Vector2d& p1) const;
  std::vector<const T*> GetAllOnSegment(const math::Vector2d& p0, const math::Vector2d& p1) const;
  std::vector<const T*> GetAllInBox(const math::OrientedBox2d& box) const;
  std::vector<const T*> GetAllInCircle(const math::Vector2d& center, double radius) const;
  std::vector<const T*> GetAllInPolygon(const math::Polygon2d& polygon) const;

  std::vector<T*> GetAllMutableInRect(const math::Vector2d& p0, const math::Vector2d& p1);
  std::vector<T*> GetAllMutableOnSegment(const math::Vector2d& p0, const math::Vector2d& p1);
  std::vector<T*> GetAllMutableInBox(const math::OrientedBox2d& box);
  std::vector<T*> GetAllMutableInCircle(const math::Vector2d& center, double radius);
  std::vector<T*> GetAllMutableInPolygon(const math::Polygon2d& polygon);

  // Use template to enforce inline function calls, see
  //   https://stackoverflow.com/questions/14677997/stdfunction-vs-template
  // In most cases, Callback is std::function<void(const T*)>.
  template <typename Callback>
  void ForEachInRect(const math::Vector2d& p0, const math::Vector2d& p1, const Callback& fn) const;
  template <typename Callback>
  void ForEachOnSegment(const math::Vector2d& p0,
                        const math::Vector2d& p1,
                        const Callback& fn) const;
  template <typename Callback>
  void ForEachInBox(const math::OrientedBox2d& box, const Callback& fn) const;
  template <typename Callback>
  void ForEachInCircle(const math::Vector2d& center, double radius, const Callback& fn) const;
  template <typename Callback>
  void ForEachInPolygon(const math::Polygon2d& polygon, const Callback& fn) const;

  // In most cases, Callback is std::function<void(T*)>.
  template <typename Callback>
  void ForEachMutableInRect(const math::Vector2d& p0, const math::Vector2d& p1, const Callback& fn);
  template <typename Callback>
  void ForEachMutableOnSegment(const math::Vector2d& p0,
                               const math::Vector2d& p1,
                               const Callback& fn);
  template <typename Callback>
  void ForEachMutableInBox(const math::OrientedBox2d& box, const Callback& fn);
  template <typename Callback>
  void ForEachMutableInCircle(const math::Vector2d& center, double radius, const Callback& fn);
  template <typename Callback>
  void ForEachMutableInPolygon(const math::Polygon2d& polygon, const Callback& fn);

 private:
  GridIndex(math::Vector2d p0,
            math::Vector2d p1,
            double resolution,
            base::Optional<T> default_value);

  int GridXChecked(double x) const;
  int GridYChecked(double y) const;

  template <typename Callback>
  void ForEachIndexInRect(const math::Vector2d& p0,
                          const math::Vector2d& p1,
                          const Callback& fn) const;
  template <typename Callback>
  void ForEachIndexOnSegment(const math::Vector2d& p0,
                             const math::Vector2d& p1,
                             const Callback& fn) const;
  template <typename Callback>
  void ForEachIndexInBox(const math::OrientedBox2d& box, const Callback& fn) const;
  template <typename Callback>
  void ForEachIndexInCircle(const math::Vector2d& center, double radius, const Callback& fn) const;
  template <typename Callback>
  void ForEachIndexInPolygon(const math::Polygon2d& polygon, const Callback& fn) const;

  double min_x_ = 0.0;
  double min_y_ = 0.0;
  double max_x_ = 0.0;
  double max_y_ = 0.0;

  double resolution_ = 0.0;
  double resolution_inverse_ = 0.0;

  base::Optional<GridCellsContainer> cells_;

  FRIEND_TEST(GridIndexTest, GridXOrYChecked);
  FRIEND_TEST(GridIndexTest, ForEachIndexInRect);
  FRIEND_TEST(GridIndexTest, ForEachIndexOnSegment);
  FRIEND_TEST(GridIndexTest, ForEachIndexInBox);
  FRIEND_TEST(GridIndexTest, ForEachIndexInCircle);
  FRIEND_TEST(GridIndexTest, ForEachIndexInPolygon);

  DISALLOW_COPY_AND_ASSIGN(GridIndex);
};

template <typename T>
using GridIndexHashMap = GridIndex<T, GridCellsHashMap<T>>;
template <typename T>
using GridIndexArray2d = GridIndex<T, GridCellsArray2d<T>>;

template <typename T, typename C>
GridIndex<T, C>::GridIndex(math::Vector2d p0, math::Vector2d p1, double resolution)
    : GridIndex(p0, p1, resolution, base::none) {}

template <typename T, typename C>
GridIndex<T, C>::GridIndex(math::Vector2d p0, math::Vector2d p1, double resolution, T default_value)
    : GridIndex(p0, p1, resolution, base::make_optional(std::move(default_value))) {}

template <typename T, typename C>
GridIndex<T, C>::GridIndex(math::Vector2d p0,
                           math::Vector2d p1,
                           double resolution,
                           base::Optional<T> default_value)
    : min_x_(std::min(p0.x, p1.x)),
      min_y_(std::min(p0.y, p1.y)),
      max_x_(std::max(p0.x, p1.x)),
      max_y_(std::max(p0.y, p1.y)),
      resolution_(resolution) {
  CHECK_LE(min_x_, max_x_);
  CHECK_LE(min_y_, max_y_);
  CHECK_GE(resolution_, math::kEpsilon);

  resolution_inverse_ = 1.0 / resolution_;

  const int x_size = 1 + math::CastToFloorInteger((max_x_ - min_x_) * resolution_inverse_);
  const int y_size = 1 + math::CastToFloorInteger((max_y_ - min_y_) * resolution_inverse_);
  if (default_value == base::none) {
    cells_.emplace(x_size, y_size);
  } else {
    cells_.emplace(x_size, y_size, std::move(*default_value));
  }
}

template <typename T, typename C>
inline void GridIndex<T, C>::Clear() {
  cells_->Clear();
}

template <typename T, typename C>
inline bool GridIndex<T, C>::IsInside(const math::Vector2i& index) const {
  return (index.x >= 0 && index.x < cells_->x_size()) &&
         (index.y >= 0 && index.y < cells_->y_size());
}

template <typename T, typename C>
inline int GridIndex<T, C>::GridXChecked(double x) const {
  return static_cast<int>((x - min_x_) * resolution_inverse_);
}

template <typename T, typename C>
inline int GridIndex<T, C>::GridYChecked(double y) const {
  return static_cast<int>((y - min_y_) * resolution_inverse_);
}

template <typename T, typename C>
inline base::Optional<math::Vector2i> GridIndex<T, C>::GetIndex(double x, double y) const {
  const double clamped_x = math::Clamp(x, min_x_, max_x_);
  if (std::abs(clamped_x - x) >= math::kEpsilon) {
    return base::none;
  }
  const double clamped_y = math::Clamp(y, min_y_, max_y_);
  if (std::abs(clamped_y - y) >= math::kEpsilon) {
    return base::none;
  }
  return math::Vector2i(GridXChecked(clamped_x), GridYChecked(clamped_y));
}

template <typename T, typename C>
inline math::Vector2i GridIndex<T, C>::GetIndexOrDie(double x, double y) const {
  const base::Optional<math::Vector2i> index = GetIndex(x, y);
  CHECK(index != base::none) << DUMP_TO_STREAM(
      x, y, min_x_, max_x_, min_y_, max_y_, resolution_, resolution_inverse_);
  return *index;
}

template <typename T, typename C>
inline const T* GridIndex<T, C>::GetByIndex(const math::Vector2i& index) const {
  return IsInside(index) ? cells_->GetByIndex(index) : nullptr;
}

template <typename T, typename C>
inline T* GridIndex<T, C>::GetMutableByIndex(const math::Vector2i& index) {
  CHECK(IsInside(index)) << DUMP_TO_STREAM(index.x, index.y, cells_->x_size(), cells_->y_size());
  return cells_->GetMutableByIndex(index);
}

template <typename T, typename C>
inline const T* GridIndex<T, C>::Get(const math::Vector2d& p) const {
  return Get(p.x, p.y);
}

template <typename T, typename C>
inline const T* GridIndex<T, C>::Get(double x, double y) const {
  const base::Optional<math::Vector2i> index = GetIndex(x, y);
  return index != base::none ? cells_->GetByIndex(*index) : nullptr;
}

template <typename T, typename C>
inline T* GridIndex<T, C>::GetMutable(const math::Vector2d& p) {
  return GetMutable(p.x, p.y);
}

template <typename T, typename C>
inline T* GridIndex<T, C>::GetMutable(double x, double y) {
  const base::Optional<math::Vector2i> index = GetIndex(x, y);
  return index != base::none ? cells_->GetMutableByIndex(*index) : nullptr;
}

template <typename T, typename C>
inline std::vector<const T*> GridIndex<T, C>::GetAllInRect(const math::Vector2d& p0,
                                                           const math::Vector2d& p1) const {
  std::vector<const T*> elements;
  ForEachInRect(p0, p1, [&elements](const T* ptr) { elements.push_back(ptr); });
  return elements;
}

template <typename T, typename C>
inline std::vector<T*> GridIndex<T, C>::GetAllMutableInRect(const math::Vector2d& p0,
                                                            const math::Vector2d& p1) {
  std::vector<T*> elements;
  ForEachMutableInRect(p0, p1, [&elements](T* ptr) { elements.push_back(ptr); });
  return elements;
}

template <typename T, typename C>
template <typename Callback>
inline void GridIndex<T, C>::ForEachInRect(const math::Vector2d& p0,
                                           const math::Vector2d& p1,
                                           const Callback& fn) const {
  ForEachIndexInRect(p0, p1, [this, &fn](const math::Vector2i& index) {
    const T* ptr = cells_->GetByIndex(index);
    if (ptr != nullptr) {
      fn(ptr);
    }
  });
}

template <typename T, typename C>
template <typename Callback>
inline void GridIndex<T, C>::ForEachMutableInRect(const math::Vector2d& p0,
                                                  const math::Vector2d& p1,
                                                  const Callback& fn) {
  ForEachIndexInRect(
      p0, p1, [this, &fn](const math::Vector2i& index) { fn(cells_->GetMutableByIndex(index)); });
}

template <typename T, typename C>
template <typename Callback>
void GridIndex<T, C>::ForEachIndexInRect(const math::Vector2d& p0,
                                         const math::Vector2d& p1,
                                         const Callback& fn) const {
  const double min_x = std::max(min_x_, std::min(p0.x, p1.x));
  const double min_y = std::max(min_y_, std::min(p0.y, p1.y));
  const double max_x = std::min(max_x_, std::max(p0.x, p1.x));
  const double max_y = std::min(max_y_, std::max(p0.y, p1.y));
  if (min_x > max_x || min_y > max_y) {
    return;
  }
  const math::Vector2i index0(GridXChecked(min_x), GridYChecked(min_y));
  const math::Vector2i index1(GridXChecked(max_x), GridYChecked(max_y));
  for (int grid_x = index0.x; grid_x <= index1.x; ++grid_x) {
    for (int grid_y = index0.y; grid_y <= index1.y; ++grid_y) {
      fn(math::Vector2i(grid_x, grid_y));
    }
  }
}

template <typename T, typename C>
inline std::vector<const T*> GridIndex<T, C>::GetAllOnSegment(const math::Vector2d& p0,
                                                              const math::Vector2d& p1) const {
  std::vector<const T*> elements;
  ForEachOnSegment(p0, p1, [&elements](const T* ptr) { elements.push_back(ptr); });
  return elements;
}

template <typename T, typename C>
inline std::vector<T*> GridIndex<T, C>::GetAllMutableOnSegment(const math::Vector2d& p0,
                                                               const math::Vector2d& p1) {
  std::vector<T*> elements;
  ForEachMutableOnSegment(p0, p1, [&elements](T* ptr) { elements.push_back(ptr); });
  return elements;
}

template <typename T, typename C>
template <typename Callback>
inline void GridIndex<T, C>::ForEachOnSegment(const math::Vector2d& p0,
                                              const math::Vector2d& p1,
                                              const Callback& fn) const {
  ForEachIndexOnSegment(p0, p1, [this, &fn](const math::Vector2i& index) {
    const T* ptr = cells_->GetByIndex(index);
    if (ptr != nullptr) {
      fn(ptr);
    }
  });
}

template <typename T, typename C>
template <typename Callback>
inline void GridIndex<T, C>::ForEachMutableOnSegment(const math::Vector2d& p0,
                                                     const math::Vector2d& p1,
                                                     const Callback& fn) {
  ForEachIndexOnSegment(
      p0, p1, [this, &fn](const math::Vector2i& index) { fn(cells_->GetMutableByIndex(index)); });
}

template <typename T, typename C>
template <typename Callback>
void GridIndex<T, C>::ForEachIndexOnSegment(const math::Vector2d& p0,
                                            const math::Vector2d& p1,
                                            const Callback& fn) const {
  const double min_x = std::max(min_x_, std::min(p0.x, p1.x));
  const double min_y = std::max(min_y_, std::min(p0.y, p1.y));
  const double max_x = std::min(max_x_, std::max(p0.x, p1.x));
  const double max_y = std::min(max_y_, std::max(p0.y, p1.y));
  if (min_x > max_x || min_y > max_y) {
    return;
  }
  const math::Vector2i index0(GridXChecked(min_x), GridYChecked(min_y));
  const math::Vector2i index1(GridXChecked(max_x), GridYChecked(max_y));
  if (math::IsNear(p0.x, p1.x, math::kEpsilon) || math::IsNear(p0.y, p1.y, math::kEpsilon)) {
    for (int grid_x = index0.x; grid_x <= index1.x; ++grid_x) {
      for (int grid_y = index0.y; grid_y <= index1.y; ++grid_y) {
        fn(math::Vector2i(grid_x, grid_y));
      }
    }
  } else {
    const double slope = (p1.y - p0.y) / (p1.x - p0.x);
    for (int grid_x = index0.x; grid_x <= index1.x; ++grid_x) {
      const double segment_x0 = std::max(min_x, min_x_ + resolution_ * grid_x);
      const double segment_x1 = std::min(max_x, min_x_ + resolution_ * grid_x + resolution_);
      const double segment_y0 = slope * (segment_x0 - p0.x) + p0.y;
      const double segment_y1 = slope * (segment_x1 - p0.x) + p0.y;
      const double segment_min_y = std::max(min_y, std::min(segment_y0, segment_y1));
      const double segment_max_y = std::min(max_y, std::max(segment_y0, segment_y1));
      if (segment_min_y > segment_max_y) {
        continue;
      }
      const int min_grid_y = GridYChecked(segment_min_y);
      const int max_grid_y = GridYChecked(segment_max_y);
      for (int grid_y = min_grid_y; grid_y <= max_grid_y; ++grid_y) {
        fn(math::Vector2i(grid_x, grid_y));
      }
    }
  }
}

template <typename T, typename C>
inline std::vector<const T*> GridIndex<T, C>::GetAllInBox(const math::OrientedBox2d& box) const {
  std::vector<const T*> elements;
  ForEachInBox(box, [&elements](const T* ptr) { elements.push_back(ptr); });
  return elements;
}

template <typename T, typename C>
inline std::vector<T*> GridIndex<T, C>::GetAllMutableInBox(const math::OrientedBox2d& box) {
  std::vector<T*> elements;
  ForEachMutableInBox(box, [&elements](T* ptr) { elements.push_back(ptr); });
  return elements;
}

template <typename T, typename C>
template <typename Callback>
inline void GridIndex<T, C>::ForEachInBox(const math::OrientedBox2d& box,
                                          const Callback& fn) const {
  ForEachIndexInBox(box, [this, &fn](const math::Vector2i& index) {
    const T* ptr = cells_->GetByIndex(index);
    if (ptr != nullptr) {
      fn(ptr);
    }
  });
}

template <typename T, typename C>
template <typename Callback>
inline void GridIndex<T, C>::ForEachMutableInBox(const math::OrientedBox2d& box,
                                                 const Callback& fn) {
  ForEachIndexInBox(
      box, [this, &fn](const math::Vector2i& index) { fn(cells_->GetMutableByIndex(index)); });
}

template <typename T, typename C>
template <typename Callback>
void GridIndex<T, C>::ForEachIndexInBox(const math::OrientedBox2d& box, const Callback& fn) const {
  const double min_x = std::max(min_x_, box.aabox().min_x());
  const double min_y = std::max(min_y_, box.aabox().min_y());
  const double max_x = std::min(max_x_, box.aabox().max_x());
  const double max_y = std::min(max_y_, box.aabox().max_y());
  if (min_x > max_x || min_y > max_y) {
    return;
  }
  const math::AxisAlignBox2d grid_cell(math::Vector2d(min_x_, min_y_ + resolution_),
                                       math::Vector2d(min_x_ + resolution_, min_y_));
  const math::Vector2i index0(GridXChecked(min_x), GridYChecked(min_y));
  const math::Vector2i index1(GridXChecked(max_x), GridYChecked(max_y));
  for (int grid_x = index0.x; grid_x <= index1.x; ++grid_x) {
    for (int grid_y = index0.y; grid_y <= index1.y; ++grid_y) {
      if (box.HasOverlap(
              grid_cell.Shifted(math::Vector2d(grid_x * resolution_, grid_y * resolution_)))) {
        fn(math::Vector2i(grid_x, grid_y));
      }
    }
  }
}

template <typename T, typename C>
inline std::vector<const T*> GridIndex<T, C>::GetAllInCircle(const math::Vector2d& center,
                                                             double radius) const {
  std::vector<const T*> elements;
  ForEachInCircle(center, radius, [&elements](const T* ptr) { elements.push_back(ptr); });
  return elements;
}

template <typename T, typename C>
inline std::vector<T*> GridIndex<T, C>::GetAllMutableInCircle(const math::Vector2d& center,
                                                              double radius) {
  std::vector<T*> elements;
  ForEachMutableInCircle(center, radius, [&elements](T* ptr) { elements.push_back(ptr); });
  return elements;
}

template <typename T, typename C>
template <typename Callback>
inline void GridIndex<T, C>::ForEachInCircle(const math::Vector2d& center,
                                             double radius,
                                             const Callback& fn) const {
  ForEachIndexInCircle(center, radius, [this, &fn](const math::Vector2i& index) {
    const T* ptr = cells_->GetByIndex(index);
    if (ptr != nullptr) {
      fn(ptr);
    }
  });
}

template <typename T, typename C>
template <typename Callback>
inline void GridIndex<T, C>::ForEachMutableInCircle(const math::Vector2d& center,
                                                    double radius,
                                                    const Callback& fn) {
  ForEachIndexInCircle(center, radius, [this, &fn](const math::Vector2i& index) {
    fn(cells_->GetMutableByIndex(index));
  });
}

template <typename T, typename C>
template <typename Callback>
void GridIndex<T, C>::ForEachIndexInCircle(const math::Vector2d& center,
                                           double radius,
                                           const Callback& fn) const {
  CHECK_GE(radius, 0.0);
  const double min_x = std::max(min_x_, center.x - radius);
  const double min_y = std::max(min_y_, center.y - radius);
  const double max_x = std::min(max_x_, center.x + radius);
  const double max_y = std::min(max_y_, center.y + radius);
  if (min_x > max_x || min_y > max_y) {
    return;
  }
  const double radius_square = math::square(radius);
  const int min_grid_x = GridXChecked(min_x);
  const int max_grid_x = GridXChecked(max_x);
  for (int grid_x = min_grid_x; grid_x <= max_grid_x; ++grid_x) {
    const double segment_x0 = min_x_ + grid_x * resolution_;
    const double segment_x1 = segment_x0 + resolution_;
    double dy = radius;
    if (segment_x0 >= center.x) {
      dy = std::sqrt(std::max(0.0, radius_square - math::square(segment_x0 - center.x)));
    } else if (segment_x1 <= center.x) {
      dy = std::sqrt(std::max(0.0, radius_square - math::square(segment_x1 - center.x)));
    }
    const double segment_min_y = std::max(min_y, center.y - dy);
    const double segment_max_y = std::min(max_y, center.y + dy);
    if (segment_min_y > segment_max_y) {
      continue;
    }
    const int min_grid_y = GridYChecked(segment_min_y);
    const int max_grid_y = GridYChecked(segment_max_y);
    for (int grid_y = min_grid_y; grid_y <= max_grid_y; ++grid_y) {
      fn(math::Vector2i(grid_x, grid_y));
    }
  }
}

template <typename T, typename C>
inline std::vector<const T*> GridIndex<T, C>::GetAllInPolygon(
    const math::Polygon2d& polygon) const {
  std::vector<const T*> elements;
  ForEachInPolygon(polygon, [&elements](const T* ptr) { elements.push_back(ptr); });
  return elements;
}

template <typename T, typename C>
inline std::vector<T*> GridIndex<T, C>::GetAllMutableInPolygon(const math::Polygon2d& polygon) {
  std::vector<T*> elements;
  ForEachMutableInPolygon(polygon, [&elements](T* ptr) { elements.push_back(ptr); });
  return elements;
}

template <typename T, typename C>
template <typename Callback>
inline void GridIndex<T, C>::ForEachInPolygon(const math::Polygon2d& polygon,
                                              const Callback& fn) const {
  ForEachIndexInPolygon(polygon, [this, &fn](const math::Vector2i& index) {
    const T* ptr = cells_->GetByIndex(index);
    if (ptr != nullptr) {
      fn(ptr);
    }
  });
}

template <typename T, typename C>
template <typename Callback>
inline void GridIndex<T, C>::ForEachMutableInPolygon(const math::Polygon2d& polygon,
                                                     const Callback& fn) {
  ForEachIndexInPolygon(
      polygon, [this, &fn](const math::Vector2i& index) { fn(cells_->GetMutableByIndex(index)); });
}

template <typename T, typename C>
template <typename Callback>
void GridIndex<T, C>::ForEachIndexInPolygon(const math::Polygon2d& polygon,
                                            const Callback& fn) const {
  const double min_x = std::max(min_x_, polygon.aabox().min_x());
  const double min_y = std::max(min_y_, polygon.aabox().min_y());
  const double max_x = std::min(max_x_, polygon.aabox().max_x());
  const double max_y = std::min(max_y_, polygon.aabox().max_y());
  if (min_x > max_x || min_y > max_y) {
    return;
  }
  const std::vector<math::Vector2i> touched_grids =
      polygon.GetTouchedGrids(resolution_, math::Vector2d(min_x_, min_y_));
  for (const auto& index : touched_grids) {
    if (IsInside(index)) {
      fn(index);
    }
  }
}

}  // namespace container
}  // namespace base
