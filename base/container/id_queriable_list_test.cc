// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include <atomic>
#include <string>
#include <unordered_set>

#include "gtest/gtest.h"

#include "base/container/id_queriable_list.h"

namespace base {
namespace container {
namespace {

int next_id() {
  static std::atomic<int> next_id(0);
  return next_id++;
}

class IdObject {
 public:
  explicit IdObject(int value) : id_(next_id()), value_(value) {}
  int id() const { return id_; }
  int value() const { return value_; }
  void set_value(int value) { value_ = value; }

 private:
  int id_ = -1;
  int value_ = 0;
};

class StringIdObject {
 public:
  explicit StringIdObject(const std::string& id, int value) : id_(id), value_(value) {}
  std::string id() const { return id_; }
  int value() const { return value_; }
  void set_value(int value) { value_ = value; }

 private:
  std::string id_ = "";
  int value_ = 0;
};

}  // namespace

TEST(IdQueriableListTest, BasicTest) {
  IdQueriableList<IdObject> id_queriable_list;
  for (int i = 0; i < 10; ++i) {
    IdObject* id_object = id_queriable_list.Emplace(i);
    EXPECT_EQ(i, id_object->id());
    EXPECT_EQ(i, id_object->value());
  }
  const IdObject& id_object = id_queriable_list.GetObjectOrDie(4);
  EXPECT_EQ(4, id_object.value());

  IdObject* mutable_id_object = id_queriable_list.GetMutableObjectOrDie(4);
  mutable_id_object->set_value(100);
  EXPECT_EQ(100, id_object.value());
}

TEST(IdQueriableListTest, StringIdTypeTest) {
  IdQueriableList<StringIdObject, std::string> id_queriable_list;
  for (int i = 0; i < 10; ++i) {
    StringIdObject* id_object = id_queriable_list.Emplace(std::to_string(i), i);
    EXPECT_EQ(std::to_string(i), id_object->id());
    EXPECT_EQ(i, id_object->value());
  }
  const StringIdObject& id_object = id_queriable_list.GetObjectOrDie("4");
  EXPECT_EQ(4, id_object.value());

  StringIdObject* mutable_id_object = id_queriable_list.GetMutableObjectOrDie("4");
  mutable_id_object->set_value(100);
  EXPECT_EQ(100, id_object.value());
}

TEST(IdQueriableListTest, IteratorTest) {
  IdQueriableList<IdObject> id_queriable_list;
  for (int i = 0; i < 10; ++i) {
    IdObject* id_object = id_queriable_list.Emplace(i);
    EXPECT_EQ(i, id_object->value());
  }
  int count = 0;
  for (auto iter = id_queriable_list.begin(); iter != id_queriable_list.end(); ++iter) {
    EXPECT_EQ(count, iter->value());
    count++;
  }
}

TEST(IdQueriableListTest, RemoveTest) {
  IdQueriableList<IdObject> id_queriable_list;
  std::unordered_set<int> value_set;
  for (int i = 0; i < 10; ++i) {
    IdObject* id_object = id_queriable_list.Emplace(i);
    EXPECT_EQ(i, id_object->value());
  }
  auto iter = id_queriable_list.begin();
  iter++;
  iter = id_queriable_list.Remove(iter);

  for (iter = id_queriable_list.begin(); iter != id_queriable_list.end(); ++iter) {
    value_set.insert(iter->value());
  }
  EXPECT_TRUE(value_set.count(0));
  EXPECT_FALSE(value_set.count(1));
  EXPECT_TRUE(value_set.count(2));
}

TEST(IdQueiableListTest, RemoveIfTest) {
  std::unordered_set<int> remove_set;
  remove_set.insert(4);
  remove_set.insert(5);
  remove_set.insert(10);

  IdQueriableList<IdObject> id_queriable_list;
  for (int i = 0; i < 10; ++i) {
    IdObject* id_object = id_queriable_list.Emplace(i);
    EXPECT_EQ(i, id_object->value());
  }

  EXPECT_EQ(10, id_queriable_list.size());

  auto predict = [&remove_set](const IdObject& id_object) {
    if (remove_set.count(id_object.value())) {
      return true;
    }
    return false;
  };

  id_queriable_list.RemoveIf(predict);
  EXPECT_EQ(8, id_queriable_list.size());

  std::unordered_set<int> value_set;
  for (auto iter = id_queriable_list.begin(); iter != id_queriable_list.end(); ++iter) {
    value_set.insert(iter->value());
  }
  EXPECT_TRUE(value_set.count(3));
  EXPECT_FALSE(value_set.count(4));
  EXPECT_FALSE(value_set.count(5));
  EXPECT_TRUE(value_set.count(6));
}

TEST(IdQueiableListTest, ClearTest) {
  IdQueriableList<IdObject> id_queriable_list;
  for (int i = 0; i < 10; ++i) {
    IdObject* id_object = id_queriable_list.Emplace(i);
    EXPECT_EQ(i, id_object->value());
  }

  EXPECT_EQ(10, id_queriable_list.size());

  id_queriable_list.clear();
  EXPECT_EQ(0, id_queriable_list.size());
}

TEST(IdQueriableList, SortTest) {
  IdQueriableList<IdObject> id_queriable_list;
  for (int i = 0; i < 10; ++i) {
    IdObject* id_object = id_queriable_list.Emplace(i);
    EXPECT_EQ(i, id_object->value());
  }
  id_queriable_list.sort([](const IdObject& id_object0, const IdObject& id_object1) {
    return id_object0.value() > id_object1.value();
  });
  int i = 9;
  for (IdObject& id_object : id_queriable_list) {
    EXPECT_EQ(id_object.value(), i);
    --i;
  }
}

}  // namespace container
}  // namespace base
