// Copyright @2023 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/container/object_pool.h"

#include <thread>
#include <utility>

#include "gtest/gtest.h"

namespace base {

class ObjectPoolTest : public ::testing::Test {
 protected:
  void SetUp() override { object_pool_ = std::make_unique<ObjectPool<int>>(); }

  std::unique_ptr<ObjectPool<int>> object_pool_;
};

TEST_F(ObjectPoolTest, TestBasicOp) {
  {
    object_pool_->Add(std::unique_ptr<int>(new int(10)));
    EXPECT_EQ(object_pool_->Size(), 1);
    ObjectPool<int>::ObjectType v1 = object_pool_->Acquire();
    EXPECT_EQ(object_pool_->Size(), 0);
  }

  EXPECT_EQ(object_pool_->Si<PERSON>(), 1);
}

TEST_F(ObjectPoolTest, TestAcquireWhenEmpty) {
  std::thread t([this]() {
    // Wait for 1s, then add a new object into the pool.
    std::this_thread::sleep_for(std::chrono::seconds(1));
    auto unique_ptr = std::make_unique<int>(10);
    object_pool_->Add(std::move(unique_ptr));
  });

  // This line should block until the above thread adds a new object.
  ObjectPool<int>::ObjectType acquired_object = object_pool_->Acquire();
  EXPECT_EQ(*(acquired_object.get()), 10);
  EXPECT_EQ(object_pool_->Size(), 0);

  t.join();
}

}  // namespace base
