// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <memory>
#include <string>
#include <unordered_map>
#include <utility>
#include <vector>

// Disable eigen assert for concatenate operation
#ifndef EIGEN_NO_DEBUG
#define EIGEN_NO_DEBUG
#include <unsupported/Eigen/CXX11/Tensor>
#undef EIGEN_NO_DEBUG
#else
#include <unsupported/Eigen/CXX11/Tensor>
#endif

#include "base/strings/format.h"
#include "base/container/tensor_axis.h"
#include "base/container/tensor_util.h"

// Doc : https://km.sankuai.com/page/865446819

namespace base {
namespace container {

template<typename Scalar, int NumIndices, int Options = 0, typename EigenIndexType = int64_t>
class Tensor : public Eigen::Tensor<Scalar, NumIndices, Options, EigenIndexType> {
 public:
  typedef Eigen::Tensor<Scalar, NumIndices, Options, EigenIndexType> SuperType;
  typedef Tensor<Scalar, NumIndices, Options, EigenIndexType> SelfType;
  typedef Tensor<Scalar, NumIndices - 1, Options, EigenIndexType> Sub1DimType;
  typedef Tensor<Scalar, NumIndices + 1, Options, EigenIndexType> Add1DimType;

  Tensor() = default;

  Tensor(const SuperType& tensor, const std::vector<std::unique_ptr<AxisBase>>& axes,
      const std::unordered_map<std::string, int>& axes_sequence) : SuperType(tensor), axes_sequence_(axes_sequence) {
    for (const std::unique_ptr<AxisBase>& axis : axes) {
      CHECK(axis.get() != nullptr) << "Copying axes have nullptr";
      axes_.push_back(Copy(axis));
      CHECK(axes_.back().get() != nullptr) << "Copied axes have nullptr";
    }
  }

  Tensor(Tensor&& other) {
    this->SuperType::operator=(SuperType(other));
    axes_ = std::move(other.axes_);
    axes_sequence_ = std::move(other.axes_sequence_);
  }

  explicit Tensor(const Tensor& other) : SuperType(SuperType(other)), axes_sequence_(other.axes_sequence_) {
    for (const std::unique_ptr<AxisBase>& axis : other.axes_) {
      axes_.push_back(Copy(axis));
    }
  }

  template<typename... IndexTypes>
  Tensor(EigenIndexType first_dim, IndexTypes... other_dims) : SuperType(first_dim, other_dims...) {}

  SelfType& operator=(const SelfType& other) {
    axes_.clear();
    axes_sequence_.clear();
    SuperType::operator=(SuperType(other));
    for (const std::unique_ptr<AxisBase>& axis : other.axes_) {
      axes_.push_back(Copy(axis));
    }
    axes_sequence_ = other.axes_sequence_;
    return *this;
  }

  SelfType& operator=(SelfType&& other) {
    axes_.clear();
    axes_sequence_.clear();
    SuperType::operator=(std::move(SuperType(other)));
    axes_ = std::move(other.axes_);
    axes_sequence_ = std::move(other.axes_sequence_);
    return *this;
  }

  template<typename IndexType, typename AxisType = typename AxisTypeSelector<IndexType>::AxisType>
  void PushAxis(const std::string& axis_name, const std::vector<IndexType>& indices) {
    CHECK(axes_sequence_.find(axis_name) == axes_sequence_.end()) << "Duplicated axis "
        << strings::ToString(axis_name);
    std::unique_ptr<AxisType> axis = std::make_unique<AxisType>(axis_name, indices);
    axes_.push_back(std::move(axis));
    axes_sequence_[axis_name] = axes_.size() - 1;
    CHECK(axes_.size() <= NumIndices) << strings::ToString("Axes num > dimension");
  }

  template<typename IndexType, typename AxisType = typename AxisTypeSelector<IndexType>::AxisType>
  void ResetAxis(const std::string& axis_name, const std::vector<IndexType>& indices) {
    CHECK(axes_sequence_.find(axis_name) != axes_sequence_.end()) << "Cannot find axis "
        << strings::ToString(axis_name);
    int sequence = axes_sequence_[axis_name];
    std::unique_ptr<AxisType> axis = std::make_unique<AxisType>(axis_name, indices);
    axes_[sequence] = std::move(axis);
    CHECK(axes_.size() == NumIndices) << "Size error";

    int changed_sequence = axes_sequence_[axis_name];
    Eigen::array<EigenIndexType, NumIndices> sz;
    for (int i = 0; i < NumIndices; ++i) {
      if (i == changed_sequence) {
        sz[i] = indices.size();
      } else {
        sz[i] = SuperType::dimension(i);
      }
    }
    SuperType::resize(sz);
  }

  template<typename ByAxisIndexType, typename AxisType = typename AxisTypeSelector<ByAxisIndexType>::AxisType>
  void Stack(const SelfType& other, const std::string& by_axis) {
    // All axes must be same
    CHECK(axes_.size() == other.axes_.size())
        << "Cannot stack because tensor axes are not same";
    for (auto& p : axes_sequence_) {
      auto f = other.axes_sequence_.find(p.first);
      CHECK(f != other.axes_sequence_.end() && f->second == p.second)
          << "Stacked tensor has no axis " << f->first
          << " or index is not " << f->second;
    }
    AxisType* axis = dynamic_cast<AxisType*>(axes_[axes_sequence_.at(by_axis)].get());
    AxisType* other_axis = dynamic_cast<AxisType*>(other.axes_[other.axes_sequence_.at(by_axis)].get());

    axis->Stack(other_axis);
    SuperType res = SuperType::concatenate(static_cast<const SuperType&>(other), axes_sequence_.at(by_axis)).eval();

    this->SuperType::operator=(res);
  }

  template<typename IndexType, typename AxisType = typename AxisTypeSelector<IndexType>::AxisType>
  Sub1DimType Chip(const IndexType& index, const std::string& by_axis) const {
    AxisType* axis = dynamic_cast<AxisType*>(axes_[axes_sequence_.at(by_axis)]);
    CHECK(axis) << "Cannot find axes " << by_axis;
    int sequence = axis->GetIndexSequence(index);
    return Chip(sequence, by_axis);
  }

  Sub1DimType Chip(int offset, const std::string& by_axis) const {
    CHECK(axes_sequence_.find(by_axis) != axes_sequence_.end()) << "Cannot find by_axis";
    const int& sequence = axes_sequence_.at(by_axis);
    Sub1DimType ret(SuperType::chip(offset, sequence), axes_, axes_sequence_);
    ret.axes().erase(ret.axes().begin() + sequence);
    ret.axes_sequence().erase(by_axis);
    for (auto& p : ret.axes_sequence()) {
      if (p.second > sequence) {
        p.second--;
      }
    }
    return ret;
  }

  template<typename Index0Type,
      typename Param0Type = typename AxisTypeSelector<Index0Type>::ParamType,
      typename Axis0Type = typename AxisTypeSelector<Param0Type>::AxisType>
  const Scalar& At(Index0Type index0) const {
    CHECK(axes_.size() == 1) << "Axes num should be 1";
    CHECK(axes_[0].get() != nullptr) << "Axis 0 is nullptr";
    Axis0Type* axis0 = dynamic_cast<Axis0Type*>(axes_[0].get());
    return SuperType::operator()(Eigen::array<EigenIndexType, 1>{ axis0->GetIndexSequence(Param0Type(index0))});
  }

  template<typename Index0Type,
      typename Param0Type = typename AxisTypeSelector<Index0Type>::ParamType,
      typename Axis0Type = typename AxisTypeSelector<Param0Type>::AxisType>
  Scalar& MultableAt(Index0Type index0) {
    CHECK(axes_.size() == 1) << "Axes num should be 1";
    CHECK(axes_[0].get() != nullptr) << "Axis 0 is nullptr";
    Axis0Type* axis0 = dynamic_cast<Axis0Type*>(axes_[0].get());
    return SuperType::operator()(Eigen::array<EigenIndexType, 1>{ axis0->GetIndexSequence(Param0Type(index0))});
  }

  template<typename Index0Type, typename Index1Type,
      typename Param0Type = typename AxisTypeSelector<Index0Type>::ParamType,
      typename Param1Type = typename AxisTypeSelector<Index1Type>::ParamType,
      typename Axis0Type = typename AxisTypeSelector<Param0Type>::AxisType,
      typename Axis1Type = typename AxisTypeSelector<Param1Type>::AxisType>
  const Scalar& At(Index0Type index0, Index1Type index1) const {
    CHECK(axes_.size() == 2) << "Axes num should be 2";
    CHECK(axes_[0].get() != nullptr) << "Axis 0 is nullptr";
    CHECK(axes_[1].get() != nullptr) << "Axis 1 is nullptr";
    Axis0Type* axis0 = dynamic_cast<Axis0Type*>(axes_[0].get());
    Axis1Type* axis1 = dynamic_cast<Axis1Type*>(axes_[1].get());
    return SuperType::operator()(Eigen::array<EigenIndexType, 2>{
        axis0->GetIndexSequence(Param0Type(index0)), axis1->GetIndexSequence(Param1Type(index1))});
  }

  template<typename Index0Type, typename Index1Type,
      typename Param0Type = typename AxisTypeSelector<Index0Type>::ParamType,
      typename Param1Type = typename AxisTypeSelector<Index1Type>::ParamType,
      typename Axis0Type = typename AxisTypeSelector<Param0Type>::AxisType,
      typename Axis1Type = typename AxisTypeSelector<Param1Type>::AxisType>
  Scalar& MutableAt(Index0Type index0, Index1Type index1) {
    CHECK(axes_.size() == 2) << "Axes num should be 2";
    CHECK(axes_[0].get() != nullptr) << "Axis 0 is nullptr";
    CHECK(axes_[1].get() != nullptr) << "Axis 1 is nullptr";
    Axis0Type* axis0 = dynamic_cast<Axis0Type*>(axes_[0].get());
    Axis1Type* axis1 = dynamic_cast<Axis1Type*>(axes_[1].get());
    return SuperType::operator()(Eigen::array<EigenIndexType, 2>{
        axis0->GetIndexSequence(Param0Type(index0)), axis1->GetIndexSequence(Param1Type(index1))});
  }

  template<typename Index0Type, typename Index1Type, typename Index2Type,
      typename Param0Type = typename AxisTypeSelector<Index0Type>::ParamType,
      typename Param1Type = typename AxisTypeSelector<Index1Type>::ParamType,
      typename Param2Type = typename AxisTypeSelector<Index2Type>::ParamType,
      typename Axis0Type = typename AxisTypeSelector<Param0Type>::AxisType,
      typename Axis1Type = typename AxisTypeSelector<Param1Type>::AxisType,
      typename Axis2Type = typename AxisTypeSelector<Param2Type>::AxisType>
  const Scalar& At(Index0Type index0, Index1Type index1, Index2Type index2) const {
    CHECK(axes_.size() == 3) << "Axes num should be 3";
    CHECK(axes_[0].get() != nullptr) << "Axis 0 is nullptr";
    CHECK(axes_[1].get() != nullptr) << "Axis 1 is nullptr";
    CHECK(axes_[2].get() != nullptr) << "Axis 2 is nullptr";
    Axis0Type* axis0 = dynamic_cast<Axis0Type*>(axes_[0].get());
    Axis1Type* axis1 = dynamic_cast<Axis1Type*>(axes_[1].get());
    Axis2Type* axis2 = dynamic_cast<Axis2Type*>(axes_[2].get());
    return SuperType::operator()(Eigen::array<EigenIndexType, 3>{
        axis0->GetIndexSequence(Param0Type(index0)), axis1->GetIndexSequence(Param1Type(index1)),
        axis2->GetIndexSequence(Param2Type(index2))});
  }

  template<typename Index0Type, typename Index1Type, typename Index2Type,
      typename Param0Type = typename AxisTypeSelector<Index0Type>::ParamType,
      typename Param1Type = typename AxisTypeSelector<Index1Type>::ParamType,
      typename Param2Type = typename AxisTypeSelector<Index2Type>::ParamType,
      typename Axis0Type = typename AxisTypeSelector<Param0Type>::AxisType,
      typename Axis1Type = typename AxisTypeSelector<Param1Type>::AxisType,
      typename Axis2Type = typename AxisTypeSelector<Param2Type>::AxisType>
  Scalar& MutableAt(Index0Type index0, Index1Type index1, Index2Type index2) {
    CHECK(axes_.size() == 3) << "Axes num should be 3";
    CHECK(axes_[0].get() != nullptr) << "Axis 0 is nullptr";
    CHECK(axes_[1].get() != nullptr) << "Axis 1 is nullptr";
    CHECK(axes_[2].get() != nullptr) << "Axis 2 is nullptr";
    Axis0Type* axis0 = dynamic_cast<Axis0Type*>(axes_[0].get());
    Axis1Type* axis1 = dynamic_cast<Axis1Type*>(axes_[1].get());
    Axis2Type* axis2 = dynamic_cast<Axis2Type*>(axes_[2].get());
    return SuperType::operator()(Eigen::array<EigenIndexType, 3>{
        axis0->GetIndexSequence(Param0Type(index0)), axis1->GetIndexSequence(Param1Type(index1)),
        axis2->GetIndexSequence(Param2Type(index2))});
  }

  template<typename Index0Type, typename Index1Type, typename Index2Type, typename Index3Type,
      typename Param0Type = typename AxisTypeSelector<Index0Type>::ParamType,
      typename Param1Type = typename AxisTypeSelector<Index1Type>::ParamType,
      typename Param2Type = typename AxisTypeSelector<Index2Type>::ParamType,
      typename Param3Type = typename AxisTypeSelector<Index3Type>::ParamType,
      typename Axis0Type = typename AxisTypeSelector<Param0Type>::AxisType,
      typename Axis1Type = typename AxisTypeSelector<Param1Type>::AxisType,
      typename Axis2Type = typename AxisTypeSelector<Param2Type>::AxisType,
      typename Axis3Type = typename AxisTypeSelector<Param3Type>::AxisType>
  const Scalar& At(Index0Type index0, Index1Type index1, Index2Type index2, Index3Type index3) const {
    CHECK(axes_.size() == 4) << "Axes num should be 4";
    CHECK(axes_[0].get() != nullptr) << "Axis 0 is nullptr";
    CHECK(axes_[1].get() != nullptr) << "Axis 1 is nullptr";
    CHECK(axes_[2].get() != nullptr) << "Axis 2 is nullptr";
    CHECK(axes_[3].get() != nullptr) << "Axis 3 is nullptr";
    Axis0Type* axis0 = dynamic_cast<Axis0Type*>(axes_[0].get());
    Axis1Type* axis1 = dynamic_cast<Axis1Type*>(axes_[1].get());
    Axis2Type* axis2 = dynamic_cast<Axis2Type*>(axes_[2].get());
    Axis3Type* axis3 = dynamic_cast<Axis3Type*>(axes_[3].get());
    return SuperType::operator()(Eigen::array<EigenIndexType, 4>{
        axis0->GetIndexSequence(Param0Type(index0)), axis1->GetIndexSequence(Param1Type(index1)),
        axis2->GetIndexSequence(Param2Type(index2)), axis3->GetIndexSequence(Param3Type(index3))});
  }

  template<typename Index0Type, typename Index1Type, typename Index2Type, typename Index3Type,
      typename Param0Type = typename AxisTypeSelector<Index0Type>::ParamType,
      typename Param1Type = typename AxisTypeSelector<Index1Type>::ParamType,
      typename Param2Type = typename AxisTypeSelector<Index2Type>::ParamType,
      typename Param3Type = typename AxisTypeSelector<Index3Type>::ParamType,
      typename Axis0Type = typename AxisTypeSelector<Param0Type>::AxisType,
      typename Axis1Type = typename AxisTypeSelector<Param1Type>::AxisType,
      typename Axis2Type = typename AxisTypeSelector<Param2Type>::AxisType,
      typename Axis3Type = typename AxisTypeSelector<Param3Type>::AxisType>
  Scalar& MultableAt(Index0Type index0, Index1Type index1, Index2Type index2, Index3Type index3) {
    CHECK(axes_.size() == 4) << "Axes num should be 4";
    CHECK(axes_[0].get() != nullptr) << "Axis 0 is nullptr";
    CHECK(axes_[1].get() != nullptr) << "Axis 1 is nullptr";
    CHECK(axes_[2].get() != nullptr) << "Axis 2 is nullptr";
    CHECK(axes_[3].get() != nullptr) << "Axis 3 is nullptr";
    Axis0Type* axis0 = dynamic_cast<Axis0Type*>(axes_[0].get());
    Axis1Type* axis1 = dynamic_cast<Axis1Type*>(axes_[1].get());
    Axis2Type* axis2 = dynamic_cast<Axis2Type*>(axes_[2].get());
    Axis3Type* axis3 = dynamic_cast<Axis3Type*>(axes_[3].get());
    return SuperType::operator()(Eigen::array<EigenIndexType, 4>{
        axis0->GetIndexSequence(Param0Type(index0)), axis1->GetIndexSequence(Param1Type(index1)),
        axis2->GetIndexSequence(Param2Type(index2)), axis3->GetIndexSequence(Param3Type(index3))});
  }

  int AxisSequence(const std::string& axis_name) {
    CHECK(axes_sequence_.find(axis_name) != axes_sequence_.end())
        << "Cannot find axis with name " << axis_name;
    return axes_sequence_[axis_name];
  }

  template<typename IndexType, typename AxisType = typename AxisTypeSelector<IndexType>::AxisType>
  const IndexType& AxisIndex(const std::string& axis_name, int index_sequence) const {
    AxisType* axis = dynamic_cast<AxisType*>(axes_[axes_sequence_.at(axis_name)].get());
    CHECK(axes_sequence_.find(axis_name) != axes_sequence_.end())
        << "Cannot find axis with name " << axis_name;
    return axis->GetIndex(index_sequence);
  }

  template<typename IndexType, typename AxisType = typename AxisTypeSelector<IndexType>::AxisType>
  const std::vector<IndexType>& AxisIndices(const std::string& axis_name) const {
    AxisType* axis = dynamic_cast<AxisType*>(axes_[axes_sequence_.at(axis_name)].get());
    CHECK(axes_sequence_.find(axis_name) != axes_sequence_.end())
        << "Cannot find axis with name " << axis_name;
    return axis->indices();
  }

  SelfType operator+(const SelfType& other) const {
    SuperType res = SuperType::operator+(SuperType(other));
    return SelfType(res, axes_, axes_sequence_);
  }
  SelfType operator-(const SelfType& other) const {
    SuperType res = SuperType::operator-(SuperType(other));
    return SelfType(res, axes_, axes_sequence_);
  }
  SelfType operator*(const SelfType& other) const {
    SuperType res = SuperType::operator*(SuperType(other));
    return SelfType(res, axes_, axes_sequence_);
  }
  SelfType operator/(const SelfType& other) const {
    SuperType res = SuperType::operator/(SuperType(other));
    return SelfType(res, axes_, axes_sequence_);
  }

  std::vector<std::unique_ptr<AxisBase>>& axes() { return axes_; }
  std::unordered_map<std::string, int>& axes_sequence() { return axes_sequence_; }

 private:
  std::vector<std::unique_ptr<AxisBase>> axes_;
  std::unordered_map<std::string, int> axes_sequence_;
};

} // namespace container
} // namespace base
