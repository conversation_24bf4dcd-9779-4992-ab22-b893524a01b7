#include "base/container/lock_free_ring_buffer.h"
#include <gtest/gtest.h>
#include <algorithm>
#include <atomic>
#include <chrono>
#include <mutex>
#include <thread>
#include <vector>

#include "glog/logging.h"

namespace base {
namespace {

TEST(LockFreeRingBufferTest, EmptyOnCreation) {
  LockFreeRingBuffer<int> buffer(5);
  EXPECT_EQ(buffer.GetCapacity(), 8);

  std::vector<int> batch;
  EXPECT_FALSE(buffer.PopBatch(&batch, 1));
}

TEST(LockFreeRingBufferTest, SinglePushPop) {
  LockFreeRingBuffer<int> buffer(5);
  ASSERT_TRUE(buffer.Push(42));

  std::vector<int> batch;
  ASSERT_TRUE(buffer.PopBatch(&batch, 4));
  ASSERT_EQ(batch.size(), 1);
  EXPECT_EQ(batch[0], 42);
}

TEST(LockFreeRingBufferTest, FullBufferRejection) {
  LockFreeRingBuffer<int> buffer(7);
  const size_t capacity = buffer.GetCapacity();
  for (size_t i = 0; i < capacity - 1; ++i) {
    EXPECT_TRUE(buffer.Push(i));
  }
  EXPECT_FALSE(buffer.Push(capacity));
}

TEST(LockFreeRingBufferTest, BatchPopBoundaryHandling) {
  LockFreeRingBuffer<int> buffer(4);
  const size_t capacity = buffer.GetCapacity();
  for (int i = 0; i < capacity - 1; ++i) {
    buffer.Push(i);
  }

  std::vector<int> batch;
  const int size = 2;
  ASSERT_TRUE(buffer.PopBatch(&batch, size));
  ASSERT_EQ(batch, std::vector<int>({0, 1}));

  for (int i = 0; i < size; ++i) {
    buffer.Push(i);
  }

  batch.clear();
  ASSERT_TRUE(buffer.PopBatch(&batch, 3));
  ASSERT_EQ(batch, std::vector<int>({2, 0, 1}));
}

TEST(LockFreeRingBufferTest, WrapAroundBehavior) {
  LockFreeRingBuffer<int> buffer(4);
  for (int i = 0; i < 4; ++i) {
    buffer.Push(i);
  }

  std::vector<int> batch;
  buffer.PopBatch(&batch, 2);

  EXPECT_TRUE(buffer.Push(4));
  EXPECT_TRUE(buffer.Push(5));

  batch.clear();
  buffer.PopBatch(&batch, 4);
  EXPECT_EQ(batch, std::vector<int>({2, 4, 5}));
}

TEST(LockFreeRingBufferTest, MultiProducerSingleConsumer) {
  LockFreeRingBuffer<int> buffer(1024);
  constexpr int kNumProducers = 4;
  constexpr int kItemsPerProducer = 10000;
  std::atomic<int> total_popped{0};

  auto producer = [&](int start) {
    for (int i = 0; i < kItemsPerProducer; ++i) {
      while (!buffer.Push(start + i)) {
        std::this_thread::yield();
      }
    }
  };

  auto consumer = [&] {
    std::vector<int> batch;
    size_t batch_size = 100;
    while (total_popped < kNumProducers * kItemsPerProducer) {
      if (buffer.PopBatch(&batch, batch_size)) {
        total_popped += batch.size();
      }
    }
  };

  std::vector<std::thread> producers;
  for (int i = 0; i < kNumProducers; ++i) {
    producers.emplace_back(producer, i * kItemsPerProducer);
  }
  std::thread consumer_thread(consumer);

  for (auto& t : producers) {
    t.join();
  }
  consumer_thread.join();

  EXPECT_EQ(total_popped, kNumProducers * kItemsPerProducer);
}

TEST(LockFreeRingBufferTest, MultiProducerMultiConsumer) {
  LockFreeRingBuffer<int> buffer(10000);
  constexpr int kNumProducers = 4;
  constexpr int kItemsPerProducer = 1000000;
  constexpr int kTotalItems = kNumProducers * kItemsPerProducer;
  std::atomic<long> total_sum_producer{0};

  constexpr int kNumConsumers = 2;
  std::atomic<int> total_popped{0};
  std::atomic<long> total_sum_consumer{0};
  std::vector<int> collected_data;
  std::mutex collected_data_mutex;

  auto producer = [&](int start) {
    long local_sum = 0;
    for (int i = 0; i < kItemsPerProducer; ++i) {
      int value = start + i;
      while (!buffer.Push(value)) {
        std::this_thread::yield();
      }
      local_sum += value;
    }
    total_sum_producer += local_sum;
  };

  auto consumer = [&] {
    std::vector<int> batch;
    const size_t batch_size = 1000;
    batch.reserve(batch_size);
    long local_sum = 0;
    while (total_popped < kTotalItems) {
      if (buffer.PopBatch(&batch, batch_size)) {
        total_popped += batch.size();
        for (const auto& data : batch) {
          local_sum += data;
        }
        {
          std::lock_guard<std::mutex> lock(collected_data_mutex);
          collected_data.insert(collected_data.end(), batch.begin(), batch.end());
        }
        batch.clear();
      }
    }
    total_sum_consumer += local_sum;
  };

  std::vector<std::thread> producers;
  producers.reserve(kNumProducers);
  for (int i = 0; i < kNumProducers; ++i) {
    producers.emplace_back(producer, i * kItemsPerProducer);
  }

  std::vector<std::thread> consumers;
  consumers.reserve(kNumConsumers);
  for (int i = 0; i < kNumConsumers; i++) {
    consumers.emplace_back(consumer);
  }

  for (auto& t : producers) {
    t.join();
  }
  for (auto& t : consumers) {
    t.join();
  }

  EXPECT_EQ(total_popped, kTotalItems);
  EXPECT_EQ(total_sum_producer, total_sum_consumer);
  ASSERT_EQ(collected_data.size(), kTotalItems);
  std::sort(collected_data.begin(), collected_data.end());
  for (int i = 0; i < kTotalItems; ++i) {
    ASSERT_EQ(collected_data[i], i) << "Data not consecutive or duplicated at position " << i
                                    << ", expected: " << i << ", actual: " << collected_data[i];
  }
}

}  // namespace
}  // namespace base