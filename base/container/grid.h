// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <cmath>
#include <memory>
#include <vector>

#include "glog/logging.h"
#include "gtest/gtest.h"

#include "base/common/macros.h"
#include "base/container/proto/grid.pb.h"
#include "base/math/polygon2d.h"
#include "base/math/vector2.h"

namespace base {
namespace container {

template <class T>
class Grid {
 public:
  Grid(double resolution, int rows, int cols);
  explicit Grid(const container_proto::Grid& proto);
  virtual ~Grid() = default;

  virtual T* GetMutableValueByGlobalXY(const math::Vector2d& global_xy);
  virtual T* GetMutableValueByRowColumn(const math::Vector2i& coord);

  virtual const T* GetValueByGlobalXY(const math::Vector2d& global_xy) const;
  virtual const T* GetValueByRowColumn(const math::Vector2i& coord) const;

  math::Vector2i GlobalXYToRowColumn(const math::Vector2d& global_xy) const;
  math::Vector2d RowColumnToGlobalXY(const math::Vector2i& coord) const;

  virtual std::vector<const T*> GetNeighbors(const math::Vector2i& coord) const;
  virtual std::vector<T*> GetMutableNeighbors(const math::Vector2i& coord);
  virtual void ApplyToNeighbors(const math::Vector2i& coord, const std::function<void(T*)>& func);
  virtual std::vector<const T*> GetNeighborsNxN(const math::Vector2i& coord, int shift_size, bool drop_center) const;
  virtual std::vector<T*> GetMutableNeighborsNxN(const math::Vector2i& coord,
                                                 int shift_size,
                                                 bool drop_center);

  virtual math::Vector2i CellToRowColumn(const T* cell) const;
  virtual math::Vector2i CellToGlobalRowColumn(const T* cell) const;
  math::Vector2d CellToGlobalXY(const T* cell) const;

  bool IsInRange(int row, int col) const;

  virtual void Recenter(const math::Vector2d& global_xy);
  virtual void RecenterWithoutClear(const math::Vector2d& global_xy);

  void Fill(const T& value);

  std::vector<T*> GetMutableCellsContainedBy(const math::Polygon2d& polygon);
  std::vector<const T*> GetCellsContainedBy(const math::Polygon2d& polygon) const;
  std::vector<T*> GetMutableCellsTouchedBy(const math::Polygon2d& polygon);
  std::vector<const T*> GetCellsTouchedBy(const math::Polygon2d& polygon) const;

  double resolution() const { return resolution_; }
  int rows() const { return rows_; }
  int cols() const { return cols_; }
  int corner_rows() const { return r0_; }
  int corner_cols() const { return c0_; }
  const std::vector<T>& array() const { return array_; }

  virtual math::Vector2i BottomLeftCorner() const;

  void Reset();

  container_proto::Grid ToProto();

 protected:
  double resolution_ = 0.0;
  int rows_ = 0;
  int cols_ = 0;
  // Grid coordinates of lower left corner of map.
  int r0_ = 0;
  int c0_ = 0;
  std::vector<T> array_;

  FRIEND_TEST(GridTest, GetValue);
  FRIEND_TEST(GridTest, ProtoTest);

  DISALLOW_COPY_AND_ASSIGN(Grid);
};

template <class T>
Grid<T>::Grid(double resolution, int rows, int cols)
    : resolution_(resolution), rows_(rows), cols_(cols) {
  CHECK_GT(resolution_, 0.0);
  CHECK_GT(rows_, 0);
  CHECK_GT(cols_, 0);
  array_.resize(rows_ * cols_, {});
}

template <class T>
Grid<T>::Grid(const container_proto::Grid& proto)
    : resolution_(proto.resolution()),
      rows_(proto.rows()),
      cols_(proto.cols()),
      r0_(proto.corner().x()),
      c0_(proto.corner().y()),
      array_(reinterpret_cast<const T*>(proto.data().data()),
             reinterpret_cast<const T*>(proto.data().data()) + rows_ * cols_) {
  const int type_size = proto.type_size();
  CHECK_EQ(sizeof(T), type_size);
}

template <class T>
container_proto::Grid Grid<T>::ToProto() {
  container_proto::Grid proto;
  proto.set_resolution(resolution_);
  proto.set_rows(rows_);
  proto.set_cols(cols_);
  proto.mutable_corner()->set_x(r0_);
  proto.mutable_corner()->set_y(c0_);
  proto.mutable_data()->assign(reinterpret_cast<const char*>(array_.data()),
                               array_.size() * sizeof(T));
  proto.set_type_size(sizeof(T));
  return proto;
}

template <class T>
bool Grid<T>::IsInRange(int row, int col) const {
  return row >= 0 && col >= 0 && row < rows_ && col < cols_;
}

template <class T>
void Grid<T>::Fill(const T& value) {
  std::fill_n(array_.begin(), rows_ * cols_, value);
}

template <class T>
void Grid<T>::Reset() {
  Fill({});
}

template <class T>
T* Grid<T>::GetMutableValueByGlobalXY(const math::Vector2d& global_xy) {
  const math::Vector2i coord = GlobalXYToRowColumn(global_xy);
  return GetMutableValueByRowColumn(coord);
}

template <class T>
T* Grid<T>::GetMutableValueByRowColumn(const math::Vector2i& coord) {
  return IsInRange(coord.x, coord.y) ? &(array_[coord.x * cols_ + coord.y]) : nullptr;
}

template <class T>
math::Vector2i Grid<T>::CellToRowColumn(const T* cell) const {
  const int offset = cell - array_.data();
  CHECK(offset >= 0 && offset < static_cast<int>(array_.size()));
  return {offset / cols_, offset % cols_};
}

template <class T>
math::Vector2i Grid<T>::CellToGlobalRowColumn(const T* cell) const {
  return CellToRowColumn(cell) + BottomLeftCorner();
}

template <class T>
void Grid<T>::Recenter(const math::Vector2d& global_xy) {
  RecenterWithoutClear(global_xy);
  Fill({});
}

// If You wants to store temporal data without clearing all data in each-iteration
// We propose using rolling grid rather than grid
template <class T>
void Grid<T>::RecenterWithoutClear(const math::Vector2d& global_xy) {
  r0_ = std::floor(global_xy.y / resolution_) - rows_ / 2;
  c0_ = std::floor(global_xy.x / resolution_) - cols_ / 2;
}

template <class T>
const T* Grid<T>::GetValueByGlobalXY(const math::Vector2d& global_xy) const {
  const math::Vector2i coord = GlobalXYToRowColumn(global_xy);
  return GetValueByRowColumn(coord);
}

template <class T>
const T* Grid<T>::GetValueByRowColumn(const math::Vector2i& coord) const {
  return IsInRange(coord.x, coord.y) ? &(array_[coord.x * cols_ + coord.y]) : nullptr;
}

template <class T>
std::vector<const T*> Grid<T>::GetNeighbors(const math::Vector2i& coord) const {
  std::vector<const T*> neighbors;
  neighbors.reserve(4);
  for (int row = -1; row <= 1; ++row) {
    for (int col = -1; col <= 1; ++col) {
      if ((std::abs(row) + std::abs(col)) != 1) {
        continue;
      }
      const T* neighbor = GetValueByRowColumn({coord.x + row, coord.y + col});
      if (!neighbor) {
        continue;
      }
      neighbors.push_back(neighbor);
    }
  }
  return neighbors;
}

template <class T>
std::vector<T*> Grid<T>::GetMutableNeighbors(const math::Vector2i& coord) {
  std::vector<const T*> neighbors = GetNeighbors(coord);
  std::vector<T*> mutable_neighbors;
  mutable_neighbors.reserve(neighbors.size());
  // Use const_cast here to avoid duplicate code.
  // NOTE, const_cast is NOT allowed to use in our codebase, this trick is not recommended.
  for (int index = 0; index < static_cast<int>(neighbors.size()); ++index) {
    mutable_neighbors.push_back(const_cast<T*>(neighbors[index]));
  }
  return mutable_neighbors;
}

template <class T>
void Grid<T>::ApplyToNeighbors(const math::Vector2i& coord, const std::function<void(T*)>& func) {
  for (int row = -1; row <= 1; ++row) {
    for (int col = -1; col <= 1; ++col) {
      if ((std::abs(row) + std::abs(col)) != 1) {
        continue;
      }
      const T* neighbor = GetValueByRowColumn({coord.x + row, coord.y + col});
      if (!neighbor) {
        continue;
      }
      func(const_cast<T*>(neighbor));
    }
  }
}

template <class T>
// if drop_center, get NxN-1 neighbor cells, if not drop_center, get NxN neighbor cells
// where N = 2 * shift_size + 1
std::vector<const T*> Grid<T>::GetNeighborsNxN(const math::Vector2i& coord, int shift_size, bool drop_center) const {
  std::vector<const T*> neighbors;
  const int n = 2 * shift_size + 1;
  if (drop_center) {
    neighbors.reserve(n * n - 1);
  } else {
    neighbors.reserve(n * n);
  }
  for (int row = -shift_size; row <= shift_size; ++row) {
    for (int col = -shift_size; col <= shift_size; ++col) {
      if (drop_center && row == 0 && col == 0) {
        continue;
      }
      const T* neighbor = GetValueByRowColumn({coord.x + row, coord.y + col});
      if (!neighbor) {
        continue;
      }
      neighbors.push_back(neighbor);
    }
  }
  return neighbors;
}

template <class T>
std::vector<T*> Grid<T>::GetMutableNeighborsNxN(const math::Vector2i& coord,
                                                int shift_size,
                                                bool drop_center) {
  std::vector<const T*> neighbors = GetNeighborsNxN(coord, shift_size, drop_center);
  std::vector<T*> mutable_neighbors;
  mutable_neighbors.reserve(neighbors.size());
  // Use const_cast here to avoid duplicate code.
  // NOTE, const_cast is NOT allowed to use in our codebase, this trick is not recommended.
  for (int index = 0; index < static_cast<int>(neighbors.size()); ++index) {
    mutable_neighbors.push_back(const_cast<T*>(neighbors[index]));
  }
  return mutable_neighbors;
}

template <class T>
math::Vector2i Grid<T>::GlobalXYToRowColumn(const math::Vector2d& global_xy) const {
  return {std::floor(global_xy.y / resolution_) - r0_, std::floor(global_xy.x / resolution_) - c0_};
}

template <class T>
math::Vector2d Grid<T>::RowColumnToGlobalXY(const math::Vector2i& coord) const {
  return {(c0_ + coord.y + 0.5) * resolution_, (r0_ + coord.x + 0.5) * resolution_};
}

template <class T>
math::Vector2d Grid<T>::CellToGlobalXY(const T* cell) const {
  const math::Vector2i coord = CellToRowColumn(cell);
  return {(c0_ + coord.y + 0.5) * resolution_, (r0_ + coord.x + 0.5) * resolution_};
}

template <class T>
std::vector<T*> Grid<T>::GetMutableCellsContainedBy(const math::Polygon2d& polygon) {
  const std::vector<const T*> const_cells = GetCellsContainedBy(polygon);
  // Use const_cast here to avoid duplicate code.
  // NOTE, const_cast is NOT allowed to use in our codebase, this trick is not recommended.
  std::vector<T*> mutable_cells;
  mutable_cells.reserve(const_cells.size());
  for (int i = 0; i < static_cast<int>(const_cells.size()); ++i) {
    mutable_cells.push_back(const_cast<T*>(const_cells[i]));
  }
  return mutable_cells;
}

template <class T>
std::vector<const T*> Grid<T>::GetCellsContainedBy(const math::Polygon2d& polygon) const {
  const std::vector<math::Vector2d> centers = polygon.GetInteriorGridCenters(resolution_);
  std::vector<const T*> cells;
  cells.reserve(centers.size());
  for (const auto& center : centers) {
    const T* cell = GetValueByGlobalXY(center);
    if (cell != nullptr) {
      cells.push_back(cell);
    }
  }
  if (cells.empty()) {
    const T* cell = GetValueByGlobalXY(polygon.GetCentroid());
    if (cell != nullptr) {
      cells.push_back(cell);
    }
  }
  return cells;
}

template <class T>
std::vector<T*> Grid<T>::GetMutableCellsTouchedBy(const math::Polygon2d& polygon) {
  const std::vector<const T*> const_cells = GetCellsTouchedBy(polygon);
  // Use const_cast here to avoid duplicate code.
  // NOTE, const_cast is NOT allowed to use in our codebase, this trick is not recommended.
  std::vector<T*> mutable_cells;
  mutable_cells.reserve(const_cells.size());
  for (int i = 0; i < static_cast<int>(const_cells.size()); ++i) {
    mutable_cells.push_back(const_cast<T*>(const_cells[i]));
  }
  return mutable_cells;
}

template <class T>
std::vector<const T*> Grid<T>::GetCellsTouchedBy(const math::Polygon2d& polygon) const {
  const std::vector<math::Vector2d> centers = polygon.GetTouchedGridCenters(resolution_);
  std::vector<const T*> cells;
  cells.reserve(centers.size());
  for (const auto& center : centers) {
    const T* cell = GetValueByGlobalXY(center);
    if (cell != nullptr) {
      cells.push_back(cell);
    }
  }
  if (cells.empty()) {
    const T* cell = GetValueByGlobalXY(polygon.GetCentroid());
    if (cell != nullptr) {
      cells.push_back(cell);
    }
  }
  return cells;
}

template <class T>
math::Vector2i Grid<T>::BottomLeftCorner() const {
  return {r0_, c0_};
}

}  // namespace container
}  // namespace base
