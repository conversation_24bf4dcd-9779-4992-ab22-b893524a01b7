// Copyright @2022 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <cstddef>
#include <iterator>
#include <memory>
#include <utility>

#include "glog/logging.h"
#include "gtest/gtest.h"

#include "base/common/macros.h"

namespace base {
namespace container {

// CircularBuffer is a replacement of boost::circular_buffer. It is a thread-unsafe buffer.
// More details please read http://www.boost.org/libs/circular_buffer/doc/circular_buffer.html
// TODO(liguancheng02): Support following methods, if needed:
//    1. operators: [];
//    2. member functions: at, set_capacity, re_size if necessary;
//    3. const iterator, reverse iterator, const-reverse iterator;
//    4. related functions: cbegin, cend, rbegin, rend, crbegin, crend;

template <class T>
class CircularBuffer final {
 public:
  class Iterator : public std::iterator<std::forward_iterator_tag,  // iterator_category
                                        T,                          // value_type
                                        std::ptrdiff_t,             // different_type
                                        const T*,                   // pointer
                                        const T&                    // reference
                                        > {
   public:
    // iterator constructors & destructors
    explicit Iterator(CircularBuffer<T>* circular_buffer) :
        circular_buffer_(CHECK_NOTNULL(circular_buffer)) {
      relative_index_ = 0;
    }
    // Internal used only, for begin() & end().
    Iterator(CircularBuffer<T>* circular_buffer, size_t relative_index) :
        circular_buffer_(CHECK_NOTNULL(circular_buffer)), relative_index_(relative_index) {
      CHECK_LE(relative_index_, static_cast<size_t>(circular_buffer_->size()));
    }
    virtual ~Iterator() = default;

    T& operator*() {
      CHECK(relative_index_ != static_cast<size_t>(circular_buffer_->size()));
      return circular_buffer_->data_[(circular_buffer_->head_ + relative_index_) % circular_buffer_->capacity_];
    }
    T* operator->() {
      CHECK(relative_index_ != static_cast<size_t>(circular_buffer_->size()));
      return &circular_buffer_->data_[(circular_buffer_->head_ + relative_index_) % circular_buffer_->capacity_];
    }
    const T& operator*() const {
      CHECK(relative_index_ != static_cast<size_t>(circular_buffer_->size()));
      return circular_buffer_->data_[(circular_buffer_->head_ + relative_index_) % circular_buffer_->capacity_];
    }
    const T* operator->() const {
      CHECK(relative_index_ != static_cast<size_t>(circular_buffer_->size()));
      return &circular_buffer_->data_[(circular_buffer_->head_ + relative_index_) % circular_buffer_->capacity_];
    }

    // Prefix increment
    Iterator& operator++() {
      CHECK(relative_index_ != static_cast<size_t>(circular_buffer_->size()));
      ++relative_index_;
      return *this;
    }

    // Suffix increment
    // NOTE: Must use type int, see:
    // 1. https://en.cppreference.com/w/cpp/language/operator_incdec
    // 2. https://learn.microsoft.com/en-us/cpp/cpp/increment-and-decrement-operator-overloading-cpp
    Iterator operator++(int) {
      CHECK(relative_index_ != static_cast<size_t>(circular_buffer_->size()));
      Iterator tmp = *this;
      ++(this->relative_index_);
      return tmp;
    }

    friend bool operator==(const Iterator& a, const Iterator& b) {
      CHECK(a.circular_buffer_ == b.circular_buffer_);
      return a.relative_index_ == b.relative_index_;
    }

    friend bool operator!=(const Iterator& a, const Iterator& b) {
      CHECK(a.circular_buffer_ == b.circular_buffer_);
      return a.relative_index_ != b.relative_index_;
    }

   private:
    // Not owned, constant for accessing the outer class data.
    CircularBuffer<T> const* circular_buffer_ = nullptr;
    // The relative position in the buffer.
    size_t relative_index_ = 0;  // 0 <= relative_index_ <= size <= capacity
  };

  class ConstIterator : public std::iterator<std::forward_iterator_tag,  // iterator_category
                                             T,                          // value_type
                                             std::ptrdiff_t,             // different_type
                                             const T*,                   // pointer
                                             const T&                    // reference
                                             > {
   public:
    // iterator constructors & destructors
    explicit ConstIterator(const CircularBuffer<T>* circular_buffer)
        : circular_buffer_(CHECK_NOTNULL(circular_buffer)) {
      relative_index_ = 0;
    }
    // Internal used only, for begin() & end().
    ConstIterator(const CircularBuffer<T>* circular_buffer, size_t relative_index)
        : circular_buffer_(CHECK_NOTNULL(circular_buffer)), relative_index_(relative_index) {
      CHECK_LE(relative_index_, static_cast<size_t>(circular_buffer_->size()));
    }
    virtual ~ConstIterator() = default;

    const T& operator*() const {
      CHECK(relative_index_ != static_cast<size_t>(circular_buffer_->size()));
      return circular_buffer_->data_[(circular_buffer_->head_ + relative_index_) % circular_buffer_->capacity_];
    }
    const T* operator->() const {
      CHECK(relative_index_ != static_cast<size_t>(circular_buffer_->size()));
      return &circular_buffer_->data_[(circular_buffer_->head_ + relative_index_) % circular_buffer_->capacity_];
    }

    // Prefix increment
    ConstIterator& operator++() {
      CHECK(relative_index_ != static_cast<size_t>(circular_buffer_->size()));
      ++relative_index_;
      return *this;
    }

    // Suffix increment
    // NOTE: Must use type int, see:
    // 1. https://en.cppreference.com/w/cpp/language/operator_incdec
    // 2. https://learn.microsoft.com/en-us/cpp/cpp/increment-and-decrement-operator-overloading-cpp
    ConstIterator operator++(int) {
      CHECK(relative_index_ != static_cast<size_t>(circular_buffer_->size()));
      ConstIterator tmp = *this;
      ++(this->relative_index_);
      return tmp;
    }

    friend bool operator==(const ConstIterator& a, const ConstIterator& b) {
      CHECK(a.circular_buffer_ == b.circular_buffer_);
      return a.relative_index_ == b.relative_index_;
    }

    friend bool operator!=(const ConstIterator& a, const ConstIterator& b) {
      CHECK(a.circular_buffer_ == b.circular_buffer_);
      return a.relative_index_ != b.relative_index_;
    }

   private:
    // Not owned, constant for accessing the outer class data.
    CircularBuffer<T> const* circular_buffer_ = nullptr;
    // The relative position in the buffer.
    size_t relative_index_ = 0;  // 0 <= relative_index_ <= size <= capacity
  };

  // Constructors & destructors
  CircularBuffer() {
    data_ = std::make_unique<T[]>(capacity_);
    reset();
  }

  explicit CircularBuffer(int capacity) : capacity_(capacity) {
    CHECK_GT(capacity, 0);
    data_ = std::make_unique<T[]>(capacity_);
    reset();
  }

  // Element access
  T& front() {
    CHECK(!empty());
    return data_[head_ % capacity_];
  }

  const T& front() const {
    CHECK(!empty());
    return data_[head_ % capacity_];
  }

  T& back() {
    CHECK(!empty());
    return data_[(tail_ - 1) % capacity_];
  }

  const T& back() const {
    CHECK(!empty());
    return data_[(tail_ - 1) % capacity_];
  }

  T& operator[](int i) {
    DCHECK_GE(i, 0);
    DCHECK_LT(i, size());
    return data_[(head_ + i) % capacity_];
  }

  const T& operator[](int i) const {
    DCHECK_GE(i, 0);
    DCHECK_LT(i, size());
    return data_[(head_ + i) % capacity_];
  }

  // Iterator
  Iterator begin() { return Iterator(this, 0); }
  Iterator end() { return Iterator(this, tail_ - head_); }
  ConstIterator begin() const { return ConstIterator(this, 0); }
  ConstIterator end() const { return ConstIterator(this, tail_ - head_); }

  // Capacity
  inline bool empty() const { return head_ == tail_; }
  inline int size() const { return tail_ - head_; }
  inline int capacity() const { return capacity_; }
  // True if the buffer already stored the maximum items.
  inline bool full() const { return static_cast<int>(tail_ - head_) == capacity_; }

  // Modifiers
  inline void reset() {
    head_ = 0;
    tail_ = 0;
  }

  void clear() {
    if (!empty()) {
      data_ = std::make_unique<T[]>(capacity_);
      reset();
    }
  }

  void pop_front() {
    CHECK(!empty());
    ++head_;
  }

  void push_back(const T& item) {
    if (full()) {
      ++head_;
    }
    data_[tail_ % capacity_] = item;
    ++tail_;
  }

  void push_back(T&& item) {
    if (full()) {
      ++head_;
    }
    data_[tail_ % capacity_] = std::move(item);
    ++tail_;
  }

  void swap(CircularBuffer<T>& another) {
    data_.swap(another.data_);
    std::swap(capacity_, another.capacity_);
    std::swap(head_, another.head_);
    std::swap(tail_, another.tail_);
  }

 private:
  int capacity_ = 1;
  size_t head_ = 0;  // Inside of buffer.
  size_t tail_ = 0;  // Outside of buffer.
  std::unique_ptr<T[]> data_;

  FRIEND_TEST(CircularBufferTest, Iterator);
  FRIEND_TEST(CircularBufferTest, ConstIterator);

  DISALLOW_COPY_AND_ASSIGN(CircularBuffer);
};

}  // namespace container
}  // namespace base
