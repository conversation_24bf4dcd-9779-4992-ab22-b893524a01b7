// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <memory>
#include <utility>

#include "glog/logging.h"

#include "base/common/macros.h"

namespace base {
namespace container {

template <typename T>
class FixedArray2d {
 public:
  FixedArray2d(int rows, int cols) : rows_(rows), cols_(cols) {
    CHECK_GT(rows, 0);
    CHECK_GT(cols, 0);
    data_ = std::make_unique<T[]>(rows * cols);
  }

  FixedArray2d() = default;
  virtual ~FixedArray2d() = default;

  FixedArray2d(FixedArray2d&& other) {
    rows_ = other.rows_;
    cols_ = other.cols_;
    data_ = std::move(other.data_);
  }

  FixedArray2d& operator=(FixedArray2d&& other) {
    rows_ = other.rows_;
    cols_ = other.cols_;
    data_ = std::move(other.data_);
    return *this;
  }

  bool IsIndexValid(int row, int col) const {
    return row >= 0 && col >= 0 && row < rows_ && col < cols_;
  }

  const T& operator()(int row, int col) const {
    DCHECK(IsIndexValid(row, col));
    return data_[ComputeIndex(row, col)];
  }

  T& operator()(int row, int col) {
    DCHECK(IsIndexValid(row, col));
    return data_[ComputeIndex(row, col)];
  }

  int rows() const { return rows_; }
  int cols() const { return cols_; }
  const T* data() const { return data_.get(); }
  T* mutable_data() { return data_.get(); }

 private:
  int ComputeIndex(int row, int col) const {
    return row * cols_ + col;
  }

  int rows_ = 0;
  int cols_ = 0;
  std::unique_ptr<T[]> data_;

  DISALLOW_COPY_AND_ASSIGN(FixedArray2d);
};

}  // namespace container
}  // namespace base
