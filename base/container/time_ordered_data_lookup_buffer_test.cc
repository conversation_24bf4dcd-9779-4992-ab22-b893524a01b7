// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>
//          <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)

#include "base/container/time_ordered_data_lookup_buffer.h"

#include "gtest/gtest.h"

#include "base/math/transform/transform.h"

namespace base {
namespace container {

using math::transform::Rigid3d;
using math::transform::RollPitchYaw;

namespace {

struct TransformLerpOperator {
  Rigid3d operator()(const Rigid3d& start, const Rigid3d& end, double factor) const {
    const Eigen::Vector3d origin =
        start.translation() + (end.translation() - start.translation()) * factor;
    const Eigen::Quaterniond rotation =
        Eigen::Quaterniond(start.rotation()).slerp(factor, Eigen::Quaterniond(end.rotation()));
    return Rigid3d(origin, rotation);
  }
};

void CheckTransform(const Rigid3d& t1, const Rigid3d& t2) {
  EXPECT_NEAR(t1.translation().x(), t2.translation().x(), 1e-6);
  EXPECT_NEAR(t1.translation().y(), t2.translation().y(), 1e-6);
  EXPECT_NEAR(t1.translation().z(), t2.translation().z(), 1e-6);
  EXPECT_NEAR(t1.rotation().x(), t2.rotation().x(), 1e-6);
  EXPECT_NEAR(t1.rotation().y(), t2.rotation().y(), 1e-6);
  EXPECT_NEAR(t1.rotation().z(), t2.rotation().z(), 1e-6);
  EXPECT_NEAR(t1.rotation().w(), t2.rotation().w(), 1e-6);
}

}  // namespace

TEST(TimeOrderedDataLookupBufferTest, IsInside) {
  TimeOrderedDataLookupBuffer<Rigid3d, TransformLerpOperator> buffer({.duration = absl::Seconds(200)});
  EXPECT_FALSE(buffer.IsInside(absl::FromUnixSeconds(50)));
  buffer.Push(absl::FromUnixSeconds(50), Rigid3d::Identity());
  EXPECT_FALSE(buffer.IsInside(absl::FromUnixSeconds(25)));
  EXPECT_TRUE(buffer.IsInside(absl::FromUnixSeconds(50)));
  EXPECT_FALSE(buffer.IsInside(absl::FromUnixSeconds(75)));
  buffer.Push(absl::FromUnixSeconds(100), Rigid3d::Identity());
  EXPECT_FALSE(buffer.IsInside(absl::FromUnixSeconds(25)));
  EXPECT_TRUE(buffer.IsInside(absl::FromUnixSeconds(50)));
  EXPECT_TRUE(buffer.IsInside(absl::FromUnixSeconds(75)));
  EXPECT_TRUE(buffer.IsInside(absl::FromUnixSeconds(100)));
  EXPECT_FALSE(buffer.IsInside(absl::FromUnixSeconds(125)));
  EXPECT_EQ(absl::FromUnixSeconds(50), buffer.GetEarliestTime());
  EXPECT_EQ(absl::FromUnixSeconds(100), buffer.GetLatestTime());
}

TEST(TimeOrderedDataLookupBufferTest, InterpolatedLookup1) {
  TimeOrderedDataLookupBuffer<Rigid3d, TransformLerpOperator> buffer({.duration = absl::Seconds(110)});
  buffer.Push(absl::FromUnixSeconds(50), Rigid3d::Identity());
  buffer.Push(absl::FromUnixSeconds(100),
              Rigid3d(Eigen::Vector3d(10.0, 10.0, 10.0), RollPitchYaw(0.0, 0.0, 2.0)));
  auto interpolated = buffer.Lookup(absl::FromUnixSeconds(75), true);
  ASSERT_TRUE(interpolated);
  CheckTransform(*interpolated,
                 Rigid3d(Eigen::Vector3d(5.0, 5.0, 5.0), RollPitchYaw(0.0, 0.0, 1.0)));
}

TEST(TimeOrderedDataLookupBufferTest, InterpolatedLookup2) {
  TimeOrderedDataLookupBuffer<Rigid3d, TransformLerpOperator> buffer({.duration = absl::Seconds(110)});
  buffer.Push(absl::FromUnixSeconds(50), Rigid3d::Identity());
  buffer.Push(absl::FromUnixSeconds(100),
              Rigid3d(Eigen::Vector3d(10.0, 10.0, 10.0), RollPitchYaw(0.0, 0.0, 2.0)));
  auto interpolated = buffer.Lookup(absl::FromUnixSeconds(50), true);
  ASSERT_TRUE(interpolated);
  CheckTransform(*interpolated, Rigid3d::Identity());
}

TEST(TimeOrderedDataLookupBufferTest, InterpolatedLookupWithSameTimestamp) {
  TimeOrderedDataLookupBuffer<Rigid3d, TransformLerpOperator> buffer({.duration = absl::Seconds(110)});
  buffer.Push(absl::FromUnixSeconds(50), Rigid3d::Identity());
  buffer.Push(absl::FromUnixSeconds(50), Rigid3d::Identity());
  ASSERT_FALSE(buffer.Lookup(absl::FromUnixSeconds(75), false));
}

TEST(TimeOrderedDataLookupBufferTest, ExtrapolateLookup1) {
  TimeOrderedDataLookupBuffer<Rigid3d, TransformLerpOperator> buffer({.duration = absl::Seconds(110)});
  buffer.Push(absl::FromUnixSeconds(50), Rigid3d::Identity());
  buffer.Push(
      absl::FromUnixSeconds(75),
      Rigid3d(Eigen::Vector3d(5.0, 5.0, 5.0), math::transform::RollPitchYaw(0.0, 0.0, 1.0)));
  auto interpolated = buffer.Lookup(absl::FromUnixSeconds(100), true);
  ASSERT_TRUE(interpolated);
  CheckTransform(*interpolated,
                 Rigid3d(Eigen::Vector3d(10.0, 10.0, 10.0), RollPitchYaw(0.0, 0.0, 2.0)));
}

TEST(TimeOrderedDataLookupBufferTest, ExtrapolateLookup2) {
  TimeOrderedDataLookupBuffer<Rigid3d, TransformLerpOperator> buffer({.duration = absl::Seconds(110)});
  buffer.Push(absl::FromUnixSeconds(75),
              Rigid3d(Eigen::Vector3d(5.0, 5.0, 5.0), RollPitchYaw(0.0, 0.0, 1.0)));
  buffer.Push(absl::FromUnixSeconds(100),
              Rigid3d(Eigen::Vector3d(10.0, 10.0, 10.0), RollPitchYaw(0.0, 0.0, 2.0)));
  auto interpolated = buffer.Lookup(absl::FromUnixSeconds(50), true);
  ASSERT_TRUE(interpolated);
  CheckTransform(*interpolated,
                 Rigid3d(Eigen::Vector3d(0.0, 0.0, 0.0), RollPitchYaw(0.0, 0.0, 0.0)));
}

TEST(TimeOrderedDataLookupBufferTest, InterpolatedLookupInDuration1) {
  TimeOrderedDataLookupBuffer<Rigid3d, TransformLerpOperator> buffer({.duration = absl::Seconds(110)});
  buffer.Push(absl::FromUnixSeconds(50), Rigid3d::Identity());
  buffer.Push(absl::FromUnixSeconds(100),
              Rigid3d(Eigen::Vector3d(10.0, 10.0, 10.0), RollPitchYaw(0.0, 0.0, 2.0)));
  auto interpolated = buffer.LookupInDuration(absl::FromUnixSeconds(75), 100, true);
  ASSERT_TRUE(interpolated);
  CheckTransform(*interpolated,
                 Rigid3d(Eigen::Vector3d(5.0, 5.0, 5.0), RollPitchYaw(0.0, 0.0, 1.0)));
  interpolated = buffer.LookupInDuration(absl::FromUnixSeconds(75), 40, true);
  ASSERT_FALSE(interpolated);
}

TEST(TimeOrderedDataLookupBufferTest, InterpolatedLookupInDuration2) {
  TimeOrderedDataLookupBuffer<Rigid3d, TransformLerpOperator> buffer({.duration = absl::Seconds(110)});
  buffer.Push(absl::FromUnixSeconds(50), Rigid3d::Identity());
  buffer.Push(absl::FromUnixSeconds(100),
              Rigid3d(Eigen::Vector3d(10.0, 10.0, 10.0), RollPitchYaw(0.0, 0.0, 2.0)));
  auto interpolated = buffer.LookupInDuration(absl::FromUnixSeconds(50), 40, true);
  ASSERT_TRUE(interpolated);
  CheckTransform(*interpolated, Rigid3d::Identity());
  interpolated = buffer.LookupInDuration(absl::FromUnixSeconds(50), 60, true);
  ASSERT_TRUE(interpolated);
  CheckTransform(*interpolated, Rigid3d::Identity());
}

TEST(TimeOrderedDataLookupBufferTest, InterpolatedLookupInDurationWithSameTimestamp) {
  TimeOrderedDataLookupBuffer<Rigid3d, TransformLerpOperator> buffer({.duration = absl::Seconds(110)});
  buffer.Push(absl::FromUnixSeconds(50), Rigid3d::Identity());
  buffer.Push(absl::FromUnixSeconds(50), Rigid3d::Identity());
  ASSERT_FALSE(buffer.LookupInDuration(absl::FromUnixSeconds(75), 40, false));
  ASSERT_FALSE(buffer.LookupInDuration(absl::FromUnixSeconds(75), 60, false));
}

TEST(TimeOrderedDataLookupBufferTest, ExtrapolateLookupInDuration1) {
  TimeOrderedDataLookupBuffer<Rigid3d, TransformLerpOperator> buffer({.duration = absl::Seconds(110)});
  buffer.Push(absl::FromUnixSeconds(50), Rigid3d::Identity());
  buffer.Push(
      absl::FromUnixSeconds(75),
      Rigid3d(Eigen::Vector3d(5.0, 5.0, 5.0), math::transform::RollPitchYaw(0.0, 0.0, 1.0)));
  auto interpolated = buffer.LookupInDuration(absl::FromUnixSeconds(100), 50, true);
  ASSERT_TRUE(interpolated);
  CheckTransform(*interpolated,
                 Rigid3d(Eigen::Vector3d(10.0, 10.0, 10.0), RollPitchYaw(0.0, 0.0, 2.0)));
  interpolated = buffer.LookupInDuration(absl::FromUnixSeconds(100), 10, true);
  ASSERT_FALSE(interpolated);
}

TEST(TimeOrderedDataLookupBufferTest, ExtrapolateLookupInDuration2) {
  TimeOrderedDataLookupBuffer<Rigid3d, TransformLerpOperator> buffer({.duration = absl::Seconds(110)});
  buffer.Push(absl::FromUnixSeconds(75),
              Rigid3d(Eigen::Vector3d(5.0, 5.0, 5.0), RollPitchYaw(0.0, 0.0, 1.0)));
  buffer.Push(absl::FromUnixSeconds(100),
              Rigid3d(Eigen::Vector3d(10.0, 10.0, 10.0), RollPitchYaw(0.0, 0.0, 2.0)));
  auto interpolated = buffer.LookupInDuration(absl::FromUnixSeconds(50), 50, true);
  ASSERT_TRUE(interpolated);
  CheckTransform(*interpolated,
                 Rigid3d(Eigen::Vector3d(0.0, 0.0, 0.0), RollPitchYaw(0.0, 0.0, 0.0)));
  interpolated = buffer.LookupInDuration(absl::FromUnixSeconds(50), 10, true);
  ASSERT_FALSE(interpolated);
}

TEST(TimeOrderedDataLookupBufferTest, LookupWithExtrapolateRange1) {
  TimeOrderedDataLookupBuffer<Rigid3d, TransformLerpOperator> buffer({.duration = absl::Seconds(150)});
  buffer.Push(absl::FromUnixSeconds(75),
              Rigid3d(Eigen::Vector3d(5.0, 5.0, 5.0), RollPitchYaw(0.0, 0.0, 1.0)));
  buffer.Push(absl::FromUnixSeconds(100),
              Rigid3d(Eigen::Vector3d(10.0, 10.0, 10.0), RollPitchYaw(0.0, 0.0, 2.0)));
  auto interpolated = buffer.LookupWithExtrapolateRange(absl::FromUnixSeconds(50), 10, true);
  ASSERT_FALSE(interpolated);
  interpolated = buffer.LookupWithExtrapolateRange(absl::FromUnixSeconds(50), 25, true);
  ASSERT_TRUE(interpolated);
  interpolated = buffer.LookupWithExtrapolateRange(absl::FromUnixSeconds(50), 30, true);
  ASSERT_TRUE(interpolated);
  interpolated = buffer.LookupWithExtrapolateRange(absl::FromUnixSeconds(120), 10, true);
  ASSERT_FALSE(interpolated);
  interpolated = buffer.LookupWithExtrapolateRange(absl::FromUnixSeconds(120), 20, true);
  ASSERT_TRUE(interpolated);
}

TEST(TimeOrderedDataLookupBufferTest, LookupWithExtrapolateRange2) {
  TimeOrderedDataLookupBuffer<Rigid3d, TransformLerpOperator> buffer({.duration = absl::Seconds(150)});
  buffer.Push(absl::FromUnixSeconds(75),
              Rigid3d(Eigen::Vector3d(5.0, 5.0, 5.0), RollPitchYaw(0.0, 0.0, 1.0)));
  buffer.Push(absl::FromUnixSeconds(100),
              Rigid3d(Eigen::Vector3d(10.0, 10.0, 10.0), RollPitchYaw(0.0, 0.0, 2.0)));
  auto interpolated = buffer.LookupWithExtrapolateRange(absl::FromUnixSeconds(80), 10, true);
  ASSERT_TRUE(interpolated);
  interpolated = buffer.LookupWithExtrapolateRange(absl::FromUnixSeconds(65), -10, true);
  ASSERT_FALSE(interpolated);
  interpolated = buffer.LookupWithExtrapolateRange(absl::FromUnixSeconds(110), -10, true);
  ASSERT_FALSE(interpolated);
}

TEST(TimeOrderedDataLookupBufferTest, OneElementCaseTest1) {
  TimeOrderedDataLookupBuffer<Rigid3d, TransformLerpOperator> buffer({.duration = absl::Seconds(110)});
  buffer.Push(absl::FromUnixSeconds(75),
              Rigid3d(Eigen::Vector3d(5.0, 5.0, 5.0), RollPitchYaw(0.0, 0.0, 1.0)));
  auto interpolated1 = buffer.Lookup(absl::FromUnixSeconds(75), true);
  ASSERT_TRUE(interpolated1);
  auto interpolated2 = buffer.LookupInDuration(absl::FromUnixSeconds(75), 10, true);
  ASSERT_TRUE(interpolated2);
  CheckTransform(*interpolated1, *interpolated2);
}

TEST(TimeOrderedDataLookupBufferTest, OneElementCaseTest2) {
  TimeOrderedDataLookupBuffer<Rigid3d, TransformLerpOperator> buffer({.duration = absl::Seconds(110)});
  buffer.Push(absl::FromUnixSeconds(75),
              Rigid3d(Eigen::Vector3d(5.0, 5.0, 5.0), RollPitchYaw(0.0, 0.0, 1.0)));
  auto interpolated1 = buffer.Lookup(absl::FromUnixSeconds(50), true);
  ASSERT_TRUE(interpolated1);
  CheckTransform(*interpolated1,
                 Rigid3d(Eigen::Vector3d(5.0, 5.0, 5.0), RollPitchYaw(0.0, 0.0, 1.0)));
  auto interpolated2 = buffer.LookupInDuration(absl::FromUnixSeconds(50), 10, true);
  ASSERT_FALSE(interpolated2);
}

TEST(TimeOrderedDataLookupBufferTest, Clear) {
  TimeOrderedDataLookupBuffer<Rigid3d, TransformLerpOperator> buffer({.duration = absl::Seconds(110)});
  buffer.Push(absl::FromUnixSeconds(75), Rigid3d::Identity());
  buffer.Push(absl::FromUnixSeconds(100), Rigid3d::Identity());
  buffer.Push(absl::FromUnixSeconds(150), Rigid3d::Identity());
  EXPECT_FALSE(buffer.IsEmpty());
  buffer.Clear();
  EXPECT_TRUE(buffer.IsEmpty());
}

TEST(TimeOrderedDataLookupBufferTest, ClearAfterTime) {
  TimeOrderedDataLookupBuffer<Rigid3d, TransformLerpOperator> buffer({.duration = absl::Seconds(110)});
  buffer.Push(1.0, Rigid3d::Identity());
  buffer.Push(2.0, Rigid3d::Identity());
  buffer.Push(3.0, Rigid3d::Identity());
  EXPECT_FALSE(buffer.IsEmpty());
  buffer.ClearAfterTime(2.1);
  EXPECT_EQ(buffer.size(), 2);
}

TEST(TimeOrderedDataLookupBufferTest, GetLatestItem) {
  TimeOrderedDataLookupBuffer<Rigid3d, TransformLerpOperator> buffer({.duration = absl::Seconds(110)});
  EXPECT_TRUE(buffer.IsEmpty());
  auto interpolated = buffer.GetLatestItem();
  EXPECT_FALSE(interpolated.has_value());
}

TEST(TimeOrderedDataLookupBufferTest, TestBufferName) {
  {
    TimeOrderedDataLookupBuffer<Rigid3d, TransformLerpOperator> buffer({.duration = absl::Seconds(110)});
    EXPECT_EQ(buffer.name(), "");
    buffer.set_name("test");  // Success because name is empty yet.
    EXPECT_EQ(buffer.name(), "test");
  }
  {
    TimeOrderedDataLookupBuffer<Rigid3d, TransformLerpOperator> buffer(
        {.duration = absl::Seconds(110), .name = "test"});
    EXPECT_EQ(buffer.name(), "test");
    buffer.set_name("test2");  // Skip because already set.
    EXPECT_EQ(buffer.name(), "test");
  }
}

TEST(TimeOrderedDataLookupBufferTest, DeathPushOlderTime) {
  {
    TimeOrderedDataLookupBuffer<Rigid3d, TransformLerpOperator> buffer({.duration = absl::Seconds(110)});
    buffer.Push(absl::FromUnixSeconds(1687150002), Rigid3d::Identity());
    constexpr char kExpectedPattern[] =
        ".*\\(1687150002\\.0000 vs\\. 1687150001\\.0000\\) New data is older than latest, buffer name: \\.";
    ASSERT_DEATH(buffer.Push(absl::FromUnixSeconds(1687150001), Rigid3d::Identity()), kExpectedPattern);
  }
  {
    TimeOrderedDataLookupBuffer<Rigid3d, TransformLerpOperator> buffer(
        {.duration = absl::Seconds(110), .name = "test"});
    buffer.Push(absl::FromUnixSeconds(1687150002), Rigid3d::Identity());
    constexpr char kExpectedPattern[] =
        ".*\\(1687150002\\.0000 vs\\. 1687150001\\.0000\\) New data is older than latest, buffer name: test\\.";
    ASSERT_DEATH(buffer.Push(absl::FromUnixSeconds(1687150001), Rigid3d::Identity()), kExpectedPattern);
  }
}



}  // namespace container
}  // namespace base
