// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <memory>
#include <string>
#include <vector>

#include "base/common/macros.h"
#include "base/container/blocking_queue.h"

namespace base {
namespace container {

template <class T>
class PartitionBuffer {
 public:
  using Buffer = base::BlockingQueue<T>;

  PartitionBuffer(const std::string& name, int partition_cnt);
  ~PartitionBuffer() = default;

  Buffer* GetOnePartition(int partition_index);
  void Push(T item, int partion_index);
  bool IsClosed() const { return closed_; }
  void Close();

  const int partition_cnt() const { return partition_cnt_; }
  const std::string& name() const { return name_; }

 private:
  std::string name_;
  const int partition_cnt_;
  std::vector<std::unique_ptr<Buffer>> buffers_;
  std::atomic<bool> closed_{false};

  DISALLOW_COPY_AND_ASSIGN(PartitionBuffer);
};

template <class T>
PartitionBuffer<T>::PartitionBuffer(const std::string& name, int partition_cnt)
    : name_(name), partition_cnt_(partition_cnt) {
  for (int i = 0; i < partition_cnt_; i++) {
    buffers_.emplace_back(std::make_unique<Buffer>(strings::Format("{}-{}", name, i)));
  }
}

template <class T>
void PartitionBuffer<T>::Close() {
  if (closed_) {
    return;
  }
  closed_ = true;
  for (const auto& buffer : buffers_) {
    if (buffer != nullptr && !buffer->IsClosed()) {
      buffer->Close();
    }
  }
}

template <class T>
base::BlockingQueue<T>* PartitionBuffer<T>::GetOnePartition(int partition_index) {
  CHECK_LT(partition_index, partition_cnt_)
      << strings::Format("Partition index {} is greater than {}.", partition_index, partition_cnt_);
  return buffers_[partition_index].get();
}

template <class T>
void PartitionBuffer<T>::Push(T item, int partition_index) {
  CHECK_LT(partition_index, partition_cnt_)
      << strings::Format("Partition index {} is greater than {}.", partition_index, partition_cnt_);
  buffers_[partition_index]->Push(item);
}

}  // namespace container
}  // namespace base
