// Copyright @2022 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include <utility>

#include "benchmark/benchmark.h"
#include "boost/circular_buffer.hpp"
#include "google/protobuf/text_format.h"

#include "base/container/circular_buffer.h"
#include "walle/pch/onboard_message.h"

namespace base {
namespace container {
namespace {

template <typename T>
using BoostBuffer = boost::circular_buffer<T>;

using Message = google::protobuf::Message;

class CBTestCase {
 public:
  CBTestCase() {
    score_ = 3.141592653589738462643383279;
    count_ = 2022;
  }

  explicit CBTestCase(const CBTestCase& other) : score_(other.Score()), count_(other.Count()) {}
  virtual ~CBTestCase() = default;

  inline double Score() const { return score_; }
  inline int Count() const { return count_; }

 private:
  double score_;
  int count_;
};

constexpr int64_t kMaxBufferSizeForSmallClass = 1e6;
const CBTestCase cb_test_case;

constexpr int64_t kMaxBufferSizeForSmallOnboardMessage = 1e3;
constexpr int kSmallObstacleCount = 100;
constexpr int64_t kMaxBufferSizeForLargeOnboardMessage = 500;
constexpr int kLargeObstacleCount = 800;

constexpr char kFakePerceptionObstacle[] = R"(
  id: 18145
  position {
    x: 232587.68794876678
    y: 2517526.8396266149
    z: 35.565759469219252
  }
  theta: -2.6315293718433805
  velocity {
    x: -1.4763398083978441
    y: 3.5166423658297226
    z: 0
  }
  length: 2.0624169149772027
  width: 3.9907486235608953
  height: 1.3079791869567998
  polygon_point {
    x: 232586.34036786819
    y: 2517527.9820031151
    z: 0
  }
  polygon_point {
    x: 232588.96666916096
    y: 2517525.3723797072
    z: 0
  }
  polygon_point {
    x: 232587.40679357349
    y: 2517528.7237240928
    z: 0
  }
  
  polygon_point {
    x: 232586.3459047793
    y: 2517528.0440307837
    z: 0
  }
  type: VEHICLE
  timestamp: 1645493247.3011992
  direction {
    x: -0.87271361318954288
    y: -0.48823247470201425
    z: 0
  }
  acceleration {
    x: 0.21104289121916306
    y: -0.20021241021724237
    z: 0
  }
  obstacle_type {
    coarse_type: CAR
    fine_type: FINE_CAR
  }
  vehicle_lights {
    turn_light_status: TURN_LIGHT_OFF
    brake_light_status: BRAKE_LIGHT_OFF
  }
  first_observation_timestamp: 1645491831.3026791
  appear_from_occlusion: false
  display_box {
    center {
      x: 232587.74713453339
      y: 2517526.7547415164
    }
    length: 5
    width: 2
    heading: 1.9682671898635538
  }
  semantic_type_info {
    bit_mask: 1024
  }
  min_z_wcs: 34.809149511611956
)";

void FakeOnboardMessage(int obstacle_size, walle::OnboardMessage* msg) {
  CHECK(msg != nullptr);
  static walle::OnboardMessage small_msg;
  if (small_msg.perception_obstacles().perception_obstacle_size() == obstacle_size) {
    msg->CopyFrom(small_msg);
    return;
  } else {
    small_msg.mutable_perception_obstacles()->clear_perception_obstacle();
  }
  static walle::PerceptionObstacle fake_obstacle;
  if (fake_obstacle.id() != 18145) {
    google::protobuf::TextFormat::ParseFromString(kFakePerceptionObstacle, &fake_obstacle);
  }
  walle::PerceptionObstacles* obstacles_proto =
      CHECK_NOTNULL(small_msg.mutable_perception_obstacles());
  while (small_msg.perception_obstacles().perception_obstacle_size() != obstacle_size) {
    walle::PerceptionObstacle* obstacle_proto =
        CHECK_NOTNULL(obstacles_proto->add_perception_obstacle());
    obstacle_proto->CopyFrom(fake_obstacle);
  }
  msg->CopyFrom(small_msg);
}

template <class Container, class T>
void RunClassBuffer(const T& t, int64_t buffer_size) {
  Container buffer(buffer_size);
  for (int64_t i = 0; i < buffer_size; ++i) {
    buffer.push_back(T(t));
  }
  for (int64_t i = 0; i < buffer_size; ++i) {
    buffer.push_back(T(t));
  }
  while (!buffer.empty()) {
    buffer.pop_front();
  }
}

template <class Container, class T>
void RunClassPtrBuffer(const T& t, int64_t buffer_size) {
  Container buffer(buffer_size);
  for (int64_t i = 0; i < buffer_size; ++i) {
    buffer.push_back(std::make_unique<T>(t));
  }
  for (int64_t i = 0; i < buffer_size; ++i) {
    buffer.push_back(std::make_unique<T>(t));
  }
  while (!buffer.empty()) {
    buffer.pop_front();
  }
}

}  // namespace

void BM_BOOST_BASIC_CLASS_BUFFER(benchmark::State& state) { /* NOLINT(runtime/references) */
  for (auto s : state) {
    RunClassBuffer<BoostBuffer<CBTestCase>, CBTestCase>(cb_test_case, kMaxBufferSizeForSmallClass);
  }
}

void BM_CUSTOM_BASIC_CLASS_BUFFER(benchmark::State& state) { /* NOLINT(runtime/references) */
  for (auto s : state) {
    RunClassBuffer<CircularBuffer<CBTestCase>, CBTestCase>(
        cb_test_case, kMaxBufferSizeForSmallClass);
  }
}

void BM_BOOST_BASIC_CLASS_PTR_BUFFER(benchmark::State& state) { /* NOLINT(runtime/references) */
  for (auto s : state) {
    RunClassPtrBuffer<BoostBuffer<std::unique_ptr<CBTestCase>>, CBTestCase>(
        cb_test_case, kMaxBufferSizeForSmallClass);
  }
}

void BM_CUSTOM_BASIC_CLASS_PTR_BUFFER(benchmark::State& state) { /* NOLINT(runtime/references) */
  for (auto s : state) {
    RunClassPtrBuffer<CircularBuffer<std::unique_ptr<CBTestCase>>, CBTestCase>(
        cb_test_case, kMaxBufferSizeForSmallClass);
  }
}

void BM_BOOST_ONBOARD_SMALL_MSG_BUFFER(benchmark::State& state) { /* NOLINT(runtime/references) */
  walle::OnboardMessage msg;
  FakeOnboardMessage(kSmallObstacleCount, &msg);
  for (auto s : state) {
    RunClassBuffer<BoostBuffer<walle::OnboardMessage>>(msg, kMaxBufferSizeForSmallOnboardMessage);
  }
}

void BM_CUSTOM_ONBOARD_SMALL_MSG_BUFFER(benchmark::State& state) { /* NOLINT(runtime/references) */
  walle::OnboardMessage msg;
  FakeOnboardMessage(kSmallObstacleCount, &msg);
  for (auto s : state) {
    RunClassBuffer<CircularBuffer<walle::OnboardMessage>>(
        msg, kMaxBufferSizeForSmallOnboardMessage);
  }
}

void BM_BOOST_ONBOARD_SMALL_MSG_PTR_BUFFER(benchmark::State& state) { /* NOLINT(runtime/references) */
  walle::OnboardMessage msg;
  FakeOnboardMessage(kSmallObstacleCount, &msg);
  for (auto s : state) {
    RunClassPtrBuffer<BoostBuffer<std::unique_ptr<walle::OnboardMessage>>>(
        msg, kMaxBufferSizeForSmallOnboardMessage);
  }
}

void BM_CUSTOM_ONBOARD_SMALL_MSG_PTR_BUFFER(benchmark::State& state) { /* NOLINT(runtime/references) */
  walle::OnboardMessage msg;
  FakeOnboardMessage(kSmallObstacleCount, &msg);
  for (auto s : state) {
    RunClassPtrBuffer<CircularBuffer<std::unique_ptr<walle::OnboardMessage>>>(
        msg, kMaxBufferSizeForSmallOnboardMessage);
  }
}

void BM_BOOST_ONBOARD_LARGE_MSG_BUFFER(benchmark::State& state) { /* NOLINT(runtime/references) */
  walle::OnboardMessage msg;
  FakeOnboardMessage(kLargeObstacleCount, &msg);
  for (auto s : state) {
    RunClassBuffer<BoostBuffer<walle::OnboardMessage>>(msg, kMaxBufferSizeForLargeOnboardMessage);
  }
}

void BM_CUSTOM_ONBOARD_LARGE_MSG_BUFFER(benchmark::State& state) { /* NOLINT(runtime/references) */
  walle::OnboardMessage msg;
  FakeOnboardMessage(kLargeObstacleCount, &msg);
  for (auto s : state) {
    RunClassBuffer<CircularBuffer<walle::OnboardMessage>>(
        msg, kMaxBufferSizeForLargeOnboardMessage);
  }
}

void BM_BOOST_ONBOARD_LARGE_MSG_PTR_BUFFER(benchmark::State& state) { /* NOLINT(runtime/references) */
  walle::OnboardMessage msg;
  FakeOnboardMessage(kLargeObstacleCount, &msg);
  for (auto s : state) {
    RunClassPtrBuffer<BoostBuffer<std::unique_ptr<walle::OnboardMessage>>>(
        msg, kMaxBufferSizeForLargeOnboardMessage);
  }
}

void BM_CUSTOM_ONBOARD_LARGE_MSG_PTR_BUFFER(benchmark::State& state) { /* NOLINT(runtime/references) */
  walle::OnboardMessage msg;
  FakeOnboardMessage(kLargeObstacleCount, &msg);
  for (auto s : state) {
    RunClassPtrBuffer<CircularBuffer<std::unique_ptr<walle::OnboardMessage>>>(
        msg, kMaxBufferSizeForLargeOnboardMessage);
  }
}

BENCHMARK(BM_BOOST_BASIC_CLASS_BUFFER);
BENCHMARK(BM_CUSTOM_BASIC_CLASS_BUFFER);
BENCHMARK(BM_BOOST_BASIC_CLASS_PTR_BUFFER);
BENCHMARK(BM_CUSTOM_BASIC_CLASS_PTR_BUFFER);

BENCHMARK(BM_BOOST_ONBOARD_SMALL_MSG_BUFFER);
BENCHMARK(BM_CUSTOM_ONBOARD_SMALL_MSG_BUFFER);
BENCHMARK(BM_BOOST_ONBOARD_SMALL_MSG_PTR_BUFFER);
BENCHMARK(BM_CUSTOM_ONBOARD_SMALL_MSG_PTR_BUFFER);

BENCHMARK(BM_BOOST_ONBOARD_LARGE_MSG_BUFFER);
BENCHMARK(BM_CUSTOM_ONBOARD_LARGE_MSG_BUFFER);
BENCHMARK(BM_BOOST_ONBOARD_LARGE_MSG_PTR_BUFFER);
BENCHMARK(BM_CUSTOM_ONBOARD_LARGE_MSG_PTR_BUFFER);

}  // namespace container
}  // namespace base

BENCHMARK_MAIN();

// TODO(liguancheng02): Update this part after everything is done.
// clang-format off
/*
Run on (20 X 5200 MHz CPU s)
CPU Caches:
  L1 Data 32K (x10)
  L1 Instruction 32K (x10)
  L2 Unified 256K (x10)
  L3 Unified 20480K (x1)
Load Average: 1.54, 1.80, 1.53
***WARNING*** CPU scaling is enabled, the benchmark real time measurements may be noisy and will incur extra overhead.
---------------------------------------------------------------------------------
Benchmark                                       Time             CPU   Iterations
---------------------------------------------------------------------------------
BM_BOOST_BASIC_CLASS_BUFFER               3697095 ns      3697139 ns          189
BM_CUSTOM_BASIC_CLASS_BUFFER              2907815 ns      2907849 ns          244
BM_BOOST_BASIC_CLASS_PTR_BUFFER          30955694 ns     30955125 ns           22
BM_CUSTOM_BASIC_CLASS_PTR_BUFFER         32009363 ns     32009576 ns           21
BM_BOOST_ONBOARD_SMALL_MSG_BUFFER       124066012 ns    124066596 ns            7
BM_CUSTOM_ONBOARD_SMALL_MSG_BUFFER      123103420 ns    123104678 ns            6
BM_BOOST_ONBOARD_SMALL_MSG_PTR_BUFFER   124200543 ns    124200335 ns            6
BM_CUSTOM_ONBOARD_SMALL_MSG_PTR_BUFFER  124288162 ns    124286896 ns            6
BM_BOOST_ONBOARD_LARGE_MSG_BUFFER       455917358 ns    455913268 ns            2
BM_CUSTOM_ONBOARD_LARGE_MSG_BUFFER      459997773 ns    459969885 ns            2
BM_BOOST_ONBOARD_LARGE_MSG_PTR_BUFFER   457414508 ns    457418950 ns            2
BM_CUSTOM_ONBOARD_LARGE_MSG_PTR_BUFFER  455667615 ns    455670752 ns            2
*/
