// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/container/lru_cache.h"

#include <memory>
#include <string>
#include <vector>

#include "gmock/gmock.h"
#include "gtest/gtest.h"

namespace base {

using testing::ElementsAreArray;

namespace {

template <typename T>
std::list<T> AsList(std::initializer_list<T> items) {
  return items;
}

template <typename Key, typename Cache>
std::vector<Key> GetKeys(const Cache& cache) {
  std::vector<Key> keys;
  for (const auto& it : cache.elements()) {
    keys.push_back(it.key);
  }
  return keys;
}

}  // namespace

TEST(LruCacheTest, IntType0) {
  LruCache<int, int> lru(3);
  ASSERT_EQ(3, lru.capacity());
  ASSERT_TRUE(lru.IsEmpty());
  ASSERT_TRUE(lru.Set(1, 1));
  ASSERT_EQ(1, lru.size());
  ASSERT_TRUE(lru.Set(2, 3));
  ASSERT_FALSE(lru.Set(2, 2));
  ASSERT_TRUE(lru.Set(3, 3));
  ASSERT_THAT(GetKeys<int>(lru), ElementsAreArray(AsList<int>({1, 2, 3})));

  ASSERT_EQ(3, lru.size());
  ASSERT_EQ(1, lru.GetOrDie(1, false));
  ASSERT_EQ(2, lru.GetOrDie(2, true));
  ASSERT_EQ(3, lru.GetOrDie(3, false));
  ASSERT_THAT(GetKeys<int>(lru), ElementsAreArray(AsList<int>({1, 3, 2})));

  ASSERT_TRUE(lru.Set(4, 4));
  ASSERT_EQ(3, lru.size());
  ASSERT_THAT(GetKeys<int>(lru), ElementsAreArray(AsList<int>({3, 2, 4})));

  ASSERT_TRUE(lru.Contains(2));
  ASSERT_EQ(2, lru.Pop(2));
  ASSERT_FALSE(lru.Contains(2));
  ASSERT_EQ(2, lru.size());
  ASSERT_THAT(GetKeys<int>(lru), ElementsAreArray(AsList<int>({3, 4})));

  ASSERT_EQ(3, lru.PopFront());
  ASSERT_EQ(4, lru.PopBack());
  ASSERT_TRUE(lru.IsEmpty());
  ASSERT_EQ(0, lru.size());
  ASSERT_THAT(GetKeys<int>(lru), ElementsAreArray(AsList<int>({})));
}

TEST(LruCacheTest, IntType1) {
  LruCache<int, int> lru(3);
  ASSERT_TRUE(lru.Set(0, 0));
  ASSERT_TRUE(lru.Set(5, 5));
  ASSERT_EQ(0, lru.GetOrDie(0));
  ASSERT_THAT(GetKeys<int>(lru), ElementsAreArray(AsList<int>({5, 0})));

  ASSERT_EQ(5, lru.GetOrDie(5, false));
  ASSERT_THAT(GetKeys<int>(lru), ElementsAreArray(AsList<int>({5, 0})));

  lru.GetMutableOrDie(5) = 9;
  ASSERT_EQ(9, lru.GetOrDie(5));
  ASSERT_THAT(GetKeys<int>(lru), ElementsAreArray(AsList<int>({0, 5})));

  ASSERT_TRUE(lru.Set(6, 6));
  ASSERT_FALSE(lru.Set(6, 6));
  ASSERT_THAT(GetKeys<int>(lru), ElementsAreArray(AsList<int>({0, 5, 6})));

  ASSERT_FALSE(lru.Set(5, 5, false));
  ASSERT_THAT(GetKeys<int>(lru), ElementsAreArray(AsList<int>({0, 5, 6})));

  ASSERT_FALSE(lru.Set(5, 5));
  ASSERT_THAT(GetKeys<int>(lru), ElementsAreArray(AsList<int>({0, 6, 5})));

  ASSERT_EQ(0, lru.PopFront());
  ASSERT_EQ(6, lru.PopFront());
  ASSERT_EQ(5, lru.PopFront());
  ASSERT_FALSE(lru.Contains(0));
  ASSERT_FALSE(lru.Contains(5));
  ASSERT_FALSE(lru.Contains(6));
  ASSERT_THAT(GetKeys<int>(lru), ElementsAreArray(AsList<int>({})));
}

TEST(LruCacheTest, StringType) {
  LruCache<std::string, std::string> lru;
  ASSERT_EQ(std::numeric_limits<int>::max(), lru.capacity());
  ASSERT_TRUE(lru.Set("1", "1"));
  ASSERT_TRUE(lru.Set("2", "2"));
  ASSERT_TRUE(lru.Set("3", "3"));
  ASSERT_FALSE(lru.Set("2", "3", false));
  ASSERT_THAT(GetKeys<std::string>(lru), ElementsAreArray(AsList<std::string>({"1", "2", "3"})));

  ASSERT_TRUE(lru.Set("4", "4"));
  ASSERT_TRUE(lru.Set("5", "5"));
  ASSERT_FALSE(lru.Set("2", "2"));
  ASSERT_THAT(GetKeys<std::string>(lru),
              ElementsAreArray(AsList<std::string>({"1", "3", "4", "5", "2"})));

  auto v3 = lru.Pop("3");
  ASSERT_TRUE(v3 != base::none && *v3 == "3");
  ASSERT_TRUE(lru.Pop("3") == base::none);

  ASSERT_THAT(GetKeys<std::string>(lru),
              ElementsAreArray(AsList<std::string>({"1", "4", "5", "2"})));

  ASSERT_EQ("2", lru.PopBack());
  ASSERT_EQ("1", lru.PopFront());
  ASSERT_TRUE(lru.Set("1", "2"));
  ASSERT_THAT(GetKeys<std::string>(lru), ElementsAreArray(AsList<std::string>({"4", "5", "1"})));
}

TEST(LruCacheTest, UniquePointerType) {
  LruCache<std::string, std::unique_ptr<std::string>> lru(3);
  ASSERT_TRUE(lru.Set("1", std::make_unique<std::string>("1")));
  ASSERT_FALSE(lru.Set("1", std::make_unique<std::string>("1")));
  ASSERT_THAT(GetKeys<std::string>(lru), ElementsAreArray(AsList<std::string>({"1"})));

  ASSERT_TRUE(lru.Set("2", std::make_unique<std::string>("2")));
  ASSERT_TRUE(lru.Set("3", std::make_unique<std::string>("3")));
  ASSERT_TRUE(lru.Set("4", std::make_unique<std::string>("4")));

  ASSERT_FALSE(lru.Set("4", std::make_unique<std::string>("4")));
  ASSERT_FALSE(lru.Set("3", std::make_unique<std::string>("3")));
  ASSERT_FALSE(lru.Set("2", std::make_unique<std::string>("2")));
  ASSERT_THAT(GetKeys<std::string>(lru), ElementsAreArray(AsList<std::string>({"4", "3", "2"})));

  lru.Clear();
  ASSERT_THAT(GetKeys<std::string>(lru), ElementsAreArray(AsList<std::string>({})));
}

TEST(LruCacheTest, SanityTest) {
  LruCache<int, int> lru(100);

  ASSERT_EQ(0, lru.size());
  ASSERT_TRUE(lru.IsEmpty());
  ASSERT_FALSE(lru.Contains(1));
  lru.Set(1, 1);
  ASSERT_EQ(1, lru.size());
  ASSERT_FALSE(lru.IsEmpty());
  ASSERT_EQ(1, lru.GetOrDie(1));
  ASSERT_TRUE(lru.Contains(1));
  lru.Set(1, 2);
  ASSERT_EQ(1, lru.size());
  ASSERT_FALSE(lru.IsEmpty());
  ASSERT_EQ(2, lru.GetOrDie(1));
  ASSERT_TRUE(lru.Contains(1));
  lru.Pop(1);
  ASSERT_EQ(0, lru.size());
  ASSERT_TRUE(lru.IsEmpty());
  ASSERT_FALSE(lru.Contains(1));

  ASSERT_EQ(0, lru.size());
  ASSERT_TRUE(lru.IsEmpty());
  ASSERT_FALSE(lru.Contains(1));
  lru.Set(1, 1);
  ASSERT_EQ(1, lru.size());
  ASSERT_FALSE(lru.IsEmpty());
  ASSERT_EQ(1, lru.GetOrDie(1));
  ASSERT_TRUE(lru.Contains(1));
  lru.Set(1, 2);
  ASSERT_EQ(1, lru.size());
  ASSERT_FALSE(lru.IsEmpty());
  ASSERT_EQ(2, lru.GetOrDie(1));
  ASSERT_TRUE(lru.Contains(1));

  ASSERT_FALSE(lru.Contains(2));
  lru.Set(2, 1);
  ASSERT_TRUE(lru.Contains(2));
  ASSERT_EQ(2, lru.size());
  ASSERT_FALSE(lru.IsEmpty());
  ASSERT_EQ(1, lru.GetOrDie(2));
  lru.Set(2, 2);
  ASSERT_EQ(2, lru.size());
  ASSERT_FALSE(lru.IsEmpty());
  ASSERT_EQ(2, lru.GetOrDie(2));
  ASSERT_TRUE(lru.Contains(2));
  lru.Pop(2);
  ASSERT_EQ(1, lru.size());
  ASSERT_FALSE(lru.IsEmpty());
  ASSERT_FALSE(lru.Contains(2));
  lru.Pop(1);
  ASSERT_EQ(0, lru.size());
  ASSERT_TRUE(lru.IsEmpty());
  ASSERT_FALSE(lru.Contains(1));
}

TEST(LruCacheTest, EvictElementsTest) {
  LruCache<int, int> lru(10000);

  ASSERT_EQ(0, lru.size());
  ASSERT_TRUE(lru.IsEmpty());
  for (int i = 0; i < 100; i++) {
    ASSERT_FALSE(lru.Contains(i));
  }

  for (int i = 0; i < 100; i++) {
    lru.Set(i, i);
    ASSERT_EQ(i + 1, lru.size());
    ASSERT_FALSE(lru.IsEmpty());
    ASSERT_TRUE(lru.Contains(i));
    ASSERT_EQ(i, lru.GetOrDie(i));
  }

  lru.EvictElements(1000000);
  ASSERT_EQ(0, lru.size());
  ASSERT_TRUE(lru.IsEmpty());
  for (int i = 0; i < 100; i++) {
    ASSERT_FALSE(lru.Contains(i));
  }

  for (int i = 0; i < 100; i++) {
    lru.Set(i, i);
    ASSERT_EQ(i + 1, lru.size());
    ASSERT_FALSE(lru.IsEmpty());
    ASSERT_TRUE(lru.Contains(i));
    ASSERT_EQ(i, lru.GetOrDie(i));
  }

  lru.EvictElements(100);
  ASSERT_EQ(0, lru.size());
  ASSERT_TRUE(lru.IsEmpty());
  for (int i = 0; i < 100; i++) {
    ASSERT_FALSE(lru.Contains(i));
  }

  for (int i = 0; i < 100; i++) {
    lru.Set(i, i);
    ASSERT_EQ(i + 1, lru.size());
    ASSERT_FALSE(lru.IsEmpty());
    ASSERT_TRUE(lru.Contains(i));
    ASSERT_EQ(i, lru.GetOrDie(i));
  }

  lru.EvictElements(99);
  ASSERT_EQ(1, lru.size());
  ASSERT_FALSE(lru.IsEmpty());
  for (int i = 0; i < 99; i++) {
    ASSERT_FALSE(lru.Contains(i));
  }
  ASSERT_TRUE(lru.Contains(99));
  ASSERT_EQ(99, lru.GetOrDie(99));

  lru.EvictElements(100);
  ASSERT_EQ(0, lru.size());
  ASSERT_TRUE(lru.IsEmpty());
  for (int i = 0; i < 100; i++) {
    ASSERT_FALSE(lru.Contains(i));
  }

  for (int i = 0; i < 100; i++) {
    lru.Set(i, i);
    ASSERT_EQ(i + 1, lru.size());
    ASSERT_FALSE(lru.IsEmpty());
    ASSERT_TRUE(lru.Contains(i));
    ASSERT_EQ(i, lru.GetOrDie(i));
  }

  lru.EvictElements(90);
  ASSERT_EQ(10, lru.size());
  ASSERT_FALSE(lru.IsEmpty());
  for (int i = 0; i < 90; i++) {
    ASSERT_FALSE(lru.Contains(i));
  }
  for (int i = 90; i < 100; i++) {
    ASSERT_TRUE(lru.Contains(i));
    ASSERT_EQ(i, lru.GetOrDie(i));
  }
}

TEST(LruCacheTest, CustomKeyEqual) {
  struct EqualTo {
    bool operator()(const int& lhs, const int& rhs) const { return (lhs % mod) == (rhs % mod); }
    int mod = 0;
  };
  struct Hash {
    size_t operator()(const int& key) const { return std::hash<int>()(key % mod); }
    int mod = 0;
  };
  const int n = 37;
  LruCache<int, int, Hash, EqualTo> lru(100, Hash{n}, EqualTo{n});
  for (int i = 0; i < 1000; i++) {
    lru.Set(i, i);
    ASSERT_TRUE(lru.Contains(i));
    ASSERT_EQ(i, lru.GetOrDie(i));
    ASSERT_TRUE(lru.Contains(i + n));
    ASSERT_EQ(i, lru.GetOrDie(i + n));
  }
  ASSERT_EQ(n, lru.size());
}

}  // namespace base
