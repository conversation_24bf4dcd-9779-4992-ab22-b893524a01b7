// Copyright @2022 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include "absl/types/span.h"

namespace base {

template <typename T>
using Span = ::absl::Span<T>;

template <typename T>
using ConstSpan = ::absl::Span<const T>;

template <int&... ExplicitArgumentBarrier, typename T>
constexpr Span<T> MakeSpan(T* p, int size) noexcept {
  return ::absl::MakeSpan(p, size);
}

template <int&... ExplicitArgumentBarrier, typename T>
constexpr Span<T> MakeSpan(T* p, int start_index, int end_index) noexcept {
  return ::absl::MakeSpan(p + start_index, end_index - start_index);
}

template <int&... ExplicitArgumentBarrier, typename T>
constexpr Span<T> MakeSpan(T* begin, T* end) noexcept {
  return ::absl::MakeSpan(begin, end);
}

template <int&... ExplicitArgumentBarrier,
          typename ContainerOfT,
          typename T = typename ContainerOfT::value_type>
constexpr Span<T> MakeSpan(ContainerOfT& c) noexcept { /* NOLINT(runtime/references) */
  return ::absl::MakeSpan(c);
}

template <int&... ExplicitArgumentBarrier,
          typename ContainerOfT,
          typename T = typename ContainerOfT::value_type>
constexpr Span<T> MakeSpan(ContainerOfT& c, /* NOLINT(runtime/references) */
                           int start_index,
                           int end_index) noexcept {
  return ::absl::MakeSpan(c.data() + start_index, end_index - start_index);
}

template <int&... ExplicitArgumentBarrier, typename T>
constexpr ConstSpan<T> MakeConstSpan(T* p, int size) noexcept {
  return ::absl::MakeConstSpan(p, size);
}

template <int&... ExplicitArgumentBarrier, typename T>
constexpr ConstSpan<T> MakeConstSpan(T* p, int start_index, int end_index) noexcept {
  return ::absl::MakeConstSpan(p + start_index, end_index - start_index);
}

template <int&... ExplicitArgumentBarrier, typename T>
constexpr ConstSpan<T> MakeConstSpan(T* begin, T* end) noexcept {
  return ::absl::MakeConstSpan(begin, end);
}

template <int&... ExplicitArgumentBarrier,
          typename ContainerOfT,
          typename T = typename ContainerOfT::value_type>
constexpr ConstSpan<T> MakeConstSpan(const ContainerOfT& c) noexcept {
  return ::absl::MakeConstSpan(c);
}

template <int&... ExplicitArgumentBarrier,
          typename ContainerOfT,
          typename T = typename ContainerOfT::value_type>
constexpr ConstSpan<T> MakeConstSpan(const ContainerOfT& c,
                                     int start_index,
                                     int end_index) noexcept {
  return ::absl::MakeConstSpan(c.data() + start_index, end_index - start_index);
}

}  // namespace base
