// Copyright @2020 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>
//          <PERSON><PERSON><PERSON> (yuh<PERSON><PERSON><PERSON>@meituan.com)
//          Na<PERSON><PERSON><PERSON> (<EMAIL>)

#include "base/container/blocking_queue.h"

#include <deque>
#include <list>
#include <thread>
#include <vector>

#include "glog/logging.h"
#include "gmock/gmock.h"
#include "gtest/gtest.h"

namespace {

std::list<int> AsList(std::initializer_list<int> items) { return items; }

}  // namespace

namespace base {

using testing::ElementsAreArray;

class BlockingQueueTest : public ::testing::Test {
 protected:
  const std::deque<int>& GetItems(const BlockingQueue<int>& blocking_queue) const {
    return blocking_queue.queue_;
  }

  bool WaitCommon(absl::Time deadline, BlockingQueue<int>* blocking_queue) const {
    return CHECK_NOTNULL(blocking_queue)->WaitCommon(BlockingQueue<int>::WaitOption(deadline));
  }
};

TEST_F(BlockingQueueTest, Push1) {
  BlockingQueue<int> blocking_queue("test");
  int64_t dropped_items = 0;
  ASSERT_TRUE(blocking_queue.IsEmpty());
  ASSERT_THAT(GetItems(blocking_queue), ElementsAreArray(AsList({})));
  ASSERT_TRUE(blocking_queue.Push(100, &dropped_items));
  ASSERT_EQ(dropped_items, 0);
  ASSERT_THAT(GetItems(blocking_queue), ElementsAreArray(AsList({100})));

  blocking_queue.Close();
  ASSERT_THAT(GetItems(blocking_queue), ElementsAreArray(AsList({100})));

  ASSERT_FALSE(blocking_queue.Push(200, &dropped_items));
  ASSERT_EQ(dropped_items, 0);
  ASSERT_THAT(GetItems(blocking_queue), ElementsAreArray(AsList({100})));

  ASSERT_FALSE(blocking_queue.IsEmpty());
  ASSERT_TRUE(blocking_queue.IsClosed());

  int value = 0;
  ASSERT_TRUE(blocking_queue.Pop(&value));
  ASSERT_TRUE(blocking_queue.IsEmpty());
  ASSERT_TRUE(blocking_queue.IsClosed());
  ASSERT_THAT(GetItems(blocking_queue), ElementsAreArray(AsList({})));
  ASSERT_EQ(value, 100);

  ASSERT_FALSE(blocking_queue.Push(300));
  ASSERT_THAT(GetItems(blocking_queue), ElementsAreArray(AsList({})));
  ASSERT_FALSE(blocking_queue.Pop(&value));
}

TEST_F(BlockingQueueTest, Push2) {
  BlockingQueue<int> blocking_queue("test", 3);
  int64_t dropped_items = 0;
  for (int i = 0; i < 3; ++i) {
    blocking_queue.Push(100 + i, &dropped_items);
    ASSERT_EQ(dropped_items, 0);
    ASSERT_FALSE(blocking_queue.IsEmpty());
    ASSERT_FALSE(blocking_queue.IsClosed());
    ASSERT_EQ(blocking_queue.pending_items(), i + 1);
  }
  ASSERT_THAT(GetItems(blocking_queue), ElementsAreArray(AsList({100, 101, 102})));

  blocking_queue.Push(200, &dropped_items);
  ASSERT_EQ(dropped_items, 1);
  ASSERT_EQ(blocking_queue.pending_items(), 3);
  ASSERT_THAT(GetItems(blocking_queue), ElementsAreArray(AsList({101, 102, 200})));

  blocking_queue.Push(300, &dropped_items);
  ASSERT_EQ(dropped_items, 1);
  ASSERT_EQ(blocking_queue.pending_items(), 3);
  ASSERT_THAT(GetItems(blocking_queue), ElementsAreArray(AsList({102, 200, 300})));

  blocking_queue.Push(400, &dropped_items);
  ASSERT_EQ(dropped_items, 1);
  ASSERT_EQ(blocking_queue.pending_items(), 3);
  ASSERT_THAT(GetItems(blocking_queue), ElementsAreArray(AsList({200, 300, 400})));

  blocking_queue.Close();
  ASSERT_FALSE(blocking_queue.IsEmpty());
  ASSERT_TRUE(blocking_queue.IsClosed());
  ASSERT_EQ(blocking_queue.pending_items(), 3);
  ASSERT_THAT(GetItems(blocking_queue), ElementsAreArray(AsList({200, 300, 400})));

  ASSERT_FALSE(blocking_queue.Push(500));
  ASSERT_EQ(blocking_queue.pending_items(), 3);
  ASSERT_THAT(GetItems(blocking_queue), ElementsAreArray(AsList({200, 300, 400})));

  std::list<int> values1;
  ASSERT_TRUE(blocking_queue.PopAll(&values1));
  ASSERT_TRUE(blocking_queue.IsEmpty());
  ASSERT_TRUE(blocking_queue.IsClosed());
  ASSERT_EQ(blocking_queue.pending_items(), 0);
  ASSERT_THAT(GetItems(blocking_queue), ElementsAreArray(AsList({})));
  ASSERT_THAT(values1, ElementsAreArray(AsList({200, 300, 400})));

  std::vector<int> values2;
  ASSERT_FALSE(blocking_queue.PopAll(&values2));
  ASSERT_THAT(values2, ElementsAreArray(AsList({})));
}

TEST_F(BlockingQueueTest, Wait1) {
  BlockingQueue<int> blocking_queue("test");
  ASSERT_FALSE(WaitCommon(absl::Now(), &blocking_queue));

  ASSERT_TRUE(blocking_queue.Push(100));
  ASSERT_THAT(GetItems(blocking_queue), ElementsAreArray(AsList({100})));
  ASSERT_TRUE(WaitCommon(absl::Now() - absl::Hours(1), &blocking_queue));

  blocking_queue.Close();
  ASSERT_TRUE(WaitCommon(absl::Now() - absl::Hours(1), &blocking_queue));
  ASSERT_THAT(GetItems(blocking_queue), ElementsAreArray(AsList({100})));

  int value = 0;
  ASSERT_TRUE(blocking_queue.Pop(&value));
  ASSERT_EQ(value, 100);
  ASSERT_FALSE(WaitCommon(absl::Now(), &blocking_queue));
}

TEST_F(BlockingQueueTest, Wait2) {
  BlockingQueue<int> blocking_queue("test");

  std::thread t1([&blocking_queue]() {
    absl::SleepFor(absl::Milliseconds(5));
    ASSERT_TRUE(blocking_queue.Push(100));
  });

  ASSERT_TRUE(WaitCommon(absl::InfiniteFuture(), &blocking_queue));

  t1.join();

  ASSERT_TRUE(WaitCommon(absl::InfiniteFuture(), &blocking_queue));

  int value = 0;
  ASSERT_TRUE(blocking_queue.Pop(&value));
  ASSERT_EQ(value, 100);
  ASSERT_FALSE(WaitCommon(absl::Now(), &blocking_queue));

  std::thread t2([&blocking_queue]() {
    absl::SleepFor(absl::Milliseconds(5));
    blocking_queue.Close();
  });

  ASSERT_FALSE(WaitCommon(absl::Now() + absl::Hours(1), &blocking_queue));

  t2.join();

  ASSERT_FALSE(WaitCommon(absl::Now() + absl::Hours(1), &blocking_queue));
}

TEST_F(BlockingQueueTest, Pop) {
  BlockingQueue<int> blocking_queue("test");

  int value0 = 0;
  ASSERT_FALSE(blocking_queue.PopWithTimeout(absl::Microseconds(1), &value0));

  ASSERT_TRUE(blocking_queue.Push(100));
  ASSERT_THAT(GetItems(blocking_queue), ElementsAreArray(AsList({100})));
  ASSERT_EQ(blocking_queue.pending_items(), 1);

  int value1 = 0;
  ASSERT_TRUE(blocking_queue.PopWithTimeout(absl::Microseconds(1), &value1));
  ASSERT_EQ(value1, 100);
  ASSERT_THAT(GetItems(blocking_queue), ElementsAreArray(AsList({})));
  ASSERT_EQ(blocking_queue.pending_items(), 0);

  std::thread t1([&blocking_queue] {
    absl::SleepFor(absl::Milliseconds(5));
    ASSERT_TRUE(blocking_queue.Push(200));
    ASSERT_TRUE(blocking_queue.Push(300));
  });

  int value3 = 0;
  ASSERT_TRUE(blocking_queue.Pop(&value3));
  ASSERT_EQ(value3, 200);

  t1.join();

  ASSERT_TRUE(blocking_queue.Pop(&value3));
  ASSERT_EQ(value3, 300);
  ASSERT_FALSE(blocking_queue.PopWithTimeout(absl::Microseconds(1), &value3));

  std::thread t2([&blocking_queue] {
    absl::SleepFor(absl::Milliseconds(5));
    ASSERT_TRUE(blocking_queue.Push(400));
    blocking_queue.Close();
  });

  int value4 = 0;
  ASSERT_TRUE(blocking_queue.Pop(&value4));
  ASSERT_EQ(value4, 400);

  t2.join();

  ASSERT_FALSE(blocking_queue.PopWithTimeout(absl::Hours(1), &value4));
}

TEST_F(BlockingQueueTest, PopN) {
  BlockingQueue<int> blocking_queue("test");

  for (int i = 0; i < 3; ++i) {
    ASSERT_TRUE(blocking_queue.Push(i));
  }
  std::deque<int> values1;
  ASSERT_TRUE(blocking_queue.PopN(10, &values1));
  ASSERT_THAT(GetItems(blocking_queue), ElementsAreArray(AsList({})));
  ASSERT_THAT(values1, ElementsAreArray(AsList({0, 1, 2})));

  for (int i = 0; i < 5; ++i) {
    ASSERT_TRUE(blocking_queue.Push(100 + i));
  }
  std::deque<int> values2;
  ASSERT_TRUE(blocking_queue.PopN(2, &values2));
  ASSERT_THAT(GetItems(blocking_queue), ElementsAreArray(AsList({102, 103, 104})));
  ASSERT_THAT(values2, ElementsAreArray(AsList({100, 101})));

  std::deque<int> values3;
  ASSERT_TRUE(blocking_queue.PopAll(&values3));
  ASSERT_THAT(GetItems(blocking_queue), ElementsAreArray(AsList({})));
  ASSERT_THAT(values3, ElementsAreArray(AsList({102, 103, 104})));

  std::thread t1([&blocking_queue]() {
    absl::SleepFor(absl::Milliseconds(5));
    ASSERT_TRUE(blocking_queue.Push(300));
  });

  std::vector<int> values4;
  ASSERT_TRUE(blocking_queue.PopNWithTimeout(100, absl::Hours(1), &values4));
  ASSERT_THAT(GetItems(blocking_queue), ElementsAreArray(AsList({})));
  ASSERT_THAT(values4, ElementsAreArray(AsList({300})));

  t1.join();

  std::thread t2([&blocking_queue] {
    absl::SleepFor(absl::Milliseconds(5));
    for (int i = 0; i < 100; ++i) {
      ASSERT_TRUE(blocking_queue.Push(400 + i));
    }
    blocking_queue.Close();
  });

  std::vector<int> values5;
  ASSERT_TRUE(blocking_queue.PopN(1, &values5));
  while (values5.size() < 100) {
    ASSERT_TRUE(blocking_queue.PopNWithTimeout(7, absl::Hours(1), &values5));
  }
  ASSERT_EQ(100, values5.size());
  for (int i = 0; i < static_cast<int>(values5.size()); ++i) {
    ASSERT_EQ(values5[i], 400 + i);
  }

  t2.join();

  ASSERT_FALSE(blocking_queue.PopAllWithTimeout(absl::Hours(1), &values5));
}

TEST_F(BlockingQueueTest, PopNWithOption) {
  BlockingQueue<int> blocking_queue("test");
  std::thread t1([&blocking_queue] {
    absl::SleepFor(absl::Milliseconds(100));
    for (int i = 0; i < 10; ++i) {
      ASSERT_TRUE(blocking_queue.Push(i));
    }
  });

  BlockingQueue<int>::WaitOption option_1(absl::InfiniteFuture(), 10, 10);

  std::deque<int> values1;
  ASSERT_TRUE(blocking_queue.PopNWithOption(option_1, &values1));
  ASSERT_THAT(GetItems(blocking_queue), ElementsAreArray(AsList({})));
  t1.join();

  std::thread t2([&blocking_queue] {
    absl::SleepFor(absl::Milliseconds(100));
    for (int i = 0; i < 10; ++i) {
      ASSERT_TRUE(blocking_queue.Push(i));
    }
  });

  std::deque<int> values2;
  BlockingQueue<int>::WaitOption option_2(absl::InfiniteFuture(), 10, 11);
  ASSERT_TRUE(blocking_queue.PopNWithOption(option_2, &values2));
  ASSERT_THAT(GetItems(blocking_queue), ElementsAreArray(AsList({})));
  t2.join();

  for (int i = 0; i < 10; ++i) {
    ASSERT_TRUE(blocking_queue.Push(i));
  }

  std::deque<int> values3;
  BlockingQueue<int>::WaitOption option_3(absl::InfiniteFuture(), 11);
  ASSERT_TRUE(blocking_queue.PopNWithOption(option_3, &values3));
  ASSERT_THAT(GetItems(blocking_queue), ElementsAreArray(AsList({})));

  for (int i = 0; i < 10; ++i) {
    ASSERT_TRUE(blocking_queue.Push(i));
  }

  std::deque<int> values4;
  BlockingQueue<int>::WaitOption option_4(absl::InfiniteFuture(), 9);
  ASSERT_TRUE(blocking_queue.PopNWithOption(option_4, &values4));
  ASSERT_THAT(GetItems(blocking_queue), ElementsAreArray(AsList({9})));

  std::deque<int> values5;
  BlockingQueue<int>::WaitOption option_5(absl::InfinitePast(), 9);
  ASSERT_TRUE(blocking_queue.PopNWithOption(option_5, &values5));
  ASSERT_THAT(GetItems(blocking_queue), ElementsAreArray(AsList({})));

  ASSERT_TRUE(blocking_queue.Push(1));
  std::deque<int> values6;
  BlockingQueue<int>::WaitOption option_6(absl::InfiniteFuture());
  ASSERT_TRUE(blocking_queue.PopNWithOption(option_6, &values6));
  ASSERT_THAT(GetItems(blocking_queue), ElementsAreArray(AsList({})));

  std::thread t3([&blocking_queue] {
    absl::SleepFor(absl::Milliseconds(50));
    ASSERT_TRUE(blocking_queue.Push(1));
  });

  std::deque<int> values7;
  ASSERT_TRUE(blocking_queue.PopNWithOption(
      BlockingQueue<int>::WaitOption(absl::Now() + absl::Milliseconds(100)), &values7));
  ASSERT_THAT(GetItems(blocking_queue), ElementsAreArray(AsList({})));
  t3.join();

  std::thread t4([&blocking_queue] {
    absl::SleepFor(absl::Milliseconds(50));
    for (int i = 0; i < 10; ++i) {
      ASSERT_TRUE(blocking_queue.Push(i));
    }
  });

  std::deque<int> values8;
  ASSERT_TRUE(blocking_queue.PopNWithOption(
      BlockingQueue<int>::WaitOption(absl::Now() + absl::Milliseconds(100), 10, 20), &values8));
  ASSERT_THAT(GetItems(blocking_queue), ElementsAreArray(AsList({})));
  t4.join();

  std::thread t5([&blocking_queue] {
    std::deque<int> values9;
    ASSERT_TRUE(blocking_queue.PopNWithOption(
        BlockingQueue<int>::WaitOption(absl::InfiniteFuture(), 10, 10), &values9));
  });

  std::thread t6([&blocking_queue] {
    std::deque<int> values10;
    ASSERT_TRUE(blocking_queue.PopNWithOption(
        BlockingQueue<int>::WaitOption(absl::InfiniteFuture(), 10, 10), &values10));
  });

  for (int i = 0; i < 10; ++i) {
    ASSERT_TRUE(blocking_queue.Push(i));
  }
  absl::SleepFor(absl::Milliseconds(50));
  for (int i = 0; i < 10; ++i) {
    ASSERT_TRUE(blocking_queue.Push(i));
  }

  t5.join();
  t6.join();
}
}  // namespace base
