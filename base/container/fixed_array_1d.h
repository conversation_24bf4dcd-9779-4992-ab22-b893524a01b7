// Copyright @2024 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <memory>
#include <utility>

#include "glog/logging.h"

#include "base/common/macros.h"

namespace base {
namespace container {

template <typename T>
class FixedArray1d {
 public:
  FixedArray1d() = default;
  explicit FixedArray1d(int size) : size_(size) {
    CHECK_GT(size, 0);
    data_ = std::make_unique<T[]>(size_);
  }

  FixedArray1d(int size, const T& default_value) : size_(size) {
    CHECK_GT(size, 0);
    data_ = std::make_unique<T[]>(size_);
    for (int i = 0; i < size_; ++i) {
      data_[i] = default_value;
    }
  }

  FixedArray1d(FixedArray1d&& other) {
    size_ = other.size_;
    data_ = std::move(other.data_);
  }

  FixedArray1d& operator=(FixedArray1d&& other) {
    size_ = other.size_;
    data_ = std::move(other.data_);
    return *this;
  }

  bool IsIndexValid(int index) const { return index >= 0 && index < size_; }

  const T& operator[](int index) const {
    DCHECK(IsIndexValid(index));
    return data_[index];
  }

  T& operator[](int index) {
    DCHECK(IsIndexValid(index));
    return data_[index];
  }

  int size() const { return size_; }
  bool empty() const { return size_ == 0; }

  const T* data() const { return data_.get(); }
  T* data() { return data_.get(); }

 private:
  int size_ = 0;
  std::unique_ptr<T[]> data_;

  DISALLOW_COPY_AND_ASSIGN(FixedArray1d);
};

template <typename T>
void InitFixedArray(FixedArray1d<T>* fixed_array) {
  CHECK(fixed_array != nullptr);
  static_assert(std::is_trivial<T>::value, "T must be trivial.");
  static_assert(std::is_standard_layout<T>::value, "T must be a standard layout class.");
  memset(fixed_array->data(), 0, fixed_array->size() * sizeof(T));
}

template <typename T>
void InitFixedArray(FixedArray1d<T>* fixed_array, int size) {
  CHECK(fixed_array != nullptr);
  CHECK_GE(fixed_array->size(), size);
  static_assert(std::is_trivial<T>::value, "T must be trivial.");
  static_assert(std::is_standard_layout<T>::value, "T must be a standard layout class.");
  memset(fixed_array->data(), 0, size * sizeof(T));
}

}  // namespace container
}  // namespace base
