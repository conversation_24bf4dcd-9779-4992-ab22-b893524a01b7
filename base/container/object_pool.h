// Copyright @2023 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <functional>
#include <memory>
#include <stack>
#include <utility>

#include "glog/logging.h"

#include "base/common/macros.h"
#include "base/synchronization/mutex.h"

namespace base {

template <typename T>
class ObjectPool final {
 public:
  using ObjectPoolPtr = ObjectPool<T>*;
  // This custom "Deleter" is used to: automatically put back the "Object" into the "ObjectPool"
  // when the "Object" is destructed.
  struct ExternalDeleter {
    explicit ExternalDeleter(std::weak_ptr<ObjectPoolPtr> pool) : pool_(pool) {}

    void operator()(T* ptr) {
      std::shared_ptr<ObjectPoolPtr> pool_ptr = pool_.lock();
      if (pool_ptr != nullptr) {
        // TODO(yanghuaxing): make it thread safe.
        (*(pool_ptr.get()))->Add(std::unique_ptr<T>{ptr});
        return;
      }
      std::default_delete<T>{}(ptr);
    }

   private:
    std::weak_ptr<ObjectPoolPtr> pool_;
  };
  using ObjectType = std::unique_ptr<T, ExternalDeleter>;

  ObjectPool() : this_ptr_(new ObjectPoolPtr(this)) {}
  virtual ~ObjectPool() = default;

  void Add(std::unique_ptr<T> t) EXCLUDES(mutex_);
  ObjectType Acquire() EXCLUDES(mutex_);
  // Note: this is the number of currently available object in the pool,
  // NOT the total number of created object.
  size_t Size() const EXCLUDES(mutex_) {
    base::ReaderMutexLock lock(&mutex_);
    return pool_.size();
  }

 private:
  // TODO(yanghuaxing): support waiting using timeout.
  bool WaitCommon() REQUIRES_SHARED(mutex_);

  std::shared_ptr<ObjectPoolPtr> this_ptr_;
  std::stack<std::unique_ptr<T>> pool_ GUARDED_BY(mutex_);
  mutable base::Mutex mutex_;

  DISALLOW_COPY_AND_ASSIGN(ObjectPool);
};

template <typename T>
void ObjectPool<T>::Add(std::unique_ptr<T> t) {
  base::WriterMutexLock lock(&mutex_);
  pool_.push(std::move(t));
}

template <typename T>
typename ObjectPool<T>::ObjectType ObjectPool<T>::Acquire() {
  base::WriterMutexLock lock(&mutex_);
  CHECK(WaitCommon());
  ObjectType object(pool_.top().release(),
                    ExternalDeleter{std::weak_ptr<ObjectPoolPtr>{this_ptr_}});
  pool_.pop();
  return object;
}

template <typename T>
bool ObjectPool<T>::WaitCommon() {
  const auto not_empty = [this]() REQUIRES_SHARED(mutex_) { return !pool_.empty(); };
  mutex_.Await(base::Condition(&not_empty));
  return !pool_.empty();
}

}  // namespace base
