// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/container/grid.h"

#include "gtest/gtest.h"

namespace base {
namespace container {

struct CellType {
  int iteration = -1;
  int num_points = 0;
  math::Vector2d center;
};

TEST(GridTest, GetValue) {
  Grid<CellType> grid(0.5, 20, 20);
  EXPECT_EQ(20, grid.rows());
  EXPECT_EQ(20, grid.cols());
  EXPECT_NEAR(0.5, grid.resolution(), 1e-6);

  grid.Recenter({0.0, 0.0});
  EXPECT_EQ(-10, grid.r0_);
  EXPECT_EQ(-10, grid.c0_);
  EXPECT_EQ(nullptr, grid.GetMutableValueByGlobalXY({-5.1, -5.1}));
  EXPECT_EQ(nullptr, grid.GetMutableValueByGlobalXY({5.1, 5.1}));

  const CellType* cell1 = grid.GetValueByGlobalXY({0.1, 0.1});
  EXPECT_NE(nullptr, cell1);
  EXPECT_EQ(-1, cell1->iteration);
  EXPECT_EQ(0, cell1->num_points);

  CellType value = {2, 3, {0.0, 0.0}};
  grid.Fill(value);
  EXPECT_NE(nullptr, cell1);
  EXPECT_EQ(2, cell1->iteration);
  EXPECT_EQ(3, cell1->num_points);

  grid.Recenter({5.0, 5.0});
  EXPECT_EQ(0, grid.r0_);
  EXPECT_EQ(0, grid.c0_);
  EXPECT_NE(nullptr, cell1);
  EXPECT_EQ(-1, cell1->iteration);
  EXPECT_EQ(0, cell1->num_points);
}

TEST(GridTest, CellToXY) {
  Grid<CellType> grid(0.5, 10, 10);
  for (int r = 0; r < 10; ++r) {
    for (int c = 0; c < 10; ++c) {
      const CellType* cell = grid.GetValueByRowColumn({r, c});
      EXPECT_NE(cell, nullptr);

      const math::Vector2i coord = grid.CellToRowColumn(cell);
      EXPECT_EQ(r, coord.x);
      EXPECT_EQ(c, coord.y);

      const math::Vector2d xy = grid.CellToGlobalXY(cell);
      EXPECT_NEAR((c + 0.5) * 0.5, xy.x, 1e-6);
      EXPECT_NEAR((r + 0.5) * 0.5, xy.y, 1e-6);
    }
  }
  grid.Recenter({5.0, 5.0});
  const CellType* cell = grid.GetValueByRowColumn({0, 0});
  const math::Vector2i coord_global = grid.CellToGlobalRowColumn(cell);
  EXPECT_EQ(5, coord_global.x);
  EXPECT_EQ(5, coord_global.y);
}

TEST(GridTest, GetMutableCellsContainedBy) {
  Grid<CellType> grid(0.5, 20, 20);
  grid.Recenter({0.0, 0.0});
  for (int r = 0; r < 20; ++r) {
    for (int c = 0; c < 20; ++c) {
      CellType* cell = grid.GetMutableValueByRowColumn({r, c});
      EXPECT_TRUE(cell != nullptr);
      cell->center = grid.RowColumnToGlobalXY({r, c});
    }
  }

  {
    const math::Polygon2d polygon(math::OrientedBox2d::FromAABox({0, 0}, {2, 2}));
    const std::vector<CellType*> cells = grid.GetMutableCellsContainedBy(polygon);
    EXPECT_EQ(16, cells.size());
    for (const auto* cell : cells) {
      EXPECT_TRUE(cell != nullptr);
      const math::Vector2d center = grid.CellToGlobalXY(cell);
      EXPECT_NEAR(cell->center.x, center.x, 1e-6);
      EXPECT_NEAR(cell->center.y, center.y, 1e-6);
    }
    grid.Recenter({5.0, 5.0});
    const std::vector<CellType*> cells2 = grid.GetMutableCellsContainedBy(polygon);
    EXPECT_EQ(16, cells2.size());
    for (const auto* cell : cells2) {
      EXPECT_TRUE(cell != nullptr);
      EXPECT_NEAR(cell->center.x, 0.0, 1e-6);
      EXPECT_NEAR(cell->center.y, 0.0, 1e-6);
    }
  }
  {
    const math::Polygon2d polygon(math::OrientedBox2d::FromAABox({2, 2}, {3, 3}));
    const std::vector<CellType*> cells = grid.GetMutableCellsContainedBy(polygon);
    EXPECT_EQ(4, cells.size());
  }
  {
    const math::Polygon2d polygon(math::OrientedBox2d::FromAABox({2, 2}, {2.5, 2.5}));
    const std::vector<CellType*> cells = grid.GetMutableCellsContainedBy(polygon);
    EXPECT_EQ(1, cells.size());
  }
  {
    const math::Polygon2d polygon(math::OrientedBox2d::FromAABox({2, 2}, {2.2, 2.2}));
    const std::vector<CellType*> cells = grid.GetMutableCellsContainedBy(polygon);
    EXPECT_EQ(1, cells.size());
  }
}

TEST(GridTest, GetCellsContainedBy) {
  Grid<CellType> grid(0.5, 20, 20);
  grid.Recenter({0.0, 0.0});
  for (int r = 0; r < 20; ++r) {
    for (int c = 0; c < 20; ++c) {
      CellType* cell = grid.GetMutableValueByRowColumn({r, c});
      EXPECT_TRUE(cell != nullptr);
      cell->center = grid.RowColumnToGlobalXY({r, c});
    }
  }

  {
    const math::Polygon2d polygon(math::OrientedBox2d::FromAABox({0, 0}, {2, 2}));
    const std::vector<const CellType*> cells = grid.GetCellsContainedBy(polygon);
    EXPECT_EQ(16, cells.size());
    for (const auto* cell : cells) {
      EXPECT_TRUE(cell != nullptr);
      const math::Vector2d center = grid.CellToGlobalXY(cell);
      EXPECT_NEAR(cell->center.x, center.x, 1e-6);
      EXPECT_NEAR(cell->center.y, center.y, 1e-6);
    }
    grid.Recenter({5.0, 5.0});
    const std::vector<const CellType*> cells2 = grid.GetCellsContainedBy(polygon);
    EXPECT_EQ(16, cells2.size());
    for (const auto* cell : cells2) {
      EXPECT_TRUE(cell != nullptr);
      EXPECT_NEAR(cell->center.x, 0.0, 1e-6);
      EXPECT_NEAR(cell->center.y, 0.0, 1e-6);
    }
  }
  {
    const math::Polygon2d polygon(math::OrientedBox2d::FromAABox({2, 2}, {3, 3}));
    const std::vector<const CellType*> cells = grid.GetCellsContainedBy(polygon);
    EXPECT_EQ(4, cells.size());
  }
  {
    const math::Polygon2d polygon(math::OrientedBox2d::FromAABox({2, 2}, {2.5, 2.5}));
    const std::vector<const CellType*> cells = grid.GetCellsContainedBy(polygon);
    EXPECT_EQ(1, cells.size());
  }
  {
    const math::Polygon2d polygon(math::OrientedBox2d::FromAABox({2, 2}, {2.2, 2.2}));
    const std::vector<const CellType*> cells = grid.GetCellsContainedBy(polygon);
    EXPECT_EQ(1, cells.size());
  }
}

TEST(GridTest, GetCellsTouchedBy) {
  Grid<CellType> grid(0.5, 20, 20);
  grid.Recenter({0.0, 0.0});
  for (int r = 0; r < 20; ++r) {
    for (int c = 0; c < 20; ++c) {
      CellType* cell = grid.GetMutableValueByRowColumn({r, c});
      EXPECT_TRUE(cell != nullptr);
      cell->center = grid.RowColumnToGlobalXY({r, c});
    }
  }
  {
    const math::Polygon2d polygon(math::OrientedBox2d::FromAABox({0.25, 0.25}, {0.75, 0.75}));
    const std::vector<const CellType*> cells = grid.GetCellsTouchedBy(polygon);
    EXPECT_EQ(4, cells.size());
  }
  {
    const math::Polygon2d polygon(math::OrientedBox2d::FromAABox({0.0, 0.0}, {2.0, 2.0}));
    const std::vector<const CellType*> cells = grid.GetCellsTouchedBy(polygon);
    EXPECT_EQ(36, cells.size());
  }
  {
    const math::Polygon2d polygon(
        math::OrientedBox2d::FromAABox({0.0, 0.0}, {2.0 - math::kEpsilon, 2.0 - math::kEpsilon}));
    const std::vector<const CellType*> cells = grid.GetCellsTouchedBy(polygon);
    EXPECT_EQ(25, cells.size());
  }
  {
    const math::Polygon2d polygon(math::OrientedBox2d::FromAABox(
        {-math::kEpsilon, -math::kEpsilon}, {2.0 - math::kEpsilon, 2.0 - math::kEpsilon}));
    const std::vector<const CellType*> cells = grid.GetCellsTouchedBy(polygon);
    EXPECT_EQ(25, cells.size());
  }
}

TEST(GridTest, ProtoTest) {
  Grid<CellType> grid1(0.5, 20, 20);
  grid1.Recenter({0.0, 0.0});
  for (int r = 0; r < 20; ++r) {
    for (int c = 0; c < 20; ++c) {
      CellType* cell = grid1.GetMutableValueByRowColumn({r, c});
      EXPECT_TRUE(cell != nullptr);
      cell->center = grid1.RowColumnToGlobalXY({r, c});
    }
  }
  container_proto::Grid proto = grid1.ToProto();
  Grid<CellType> grid2(proto);
  EXPECT_NEAR(grid1.resolution(), grid2.resolution(), 1e-6);
  EXPECT_EQ(grid1.rows(), grid2.rows());
  EXPECT_EQ(grid1.cols(), grid2.cols());
  EXPECT_EQ(grid1.r0_, grid2.r0_);
  EXPECT_EQ(grid1.c0_, grid2.c0_);
  for (int i = 0; i < grid1.rows() * grid1.cols(); i++) {
    EXPECT_EQ(grid1.array_[i].iteration, grid2.array_[i].iteration);
    EXPECT_EQ(grid1.array_[i].num_points, grid2.array_[i].num_points);
    EXPECT_NEAR(grid1.array_[i].center.x, grid2.array_[i].center.x, 1e-6);
    EXPECT_NEAR(grid1.array_[i].center.y, grid2.array_[i].center.y, 1e-6);
  }
}

TEST(GridTest, GetNeighborsTest) {
  Grid<CellType> grid(0.5, 20, 20);
  grid.Recenter({0.0, 0.0});
  for (int r = 0; r < 20; ++r) {
    for (int c = 0; c < 20; ++c) {
      CellType* cell = grid.GetMutableValueByRowColumn({r, c});
      EXPECT_TRUE(cell != nullptr);
      cell->center = grid.RowColumnToGlobalXY({r, c});
    }
  }

  {
    const std::vector<const CellType*> neighbors = grid.GetNeighbors({5, 5});
    EXPECT_EQ(4, neighbors.size());
  }
  {
    const std::vector<const CellType*> neighbors = grid.GetNeighbors({0, 5});
    EXPECT_EQ(3, neighbors.size());
  }
}

TEST(GridTest, GetMutableNeighborsTest) {
  Grid<CellType> grid(0.5, 20, 20);
  grid.Recenter({0.0, 0.0});
  for (int r = 0; r < 20; ++r) {
    for (int c = 0; c < 20; ++c) {
      CellType* cell = grid.GetMutableValueByRowColumn({r, c});
      EXPECT_TRUE(cell != nullptr);
      cell->center = grid.RowColumnToGlobalXY({r, c});
    }
  }

  {
    std::vector<CellType*> neighbors = grid.GetMutableNeighbors({5, 5});
    EXPECT_EQ(4, neighbors.size());
  }
  {
    std::vector<CellType*> neighbors = grid.GetMutableNeighbors({0, 5});
    EXPECT_EQ(3, neighbors.size());
  }
}

TEST(GridTest, GetNeighborsNxNTest) {
  Grid<CellType> grid(0.5, 20, 20);
  grid.Recenter({0.0, 0.0});
  for (int r = 0; r < 20; ++r) {
    for (int c = 0; c < 20; ++c) {
      CellType* cell = grid.GetMutableValueByRowColumn({r, c});
      EXPECT_TRUE(cell != nullptr);
      cell->center = grid.RowColumnToGlobalXY({r, c});
    }
  }

  {
    const std::vector<const CellType*> neighbors = grid.GetNeighborsNxN({5, 5}, 1, true);
    EXPECT_EQ(8, neighbors.size());
  }
  {
    const std::vector<const CellType*> neighbors = grid.GetNeighborsNxN({5, 5}, 1, false);
    EXPECT_EQ(9, neighbors.size());
  }
  {
    const std::vector<const CellType*> neighbors = grid.GetNeighborsNxN({0, 5}, 2, true);
    EXPECT_EQ(14, neighbors.size());
  }
  {
    const std::vector<const CellType*> neighbors = grid.GetNeighborsNxN({0, 5}, 2, false);
    EXPECT_EQ(15, neighbors.size());
  }
}

TEST(GridTest, ApplyToNeighborsTest) {
  Grid<CellType> grid(0.5, 20, 20);
  grid.Recenter({0.0, 0.0});
  for (int r = 0; r < 20; ++r) {
    for (int c = 0; c < 20; ++c) {
      CellType* cell = grid.GetMutableValueByRowColumn({r, c});
      EXPECT_TRUE(cell != nullptr);
      cell->iteration = 0;
      cell->center = grid.RowColumnToGlobalXY({r, c});
    }
  }
  {
    grid.ApplyToNeighbors({5, 5}, [](CellType* cell) { cell->iteration++; });
    std::vector<CellType*> neighbors = grid.GetMutableNeighbors({5, 5});
    for (const auto& neighbor : neighbors) {
      EXPECT_EQ(1, neighbor->iteration);
    }
  }
}

}  // namespace container
}  // namespace base
