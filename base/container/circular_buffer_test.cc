// Copyright @2022 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "glog/logging.h"
#include "gtest/gtest.h"

#include "base/container/circular_buffer.h"

#include <iostream>

namespace base {
namespace container {
namespace {

constexpr int kBufferSize = 9;
constexpr double kDelta = 1e-3;

class TestDouble  {
 public:
  TestDouble() {}
  explicit TestDouble(double value) : value_(value) {}
  virtual ~TestDouble() = default;
  double value() const { return value_; }
  void set_value(double value) { value_ = value; }

 private:
  double value_ = -1.0;
};

}  // namespace

TEST(CircularBufferTest, DefaultBuffer) {
  CircularBuffer<int> buffer;
  EXPECT_EQ(buffer.capacity(), 1);
  EXPECT_EQ(buffer.size(), 0);
  EXPECT_TRUE(buffer.empty());
  EXPECT_FALSE(buffer.full());

  buffer.push_back(0);
  EXPECT_EQ(buffer.size(), 1);
  EXPECT_EQ(buffer.front(), 0);
  EXPECT_EQ(buffer.back(), 0);
  EXPECT_FALSE(buffer.empty());
  EXPECT_TRUE(buffer.full());

  buffer.push_back(1);
  EXPECT_EQ(buffer.size(), 1);
  EXPECT_EQ(buffer.front(), 1);
  EXPECT_EQ(buffer.back(), 1);
  EXPECT_FALSE(buffer.empty());
  EXPECT_TRUE(buffer.full());

  buffer.pop_front();
  EXPECT_TRUE(buffer.empty());
  EXPECT_FALSE(buffer.full());
  EXPECT_EQ(buffer.size(), 0);
}

TEST(CircularBufferTest, DeathDefaultBuffer) {
  CircularBuffer<int> buffer;
  ASSERT_DEATH(buffer.pop_front(), "");
  ASSERT_DEATH(buffer.front(), "");
  ASSERT_DEATH(buffer.back(), "");

  buffer.push_back(0);
  buffer.push_back(1);
  buffer.pop_front();
  ASSERT_DEATH(buffer.front(), "");
  ASSERT_DEATH(buffer.back(), "");
  ASSERT_DEATH(buffer.pop_front(), "");
}

TEST(CircularBufferTest, DeathInvalidSize) {
  ASSERT_DEATH(CircularBuffer<int> buffer(0), "");
  ASSERT_DEATH(CircularBuffer<int> buffer(-1), "");
}

TEST(CircularBufferTest, UniquePtrBuffer) {
  CircularBuffer<std::unique_ptr<int>> buffer;
  EXPECT_EQ(buffer.capacity(), 1);

  buffer.push_back(std::make_unique<int>(0));
  EXPECT_EQ(*buffer.front(), 0);
  EXPECT_EQ(*buffer.back(), 0);

  buffer.push_back(std::make_unique<int>(1));
  EXPECT_EQ(*buffer.front(), 1);
  EXPECT_EQ(*buffer.back(), 1);

  buffer.pop_front();
  EXPECT_EQ(buffer.size(), 0);
}

TEST(CircularBufferTest, OverwriteBuffer) {
  CircularBuffer<int> buffer(kBufferSize);
  EXPECT_EQ(buffer.capacity(), kBufferSize);

  for (int i = 0; i < kBufferSize; ++i) {
    buffer.push_back(i);
    EXPECT_EQ(buffer.size(), i + 1);
  }

  for (int i = 0; !buffer.empty(); ++i) {
    EXPECT_EQ(buffer.front(), i);
    buffer.pop_front();
    EXPECT_EQ(buffer.size(), kBufferSize - i - 1);
  }

  // Overwrite the 1st half.
  constexpr int kOverwriteBufferTime = kBufferSize * 2;
  for (int i = 0; i < kOverwriteBufferTime; ++i) {
    buffer.push_back(i);
    EXPECT_EQ(buffer.size(), i < kBufferSize ? i + 1 : kBufferSize);
  }

  for (int i = 0; i < kBufferSize; ++i) {
    EXPECT_EQ(buffer.front(), i + kBufferSize);
    buffer.pop_front();
    EXPECT_EQ(buffer.size(), kBufferSize - i - 1);
  }
}

TEST(CircularBufferTest, Reset) {
  CircularBuffer<int> buffer(2);
  EXPECT_TRUE(buffer.empty());
  buffer.push_back(1);
  buffer.push_back(2);
  EXPECT_EQ(buffer.size(), 2);
  buffer.reset();
  EXPECT_TRUE(buffer.empty());
}

TEST(CircularBufferTest, Iterator) {
  CircularBuffer<TestDouble> buffer(kBufferSize);
  for (int i = 0; i < kBufferSize; ++i) {
    buffer.push_back(TestDouble(1.0 * i));
  }
  EXPECT_NEAR((*buffer.begin()).value(), 0.0, kDelta);
  EXPECT_NEAR(buffer.begin()->value(), 0.0, kDelta);

  // Iteration
  double expected_value = 0.0;
  for (const auto& item : buffer) {
    EXPECT_NEAR(item.value(), expected_value, kDelta);
    expected_value += 1.0;
  }

  // Editable
  constexpr double kAddToAll = 10.0;
  for (auto& item : buffer) {
    item.set_value(item.value() + kAddToAll);
  }
  expected_value = kAddToAll;
  for (const auto& item : buffer) {
    EXPECT_NEAR(item.value(), expected_value, kDelta);
    expected_value += 1.0;
  }

  {
    // ++iterator
    int counter = 0;
    for (CircularBuffer<TestDouble>::Iterator iter = buffer.begin(); iter != buffer.end(); ++iter) {
      ++counter;
    }
    EXPECT_EQ(9, counter);
  }

  {
    // Buffer is NOT full.
    EXPECT_TRUE(buffer.full());
    buffer.pop_front();
    buffer.pop_front();
    EXPECT_EQ(buffer.size(), buffer.capacity() - 2);
    EXPECT_FALSE(buffer.full());

    {
      // `for (auto& item : buffer)` calls `++iter` and `iter != container.end()`
      int counter = 0;
      for (const auto& item : buffer) {
        counter++;
        LOG(ERROR) << "time" << counter << ":" << buffer.front().value() << "," << buffer.back().value();
        LOG(ERROR) << "time" << counter << ":" << item.value() << ","
                   << buffer.head_ << "," << buffer.tail_;
      }
      EXPECT_EQ(counter, buffer.size());
    }

    {
      // Test `iter++`, `iter1 == iter2`, `operator*`, `operator->`
      int counter = 0;
      CircularBuffer<TestDouble>::Iterator iter = buffer.begin();
      bool flag = iter == buffer.end();  // Avoid compiler optimize from `==` to `!=`.
      while (!flag) {
        counter++;
        LOG(ERROR) << "time" << counter << ":" << buffer.front().value() << "," << buffer.back().value();
        LOG(ERROR) << "time" << counter << ":" << (*iter).value() << "," << iter->value() << ","
                   << buffer.head_ << "," << buffer.tail_;
        iter++;
        flag = iter == buffer.end();
      }
      EXPECT_EQ(counter, buffer.size());
    }
  }
}

TEST(CircularBufferTest, ConstIterator) {
  CircularBuffer<TestDouble> buffer(kBufferSize);
  for (int i = 0; i < kBufferSize; ++i) {
    buffer.push_back(TestDouble(1.0 * i));
  }
  EXPECT_NEAR((*buffer.begin()).value(), 0.0, kDelta);
  EXPECT_NEAR(buffer.begin()->value(), 0.0, kDelta);

  const CircularBuffer<TestDouble>& const_buffer = buffer;
  // Iteration
  double expected_value = 0.0;
  for (const auto& item : const_buffer) {
    EXPECT_NEAR(item.value(), expected_value, kDelta);
    expected_value += 1.0;
  }

  {
    // ++iterator
    int counter = 0;
    for (CircularBuffer<TestDouble>::ConstIterator iter = const_buffer.begin();
         iter != const_buffer.end();
         ++iter) {
      ++counter;
    }
    EXPECT_EQ(9, counter);
  }
  {
    // iterator++
    int counter = 0;
    for (CircularBuffer<TestDouble>::ConstIterator iter = const_buffer.begin();
         iter != const_buffer.end();
         iter++) {
      ++counter;
    }
    EXPECT_EQ(9, counter);
  }
}

TEST(CircularBufferTest, Swap) {
  constexpr int kLeftSize = 5;
  constexpr int kRightSize = 7;

  CircularBuffer<int> left(kLeftSize);
  for (int i = 0; i < kLeftSize; ++i) {
    if (i % 2 != 0) {
      left.push_back(i);
    }
  }
  constexpr int kInsertedLeft[] = {1, 3};
  CircularBuffer<int> right(kRightSize);
    for (int i = 0; i < kRightSize; ++i) {
    if (i % 2 == 0) {
      right.push_back(i);
    }
  }
  constexpr int kInsertedRight[] = {0, 2, 4, 6};

  ASSERT_EQ(left.size(), 2);
  int index = 0;
  for (int i : left) {
    EXPECT_EQ(i, kInsertedLeft[index++]);
  }
  ASSERT_EQ(right.size(), 4);
  index = 0;
  for (int i : right) {
    EXPECT_EQ(i, kInsertedRight[index++]);
  }
  CircularBuffer<int> const* left_ptr = &left;
  CircularBuffer<int> const* right_ptr = &right;

  left.swap(right);

  ASSERT_EQ(left.size(), 4);
  index = 0;
  for (int i : left) {
    EXPECT_EQ(i, kInsertedRight[index++]);
  }
  ASSERT_EQ(right.size(), 2);
  index = 0;
  for (int i : right) {
    EXPECT_EQ(i, kInsertedLeft[index++]);
  }
  EXPECT_EQ(left_ptr, &left);
  EXPECT_EQ(right_ptr, &right);
}

}  // namespace container
}  // namespace base
