package(default_visibility = ["//visibility:public"])

cc_library(
    name = "array_view",
    hdrs = ["array_view.h"],
)

cc_library(
    name = "span",
    hdrs = [
        "span.h",
    ],
    deps = [
        "@com_google_absl//absl/types:span",
    ],
)

cc_library(
    name = "blocking_queue",
    hdrs = [
        "blocking_queue.h",
    ],
    deps = [
        "//base/common:macros",
        "//base/common:numeric",
        "//base/strings:format",
        "//base/synchronization:mutex",
    ],
)

cc_test(
    name = "blocking_queue_test",
    size = "small",
    srcs = [
        "blocking_queue_test.cc",
    ],
    tags = [
        "ci",
        "ci_cpu",
    ],
    deps = [
        ":blocking_queue",
        "//base/testing:test_main",
    ],
)

cc_library(
    name = "utils",
    hdrs = ["utils.h"],
    deps = ["//base/common:status"],
)

cc_test(
    name = "utils_test",
    size = "small",
    srcs = ["utils_test.cc"],
    deps = [
        ":utils",
        "//base/testing:test_main",
        "//modules/planning/common:vector_shell",
    ],
)

cc_library(
    name = "enum_class_hash",
    hdrs = ["enum_class_hash.h"],
    deps = ["//base/common:status"],
)

cc_test(
    name = "enum_class_hash_test",
    size = "small",
    srcs = ["enum_class_hash_test.cc"],
    tags = [
        "ci",
        "ci_cpu",
    ],
    deps = [
        ":enum_class_hash",
        "//base/testing:test_main",
    ],
)

cc_library(
    name = "time_ordered_data_lookup_buffer",
    hdrs = [
        "time_ordered_data_lookup_buffer.h",
    ],
    deps = [
        "//base/common:optional",
        "//base/math",
        "//base/math:constants",
        "//base/math:piecewise_linear_function",
        "//base/math/transform",
        "//base/strings:format",
        "//walle/engine/util:time",
    ],
)

cc_test(
    name = "time_ordered_data_lookup_buffer_test",
    size = "small",
    srcs = [
        "time_ordered_data_lookup_buffer_test.cc",
    ],
    tags = [
        "ci",
        "ci_cpu",
    ],
    deps = [
        ":time_ordered_data_lookup_buffer",
        "//base/testing:test_main",
    ],
)

cc_library(
    name = "rolling_grid",
    hdrs = [
        "rolling_grid.h",
    ],
    deps = [
        ":grid",
        "@glog",
        "@gtest",
    ],
)

cc_test(
    name = "rolling_grid_test",
    size = "small",
    srcs = ["rolling_grid_test.cc"],
    tags = [
        "ci",
        "ci_cpu",
    ],
    deps = [
        ":rolling_grid",
        "//base/testing:test_main",
    ],
)

cc_library(
    name = "grid_cells",
    hdrs = [
        "grid_cells.h",
    ],
    deps = [
        ":hash_map",
        "//base/common:macros",
        "//base/common:optional",
        "//base/math",
        "//base/strings:macros",
        "@gtest",
    ],
)

cc_test(
    name = "grid_cells_test",
    size = "small",
    srcs = [
        "grid_cells_test.cc",
    ],
    tags = [
        "ci",
        "ci_cpu",
    ],
    deps = [
        ":grid_cells",
        "//base/testing:test_main",
    ],
)

cc_library(
    name = "grid_index",
    hdrs = [
        "grid_index.h",
    ],
    deps = [
        ":grid_cells",
        "//base/common:macros",
        "//base/common:optional",
        "//base/math",
        "//base/strings:macros",
        "@gtest",
    ],
)

cc_test(
    name = "grid_index_test",
    size = "small",
    srcs = [
        "grid_index_test.cc",
    ],
    tags = [
        "ci",
        "ci_cpu",
    ],
    deps = [
        ":grid_index",
        "//base/testing:random",
        "//base/testing:test_main",
    ],
)

cc_binary(
    name = "grid_index_benchmark",
    srcs = [
        "grid_index_benchmark.cc",
    ],
    deps = [
        ":grid_index",
        "@benchmark",
    ],
)

cc_library(
    name = "grid",
    hdrs = [
        "grid.h",
    ],
    deps = [
        "//base/common:macros",
        "//base/container/proto:cc_grid_proto",
        "//base/math",
        "@gtest",
    ],
)

cc_test(
    name = "grid_test",
    size = "small",
    srcs = ["grid_test.cc"],
    tags = [
        "ci",
        "ci_cpu",
    ],
    deps = [
        ":grid",
        "//base/testing:test_main",
    ],
)

cc_library(
    name = "fixed_array_2d",
    hdrs = [
        "fixed_array_2d.h",
    ],
    deps = [
        "//base/common:macros",
        "@glog",
    ],
)

cc_test(
    name = "fixed_array_2d_test",
    size = "small",
    srcs = ["fixed_array_2d_test.cc"],
    tags = [
        "ci",
        "ci_cpu",
    ],
    deps = [
        ":fixed_array_2d",
        "//base/testing:test_main",
    ],
)

cc_library(
    name = "fixed_array_1d",
    hdrs = [
        "fixed_array_1d.h",
    ],
    deps = [
        "//base/common:macros",
        "@glog",
    ],
)

cc_test(
    name = "fixed_array_1d_test",
    size = "small",
    srcs = ["fixed_array_1d_test.cc"],
    tags = [
        "ci",
        "ci_cpu",
    ],
    deps = [
        ":fixed_array_1d",
        "//base/testing:test_main",
    ],
)

cc_library(
    name = "tensor",
    hdrs = [
        "tensor.h",
        "tensor_axis.h",
        "tensor_util.h",
    ],
    deps = [
        "//base/container:utils",
        "//base/strings:format",
        "@eigen",
    ],
)

cc_test(
    name = "tensor_test",
    size = "small",
    srcs = ["tensor_test.cc"],
    tags = [
        "ci",
        "ci_cpu",
    ],
    deps = [
        ":tensor",
        "//base/testing:test_main",
    ],
)

cc_library(
    name = "lru_cache",
    hdrs = [
        "lru_cache.h",
    ],
    deps = [
        "//base/common:macros",
        "//base/common:optional",
        "@glog",
        "@gtest",
    ],
)

cc_test(
    name = "lru_cache_test",
    size = "small",
    srcs = [
        "lru_cache_test.cc",
    ],
    tags = [
        "ci",
        "ci_cpu",
    ],
    deps = [
        ":lru_cache",
        "//base/testing:test_main",
    ],
)

cc_library(
    name = "partition_buffer",
    hdrs = ["partition_buffer.h"],
    deps = [
        "//base/common:macros",
        "//base/container:blocking_queue",
    ],
)

cc_test(
    name = "partition_buffer_test",
    size = "small",
    srcs = ["partition_buffer_test.cc"],
    tags = [
        "ci",
        "ci_cpu",
    ],
    deps = [
        ":partition_buffer",
        "//base/testing:test_main",
    ],
)

cc_library(
    name = "id_queriable_list",
    hdrs = ["id_queriable_list.h"],
    deps = [
        "//base/common:field_macros",
        "//base/common:macros",
        "//base/container:utils",
        "@glog",
    ],
)

cc_test(
    name = "id_queriable_list_test",
    size = "small",
    srcs = ["id_queriable_list_test.cc"],
    tags = [
        "ci",
        "ci_cpu",
    ],
    deps = [
        ":id_queriable_list",
        "@gtest//:main",
    ],
)

cc_library(
    name = "segment_tree",
    hdrs = ["segment_tree.h"],
    deps = [
        "@glog",
    ],
)

cc_test(
    name = "segment_tree_test",
    size = "small",
    srcs = ["segment_tree_test.cc"],
    tags = [
        "ci",
        "ci_cpu",
    ],
    deps = [
        ":segment_tree",
        "@gtest//:main",
    ],
)

cc_library(
    name = "circular_queue",
    hdrs = ["circular_queue.h"],
)

cc_library(
    name = "circular_buffer",
    hdrs = [
        "circular_buffer.h",
    ],
    deps = [
        "//base/common:macros",
        "@glog",
        "@gtest",
    ],
)

cc_library(
    name = "keyed_double_buffer",
    hdrs = [
        "keyed_double_buffer.h",
    ],
    deps = [
        "//base/common:macros",
        "//base/container:utils",
        "@glog",
    ],
)

cc_test(
    name = "circular_buffer_test",
    size = "small",
    srcs = ["circular_buffer_test.cc"],
    tags = [
        "ci",
        "ci_cpu",
    ],
    deps = [
        ":circular_buffer",
        "//base/testing:test_main",
    ],
)

cc_binary(
    name = "circular_buffer_benchmark",
    srcs = ["circular_buffer_benchmark.cc"],
    deps = [
        ":circular_buffer",
        "//walle/pch:onboard_message",
        "@benchmark",
    ],
)

cc_test(
    name = "keyed_double_buffer_test",
    size = "small",
    srcs = ["keyed_double_buffer_test.cc"],
    tags = [
        "ci",
        "ci_cpu",
    ],
    deps = [
        ":keyed_double_buffer",
        "//base/strings:format",
        "//base/testing:test_main",
    ],
)

cc_library(
    name = "object_pool",
    hdrs = ["object_pool.h"],
    deps = [
        "//base/common:macros",
        "//base/synchronization:mutex",
    ],
)

cc_test(
    name = "object_pool_test",
    size = "small",
    srcs = ["object_pool_test.cc"],
    tags = [
        "ci",
        "ci_cpu",
    ],
    deps = [
        ":object_pool",
        "//base/testing:test_main",
    ],
)

cc_library(
    name = "boost_shared_memory",
    hdrs = [
        "boost_shared_memory.h",
    ],
    linkopts = [
        "-lpthread",
        "-lrt",
    ],
    deps = [
        "@boost",
    ],
)

cc_test(
    name = "boost_shared_memory_test",
    size = "small",
    srcs = ["boost_shared_memory_test.cc"],
    tags = [
        "ci",
        "ci_cpu",
    ],
    deps = [
        ":boost_shared_memory",
        "//base/testing:test_main",
    ],
)

cc_library(
    name = "hash_set",
    hdrs = [
        "hash_set.h",
    ],
    deps = [
        "@com_google_absl//absl/container:flat_hash_set",
        "@com_google_absl//absl/container:node_hash_set",
        "@com_google_absl//absl/hash",
    ],
)

cc_library(
    name = "hash_map",
    hdrs = [
        "hash_map.h",
    ],
    deps = [
        "@com_google_absl//absl/container:flat_hash_map",
        "@com_google_absl//absl/container:node_hash_map",
        "@com_google_absl//absl/hash",
    ],
)

cc_library(
    name = "lock_free_ring_buffer",
    hdrs = ["lock_free_ring_buffer.h"],
    visibility = ["//visibility:public"],
)

cc_test(
    name = "lock_free_ring_buffer_test",
    size = "small",
    srcs = ["lock_free_ring_buffer_test.cc"],
    tags = [
        "ci",
        "ci_cpu",
    ],
    deps = [
        ":lock_free_ring_buffer",
        "//base/testing:test_main",
        "@glog",
    ],
)
