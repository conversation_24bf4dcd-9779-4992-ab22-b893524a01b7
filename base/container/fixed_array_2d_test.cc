// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "gtest/gtest.h"

#include "base/container/fixed_array_2d.h"

namespace base {
namespace container {
namespace {

constexpr int kRows = 1080;
constexpr int kCols = 1920;

struct IdWithIteration {
  int id = -1;
  int iteration = -1;
};

}  // namespace

TEST(FixArray2dTest, BasicTest) {
  FixedArray2d<IdWithIteration> fix_array_2d(kRows, kCols);
  fix_array_2d(15, 26) = IdWithIteration{5, 6};
  const IdWithIteration* data = fix_array_2d.data();
  const IdWithIteration& test_value = data[15 * kCols + 26];
  EXPECT_EQ(5, test_value.id);
  EXPECT_EQ(6, test_value.iteration);
  EXPECT_EQ(kRows, fix_array_2d.rows());
  EXPECT_EQ(kCols, fix_array_2d.cols());

  EXPECT_TRUE(fix_array_2d.IsIndexValid(1079, 1919));
  EXPECT_FALSE(fix_array_2d.IsIndexValid(1079, 1920));
  EXPECT_FALSE(fix_array_2d.IsIndexValid(-1, 1919));
}

}  // namespace container
}  // namespace base
