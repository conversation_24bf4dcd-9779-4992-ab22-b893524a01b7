// Copyright @2020 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/container/rolling_grid.h"

#include <vector>

#include "gtest/gtest.h"

namespace base {
namespace container {

struct CellType {
  int iteration = -1;
  int num_points = 0;
  math::Vector2d center;
};

TEST(RollingGridTest, BuildTest) {
  constexpr double resolution = 0.5;
  RollingGrid<CellType> grid1(resolution, 20, 20);
  grid1.Recenter({0.0, 0.0});
  EXPECT_EQ(-10, grid1.r0_);
  EXPECT_EQ(-10, grid1.c0_);
  EXPECT_EQ(10, grid1.array_c0_);
  EXPECT_EQ(10, grid1.array_r0_);
  EXPECT_EQ(nullptr, grid1.GetMutableValueByGlobalXY({-5.1, -5.1}));
  EXPECT_EQ(nullptr, grid1.GetMutableVal<PERSON>ByGlobalXY({5.1, 5.1}));

  const CellType* cell1 = grid1.GetValueByGlobalXY({0.1, 0.1});
  EXPECT_NE(nullptr, cell1);
  EXPECT_EQ(-1, cell1->iteration);
  EXPECT_EQ(0, cell1->num_points);

  std::vector<CellType> array_copy = grid1.array_;

  const double global_x = (grid1.r0_ + grid1.rows() / 2) * resolution;
  const double global_y = (grid1.c0_ + grid1.cols() / 2) * resolution;
  RollingGrid<CellType>::Options options{
      .resolution = grid1.resolution_,
      .rows = grid1.rows_,
      .cols = grid1.cols_,
      .array_r0 = grid1.array_r0(),
      .array_c0 = grid1.array_c0(),
      .global_x = global_x,
      .global_y = global_y,
  };
  RollingGrid<CellType> grid2(options, std::move(array_copy));
  EXPECT_EQ(-10, grid2.r0_);
  EXPECT_EQ(-10, grid2.c0_);
  EXPECT_EQ(10, grid2.array_c0_);
  EXPECT_EQ(10, grid2.array_r0_);
  const CellType* cell2 = grid2.GetValueByGlobalXY({0.1, 0.1});
  EXPECT_NE(nullptr, cell2);
  EXPECT_EQ(-1, cell2->iteration);
  EXPECT_EQ(0, cell2->num_points);
}

TEST(RollingGridTest, GetValue) {
  RollingGrid<CellType> grid(0.5, 20, 20);
  EXPECT_EQ(20, grid.rows());
  EXPECT_EQ(20, grid.cols());
  EXPECT_EQ(0, grid.array_c0_);
  EXPECT_EQ(0, grid.array_r0_);
  EXPECT_NEAR(0.5, grid.resolution(), 1e-6);

  grid.Recenter({0.0, 0.0});
  EXPECT_EQ(-10, grid.r0_);
  EXPECT_EQ(-10, grid.c0_);
  EXPECT_EQ(10, grid.array_c0_);
  EXPECT_EQ(10, grid.array_r0_);
  EXPECT_EQ(nullptr, grid.GetMutableValueByGlobalXY({-5.1, -5.1}));
  EXPECT_EQ(nullptr, grid.GetMutableValueByGlobalXY({5.1, 5.1}));

  const CellType* cell1 = grid.GetValueByGlobalXY({0.1, 0.1});
  EXPECT_NE(nullptr, cell1);
  EXPECT_EQ(-1, cell1->iteration);
  EXPECT_EQ(0, cell1->num_points);

  CellType value = {2, 3, {0.0, 0.0}};
  grid.Fill(value);
  EXPECT_NE(nullptr, cell1);
  EXPECT_EQ(2, cell1->iteration);
  EXPECT_EQ(3, cell1->num_points);

  grid.Recenter({5.0, 5.0});
  EXPECT_EQ(0, grid.r0_);
  EXPECT_EQ(0, grid.c0_);
  EXPECT_EQ(0, grid.array_c0_);
  EXPECT_EQ(0, grid.array_r0_);
}

TEST(RollingGridTest, MoveGrid) {
  RollingGrid<CellType> grid(0.5, 10, 10);
  for (int i = 0; i < 20; ++i) {
    EXPECT_EQ(0, grid.array_c0_);
    EXPECT_EQ(i % 10, grid.array_r0_);
    grid.AddRowNorth();
  }

  for (int i = 0; i < 20; ++i) {
    EXPECT_EQ(i % 10, grid.array_c0_);
    EXPECT_EQ(0, grid.array_r0_);
    grid.AddColumnEast();
  }

  for (int i = 20; i > 0; --i) {
    EXPECT_EQ(0, grid.array_c0_);
    EXPECT_EQ(i % 10, grid.array_r0_);
    grid.AddRowSouth();
  }

  for (int i = 20; i > 0; --i) {
    EXPECT_EQ(i % 10, grid.array_c0_);
    EXPECT_EQ(0, grid.array_r0_);
    grid.AddColumnWest();
  }
}

TEST(RollingGridTest, CellToXY) {
  RollingGrid<CellType> grid(0.5, 10, 10);
  for (int r = 0; r < 10; ++r) {
    for (int c = 0; c < 10; ++c) {
      const CellType* cell = grid.GetValueByRowColumn({r, c});
      EXPECT_NE(cell, nullptr);

      const math::Vector2i coord = grid.CellToRowColumn(cell);
      EXPECT_EQ(r, coord.x);
      EXPECT_EQ(c, coord.y);

      const math::Vector2d xy = grid.CellToGlobalXY(cell);
      EXPECT_NEAR((c + 0.5) * 0.5, xy.x, 1e-6);
      EXPECT_NEAR((r + 0.5) * 0.5, xy.y, 1e-6);
    }
  }
}

TEST(RollingGridTest, GetMutableCellsContainedBy) {
  RollingGrid<CellType> grid(0.5, 20, 20);
  grid.Recenter({0.0, 0.0});
  for (int r = 0; r < 20; ++r) {
    for (int c = 0; c < 20; ++c) {
      CellType* cell = grid.GetMutableValueByRowColumn({r, c});
      EXPECT_TRUE(cell != nullptr);
      cell->center = grid.RowColumnToGlobalXY({r, c});
    }
  }

  const math::Polygon2d polygon(math::OrientedBox2d::FromAABox({0, 0}, {2, 2}));
  const std::vector<CellType*> cells1 = grid.GetMutableCellsContainedBy(polygon);
  EXPECT_EQ(16, cells1.size());
  for (const auto* cell : cells1) {
    EXPECT_TRUE(cell != nullptr);
    const math::Vector2d center = grid.CellToGlobalXY(cell);
    EXPECT_NEAR(cell->center.x, center.x, 1e-6);
    EXPECT_NEAR(cell->center.y, center.y, 1e-6);
  }

  grid.Recenter({5.0, 5.0});
  const std::vector<CellType*> cells2 = grid.GetMutableCellsContainedBy(polygon);
  EXPECT_EQ(16, cells2.size());
  for (const auto* cell : cells2) {
    EXPECT_TRUE(cell != nullptr);
    const math::Vector2d center = grid.CellToGlobalXY(cell);
    EXPECT_NEAR(cell->center.x, center.x, 1e-6);
    EXPECT_NEAR(cell->center.y, center.y, 1e-6);
  }

  grid.Recenter({6.0, 6.0});
  const std::vector<CellType*> cells3 = grid.GetMutableCellsContainedBy(polygon);
  EXPECT_EQ(4, cells3.size());
  for (const auto* cell : cells3) {
    EXPECT_TRUE(cell != nullptr);
    const math::Vector2d center = grid.CellToGlobalXY(cell);
    EXPECT_NEAR(cell->center.x, center.x, 1e-6);
    EXPECT_NEAR(cell->center.y, center.y, 1e-6);
  }

  grid.Recenter({15.0, 15.0});
  const std::vector<CellType*> cells4 = grid.GetMutableCellsContainedBy(polygon);
  EXPECT_TRUE(cells4.empty());
}

TEST(RollingGridTest, GetNeighborsTest) {
  RollingGrid<CellType> grid(0.5, 20, 20);
  grid.Recenter({0.0, 0.0});
  for (int r = 0; r < 20; ++r) {
    for (int c = 0; c < 20; ++c) {
      CellType* cell = grid.GetMutableValueByRowColumn({r, c});
      EXPECT_TRUE(cell != nullptr);
      cell->center = grid.RowColumnToGlobalXY({r, c});
    }
  }

  {
    const std::vector<const CellType*> neighbors = grid.GetNeighbors({5, 5});
    EXPECT_EQ(4, neighbors.size());
  }
  {
    const std::vector<const CellType*> neighbors = grid.GetNeighbors({0, 5});
    EXPECT_EQ(3, neighbors.size());
  }
}

}  // namespace container
}  // namespace base
