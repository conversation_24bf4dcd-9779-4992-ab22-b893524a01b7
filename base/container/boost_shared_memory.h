// Copyright @2024 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <functional>

#include <boost/interprocess/allocators/allocator.hpp>
#include <boost/interprocess/containers/vector.hpp>
#include <boost/interprocess/managed_shared_memory.hpp>
#include <boost/interprocess/smart_ptr/shared_ptr.hpp>
#include <boost/interprocess/smart_ptr/unique_ptr.hpp>
#include <boost/unordered_map.hpp>
#include <boost/unordered_set.hpp>

namespace base {

using boost::interprocess::anonymous_instance;
using boost::interprocess::make_managed_shared_ptr;
using boost::interprocess::make_managed_unique_ptr;
using boost::interprocess::shared_memory_object;
using boost_managed_memory = boost::interprocess::fixed_managed_shared_memory;
using segment_manager_type = boost_managed_memory::segment_manager;
using void_allocator_type = boost::interprocess::allocator<void, segment_manager_type>;

template <class Type>
using BoostSharedPtr =
    typename boost::interprocess::managed_shared_ptr<Type, boost_managed_memory>::type;

template <class Type>
using BoostUniquePtr =
    typename boost::interprocess::managed_unique_ptr<Type, boost_managed_memory>::type;

template <class KeyType, class ValueType>
using BoostUnorderedMapAllocator =
    boost::interprocess::allocator<std::pair<KeyType, ValueType>, segment_manager_type>;

template <class KeyType, class ValueType>
using BoostUnorderedMap = boost::unordered_map<KeyType,
                                               ValueType,
                                               boost::hash<KeyType>,
                                               std::equal_to<KeyType>,
                                               BoostUnorderedMapAllocator<KeyType, ValueType>>;

template <class KeyType>
using BoostUnorderedSetAllocator = boost::interprocess::allocator<KeyType, segment_manager_type>;

template <class KeyType>
using BoostUnorderedSet = boost::unordered_set<KeyType,
                                               boost::hash<KeyType>,
                                               std::equal_to<KeyType>,
                                               BoostUnorderedSetAllocator<KeyType>>;

template <class Type>
using BoostVector =
    boost::container::vector<Type, boost::interprocess::allocator<Type, segment_manager_type>>;

}  // namespace base
