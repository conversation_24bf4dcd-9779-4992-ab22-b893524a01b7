// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/container/partition_buffer.h"

#include <string>

#include "gtest/gtest.h"

namespace base {
namespace container {

TEST(PartitionBufferTest, PushGetTest) {
  int kPartitionNum = 10;
  PartitionBuffer<int> partition_buffer("test", kPartitionNum);

  for (int i = 0; i < 100; i++) {
    partition_buffer.Push(i, i % kPartitionNum);
  }

  base::BlockingQueue<int>* buffer = partition_buffer.GetOnePartition(2);
  EXPECT_EQ(buffer->pending_items(), kPartitionNum);
}

}  // namespace container
}  // namespace base
