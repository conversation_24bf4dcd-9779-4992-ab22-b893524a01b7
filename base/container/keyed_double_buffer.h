// Copyright @2023 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <atomic>
#include <memory>
#include <string>
#include <unordered_map>
#include <utility>
#include <vector>

#include "boost/circular_buffer.hpp"

#include "base/common/macros.h"
#include "base/container/utils.h"
#include "base/synchronization/mutex.h"

namespace base {

template <typename T>
class KeyedDoubleBuffer final {
 public:
  using Buffer = boost::circular_buffer<T>;
  struct Options {
    bool extra_default_buffer = false;
    int default_buffer_size = 512;
    std::string default_buffer_name = "default_buffer";
  };

  explicit KeyedDoubleBuffer(const std::unordered_map<std::string, int>& buffer_key_size,
                             Options options = Options{});
  ~KeyedDoubleBuffer();

  bool Push(const std::string& key, const T& value);
  // Not thread-safe while multi-thread take.
  std::vector<const KeyedDoubleBuffer::Buffer*> TakeAllBuffer();
  const KeyedDoubleBuffer::Buffer& TakeBuffer(const std::string& key) {
    DoubleBuffer* double_buffer = base::FindOrDie(double_buffer_map_, key);
    return TakeBuffer(double_buffer);
  }
  void Close() { is_closed_ = true; }

  bool IsEmpty() const;
  bool IsClosed() const { return is_closed_; }

 private:
  struct DoubleBuffer {
    explicit DoubleBuffer(int buffer_capacity) {
      // Create double buffer for swap.
      buffer[0] = std::make_unique<Buffer>(buffer_capacity);
      buffer[1] = std::make_unique<Buffer>(buffer_capacity);
    }
    std::unique_ptr<Buffer> buffer[2];
    std::atomic<int8_t> active_index{0};
    base::Mutex mutex;
  };
  const KeyedDoubleBuffer::Buffer& TakeBuffer(DoubleBuffer* double_buffer);

  Options options_;
  std::atomic<bool> is_closed_{false};
  std::vector<std::unique_ptr<DoubleBuffer>> double_buffer_list_;
  std::unordered_map<std::string, DoubleBuffer*> double_buffer_map_;

  DISALLOW_COPY_AND_ASSIGN(KeyedDoubleBuffer);
};

template <typename T>
KeyedDoubleBuffer<T>::KeyedDoubleBuffer(const std::unordered_map<std::string, int>& buffer_key_size,
                                        KeyedDoubleBuffer::Options options)
    : options_(std::move(options)) {
  for (const auto& entry : buffer_key_size) {
    const std::string& key = entry.first;
    const int buffer_size = entry.second;
    auto buffer = std::make_unique<DoubleBuffer>(buffer_size);
    double_buffer_map_[key] = buffer.get();
    double_buffer_list_.push_back(std::move(buffer));
  }
  if (options_.extra_default_buffer) {
    auto buffer = std::make_unique<DoubleBuffer>(options_.default_buffer_size);
    double_buffer_map_[options_.default_buffer_name] = buffer.get();
    double_buffer_list_.push_back(std::move(buffer));
  }
}

template <typename T>
KeyedDoubleBuffer<T>::~KeyedDoubleBuffer() {
  if (!is_closed_) {
    Close();
  }
}

template <typename T>
bool KeyedDoubleBuffer<T>::Push(const std::string& key, const T& value) {
  if (is_closed_) {
    return false;
  }
  bool dropped = false;
  DoubleBuffer* double_buffer = base::FindWithDefault(
      double_buffer_map_, key, double_buffer_map_[options_.default_buffer_name]);
  {
    base::MutexLock scoped_lock(&double_buffer->mutex);
    if (double_buffer->buffer[double_buffer->active_index]->full()) {
      dropped = true;
    }
    double_buffer->buffer[double_buffer->active_index]->push_back(std::move(value));
  }
  LOG_IF_EVERY_N(INFO, dropped, 10000) << "Drop item, key: " << key;
  return true;
}

template <typename T>
std::vector<const typename KeyedDoubleBuffer<T>::Buffer*> KeyedDoubleBuffer<T>::TakeAllBuffer() {
  std::vector<const KeyedDoubleBuffer<T>::Buffer*> buffer_list;
  buffer_list.reserve(double_buffer_list_.size());
  for (std::unique_ptr<DoubleBuffer>& double_buffer : double_buffer_list_) {
    const KeyedDoubleBuffer<T>::Buffer& buffer = TakeBuffer(double_buffer.get());
    if (buffer.empty()) {
      continue;
    }
    buffer_list.emplace_back(&buffer);
  }
  return buffer_list;
}

template <typename T>
const typename KeyedDoubleBuffer<T>::Buffer& KeyedDoubleBuffer<T>::TakeBuffer(
    DoubleBuffer* double_buffer) {
  double_buffer->buffer[1 - double_buffer->active_index]->clear();
  base::MutexLock scoped_lock(&double_buffer->mutex);
  if (!double_buffer->buffer[double_buffer->active_index]->empty()) {
    double_buffer->active_index = 1 - double_buffer->active_index;
  }
  return *double_buffer->buffer[1 - double_buffer->active_index];
}

template <typename T>
bool KeyedDoubleBuffer<T>::IsEmpty() const {
  for (const std::unique_ptr<DoubleBuffer>& double_buffer : double_buffer_list_) {
    base::MutexLock scoped_lock(&double_buffer->mutex);
    if (!double_buffer->buffer[double_buffer->active_index]->empty()) {
      return false;
    }
  }
  return true;
}

}  // namespace base
