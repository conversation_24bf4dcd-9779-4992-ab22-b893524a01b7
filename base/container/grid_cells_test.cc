// Copyright @2025 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/container/grid_cells.h"

#include "gmock/gmock.h"
#include "gtest/gtest.h"

namespace base {
namespace container {
namespace {

class CellType0 {
 public:
  int v = 0;
};

class CellType1 {
 public:
  explicit CellType1(int v) : v_(v) {}

  CellType1() = default;

  int v() const { return v_; }

 private:
  int v_ = 0;
};

}  // namespace

TEST(GridCellsHashMapTest, Constructor) {
  {
    const GridCellsHashMap<int> grid_cells;
    ASSERT_EQ(grid_cells.x_size(), 0);
    ASSERT_EQ(grid_cells.y_size(), 0);
    ASSERT_EQ(grid_cells.index_to_cells().size(), 0);
  }
  {
    const GridCellsHashMap<int> grid_cells(3, 7);
    ASSERT_EQ(grid_cells.x_size(), 3);
    ASSERT_EQ(grid_cells.y_size(), 7);
    ASSERT_EQ(grid_cells.index_to_cells().size(), 0);
    for (int x = 0; x < 3; ++x) {
      ASSERT_EQ(grid_cells.GetByIndex({x, 0}), nullptr);
      ASSERT_EQ(grid_cells.GetByIndex({x, 1}), nullptr);
      ASSERT_EQ(grid_cells.GetByIndex({x, 2}), nullptr);
    }
    ASSERT_EQ(grid_cells.index_to_cells().size(), 0);
  }
  {
    GridCellsHashMap<int> grid_cells(3, 7, 100);
    ASSERT_EQ(grid_cells.x_size(), 3);
    ASSERT_EQ(grid_cells.y_size(), 7);
    ASSERT_EQ(grid_cells.index_to_cells().size(), 0);
    for (int x = 0; x < 3; ++x) {
      for (int y = 0; y < 3; ++y) {
        (*CHECK_NOTNULL(grid_cells.GetMutableByIndex({x, y})))++;
      }
    }
    ASSERT_EQ(grid_cells.index_to_cells().size(), 9);
    for (int x = 0; x < grid_cells.x_size(); ++x) {
      for (int y = 0; y < grid_cells.y_size(); ++y) {
        if (x >= 0 && x < 3 && y >= 0 && y < 3) {
          ASSERT_EQ(*CHECK_NOTNULL(grid_cells.GetByIndex({x, y})), 101);
        } else {
          ASSERT_EQ(grid_cells.GetByIndex({x, y}), nullptr);
        }
      }
    }
  }
  {
    GridCellsHashMap<CellType0> grid_cells(5, 11, CellType0{.v = 37});
    ASSERT_EQ(grid_cells.x_size(), 5);
    ASSERT_EQ(grid_cells.y_size(), 11);
    ASSERT_EQ(grid_cells.index_to_cells().size(), 0);
    for (int x = 0; x < 3; ++x) {
      for (int y = 0; y < 3; ++y) {
        CellType0* p = CHECK_NOTNULL(grid_cells.GetMutableByIndex({x, y}));
        ASSERT_EQ(p->v, 37);
        p->v++;
      }
    }
    ASSERT_EQ(grid_cells.index_to_cells().size(), 9);
    for (int x = 0; x < 3; ++x) {
      for (int y = 0; y < 3; ++y) {
        ASSERT_EQ(CHECK_NOTNULL(grid_cells.GetByIndex({x, y}))->v, 38);
      }
    }
  }
  {
    GridCellsHashMap<int> grid_cells(3, 7, 89);
    for (int x = 0; x < grid_cells.x_size(); ++x) {
      for (int y = 0; y < grid_cells.y_size(); ++y) {
        ASSERT_EQ(*CHECK_NOTNULL(grid_cells.GetMutableByIndex({x, y})), 89);
        (*CHECK_NOTNULL(grid_cells.GetMutableByIndex({x, y})))++;
        ASSERT_EQ(*CHECK_NOTNULL(grid_cells.GetByIndex({x, y})), 90);
      }
    }
  }
}

TEST(GridCellsArray2dTest, Constructor) {
  {
    const GridCellsArray2d<int> grid_cells;
    ASSERT_EQ(grid_cells.x_size(), 0);
    ASSERT_EQ(grid_cells.y_size(), 0);
    ASSERT_EQ(grid_cells.cells_array().size(), 0);
  }
  {
    const GridCellsArray2d<int> grid_cells(3, 7);
    ASSERT_EQ(grid_cells.x_size(), 3);
    ASSERT_EQ(grid_cells.y_size(), 7);
    ASSERT_EQ(grid_cells.cells_array().size(), 21);
    for (int x = 0; x < 3; ++x) {
      ASSERT_NE(grid_cells.GetByIndex({x, 0}), nullptr);
      ASSERT_NE(grid_cells.GetByIndex({x, 1}), nullptr);
      ASSERT_NE(grid_cells.GetByIndex({x, 2}), nullptr);
    }
    ASSERT_EQ(grid_cells.cells_array().size(), 21);
  }
  {
    GridCellsArray2d<int> grid_cells(3, 7, 100);
    ASSERT_EQ(grid_cells.x_size(), 3);
    ASSERT_EQ(grid_cells.y_size(), 7);
    ASSERT_EQ(grid_cells.cells_array().size(), 21);
    for (int x = 0; x < 3; ++x) {
      for (int y = 0; y < 3; ++y) {
        (*CHECK_NOTNULL(grid_cells.GetMutableByIndex({x, y})))++;
      }
    }
    ASSERT_EQ(grid_cells.cells_array().size(), 21);
    for (int x = 0; x < grid_cells.x_size(); ++x) {
      for (int y = 0; y < grid_cells.y_size(); ++y) {
        if (x >= 0 && x < 3 && y >= 0 && y < 3) {
          ASSERT_EQ(*CHECK_NOTNULL(grid_cells.GetByIndex({x, y})), 101);
        } else {
          ASSERT_EQ(*CHECK_NOTNULL(grid_cells.GetByIndex({x, y})), 100);
        }
      }
    }
  }
  {
    GridCellsArray2d<CellType0> grid_cells(5, 11, CellType0{.v = 37});
    ASSERT_EQ(grid_cells.x_size(), 5);
    ASSERT_EQ(grid_cells.y_size(), 11);
    ASSERT_EQ(grid_cells.cells_array().size(), 55);
    for (int x = 0; x < 3; ++x) {
      for (int y = 0; y < 3; ++y) {
        CellType0* p = CHECK_NOTNULL(grid_cells.GetMutableByIndex({x, y}));
        ASSERT_EQ(p->v, 37);
        p->v++;
      }
    }
    ASSERT_EQ(grid_cells.cells_array().size(), 55);
    for (int x = 0; x < 3; ++x) {
      for (int y = 0; y < 3; ++y) {
        ASSERT_EQ(CHECK_NOTNULL(grid_cells.GetByIndex({x, y}))->v, 38);
      }
    }
  }
  {
    GridCellsArray2d<int> grid_cells(3, 7, 89);
    for (int x = 0; x < grid_cells.x_size(); ++x) {
      for (int y = 0; y < grid_cells.y_size(); ++y) {
        ASSERT_EQ(*CHECK_NOTNULL(grid_cells.GetMutableByIndex({x, y})), 89);
        (*CHECK_NOTNULL(grid_cells.GetMutableByIndex({x, y})))++;
        ASSERT_EQ(*CHECK_NOTNULL(grid_cells.GetByIndex({x, y})), 90);
      }
    }
  }
}

TEST(GridCellsArray2dTest, PackedOffset) {
  {
    const GridCellsArray2d<int> grid_cells(3, 10);
    ASSERT_EQ(grid_cells.PackedOffset({0, 0}), 0);
    ASSERT_EQ(grid_cells.PackedOffset({0, 1}), 1);
    ASSERT_EQ(grid_cells.PackedOffset({1, 0}), 10);
    ASSERT_EQ(grid_cells.PackedOffset({1, 2}), 12);
    ASSERT_EQ(grid_cells.UnpackOffset(0), math::Vector2i(0, 0));
    ASSERT_EQ(grid_cells.UnpackOffset(1), math::Vector2i(0, 1));
    ASSERT_EQ(grid_cells.UnpackOffset(10), math::Vector2i(1, 0));
    ASSERT_EQ(grid_cells.UnpackOffset(12), math::Vector2i(1, 2));
  }
  {
    const GridCellsArray2d<int> grid_cells(3, 5);
    ASSERT_EQ(grid_cells.PackedOffset({0, 0}), 0);
    ASSERT_EQ(grid_cells.PackedOffset({0, 1}), 1);
    ASSERT_EQ(grid_cells.PackedOffset({1, 0}), 5);
    ASSERT_EQ(grid_cells.PackedOffset({1, 1}), 6);
    ASSERT_EQ(grid_cells.UnpackOffset(0), math::Vector2i(0, 0));
    ASSERT_EQ(grid_cells.UnpackOffset(1), math::Vector2i(0, 1));
    ASSERT_EQ(grid_cells.UnpackOffset(5), math::Vector2i(1, 0));
    ASSERT_EQ(grid_cells.UnpackOffset(6), math::Vector2i(1, 1));
  }
}

TEST(GridCellsArray2dTest, Clear) {
  GridCellsArray2d<int> grid_cells(3, 10, 89);
  for (int x = 0; x < grid_cells.x_size(); ++x) {
    for (int y = 0; y < grid_cells.y_size(); ++y) {
      ASSERT_EQ(*CHECK_NOTNULL(grid_cells.GetMutableByIndex({x, y})), 89);
      (*CHECK_NOTNULL(grid_cells.GetMutableByIndex({x, y})))++;
      ASSERT_EQ(*CHECK_NOTNULL(grid_cells.GetByIndex({x, y})), 90);
    }
  }
  grid_cells.Clear();
  for (int x = 0; x < grid_cells.x_size(); ++x) {
    for (int y = 0; y < grid_cells.y_size(); ++y) {
      ASSERT_EQ(*CHECK_NOTNULL(grid_cells.GetByIndex({x, y})), 89);
    }
  }
}

}  // namespace container
}  // namespace base
