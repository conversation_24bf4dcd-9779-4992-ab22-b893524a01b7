// Copyright @2020 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>
//          <PERSON><PERSON> (<EMAIL>)
//          <PERSON><PERSON><PERSON> (yuh<PERSON><PERSON><EMAIL>)
//          Na<PERSON><PERSON><PERSON> (<EMAIL>)

#pragma once

#include <deque>
#include <functional>
#include <limits>
#include <string>
#include <utility>

#include "glog/logging.h"

#include "base/common/macros.h"
#include "base/common/numeric.h"
#include "base/strings/format.h"
#include "base/synchronization/mutex.h"

namespace base {

template <typename T>
class BlockingQueue final {
 public:
  struct WaitOption {
    explicit WaitOption(absl::Time deadline, int64_t max_items = 1)
        : deadline(deadline), max_items(max_items) {
      CHECK_LE(min_items, max_items);
    }
    WaitOption(absl::Time deadline, int64_t min_items, int64_t max_items)
        : deadline(deadline), min_items(min_items), max_items(max_items) {
      CHECK_GE(min_items, 1);
      CHECK_LE(min_items, max_items);
    }
    absl::Time deadline;
    int64_t min_items = 1;
    int64_t max_items = 1;
  };

  // Set max_pending_items=0 for unlimited queue size.
  explicit BlockingQueue(std::string name, int64_t max_pending_items = 0)
      : name_(std::move(name)), max_pending_items_(max_pending_items) {
    CHECK_GE(max_pending_items_, 0);
  }

  ~BlockingQueue() = default;

  const std::string& name() const { return name_; }
  int64_t pending_items() const { return pending_items_; }
  int64_t dropped_items() const { return dropped_items_; }

  bool IsEmpty() const { return pending_items_ == 0; }
  bool IsClosed() const { return closed_; }
  void Clear();

  // BlockingQueue::Close()
  //
  // Close the queue and wake all waiters.
  //
  // After the last item has been received from a closed queue, any further `Pop*()` or `Wait*()`
  // operation will fail and return `false` without blocking.
  void Close();

  // BlockingQueue::Push()
  //
  // Push the item T into this queue Q and wake at least one waiter if one exists, returning `true`
  // upon success and `false` without insertion if Q has been closed.
  bool Push(T item, int64_t* dropped_items = nullptr);

  // TODO(wangnaizheng):
  // Add PushWithTimeout & PushWithDeadline.

  // BlockingQueue::Pop*()
  //
  // Receive and remove the head item(s) of the queue, waiting if necessary until at least one item
  // becomes available.
  //
  // Return `true` if at least one item is received. Otherwise, return `false`, if queue is empty
  // when timeout has expired, or has been closed by a call to `Close()`.
  bool Pop(T* popped_item) { return PopCommon(WaitOption(kInfiniteFuture), popped_item); }
  bool PopWithTimeout(absl::Duration timeout, T* popped_item) {
    return PopCommon(WaitOption(absl::Now() + timeout), popped_item);
  }
  bool TryPop(T* popped_item) { return PopCommon(WaitOption(kInfinitePast), popped_item); }
  template <typename ContainerOfT>
  bool PopN(int64_t max_items, ContainerOfT* popped_items) {
    return PopNCommon(WaitOption(kInfiniteFuture, max_items), popped_items);
  }
  template <typename ContainerOfT>
  bool PopNWithTimeout(int64_t max_items, absl::Duration timeout, ContainerOfT* popped_items) {
    return PopNCommon(WaitOption(absl::Now() + timeout, max_items), popped_items);
  }
  template <typename ContainerOfT>
  bool PopNWithTimeout(int64_t min_items,
                       int64_t max_items,
                       absl::Duration timeout,
                       ContainerOfT* popped_items) {
    return PopNCommon(WaitOption(absl::Now() + timeout, min_items, max_items), popped_items);
  }
  template <typename ContainerOfT>
  bool TryPopN(int64_t max_items, ContainerOfT* popped_items) {
    return PopNCommon(WaitOption(kInfinitePast, max_items), popped_items);
  }

  template <typename ContainerOfT>
  bool PopAll(ContainerOfT* popped_items) {
    return PopNCommon(WaitOption(kInfiniteFuture, kMaxPendingItems), popped_items);
  }
  template <typename ContainerOfT>
  bool PopAllWithTimeout(absl::Duration timeout, ContainerOfT* popped_items) {
    return PopNCommon(WaitOption(absl::Now() + timeout, kMaxPendingItems), popped_items);
  }
  template <typename ContainerOfT>
  bool TryPopAll(ContainerOfT* popped_items) {
    return PopNCommon(WaitOption(kInfinitePast, kMaxPendingItems), popped_items);
  }

  template <typename ContainerOfT>
  bool PopNWithOption(const WaitOption& option, ContainerOfT* popped_items) {
    return PopNCommon(option, popped_items);
  }

  // BlockingQueue::Wait*()
  //
  // Wait until awakened by a call to `Close()` or `Push()`.
  //
  // Return `true` if the queue is non-empty. Otherwise, return `false` if queue is empty when
  // timeout has expired, or has been closed by a call to `Close()`.
  void Wait() const { WaitCommon(WaitOption(kInfiniteFuture)); }
  bool WaitWithTimeout(absl::Duration timeout) const {
    return WaitCommon(WaitOption(absl::Now() + timeout));
  }
  bool TryWait() const { return WaitCommon(WaitOption(kInfinitePast)); }

 private:
  static constexpr int64_t kMaxPendingItems = std::numeric_limits<int64_t>::max();

  bool PopCommon(const WaitOption& option, T* popped_item) EXCLUDES(mutex_);
  template <typename ContainerOfT>
  bool PopNCommon(const WaitOption& option, ContainerOfT* popped_items) EXCLUDES(mutex_);
  bool WaitCommon(const WaitOption& option) const EXCLUDES(mutex_);

  // BlockingQueue::WaitCommonLocked()
  //
  // Return `true` if the queue is non-empty. Otherwise, return `false` if queue is empty when
  // timeout has expired, or has been closed by a call to `Close()`.
  bool WaitCommonLocked(const WaitOption& option) const REQUIRES_SHARED(mutex_);

  const absl::Time kInfiniteFuture = absl::InfiniteFuture();
  const absl::Time kInfinitePast = absl::InfinitePast();

  const std::string name_;
  const int64_t max_pending_items_ = 0;

  std::atomic<int64_t> pending_items_{0};
  std::atomic<int64_t> dropped_items_{0};
  std::atomic<bool> closed_{false};

  mutable base::Mutex mutex_;

  std::deque<T> queue_ GUARDED_BY(mutex_);

  friend class BlockingQueueTest;

  DISALLOW_COPY_AND_ASSIGN(BlockingQueue);
};

using TaskBlockingQueue = base::BlockingQueue<std::function<void()>>;

template <typename T>
void BlockingQueue<T>::Close() {
  base::MutexLock scoped_lock(&mutex_);
  closed_ = true;
}

template <typename T>
bool BlockingQueue<T>::Push(T item, int64_t* dropped_items) {
  base::MutexLock scoped_lock(&mutex_);
  if (closed_) {
    return false;
  }
  int64_t num_pops = 0;
  if (max_pending_items_ != 0) {
    while (static_cast<int64_t>(queue_.size()) >= max_pending_items_) {
      queue_.pop_front();
      num_pops++;
    }
    dropped_items_ += num_pops;
  }
  if (dropped_items != nullptr) {
    *dropped_items = num_pops;
  }

  queue_.push_back(std::move(item));
  pending_items_ = queue_.size();

  LOG_IF(INFO, num_pops != 0 && IsLeadingInteger(dropped_items_))
      << strings::Format("[{}] blocking-queue overflow: dropped {} items.", name_, dropped_items_);
  return true;
}

template <typename T>
void BlockingQueue<T>::Clear() {
  base::MutexLock scoped_lock(&mutex_);
  queue_.clear();
  pending_items_ = queue_.size();
}

template <typename T>
bool BlockingQueue<T>::PopCommon(const WaitOption& option, T* popped_item) {
  CHECK(popped_item != nullptr);
  base::MutexLock scoped_lock(&mutex_);
  if (!WaitCommonLocked(option)) {
    return false;
  }
  CHECK(!queue_.empty());
  *popped_item = std::move(queue_.front());
  queue_.pop_front();
  pending_items_ = queue_.size();
  return true;
}

template <typename T>
template <typename ContainerOfT>
bool BlockingQueue<T>::PopNCommon(const WaitOption& option, ContainerOfT* popped_items) {
  CHECK(popped_items != nullptr);
  mutex_.Lock();
  if (!WaitCommonLocked(option)) {
    mutex_.Unlock();
    return false;
  }
  CHECK(!queue_.empty());
  if (option.max_items >= static_cast<int64_t>(queue_.size())) {
    std::deque<T> queue;
    queue.swap(queue_);
    pending_items_ = 0;
    mutex_.Unlock();
    while (!queue.empty()) {
      popped_items->push_back(std::move(queue.front()));
      queue.pop_front();
    }
  } else {
    for (int64_t i = 0; i < option.max_items; ++i) {
      popped_items->push_back(std::move(queue_.front()));
      queue_.pop_front();
    }
    pending_items_ = queue_.size();
    mutex_.Unlock();
  }
  return true;
}

template <typename T>
bool BlockingQueue<T>::WaitCommon(const WaitOption& option) const {
  base::MutexLock scoped_lock(&mutex_);
  return WaitCommonLocked(option);
}

template <typename T>
bool BlockingQueue<T>::WaitCommonLocked(const WaitOption& option) const {
  const auto is_notified = [this, &option]() REQUIRES_SHARED(mutex_) {
    return static_cast<int64_t>(queue_.size()) >= option.min_items || closed_;
  };
  if (option.deadline == kInfiniteFuture) {
    mutex_.Await(absl::Condition(&is_notified));
  } else if (option.deadline != kInfinitePast) {
    mutex_.AwaitWithDeadline(absl::Condition(&is_notified), option.deadline);
  }
  return !queue_.empty();
}

}  // namespace base
