// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <memory>
#include <string>
#include <unordered_map>
#include <vector>

#include "glog/logging.h"

#include "base/container/tensor_util.h"

namespace base {
namespace container {

class AxisBase {
 public:
  explicit AxisBase(const std::string& name) : name_(name) {}
  virtual ~AxisBase() {}

 protected:
  std::string name_;
};

template<typename IndexType>
class Axis : public AxisBase {
 public:
  explicit Axis(const Axis<IndexType>* other) : AxisBase(other->name_) {
    indices_ = other->indices_;
    forward_map_ = other->forward_map_;
    backward_map_ = other->backward_map_;
  }
  Axis(const std::string& name, const std::vector<IndexType>& indices) : AxisBase(name) {
    indices_ = indices;
    for (std::size_t i = 0; i < indices.size(); ++i) {
      const IndexType& index = indices[i];
      forward_map_[i] = index;
      backward_map_[index] = i;
    }
  }

  bool Contains(const IndexType& index) {
    return backward_map_.find(index) != backward_map_.end();
  }

  int GetIndexSequence(const IndexType& index) {
    CHECK(Contains(index)) << "Cannot find index " << strings::ToString(index)
        << " in axis " << name_;
    return backward_map_[index];
  }

  const IndexType& GetIndex(int index_sequence) {
    CHECK(std::size_t(index_sequence + 1) <= indices_.size());
    return indices_[index_sequence];
  }

  bool Intersect(Axis<IndexType>* other) {
    for (auto& p : backward_map_) {
      if (other->backward_map_.find(p.first) != other->backward_map_.end()) {
        return true;
      }
    }
    return false;
  }

  void Stack(Axis<IndexType>* other) {
    int start_sequence = indices_.size();
    for (const IndexType& index : other->indices_) {
      indices_.push_back(index);
      forward_map_[start_sequence] = index;
      backward_map_[index] = start_sequence;
      start_sequence++;
    }
  }

  const std::vector<IndexType>& indices() const { return indices_; }

  std::unique_ptr<AxisBase> Copy() const {
    return std::make_unique<Axis<IndexType>>(this);
  }

 private:
  std::vector<IndexType> indices_;
  std::unordered_map<int, IndexType> forward_map_;
  std::unordered_map<IndexType, int> backward_map_;
};

template<typename InIndexType>
struct AxisTypeSelector {
  typedef int AxisType;
  typedef int ParamType;
};
template<>
struct AxisTypeSelector<std::string> {
  typedef Axis<std::string> AxisType;
  typedef std::string ParamType;
};
template<>
struct AxisTypeSelector<int> {
  typedef Axis<int> AxisType;
  typedef int ParamType;
};
template<>
struct AxisTypeSelector<const char*> {
  typedef Axis<std::string> AxisType;
  typedef std::string ParamType;
};
template<>
struct AxisTypeSelector<float> {
  typedef Axis<float> AxisType;
  typedef float ParamType;
};
template<>
struct AxisTypeSelector<double> {
  typedef Axis<float> AxisType;
  typedef float ParamType;
};
inline std::unique_ptr<AxisBase> Copy(const std::unique_ptr<AxisBase>& in) {
  const AxisBase* base = in.get();
  const Axis<std::string>* axis_string = dynamic_cast<const Axis<std::string>*>(base);
  if (axis_string) {
    return axis_string->Copy();
  }
  const Axis<int>* axis_int = dynamic_cast<const Axis<int>*>(base);
  if (axis_int) {
    return axis_int->Copy();
  }
  const Axis<float>* axis_float = dynamic_cast<const Axis<float>*>(base);
  if (axis_float) {
    return axis_float->Copy();
  }
  const Axis<double>* axis_double = dynamic_cast<const Axis<double>*>(base);
  if (axis_double) {
    return axis_double->Copy();
  }
  CHECK(false) << "Copy failed, Please use supported axis type";
  return nullptr;
}

} // namespace container
} // namespace base
