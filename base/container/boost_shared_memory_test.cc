// Copyright @2024 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/container/boost_shared_memory.h"

#include <string>

#include "gtest/gtest.h"

namespace {

// Shared memory mapping address, big enough to ensure not be used by anyone else.
constexpr uintptr_t kSharedMemoryAddress = 0x7f7f11cf0000;
constexpr char kSharedMemoryName[] = "TmpSharedMemory";
constexpr int64_t kSharedMemorySize = 65536;  // 64KB
struct SharedMemoryRemover {
  SharedMemoryRemover() { base::shared_memory_object::remove(kSharedMemoryName); }
  ~SharedMemoryRemover() { base::shared_memory_object::remove(kSharedMemoryName); }
};

}  // namespace

namespace base {

TEST(BoostShmTest, PointerTest) {
  SharedMemoryRemover remover;
  boost_managed_memory segment =
      boost_managed_memory(boost::interprocess::open_or_create,
                           kSharedMemoryName,
                           kSharedMemorySize,
                           reinterpret_cast<void*>(kSharedMemoryAddress));
  BoostUniquePtr<int> uniqueptr1 =
      make_managed_unique_ptr<int>(segment.construct<int>("uniqueptr1")(10), segment);
  EXPECT_NE(uniqueptr1.get(), nullptr);
  uniqueptr1.reset();
  EXPECT_EQ(uniqueptr1.get(), nullptr);

  BoostSharedPtr<int> sharedptr1 =
      make_managed_shared_ptr<int>(segment.construct<int>("sharedptr1")(20), segment);
  EXPECT_EQ(sharedptr1.use_count(), 1);
  using ResultT = std::pair<int*, boost_managed_memory::size_type>;
  ResultT res = segment.find<int>("sharedptr1");
  ASSERT_TRUE(res.second);
  int* uniqueptr2 = res.first;
  EXPECT_EQ(*uniqueptr2, 20);
}

TEST(BoostShmTest, ContainerTest) {
  using MapType = std::pair<std::string, int>;
  SharedMemoryRemover remover;
  {
    boost_managed_memory segment =
        boost_managed_memory(boost::interprocess::open_or_create,
                             kSharedMemoryName,
                             kSharedMemorySize,
                             reinterpret_cast<void*>(kSharedMemoryAddress));
    BoostUnorderedMap<std::string, int>* unordered_map1 =
        segment.construct<BoostUnorderedMap<std::string, int>>("map1")(
            3u,
            boost::hash<std::string>(),
            std::equal_to<std::string>(),
            segment.get_allocator<int>());
    for (int i = 0; i < 10; ++i) {
      std::string key{"key" + std::to_string(i)};
      unordered_map1->insert(MapType(key, i));
    }

    BoostVector<int>* vector1 =
        segment.construct<BoostVector<int>>("vector1")(segment.get_segment_manager());
    vector1->push_back(100);
    vector1->push_back(200);

    BoostUnorderedSet<std::string>* unordered_set1 =
        segment.construct<BoostUnorderedSet<std::string>>("set1")(
            3u,
            boost::hash<std::string>(),
            std::equal_to<std::string>(),
            segment.get_allocator<std::string>());
    unordered_set1->insert("set_element1");
    unordered_set1->insert("set_element2");
  }
  {
    boost_managed_memory segment =
        boost_managed_memory(boost::interprocess::open_or_create,
                             kSharedMemoryName,
                             kSharedMemorySize,
                             reinterpret_cast<void*>(kSharedMemoryAddress));
    {
      using ResultT =
          std::pair<BoostUnorderedMap<std::string, int>*, boost_managed_memory::size_type>;
      ResultT res = segment.find<BoostUnorderedMap<std::string, int>>("map1");
      ASSERT_TRUE(res.second);
      BoostUnorderedMap<std::string, int>* unordered_map2 = res.first;
      EXPECT_EQ(unordered_map2->size(), 10);
      EXPECT_EQ(unordered_map2->at("key1"), 1);
    }
    {
      using ResultT = std::pair<BoostVector<int>*, boost_managed_memory::size_type>;
      ResultT res = segment.find<BoostVector<int>>("vector1");
      ASSERT_TRUE(res.second);
      BoostVector<int>* vector2 = res.first;
      EXPECT_EQ(vector2->size(), 2);
      EXPECT_EQ(vector2->at(0), 100);
      EXPECT_EQ(vector2->at(1), 200);
    }
    {
      using ResultT = std::pair<BoostUnorderedSet<std::string>*, boost_managed_memory::size_type>;
      ResultT res = segment.find<BoostUnorderedSet<std::string>>("set1");
      ASSERT_TRUE(res.second);
      BoostUnorderedSet<std::string>* unordered_set2 = res.first;
      EXPECT_EQ(unordered_set2->size(), 2);
      EXPECT_EQ(unordered_set2->count("set_element1"), 1);
      EXPECT_TRUE(unordered_set2->find(std::string{"set_element2"}) != unordered_set2->end());
    }
  }
}

}  // namespace base
