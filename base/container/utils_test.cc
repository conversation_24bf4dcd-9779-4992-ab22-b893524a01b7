// Copyright @2022 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include <deque>
#include <map>
#include <memory>
#include <set>
#include <string>
#include <unordered_map>
#include <unordered_set>
#include <vector>

#include "glog/logging.h"
#include "gtest/gtest.h"

#include "base/container/utils.h"
#include "modules/planning/common/vector_shell.h"

namespace base {

//
// Origin functions
//

TEST(UtilsTest, IsKeySame) {
  {
    std::map<std::string, int> map_1;
    std::map<std::string, int> map_2{{"test_key_1", 100}};
    std::map<std::string, int> map_3{{"test_key_2", 100}};
    std::map<std::string, int> map_4{{"test_key_1", 100}};
    EXPECT_FALSE(IsKeySame(&map_2, &map_1));
    EXPECT_FALSE(IsKeySame(&map_2, &map_3));
    EXPECT_TRUE(IsKeySame(&map_2, &map_4));
  }
  {
    std::unordered_map<std::string, int> map_1;
    std::unordered_map<std::string, int> map_2{{"test_key_1", 100}};
    std::unordered_map<std::string, int> map_3{{"test_key_2", 100}};
    std::unordered_map<std::string, int> map_4{{"test_key_1", 100}};
    EXPECT_FALSE(IsKeySame(&map_2, &map_1));
    EXPECT_FALSE(IsKeySame(&map_2, &map_3));
    EXPECT_TRUE(IsKeySame(&map_2, &map_4));
  }
}

//
// Find*()
//

TEST(UtilsTest, DeathFindOrDie) {
  {
    std::map<std::string, int> test_map{{"test_key", 100}};
    const int res = FindOrDie(test_map, "test_key");
    EXPECT_EQ(res, 100);
    ASSERT_DEATH(FindOrDie(test_map, "not_exist_key"), "Map key not found: not_exist_key");
  }
  {
    std::unordered_map<std::string, int> test_map{{"test_key", 100}};
    const int res = FindOrDie(test_map, "test_key");
    EXPECT_EQ(res, 100);
    ASSERT_DEATH(FindOrDie(test_map, "not_exist_key"), "Map key not found: not_exist_key");
  }
  {
    std::map<std::string, int> test_map{{"test_key", 100}};
    EXPECT_EQ(*CHECK_NOTNULL(FindOrDie(&test_map, "test_key")), 100);
    ASSERT_DEATH(FindOrDie(&test_map, "not_exist_key"), "Map key not found: not_exist_key");
  }
  {
    std::unordered_map<std::string, int> test_map{{"test_key", 100}};
    EXPECT_EQ(*CHECK_NOTNULL(FindOrDie(&test_map, "test_key")), 100);
    ASSERT_DEATH(FindOrDie(&test_map, "not_exist_key"), "Map key not found: not_exist_key");
  }
}

TEST(UtilsTest, DeathFindOrDieNoPrint) {
  {
    std::map<std::string, int> test_map{{"test_key", 100}};
    const auto& res = FindOrDieNoPrint(test_map, "test_key");
    EXPECT_EQ(res, 100);
    ASSERT_DEATH(FindOrDieNoPrint(test_map, "not_exist_key"), "Map key not found");
  }
  {
    std::unordered_map<std::string, int> test_map{{"test_key", 100}};
    const auto& res = FindOrDieNoPrint(test_map, "test_key");
    EXPECT_EQ(res, 100);
    ASSERT_DEATH(FindOrDieNoPrint(test_map, "not_exist_key"), "Map key not found");
  }
  {
    std::map<std::string, int> test_map{{"test_key", 100}};
    auto& res = FindOrDieNoPrint(&test_map, "test_key");
    EXPECT_EQ(res, 100);
    ASSERT_DEATH(FindOrDieNoPrint(&test_map, "not_exist_key"), "Map key not found");
  }
  {
    std::unordered_map<std::string, int> test_map{{"test_key", 100}};
    auto& res = FindOrDieNoPrint(&test_map, "test_key");
    EXPECT_EQ(res, 100);
    ASSERT_DEATH(FindOrDieNoPrint(&test_map, "not_exist_key"), "Map key not found");
  }
}

TEST(UtilsTest, FindOrNull) {
  {
    std::map<std::string, int> test_map{{"test_key", 100}};
    const int* res = FindOrNull(test_map, "test_key");
    EXPECT_EQ(*res, 100);
    const int* res_null = FindOrNull(test_map, "not_exist_key");
    EXPECT_EQ(res_null, nullptr);
  }
  {
    std::unordered_map<std::string, int> test_map{{"test_key", 100}};
    const int* res = FindOrNull(test_map, "test_key");
    EXPECT_EQ(*res, 100);
    const int* res_null = FindOrNull(test_map, "not_exist_key");
    EXPECT_EQ(res_null, nullptr);
  }
  {
    std::map<std::string, int> test_map{{"test_key", 100}};
    int* res = FindOrNull(&test_map, "test_key");
    EXPECT_EQ(*res, 100);
    int* res_null = FindOrNull(&test_map, "not_exist_key");
    EXPECT_EQ(res_null, nullptr);
  }
  {
    std::unordered_map<std::string, int> test_map{{"test_key", 100}};
    int* res = FindOrNull(&test_map, "test_key");
    EXPECT_EQ(*res, 100);
    int* res_null = FindOrNull(&test_map, "not_exist_key");
    EXPECT_EQ(res_null, nullptr);
  }
}

TEST(UtilsTest, FindWithDefault) {
  {
    std::map<std::string, int> test_map{{"test_key", 100}};
    EXPECT_EQ(FindWithDefault(test_map, "test_key", 111), 100);
    EXPECT_EQ(FindWithDefault(test_map, "not_exist_key", 111), 111);
  }
  {
    std::unordered_map<std::string, int> test_map{{"test_key", 100}};
    EXPECT_EQ(FindWithDefault(test_map, "test_key", 111), 100);
    EXPECT_EQ(FindWithDefault(test_map, "not_exist_key", 111), 111);
  }
  {
    std::map<std::string, int> test_map{{"test_key", 100}};
    EXPECT_EQ(FindWithDefault(&test_map, "test_key", 111), 100);
    EXPECT_EQ(FindWithDefault(&test_map, "not_exist_key", 111), 111);
  }
  {
    std::unordered_map<std::string, int> test_map{{"test_key", 100}};
    EXPECT_EQ(FindWithDefault(&test_map, "test_key", 111), 100);
    EXPECT_EQ(FindWithDefault(&test_map, "not_exist_key", 111), 111);
  }
}

TEST(UtilsTest, FindPtrOrNull) {
  {
    std::map<std::string, int> test_map{{"test_key", 100}};
    std::map<std::string, const int*> test_ptr_map;
    for (const auto& item : test_map) {
      test_ptr_map.emplace(item.first, &item.second);
    }
    test_ptr_map.emplace("key_to_null", nullptr);
    EXPECT_EQ(FindPtrOrNull(test_ptr_map, "test_key"), &test_map.at("test_key"));
    EXPECT_EQ(FindPtrOrNull(test_ptr_map, "key_to_null"), nullptr);
    EXPECT_EQ(FindPtrOrNull(test_ptr_map, "not_exist_key"), nullptr);
    EXPECT_EQ(FindPtrOrNull(&test_ptr_map, "test_key"), &test_map.at("test_key"));
    EXPECT_EQ(FindPtrOrNull(&test_ptr_map, "key_to_null"), nullptr);
    EXPECT_EQ(FindPtrOrNull(&test_ptr_map, "not_exist_key"), nullptr);
  }
  {
    std::unordered_map<std::string, int> test_map{{"test_key", 100}};
    std::unordered_map<std::string, const int*> test_ptr_map;
    for (const auto& item : test_map) {
      test_ptr_map.emplace(item.first, &item.second);
    }
    test_ptr_map.emplace("key_to_null", nullptr);
    EXPECT_EQ(FindPtrOrNull(test_ptr_map, "test_key"), &test_map.at("test_key"));
    EXPECT_EQ(FindPtrOrNull(test_ptr_map, "key_to_null"), nullptr);
    EXPECT_EQ(FindPtrOrNull(test_ptr_map, "not_exist_key"), nullptr);
    EXPECT_EQ(FindPtrOrNull(&test_ptr_map, "test_key"), &test_map.at("test_key"));
    EXPECT_EQ(FindPtrOrNull(&test_ptr_map, "key_to_null"), nullptr);
    EXPECT_EQ(FindPtrOrNull(&test_ptr_map, "not_exist_key"), nullptr);
  }
}

TEST(UtilsTest, FindLinkedPtrOrNull) {
  {
    std::map<std::string, std::unique_ptr<int>> test_map;
    test_map.emplace("test_key", std::make_unique<int>(100));
    EXPECT_EQ(*FindLinkedPtrOrNull(test_map, "test_key"), 100);
    EXPECT_EQ(FindLinkedPtrOrNull(test_map, "test_key"), test_map.at("test_key").get());
    EXPECT_EQ(FindLinkedPtrOrNull(test_map, "not_exist_key"), nullptr);
  }
  {
    std::unordered_map<std::string, std::unique_ptr<int>> test_map;
    test_map.emplace("test_key", std::make_unique<int>(100));
    EXPECT_EQ(*FindLinkedPtrOrNull(test_map, "test_key"), 100);
    EXPECT_EQ(FindLinkedPtrOrNull(test_map, "test_key"), test_map.at("test_key").get());
    EXPECT_EQ(FindLinkedPtrOrNull(test_map, "not_exist_key"), nullptr);
  }
}

TEST(UtilsTest, DeathFindLinkedPtrOrDie) {
  {
    std::map<std::string, std::unique_ptr<int>> test_map;
    test_map.emplace("test_key", std::make_unique<int>(100));
    EXPECT_EQ(FindLinkedPtrOrDie(test_map, "test_key"), 100);
    ASSERT_DEATH(FindLinkedPtrOrDie(test_map, "not_exist_key"), "Map key not found: not_exist_key");
  }
  {
    std::unordered_map<std::string, std::unique_ptr<int>> test_map;
    test_map.emplace("test_key", std::make_unique<int>(100));
    EXPECT_EQ(FindLinkedPtrOrDie(test_map, "test_key"), 100);
    ASSERT_DEATH(FindLinkedPtrOrDie(test_map, "not_exist_key"), "Map key not found: not_exist_key");
  }
}

TEST(UtilsTest, FindCopy) {
  {
    std::map<std::string, int> test_map{{"test_key", 100}};
    {
      int result = -1;
      int* result_ptr = &result;
      EXPECT_TRUE(FindCopy(test_map, "test_key", result_ptr));
      EXPECT_EQ(result, 100);
      EXPECT_EQ(result_ptr, &result);
      EXPECT_NE(result_ptr, &test_map.at("test_key"));
    }
    {
      int result = -1;
      int* result_ptr = &result;
      EXPECT_FALSE(FindCopy(test_map, "not_exist_key", result_ptr));
      EXPECT_EQ(result, -1);
      EXPECT_EQ(result_ptr, &result);
    }
  }
  {
    std::unordered_map<std::string, int> test_map{{"test_key", 100}};
    {
      int result = -1;
      int* result_ptr = &result;
      EXPECT_TRUE(FindCopy(test_map, "test_key", result_ptr));
      EXPECT_EQ(result, 100);
      EXPECT_EQ(result_ptr, &result);
      EXPECT_NE(result_ptr, &test_map.at("test_key"));
    }
    {
      int result = -1;
      int* result_ptr = &result;
      EXPECT_FALSE(FindCopy(test_map, "not_exist_key", result_ptr));
      EXPECT_EQ(result, -1);
      EXPECT_EQ(result_ptr, &result);
    }
  }
}

//
// Contains*()
//

TEST(UtilsTest, ContainsKey) {
  {
    std::map<std::string, int> test_map{
        {"test_key_1", 100},
        {"test_key_2", 101},
    };
    ASSERT_EQ(test_map.size(), 2);
    EXPECT_TRUE(ContainsKey(test_map, "test_key_1"));
    EXPECT_TRUE(ContainsKey(test_map, "test_key_2"));
    EXPECT_FALSE(ContainsKey(test_map, "test_key_3"));
  }
  {
    std::unordered_map<std::string, int> test_map{
        {"test_key_1", 100},
        {"test_key_2", 101},
    };
    ASSERT_EQ(test_map.size(), 2);
    EXPECT_TRUE(ContainsKey(test_map, "test_key_1"));
    EXPECT_TRUE(ContainsKey(test_map, "test_key_2"));
    EXPECT_FALSE(ContainsKey(test_map, "test_key_3"));
  }
}

TEST(UtilsTest, ContainsKeyValuePair) {
  {
    std::map<std::string, int> test_map{
        {"test_key_1", 100},
        {"test_key_2", 101},
    };
    ASSERT_EQ(test_map.size(), 2);
    EXPECT_TRUE(ContainsKeyValuePair(test_map, "test_key_1", 100));
    EXPECT_FALSE(ContainsKeyValuePair(test_map, "test_key_2", 102));
    EXPECT_FALSE(ContainsKeyValuePair(test_map, "test_key_3", 100));
    EXPECT_FALSE(ContainsKeyValuePair(test_map, "test_key_4", 102));
  }
  {
    std::unordered_map<std::string, int> test_map{
        {"test_key_1", 100},
        {"test_key_2", 101},
    };
    ASSERT_EQ(test_map.size(), 2);
    EXPECT_TRUE(ContainsKeyValuePair(test_map, "test_key_1", 100));
    EXPECT_FALSE(ContainsKeyValuePair(test_map, "test_key_2", 102));
    EXPECT_FALSE(ContainsKeyValuePair(test_map, "test_key_3", 100));
    EXPECT_FALSE(ContainsKeyValuePair(test_map, "test_key_4", 102));
  }
}

TEST(UtilsTest, Contains) {
  {
    const std::vector<int> test_vector = {1, 2, 3};
    EXPECT_TRUE(Contains(test_vector, 1));
    EXPECT_TRUE(Contains(test_vector, 2));
    EXPECT_TRUE(Contains(test_vector, 3));
    EXPECT_FALSE(Contains(test_vector, 4));
  }
  {
    const std::array<int, 3> test_array = {1, 2, 3};
    EXPECT_TRUE(Contains(test_array, 1));
    EXPECT_TRUE(Contains(test_array, 2));
    EXPECT_TRUE(Contains(test_array, 3));
    EXPECT_FALSE(Contains(test_array, 4));
  }
  {
    struct A {
      int id;
      explicit A(int id) : id(id) {}
    };
    struct B {
      int id;
      explicit B(int id) : id(id) {}
    };

    using APtr = std::shared_ptr<A>;
    using BPtr = std::shared_ptr<B>;
    struct PtrEqual {
      bool operator()(const APtr& key1, const APtr& key2) const {
        if (key1 == nullptr || key2 == nullptr) {
          return false;
        }
        return key1->id == key2->id;
      }
    };
    walle::planning::VectorShell<APtr, PtrEqual> a_vec;
    walle::planning::VectorShell<BPtr> b_vec;
    APtr a1 = std::make_shared<A>(1);
    APtr a2 = std::make_shared<A>(2);
    APtr a_1 = std::make_shared<A>(1);
    BPtr b1 = std::make_shared<B>(1);
    BPtr b2 = std::make_shared<B>(2);
    BPtr b_1 = std::make_shared<B>(1);
    a_vec.push_back(a1);
    a_vec.push_back(a2);
    b_vec.push_back(b1);
    b_vec.push_back(b2);
    EXPECT_TRUE(Contains(a_vec, a_1));
    EXPECT_FALSE(Contains(b_vec, b_1));
  }
}

//
// Insert*()
//

TEST(UtilsTest, InsertOrUpdate) {
  {
    std::map<std::string, int> test_map;
    EXPECT_EQ(test_map.size(), 0);
    EXPECT_TRUE(InsertOrUpdate(&test_map, "test_key", 100));
    EXPECT_EQ(test_map.size(), 1);
    ASSERT_EQ(test_map.count("test_key"), 1);
    EXPECT_EQ(test_map.at("test_key"), 100);

    EXPECT_FALSE(InsertOrUpdate(&test_map, "test_key", 200));
    EXPECT_EQ(test_map.size(), 1);
    ASSERT_EQ(test_map.count("test_key"), 1);
    EXPECT_EQ(test_map.at("test_key"), 200);
  }
  {
    std::unordered_map<std::string, int> test_map;
    EXPECT_EQ(test_map.size(), 0);
    EXPECT_TRUE(InsertOrUpdate(&test_map, "test_key", 100));
    EXPECT_EQ(test_map.size(), 1);
    ASSERT_EQ(test_map.count("test_key"), 1);
    EXPECT_EQ(test_map.at("test_key"), 100);

    EXPECT_FALSE(InsertOrUpdate(&test_map, "test_key", 200));
    EXPECT_EQ(test_map.size(), 1);
    ASSERT_EQ(test_map.count("test_key"), 1);
    EXPECT_EQ(test_map.at("test_key"), 200);
  }
}

TEST(UtilsTest, InsertOrUpdateMany) {
  std::vector<std::pair<std::string, int>> input_1{
      {"test_key_1", 100},
      {"test_key_2", 101},
      {"test_key_7", 107},
  };
  std::map<std::string, int> input_2{
      {"test_key_1", 200},
      {"test_key_2", 201},
      {"test_key_8", 208},
  };
  std::unordered_map<std::string, int> input_3{
      {"test_key_1", 300},
      {"test_key_2", 301},
      {"test_key_9", 309},
  };
  std::vector<std::pair<std::string, int>> input_4{
      {"test_key_1", 400},
      {"test_key_2", 401},
      {"test_key_0", 410},
  };
  {
    std::map<std::string, int> test_map;
    EXPECT_EQ(test_map.size(), 0);
    InsertOrUpdateMany(&test_map, input_1.begin(), input_1.end());
    EXPECT_EQ(test_map.size(), 3);
    EXPECT_EQ(test_map.at("test_key_1"), 100);
    EXPECT_EQ(test_map.at("test_key_2"), 101);
    EXPECT_EQ(test_map.at("test_key_7"), 107);
    InsertOrUpdateMany(&test_map, input_2.begin(), input_2.end());
    EXPECT_EQ(test_map.size(), 4);
    EXPECT_EQ(test_map.at("test_key_1"), 200);
    EXPECT_EQ(test_map.at("test_key_2"), 201);
    EXPECT_EQ(test_map.at("test_key_7"), 107);
    EXPECT_EQ(test_map.at("test_key_8"), 208);
    InsertOrUpdateMany(&test_map, input_3.begin(), input_3.end());
    EXPECT_EQ(test_map.size(), 5);
    EXPECT_EQ(test_map.at("test_key_1"), 300);
    EXPECT_EQ(test_map.at("test_key_2"), 301);
    EXPECT_EQ(test_map.at("test_key_7"), 107);
    EXPECT_EQ(test_map.at("test_key_8"), 208);
    EXPECT_EQ(test_map.at("test_key_9"), 309);
  }
  {
    std::unordered_map<std::string, int> test_map;
    EXPECT_EQ(test_map.size(), 0);
    InsertOrUpdateMany(&test_map, input_1.begin(), input_1.end());
    EXPECT_EQ(test_map.size(), 3);
    EXPECT_EQ(test_map.at("test_key_1"), 100);
    EXPECT_EQ(test_map.at("test_key_2"), 101);
    EXPECT_EQ(test_map.at("test_key_7"), 107);
    InsertOrUpdateMany(&test_map, input_2.begin(), input_2.end());
    EXPECT_EQ(test_map.size(), 4);
    EXPECT_EQ(test_map.at("test_key_1"), 200);
    EXPECT_EQ(test_map.at("test_key_2"), 201);
    EXPECT_EQ(test_map.at("test_key_7"), 107);
    EXPECT_EQ(test_map.at("test_key_8"), 208);
    InsertOrUpdateMany(&test_map, input_3.begin(), input_3.end());
    EXPECT_EQ(test_map.size(), 5);
    EXPECT_EQ(test_map.at("test_key_1"), 300);
    EXPECT_EQ(test_map.at("test_key_2"), 301);
    EXPECT_EQ(test_map.at("test_key_7"), 107);
    EXPECT_EQ(test_map.at("test_key_8"), 208);
    EXPECT_EQ(test_map.at("test_key_9"), 309);
  }
}

TEST(UtilsTest, InsertIfNotPresent) {
  {
    std::map<std::string, int> test_map;
    EXPECT_EQ(test_map.size(), 0);
    EXPECT_TRUE(InsertIfNotPresent(&test_map, "test_key", 100));
    EXPECT_EQ(test_map.size(), 1);
    ASSERT_EQ(test_map.count("test_key"), 1);
    EXPECT_EQ(test_map.at("test_key"), 100);

    EXPECT_FALSE(InsertIfNotPresent(&test_map, "test_key", 200));
    EXPECT_EQ(test_map.size(), 1);
    ASSERT_EQ(test_map.count("test_key"), 1);
    EXPECT_EQ(test_map.at("test_key"), 100);
  }
  {
    std::map<std::string, int> test_map;
    EXPECT_EQ(test_map.size(), 0);
    EXPECT_TRUE(InsertIfNotPresent(&test_map, {"test_key", 100}));
    EXPECT_EQ(test_map.size(), 1);
    ASSERT_EQ(test_map.count("test_key"), 1);
    EXPECT_EQ(test_map.at("test_key"), 100);

    EXPECT_FALSE(InsertIfNotPresent(&test_map, {"test_key", 200}));
    EXPECT_EQ(test_map.size(), 1);
    ASSERT_EQ(test_map.count("test_key"), 1);
    EXPECT_EQ(test_map.at("test_key"), 100);
  }
  {
    std::unordered_map<std::string, int> test_map;
    EXPECT_EQ(test_map.size(), 0);
    EXPECT_TRUE(InsertIfNotPresent(&test_map, "test_key", 100));
    EXPECT_EQ(test_map.size(), 1);
    ASSERT_EQ(test_map.count("test_key"), 1);
    EXPECT_EQ(test_map.at("test_key"), 100);

    EXPECT_FALSE(InsertIfNotPresent(&test_map, "test_key", 200));
    EXPECT_EQ(test_map.size(), 1);
    ASSERT_EQ(test_map.count("test_key"), 1);
    EXPECT_EQ(test_map.at("test_key"), 100);
  }
  {
    std::unordered_map<std::string, int> test_map;
    EXPECT_EQ(test_map.size(), 0);
    EXPECT_TRUE(InsertIfNotPresent(&test_map, {"test_key", 100}));
    EXPECT_EQ(test_map.size(), 1);
    ASSERT_EQ(test_map.count("test_key"), 1);
    EXPECT_EQ(test_map.at("test_key"), 100);

    EXPECT_FALSE(InsertIfNotPresent(&test_map, {"test_key", 200}));
    EXPECT_EQ(test_map.size(), 1);
    ASSERT_EQ(test_map.count("test_key"), 1);
    EXPECT_EQ(test_map.at("test_key"), 100);
  }
}

TEST(UtilsTest, DeathInsertOrDie) {
  {
    std::map<std::string, int> test_map;
    EXPECT_EQ(test_map.size(), 0);
    InsertOrDie(&test_map, "test_key", 100);
    EXPECT_EQ(test_map.size(), 1);
    ASSERT_EQ(test_map.count("test_key"), 1);
    EXPECT_EQ(test_map.at("test_key"), 100);

    ASSERT_DEATH(InsertOrDie(&test_map, "test_key", 200), "duplicate key: test_key");
  }
  {
    std::unordered_map<std::string, int> test_map;
    EXPECT_EQ(test_map.size(), 0);
    InsertOrDie(&test_map, "test_key", 100);
    EXPECT_EQ(test_map.size(), 1);
    ASSERT_EQ(test_map.count("test_key"), 1);
    EXPECT_EQ(test_map.at("test_key"), 100);

    ASSERT_DEATH(InsertOrDie(&test_map, "test_key", 200), "duplicate key: test_key");
  }
  {
    std::map<std::string, int> test_map;
    EXPECT_EQ(test_map.size(), 0);
    InsertOrDie(&test_map, {"test_key", 100});
    EXPECT_EQ(test_map.size(), 1);
    ASSERT_EQ(test_map.count("test_key"), 1);
    EXPECT_EQ(test_map.at("test_key"), 100);

    ASSERT_DEATH(InsertOrDie(&test_map, {"test_key", 200}), "duplicate key: test_key");
  }
  {
    std::unordered_map<std::string, int> test_map;
    EXPECT_EQ(test_map.size(), 0);
    InsertOrDie(&test_map, {"test_key", 100});
    EXPECT_EQ(test_map.size(), 1);
    ASSERT_EQ(test_map.count("test_key"), 1);
    EXPECT_EQ(test_map.at("test_key"), 100);

    ASSERT_DEATH(InsertOrDie(&test_map, {"test_key", 200}), "duplicate key: test_key");
  }
}

TEST(UtilsTest, DeathInsertOrDieNoPrint) {
  {
    std::map<std::string, int> test_map;
    EXPECT_EQ(test_map.size(), 0);
    InsertOrDieNoPrint(&test_map, "test_key", 100);
    EXPECT_EQ(test_map.size(), 1);
    ASSERT_EQ(test_map.count("test_key"), 1);
    EXPECT_EQ(test_map.at("test_key"), 100);

    ASSERT_DEATH(InsertOrDieNoPrint(&test_map, "test_key", 200), "duplicate key.");
  }
  {
    std::unordered_map<std::string, int> test_map;
    EXPECT_EQ(test_map.size(), 0);
    InsertOrDieNoPrint(&test_map, "test_key", 100);
    EXPECT_EQ(test_map.size(), 1);
    ASSERT_EQ(test_map.count("test_key"), 1);
    EXPECT_EQ(test_map.at("test_key"), 100);

    ASSERT_DEATH(InsertOrDieNoPrint(&test_map, "test_key", 200), "duplicate key.");
  }
  {
    std::map<std::string, int> test_map;
    EXPECT_EQ(test_map.size(), 0);
    InsertOrDieNoPrint(&test_map, "test_key", 100);
    EXPECT_EQ(test_map.size(), 1);
    ASSERT_EQ(test_map.count("test_key"), 1);
    EXPECT_EQ(test_map.at("test_key"), 100);

    ASSERT_DEATH(InsertOrDieNoPrint(&test_map, {"test_key", 200}), "duplicate key.");
  }
  {
    std::unordered_map<std::string, int> test_map;
    EXPECT_EQ(test_map.size(), 0);
    InsertOrDieNoPrint(&test_map, "test_key", 100);
    EXPECT_EQ(test_map.size(), 1);
    ASSERT_EQ(test_map.count("test_key"), 1);
    EXPECT_EQ(test_map.at("test_key"), 100);

    ASSERT_DEATH(InsertOrDieNoPrint(&test_map, {"test_key", 200}), "duplicate key.");
  }
}

TEST(UtilsTest, DeathInsertKeyOrDie) {
  {
    std::map<std::string, int> test_map;
    EXPECT_EQ(test_map.size(), 0);
    InsertKeyOrDie(&test_map, "test_key");
    EXPECT_EQ(test_map.size(), 1);
    ASSERT_EQ(test_map.count("test_key"), 1);
    EXPECT_EQ(test_map.at("test_key"), 0);

    ASSERT_DEATH(InsertKeyOrDie(&test_map, "test_key"), "duplicate key: test_key");
  }
  {
    std::unordered_map<std::string, int> test_map;
    EXPECT_EQ(test_map.size(), 0);
    InsertKeyOrDie(&test_map, "test_key");
    EXPECT_EQ(test_map.size(), 1);
    ASSERT_EQ(test_map.count("test_key"), 1);
    EXPECT_EQ(test_map.at("test_key"), 0);

    ASSERT_DEATH(InsertKeyOrDie(&test_map, "test_key"), "duplicate key: test_key");
  }
}

//
// Lookup*()
//

TEST(UtilsTest, LookupOrInsert) {
  {
    std::map<std::string, int> test_map{{"test_key", 1}};

    EXPECT_EQ(test_map.size(), 1);
    EXPECT_EQ(LookupOrInsert(&test_map, "test_key", 2), 1);
    EXPECT_EQ(test_map.size(), 1);
    EXPECT_EQ(test_map.count("test_key"), 1);
    EXPECT_EQ(test_map.at("test_key"), 1);
    EXPECT_EQ(LookupOrInsert(&test_map, "not_exist_key", 3), 3);
    EXPECT_EQ(test_map.size(), 2);
    EXPECT_EQ(test_map.count("not_exist_key"), 1);
    EXPECT_EQ(test_map.at("not_exist_key"), 3);
  }
  {
    std::map<std::string, int> test_map{{"test_key", 1}};

    EXPECT_EQ(test_map.size(), 1);
    EXPECT_EQ(LookupOrInsert(&test_map, {"test_key", 2}), 1);
    EXPECT_EQ(test_map.size(), 1);
    EXPECT_EQ(test_map.count("test_key"), 1);
    EXPECT_EQ(test_map.at("test_key"), 1);
    EXPECT_EQ(LookupOrInsert(&test_map, {"not_exist_key", 3}), 3);
    EXPECT_EQ(test_map.size(), 2);
    EXPECT_EQ(test_map.count("not_exist_key"), 1);
    EXPECT_EQ(test_map.at("not_exist_key"), 3);
  }
  {
    std::unordered_map<std::string, int> test_map{{"test_key", 1}};

    EXPECT_EQ(test_map.size(), 1);
    EXPECT_EQ(LookupOrInsert(&test_map, "test_key", 2), 1);
    EXPECT_EQ(test_map.size(), 1);
    EXPECT_EQ(test_map.count("test_key"), 1);
    EXPECT_EQ(test_map.at("test_key"), 1);
    EXPECT_EQ(LookupOrInsert(&test_map, "not_exist_key", 3), 3);
    EXPECT_EQ(test_map.size(), 2);
    EXPECT_EQ(test_map.count("not_exist_key"), 1);
    EXPECT_EQ(test_map.at("not_exist_key"), 3);
  }
  {
    std::unordered_map<std::string, int> test_map{{"test_key", 1}};

    EXPECT_EQ(test_map.size(), 1);
    EXPECT_EQ(LookupOrInsert(&test_map, {"test_key", 2}), 1);
    EXPECT_EQ(test_map.size(), 1);
    EXPECT_EQ(test_map.count("test_key"), 1);
    EXPECT_EQ(test_map.at("test_key"), 1);
    EXPECT_EQ(LookupOrInsert(&test_map, {"not_exist_key", 3}), 3);
    EXPECT_EQ(test_map.size(), 2);
    EXPECT_EQ(test_map.count("not_exist_key"), 1);
    EXPECT_EQ(test_map.at("not_exist_key"), 3);
  }
}

TEST(UtilsTest, AddTokenCounts) {
  const std::vector<std::string> v = {"a", "b", "c", "a", "b"};
  {
    std::map<std::string, int> test_map_1;
    std::unordered_map<std::string, int> test_map_2;
    AddTokenCounts(v, 1, &test_map_1);
    EXPECT_EQ(test_map_1.size(), 3);
    EXPECT_EQ(test_map_1.count("a"), 1);
    EXPECT_EQ(test_map_1["a"], 2);
    EXPECT_EQ(test_map_1.count("b"), 1);
    EXPECT_EQ(test_map_1["b"], 2);
    EXPECT_EQ(test_map_1.count("c"), 1);
    EXPECT_EQ(test_map_1["c"], 1);
    AddTokenCounts(v, 1, &test_map_2);
    EXPECT_EQ(test_map_2.size(), 3);
    EXPECT_EQ(test_map_2.count("a"), 1);
    EXPECT_EQ(test_map_2["a"], 2);
    EXPECT_EQ(test_map_2.count("b"), 1);
    EXPECT_EQ(test_map_2["b"], 2);
    EXPECT_EQ(test_map_2.count("c"), 1);
    EXPECT_EQ(test_map_2["c"], 1);
  }
  {
    std::map<std::string, int> test_map_1;
    std::unordered_map<std::string, int> test_map_2;
    AddTokenCounts(v, 2, &test_map_1);
    EXPECT_EQ(test_map_1.size(), 3);
    EXPECT_EQ(test_map_1.count("a"), 1);
    EXPECT_EQ(test_map_1["a"], 4);
    EXPECT_EQ(test_map_1.count("b"), 1);
    EXPECT_EQ(test_map_1["b"], 4);
    EXPECT_EQ(test_map_1.count("c"), 1);
    EXPECT_EQ(test_map_1["c"], 2);
    AddTokenCounts(v, 2, &test_map_2);
    EXPECT_EQ(test_map_2.size(), 3);
    EXPECT_EQ(test_map_2.count("a"), 1);
    EXPECT_EQ(test_map_2["a"], 4);
    EXPECT_EQ(test_map_2.count("b"), 1);
    EXPECT_EQ(test_map_2["b"], 4);
    EXPECT_EQ(test_map_2.count("c"), 1);
    EXPECT_EQ(test_map_2["c"], 2);
  }
}

//
// Misc Utility Functions
//

TEST(UtilsTest, UpdateReturnCopy) {
  {
    std::map<std::string, int> test_map{{"test_key", 1}};

    EXPECT_EQ(test_map.size(), 1);
    EXPECT_TRUE(UpdateReturnCopy(&test_map, "test_key", 2));
    EXPECT_EQ(test_map.size(), 1);
    EXPECT_EQ(test_map.count("test_key"), 1);
    EXPECT_EQ(test_map.at("test_key"), 2);

    int previous = 0;
    EXPECT_TRUE(UpdateReturnCopy(&test_map, "test_key", 3, &previous));
    EXPECT_EQ(test_map.size(), 1);
    EXPECT_EQ(test_map.count("test_key"), 1);
    EXPECT_EQ(test_map.at("test_key"), 3);
    EXPECT_EQ(previous, 2);

    EXPECT_EQ(test_map.count("not_exist_key_1"), 0);
    EXPECT_FALSE(UpdateReturnCopy(&test_map, "not_exist_key_1", 1));
    EXPECT_EQ(test_map.size(), 2);
    EXPECT_EQ(test_map.count("not_exist_key_1"), 1);
    EXPECT_EQ(test_map.at("not_exist_key_1"), 1);

    EXPECT_EQ(test_map.count("not_exist_key_2"), 0);
    previous = 0;
    EXPECT_FALSE(UpdateReturnCopy(&test_map, "not_exist_key_2", 2, &previous));
    EXPECT_EQ(test_map.size(), 3);
    EXPECT_EQ(test_map.count("not_exist_key_2"), 1);
    EXPECT_EQ(test_map.at("not_exist_key_2"), 2);
    EXPECT_EQ(previous, 0);
  }
  {
    std::unordered_map<std::string, int> test_map{{"test_key", 1}};

    EXPECT_EQ(test_map.size(), 1);
    EXPECT_TRUE(UpdateReturnCopy(&test_map, "test_key", 2));
    EXPECT_EQ(test_map.size(), 1);
    EXPECT_EQ(test_map.count("test_key"), 1);
    EXPECT_EQ(test_map.at("test_key"), 2);

    int previous = 0;
    EXPECT_TRUE(UpdateReturnCopy(&test_map, "test_key", 3, &previous));
    EXPECT_EQ(test_map.size(), 1);
    EXPECT_EQ(test_map.count("test_key"), 1);
    EXPECT_EQ(test_map.at("test_key"), 3);
    EXPECT_EQ(previous, 2);

    EXPECT_EQ(test_map.count("not_exist_key_1"), 0);
    EXPECT_FALSE(UpdateReturnCopy(&test_map, "not_exist_key_1", 1));
    EXPECT_EQ(test_map.size(), 2);
    EXPECT_EQ(test_map.count("not_exist_key_1"), 1);
    EXPECT_EQ(test_map.at("not_exist_key_1"), 1);

    EXPECT_EQ(test_map.count("not_exist_key_2"), 0);
    previous = 0;
    EXPECT_FALSE(UpdateReturnCopy(&test_map, "not_exist_key_2", 2, &previous));
    EXPECT_EQ(test_map.size(), 3);
    EXPECT_EQ(test_map.count("not_exist_key_2"), 1);
    EXPECT_EQ(test_map.at("not_exist_key_2"), 2);
    EXPECT_EQ(previous, 0);
  }
  {
    std::map<std::string, int> test_map{{"test_key", 1}};

    EXPECT_EQ(test_map.size(), 1);
    EXPECT_TRUE(UpdateReturnCopy(&test_map, {"test_key", 2}));
    EXPECT_EQ(test_map.size(), 1);
    EXPECT_EQ(test_map.count("test_key"), 1);
    EXPECT_EQ(test_map.at("test_key"), 2);

    int previous = 0;
    EXPECT_TRUE(UpdateReturnCopy(&test_map, {"test_key", 3}, &previous));
    EXPECT_EQ(test_map.size(), 1);
    EXPECT_EQ(test_map.count("test_key"), 1);
    EXPECT_EQ(test_map.at("test_key"), 3);
    EXPECT_EQ(previous, 2);

    EXPECT_EQ(test_map.count("not_exist_key_1"), 0);
    EXPECT_FALSE(UpdateReturnCopy(&test_map, {"not_exist_key_1"}, 1));
    EXPECT_EQ(test_map.size(), 2);
    EXPECT_EQ(test_map.count("not_exist_key_1"), 1);
    EXPECT_EQ(test_map.at("not_exist_key_1"), 1);

    EXPECT_EQ(test_map.count("not_exist_key_2"), 0);
    previous = 0;
    EXPECT_FALSE(UpdateReturnCopy(&test_map, {"not_exist_key_2", 2}, &previous));
    EXPECT_EQ(test_map.size(), 3);
    EXPECT_EQ(test_map.count("not_exist_key_2"), 1);
    EXPECT_EQ(test_map.at("not_exist_key_2"), 2);
    EXPECT_EQ(previous, 0);
  }
  {
    std::unordered_map<std::string, int> test_map{{"test_key", 1}};

    EXPECT_EQ(test_map.size(), 1);
    EXPECT_TRUE(UpdateReturnCopy(&test_map, {"test_key", 2}));
    EXPECT_EQ(test_map.size(), 1);
    EXPECT_EQ(test_map.count("test_key"), 1);
    EXPECT_EQ(test_map.at("test_key"), 2);

    int previous = 0;
    EXPECT_TRUE(UpdateReturnCopy(&test_map, {"test_key", 3}, &previous));
    EXPECT_EQ(test_map.size(), 1);
    EXPECT_EQ(test_map.count("test_key"), 1);
    EXPECT_EQ(test_map.at("test_key"), 3);
    EXPECT_EQ(previous, 2);

    EXPECT_EQ(test_map.count("not_exist_key_1"), 0);
    EXPECT_FALSE(UpdateReturnCopy(&test_map, {"not_exist_key_1", 1}));
    EXPECT_EQ(test_map.size(), 2);
    EXPECT_EQ(test_map.count("not_exist_key_1"), 1);
    EXPECT_EQ(test_map.at("not_exist_key_1"), 1);

    EXPECT_EQ(test_map.count("not_exist_key_2"), 0);
    previous = 0;
    EXPECT_FALSE(UpdateReturnCopy(&test_map, {"not_exist_key_2", 2}, &previous));
    EXPECT_EQ(test_map.size(), 3);
    EXPECT_EQ(test_map.count("not_exist_key_2"), 1);
    EXPECT_EQ(test_map.at("not_exist_key_2"), 2);
    EXPECT_EQ(previous, 0);
  }
}

TEST(UtilsTest, InsertOrReturnExisting) {
  {
    std::map<std::string, int> test_map{{"test_key", 1}};
    int* res = nullptr;
    res = InsertOrReturnExisting(&test_map, {"test_key", 2});
    EXPECT_EQ(test_map.count("test_key"), 1);
    EXPECT_EQ(res, &test_map.at("test_key"));
    EXPECT_EQ(*res, 1);

    EXPECT_EQ(InsertOrReturnExisting(&test_map, {"not_exist_key", 3}), nullptr);
    EXPECT_EQ(test_map.count("not_exist_key"), 1);
    EXPECT_EQ(test_map.at("not_exist_key"), 3);
  }
  {
    std::unordered_map<std::string, int> test_map{{"test_key", 1}};
    int* res = nullptr;
    res = InsertOrReturnExisting(&test_map, {"test_key", 2});
    EXPECT_EQ(test_map.count("test_key"), 1);
    EXPECT_EQ(res, &test_map.at("test_key"));
    EXPECT_EQ(*res, 1);

    EXPECT_EQ(InsertOrReturnExisting(&test_map, {"not_exist_key", 3}), nullptr);
    EXPECT_EQ(test_map.count("not_exist_key"), 1);
    EXPECT_EQ(test_map.at("not_exist_key"), 3);
  }
  {
    std::map<std::string, int> test_map{{"test_key", 1}};
    int* res = nullptr;
    res = InsertOrReturnExisting(&test_map, "test_key", 2);
    EXPECT_EQ(test_map.count("test_key"), 1);
    EXPECT_EQ(res, &test_map.at("test_key"));
    EXPECT_EQ(*res, 1);

    EXPECT_EQ(InsertOrReturnExisting(&test_map, "not_exist_key", 3), nullptr);
    EXPECT_EQ(test_map.count("not_exist_key"), 1);
    EXPECT_EQ(test_map.at("not_exist_key"), 3);
  }
  {
    std::unordered_map<std::string, int> test_map{{"test_key", 1}};
    int* res = nullptr;
    res = InsertOrReturnExisting(&test_map, "test_key", 2);
    EXPECT_EQ(test_map.count("test_key"), 1);
    EXPECT_EQ(res, &test_map.at("test_key"));
    EXPECT_EQ(*res, 1);

    EXPECT_EQ(InsertOrReturnExisting(&test_map, "not_exist_key", 3), nullptr);
    EXPECT_EQ(test_map.count("not_exist_key"), 1);
    EXPECT_EQ(test_map.at("not_exist_key"), 3);
  }
}

TEST(UtilsTest, EraseKeyReturnPreviousValue) {
  {
    std::map<std::string, int> test_map{{"test_key", 1}};
    EXPECT_FALSE(EraseKeyReturnPreviousValue(&test_map, ""));
    EXPECT_EQ(test_map.count("test_key"), 1);
    EXPECT_EQ(test_map.at("test_key"), 1);
    EXPECT_TRUE(EraseKeyReturnPreviousValue(&test_map, "test_key"));
    EXPECT_EQ(test_map.count("test_key"), 0);
  }
  {
    std::map<std::string, int> test_map{{"test_key", 1}};
    int previous = 0;
    EXPECT_FALSE(EraseKeyReturnPreviousValue(&test_map, "", &previous));
    EXPECT_EQ(previous, 0);
    EXPECT_EQ(test_map.count("test_key"), 1);
    EXPECT_EQ(test_map.at("test_key"), 1);
    EXPECT_TRUE(EraseKeyReturnPreviousValue(&test_map, "test_key", &previous));
    EXPECT_EQ(previous, 1);
    EXPECT_EQ(test_map.count("test_key"), 0);
  }
  {
    std::unordered_map<std::string, int> test_map{{"test_key", 1}};
    EXPECT_FALSE(EraseKeyReturnPreviousValue(&test_map, ""));
    EXPECT_EQ(test_map.count("test_key"), 1);
    EXPECT_EQ(test_map.at("test_key"), 1);
    EXPECT_TRUE(EraseKeyReturnPreviousValue(&test_map, "test_key"));
    EXPECT_EQ(test_map.count("test_key"), 0);
  }
  {
    std::unordered_map<std::string, int> test_map{{"test_key", 1}};
    int previous = 0;
    EXPECT_FALSE(EraseKeyReturnPreviousValue(&test_map, "", &previous));
    EXPECT_EQ(previous, 0);
    EXPECT_EQ(test_map.count("test_key"), 1);
    EXPECT_EQ(test_map.at("test_key"), 1);
    EXPECT_TRUE(EraseKeyReturnPreviousValue(&test_map, "test_key", &previous));
    EXPECT_EQ(previous, 1);
    EXPECT_EQ(test_map.count("test_key"), 0);
  }
}

TEST(UtilsTest, InsertKeysFromMap) {
  {
    std::map<std::string, int> test_map{{"test_key_1", 0}, {"test_key_2", 1}};
    std::set<std::string> test_set{"test_key_0", "test_key_1"};
    InsertKeysFromMap(test_map, &test_set);
    EXPECT_EQ(test_set.size(), 3);
    EXPECT_EQ(test_set.count("test_key_0"), 1);
    EXPECT_EQ(test_set.count("test_key_1"), 1);
    EXPECT_EQ(test_set.count("test_key_2"), 1);
  }
  {
    std::map<std::string, int> test_map{{"test_key_1", 0}, {"test_key_2", 1}};
    std::unordered_set<std::string> test_set{"test_key_0", "test_key_1"};
    InsertKeysFromMap(test_map, &test_set);
    EXPECT_EQ(test_set.size(), 3);
    EXPECT_EQ(test_set.count("test_key_0"), 1);
    EXPECT_EQ(test_set.count("test_key_1"), 1);
    EXPECT_EQ(test_set.count("test_key_2"), 1);
  }
  {
    std::unordered_map<std::string, int> test_map{{"test_key_1", 0}, {"test_key_2", 1}};
    std::set<std::string> test_set{"test_key_0", "test_key_1"};
    InsertKeysFromMap(test_map, &test_set);
    EXPECT_EQ(test_set.size(), 3);
    EXPECT_EQ(test_set.count("test_key_0"), 1);
    EXPECT_EQ(test_set.count("test_key_1"), 1);
    EXPECT_EQ(test_set.count("test_key_2"), 1);
  }
  {
    std::unordered_map<std::string, int> test_map{{"test_key_1", 0}, {"test_key_2", 1}};
    std::unordered_set<std::string> test_set{"test_key_0", "test_key_1"};
    InsertKeysFromMap(test_map, &test_set);
    EXPECT_EQ(test_set.size(), 3);
    EXPECT_EQ(test_set.count("test_key_0"), 1);
    EXPECT_EQ(test_set.count("test_key_1"), 1);
    EXPECT_EQ(test_set.count("test_key_2"), 1);
  }
}

TEST(UtilsTest, InsertValuesFromMap) {
  {
    std::map<std::string, int> test_map{{"test_key_1", 0}, {"test_key_2", 1}};
    std::set<int> test_set{1, 2};
    InsertValuesFromMap(test_map, &test_set);
    EXPECT_EQ(test_set.size(), 3);
    EXPECT_EQ(test_set.count(0), 1);
    EXPECT_EQ(test_set.count(1), 1);
    EXPECT_EQ(test_set.count(2), 1);
  }
  {
    std::map<std::string, int> test_map{{"test_key_1", 0}, {"test_key_2", 1}};
    std::unordered_set<int> test_set{1, 2};
    InsertValuesFromMap(test_map, &test_set);
    EXPECT_EQ(test_set.size(), 3);
    EXPECT_EQ(test_set.count(0), 1);
    EXPECT_EQ(test_set.count(1), 1);
    EXPECT_EQ(test_set.count(2), 1);
  }
  {
    std::unordered_map<std::string, int> test_map{{"test_key_1", 0}, {"test_key_2", 1}};
    std::set<int> test_set{1, 2};
    InsertValuesFromMap(test_map, &test_set);
    EXPECT_EQ(test_set.size(), 3);
    EXPECT_EQ(test_set.count(0), 1);
    EXPECT_EQ(test_set.count(1), 1);
    EXPECT_EQ(test_set.count(2), 1);
  }
  {
    std::unordered_map<std::string, int> test_map{{"test_key_1", 0}, {"test_key_2", 1}};
    std::unordered_set<int> test_set{1, 2};
    InsertValuesFromMap(test_map, &test_set);
    EXPECT_EQ(test_set.size(), 3);
    EXPECT_EQ(test_set.count(0), 1);
    EXPECT_EQ(test_set.count(1), 1);
    EXPECT_EQ(test_set.count(2), 1);
  }
}

TEST(UtilsTest, AppendKeysFromMap) {
  {
    std::map<std::string, int> test_map{{"test_key_1", 0}, {"test_key_2", 1}};
    std::vector<std::string> test_vector{"test_key_0", "test_key_1"};
    AppendKeysFromMap(test_map, &test_vector);
    EXPECT_EQ(test_vector.size(), 4);
    EXPECT_EQ(test_vector[0], "test_key_0");
    EXPECT_EQ(test_vector[1], "test_key_1");
    EXPECT_EQ(test_vector[2], "test_key_1");
    EXPECT_EQ(test_vector[3], "test_key_2");
  }
  {
    std::unordered_map<std::string, int> test_map{{"test_key_1", 0}, {"test_key_2", 1}};
    std::vector<std::string> test_vector{"test_key_0", "test_key_1"};
    AppendKeysFromMap(test_map, &test_vector);
    EXPECT_EQ(test_vector.size(), 4);
    std::unordered_map<std::string, int> counter;
    for (const auto& item : test_vector) {
      if (counter.count(item)) {
        ++counter[item];
      } else {
        counter[item] = 1;
      }
    }
    EXPECT_EQ(counter.size(), 3);
    EXPECT_EQ(counter["test_key_0"], 1);
    EXPECT_EQ(counter["test_key_1"], 2);
    EXPECT_EQ(counter["test_key_2"], 1);
  }
  {
    std::map<std::string, int> test_map{{"test_key_1", 0}, {"test_key_2", 1}};
    std::deque<std::string> test_vector{"test_key_0", "test_key_1"};
    AppendKeysFromMap(test_map, &test_vector);
    EXPECT_EQ(test_vector.size(), 4);
    EXPECT_EQ(test_vector[0], "test_key_0");
    EXPECT_EQ(test_vector[1], "test_key_1");
    EXPECT_EQ(test_vector[2], "test_key_1");
    EXPECT_EQ(test_vector[3], "test_key_2");
  }
  {
    std::unordered_map<std::string, int> test_map{{"test_key_1", 0}, {"test_key_2", 1}};
    std::deque<std::string> test_vector{"test_key_0", "test_key_1"};
    AppendKeysFromMap(test_map, &test_vector);
    EXPECT_EQ(test_vector.size(), 4);
    std::unordered_map<std::string, int> counter;
    for (const auto& item : test_vector) {
      if (counter.count(item)) {
        ++counter[item];
      } else {
        counter[item] = 1;
      }
    }
    EXPECT_EQ(counter.size(), 3);
    EXPECT_EQ(counter["test_key_0"], 1);
    EXPECT_EQ(counter["test_key_1"], 2);
    EXPECT_EQ(counter["test_key_2"], 1);
  }
}

TEST(UtilsTest, AppendKeysFromMapByValue) {
  {
    std::map<std::string, std::string> test_map{{"test_key_1", "test_value_1"},
                                                {"test_key_2", "test_value_2"}};
    std::vector<std::string> test_vector{"test_key_0", "test_key_1"};
    AppendKeysFromMapByValue(test_map, "test_value_3", &test_vector);
    EXPECT_EQ(test_vector.size(), 2);
    EXPECT_EQ(test_vector[0], "test_key_0");
    EXPECT_EQ(test_vector[1], "test_key_1");
    AppendKeysFromMapByValue(test_map, "test_value_1", &test_vector);
    EXPECT_EQ(test_vector.size(), 3);
    EXPECT_EQ(test_vector[0], "test_key_0");
    EXPECT_EQ(test_vector[1], "test_key_1");
    EXPECT_EQ(test_vector[2], "test_key_1");
  }
}

TEST(UtilsTest, AppendValuesFromMap) {
  {
    std::map<std::string, int> test_map{{"test_key_1", 0}, {"test_key_2", 1}};
    std::vector<int> test_vector{1, 2};
    AppendValuesFromMap(test_map, &test_vector);
    EXPECT_EQ(test_vector.size(), 4);
    EXPECT_EQ(test_vector[0], 1);
    EXPECT_EQ(test_vector[1], 2);
    EXPECT_EQ(test_vector[2], 0);
    EXPECT_EQ(test_vector[3], 1);
  }
  {
    std::unordered_map<std::string, int> test_map{{"test_key_1", 0}, {"test_key_2", 1}};
    std::vector<int> test_vector{1, 2};
    AppendValuesFromMap(test_map, &test_vector);
    EXPECT_EQ(test_vector.size(), 4);
    std::unordered_map<int, int> counter;
    for (const int& item : test_vector) {
      if (counter.count(item)) {
        ++counter[item];
      } else {
        counter[item] = 1;
      }
    }
    EXPECT_EQ(counter.size(), 3);
    EXPECT_EQ(counter[0], 1);
    EXPECT_EQ(counter[1], 2);
    EXPECT_EQ(counter[2], 1);
  }
  {
    std::map<std::string, int> test_map{{"test_key_1", 0}, {"test_key_2", 1}};
    std::deque<int> test_vector{1, 2};
    AppendValuesFromMap(test_map, &test_vector);
    EXPECT_EQ(test_vector.size(), 4);
    EXPECT_EQ(test_vector[0], 1);
    EXPECT_EQ(test_vector[1], 2);
    EXPECT_EQ(test_vector[2], 0);
    EXPECT_EQ(test_vector[3], 1);
  }
  {
    std::unordered_map<std::string, int> test_map{{"test_key_1", 0}, {"test_key_2", 1}};
    std::deque<int> test_vector{1, 2};
    AppendValuesFromMap(test_map, &test_vector);
    EXPECT_EQ(test_vector.size(), 4);
    std::unordered_map<int, int> counter;
    for (const int& item : test_vector) {
      if (counter.count(item)) {
        ++counter[item];
      } else {
        counter[item] = 1;
      }
    }
    EXPECT_EQ(counter.size(), 3);
    EXPECT_EQ(counter[0], 1);
    EXPECT_EQ(counter[1], 2);
    EXPECT_EQ(counter[2], 1);
  }
}

}  // namespace base
