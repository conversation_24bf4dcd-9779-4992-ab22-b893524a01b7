// Copyright @2019 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include "glog/logging.h"

namespace base {

template <typename T>
class ArrayView {
 public:
  ArrayView(T* data, int size);
  ArrayView(T* data, int start_index, int end_index);

  ~ArrayView() = default;

  int size() const { return size_; }

  bool empty() const { return size_ == 0; }

  T* data() { return data_; }
  const T* data() const { return data_; }

  T* begin() { return data_; }
  const T* begin() const { return data_; }

  T* end() { return data_ + size_; }
  const T* end() const { return data_ + size_; }

  T& front() { return (*this)[0]; }
  const T& front() const { return (*this)[0]; }

  T& back() { return (*this)[size_ - 1]; }
  const T& back() const { return (*this)[size_ - 1]; }

  T& operator[](int index);
  const T& operator[](int index) const;

 protected:
  T* data_ = nullptr;  // Not owned.
  int size_ = 0;

  // Shallow copy and move are OK.
};

template <typename T>
using ConstArrayView = ArrayView<const T>;

template <typename T>
ArrayView<T>::ArrayView(T* data, int size) : data_(data), size_(size) {
  CHECK_GE(size, 0) << "ArrayView should not have negative size.";
  if (size > 0) {
    CHECK(data != nullptr) << "Data cannot be nullptr when size is not empty";
  }
}

template <typename T>
ArrayView<T>::ArrayView(T* data, int start_index, int end_index)
    : ArrayView(start_index != end_index ? data + start_index : nullptr, end_index - start_index) {}

template <typename T>
T& ArrayView<T>::operator[](int index) {
  DCHECK(0 <= index && index < size_);
  return data_[index];
}

template <typename T>
const T& ArrayView<T>::operator[](int index) const {
  DCHECK(0 <= index && index < size_);
  return data_[index];
}

template <typename ContainerOfT, typename T = typename ContainerOfT::value_type>
ConstArrayView<T> ToConstArrayView(const ContainerOfT& c) {
  return ConstArrayView<T>(c.data(), c.size());
}

template <typename ContainerOfT, typename T = typename ContainerOfT::value_type>
ConstArrayView<T> ToConstArrayView(const ContainerOfT& c, int start_index, int end_index) {
  return ConstArrayView<T>(c.data(), start_index, end_index);
}

}  // namespace base
