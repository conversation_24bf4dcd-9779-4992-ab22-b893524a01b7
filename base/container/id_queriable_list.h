// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <algorithm>
#include <list>
#include <string>
#include <unordered_map>
#include <utility>

#include "base/common/field_macros.h"
#include "base/container/utils.h"
#include "base/common/macros.h"
#include "glog/logging.h"

namespace base {
namespace container {

template <class ObjectType, class IdType = int>
class IdQueriableList {
 public:
  using ObjectIterator = typename std::list<ObjectType>::iterator;
  using ConstObjectIterator = typename std::list<ObjectType>::const_iterator;

  IdQueriableList() = default;
  virtual ~IdQueriableList() = default;

  template <class... Args>
  ObjectType* Emplace(Args&&... args) {
    object_list_.emplace_back(std::forward<Args&&>(args)...);
    ObjectType* object = &object_list_.back();
    // ObjectType has to implement id() method to return an Integer.
    base::InsertIfNotPresent(&id_to_object_map_, object->id(), object);
    return object;
  }

  bool HasObject(IdType id) const { return id_to_object_map_.count(id); }

  const ObjectType& GetObjectOrDie(IdType id) const {
    return *base::FindOrDieNoPrint(id_to_object_map_, id);
  }

  ObjectType* GetMutableObjectOrDie(IdType id) {
    return base::FindOrDieNoPrint(&id_to_object_map_, id);
  }

  ObjectIterator begin() { return object_list_.begin(); }

  ObjectIterator end() { return object_list_.end(); }

  const ConstObjectIterator begin() const { return object_list_.begin(); }

  const ConstObjectIterator end() const { return object_list_.end(); }

  const ObjectType& back() const { return object_list_.back(); }

  void sort(std::function<bool(const ObjectType& ob0, const ObjectType& ob1)> cmp) {
    object_list_.sort(cmp);
  }

  ObjectIterator Remove(ObjectIterator iter) {
    id_to_object_map_.erase(iter->id());
    return object_list_.erase(iter);
  }

  int size() const { return id_to_object_map_.size(); }
  bool empty() const { return id_to_object_map_.empty(); }

  void RemoveIf(std::function<bool(const ObjectType& object)> predict) {
    for (auto iter = begin(); iter != end();) {
      if (predict(*iter)) {
        iter = Remove(iter);
      } else {
        iter++;
      }
    }
  }

  void clear() {
    id_to_object_map_.clear();
    object_list_.clear();
  }

  void Merge(IdQueriableList& other) {
    object_list_.splice(object_list_.end(), other.object_list_);
    for (auto& object : object_list_) {
      id_to_object_map_[object.id()] = &object;
    }
    other.id_to_object_map_.clear();
  }

 private:
  std::unordered_map<IdType, ObjectType*> id_to_object_map_;
  std::list<ObjectType> object_list_;

  DISALLOW_COPY_AND_ASSIGN(IdQueriableList);
};

}  // namespace container
}  // namespace base
