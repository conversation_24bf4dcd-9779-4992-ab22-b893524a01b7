// Copyright @2022 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/container/enum_class_hash.h"

#include <unordered_set>

#include "gtest/gtest.h"

namespace {

enum class DummyEnumClass { kClass0 = 0, kClass1 = 1, kClass2 = 2 };

}  // namespace

TEST(EnumClassHashTest, BasicTest) {
  const std::unordered_set<DummyEnumClass, EnumClassHash> enum_class_set = {
      DummyEnumClass::kClass0,
      DummyEnumClass::kClass2,
  };
  EXPECT_TRUE(enum_class_set.count(DummyEnumClass::kClass0));
}
