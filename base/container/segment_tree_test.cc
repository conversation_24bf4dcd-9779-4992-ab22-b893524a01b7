// Copyright @2022 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/container/segment_tree.h"

#include "gtest/gtest.h"

namespace base {
namespace container {

class SegmentTreeTest : public ::testing::Test {
 protected:
  struct SimpleNode {
    double sum = 0.0;
    const SimpleNode* left = nullptr;
    const SimpleNode* right = nullptr;
  };
  // sequence: 1, 3, 5...
  static SimpleNode CreateSimpleSequence(int index) {
    return SimpleNode{.sum = static_cast<double>(2 * index + 1), .left = nullptr, .right = nullptr};
  }
  static SimpleNode Join(const SimpleNode& left, const SimpleNode& right) {
    return SimpleNode{.sum = left.sum + right.sum, .left = &left, .right = &right};
  }

  template <typename Function>
  static void LeafTraversal(const Function& func, const SimpleNode* node) {
    if (!node->left) {
      func(node);
    } else {
      DCHECK(node->right);
      LeafTraversal(func, node->left);
      LeafTraversal(func, node->right);
    }
  }

  static double SumQuery(int start, int end, int segment_start, int segment_end,
                         const SimpleNode* node) {
    CHECK_GE(start, segment_start);
    CHECK_LE(end, segment_end);
    CHECK_GE(end, start);
    CHECK_NOTNULL(node);
    if (start == segment_start && end == segment_end) {
      return node->sum;
    }
    const int segment_mid = segment_start + (segment_end - segment_start) / 2;
    if (start <= segment_mid && end > segment_mid) {
      return SumQuery(start, segment_mid, segment_start, segment_mid, node->left) +
             SumQuery(segment_mid + 1, end, segment_mid + 1, segment_end, node->right);
    }
    if (start <= segment_mid) {
      return SumQuery(start, end, segment_start, segment_mid, node->left);
    } else {
      return SumQuery(start, end, segment_mid + 1, segment_end, node->right);
    }
  }

  static double SumQuery(int start, int end, const std::vector<SimpleNode>& nodes) {
    CHECK_GE(start, 0);
    CHECK_LT(end, nodes.size() / 2);
    CHECK_GE(end, start);
    return SumQuery(start, end, 0, nodes.size() / 2 - 1, &nodes[0]);
  }
};

TEST_F(SegmentTreeTest, Basic) {
  {
    const std::vector<SimpleNode> sum_segment_tree0 =
        SegmentTree<SimpleNode>::Build(CreateSimpleSequence, Join, 4);
    ASSERT_EQ(sum_segment_tree0.size(), 8);
    ASSERT_NEAR(sum_segment_tree0[0].sum, 16.0, 1e-5);

    std::vector<double> leaf_values;
    const auto get_leaf = [&leaf_values](const SimpleNode* node) {
      leaf_values.emplace_back(node->sum);
    };
    LeafTraversal(get_leaf, &sum_segment_tree0[0]);
    ASSERT_EQ(leaf_values.size(), 4);
    for (int i = 0; i < 4; ++i) {
      ASSERT_NEAR(leaf_values[i], CreateSimpleSequence(i).sum, 1e-5);
    }
  }
  {
    const std::vector<SimpleNode> sum_segment_tree1 =
        SegmentTree<SimpleNode>::Build(CreateSimpleSequence, Join, 10);
    ASSERT_EQ(sum_segment_tree1.size(), 20);
    ASSERT_NEAR(sum_segment_tree1[0].sum, 100.0, 1e-5);
    std::vector<double> leaf_values;
    const auto get_leaf = [&leaf_values](const SimpleNode* node) {
      leaf_values.emplace_back(node->sum);
    };
    LeafTraversal(get_leaf, &sum_segment_tree1[0]);
    ASSERT_EQ(leaf_values.size(), 10);
    for (int i = 0; i < 10; ++i) {
      ASSERT_NEAR(leaf_values[i], CreateSimpleSequence(i).sum, 1e-5);
    }
  }
}

TEST_F(SegmentTreeTest, StandardSumQuery) {
  const std::vector<SimpleNode> sum_segment_tree1 =
      SegmentTree<SimpleNode>::Build(CreateSimpleSequence, Join, 10);

  ASSERT_NEAR(SumQuery(0, 2, sum_segment_tree1), 9.0, 1e-5);
  ASSERT_NEAR(SumQuery(1, 3, sum_segment_tree1), 15.0, 1e-5);
  ASSERT_NEAR(SumQuery(4, 8, sum_segment_tree1), 65.0, 1e-5);
}

}  // namespace container
}  // namespace base
