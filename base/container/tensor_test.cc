// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include <algorithm>
#include <memory>
#include <string>
#include <utility>
#include <vector>

#include "gtest/gtest.h"
#include "glog/logging.h"

#include "base/container/tensor.h"
#include "base/container/tensor_util.h"

namespace base {
namespace container {

TEST(TensorTest, PushAxis) {
  Tensor<float, 3> t(3, 3, 3);
  t.<PERSON>xi<PERSON><int>("axis1", std::vector<int>{11, 22, 33});
  t.PushAxis<std::string>("axis2", std::vector<std::string>{"a2", "b2", "c2"});
  t.<PERSON>ush<PERSON>xis<float>("axis3", std::vector<float>{1.11, 2.22, 3.33});

  EXPECT_EQ(t.AxisSequence("axis1"), 0);
  EXPECT_EQ(t.AxisSequence("axis2"), 1);
  EXPECT_EQ(t.AxisSequence("axis3"), 2);
}

TEST(TensorTest, MutableAt) {
  Tensor<float, 2> t1(3, 3);
  t1.PushAxis<std::string>("axis0", std::vector<std::string>{"a0", "b0", "c0"});
  t1.PushAxis<int>("axis1", std::vector<int>{11, 22, 33});
  t1.setConstant(66.66);

  t1.MutableAt("a0", 11) = 11.11;
  t1.MutableAt("b0", 22) = 22.22;

  EXPECT_FLOAT_EQ(t1.At("a0", 11), 11.11);
  EXPECT_FLOAT_EQ(t1.At("b0", 22), 22.22);
}

TEST(TensorTest, At) {
  Tensor<float, 2> t1(3, 3);
  t1.PushAxis<std::string>("axis0", std::vector<std::string>{"a0", "b0", "c0"});
  t1.PushAxis<int>("axis1", std::vector<int>{11, 22, 33});
  t1.setConstant(66.66);

  EXPECT_FLOAT_EQ(t1(0, 0), 66.66);
  EXPECT_FLOAT_EQ(t1.At("b0", 33), 66.66);

  Tensor<float, 3> t2(3, 3, 4);
  t2.PushAxis<std::string>("axis0", std::vector<std::string>{"a0", "b0", "c0"});
  t2.PushAxis<int>("axis1", std::vector<int>{11, 22, 33});
  t2.PushAxis<float>("axis2", std::vector<float>{1.1, 2.2, 3.3, 4.4});
  t2.setConstant(11.11);

  EXPECT_FLOAT_EQ(t2(0, 0, 2), 11.11);
  EXPECT_FLOAT_EQ(t2.At("b0", 33, 1.1), 11.11);

  Tensor<int, 4> t3(3, 3, 4, 2);
  t3.PushAxis<std::string>("axis0", std::vector<std::string>{"a0", "b0", "c0"});
  t3.PushAxis<int>("axis1", std::vector<int>{11, 22, 33});
  t3.PushAxis<float>("axis2", std::vector<float>{1.1, 2.2, 3.3, 4.4});
  t3.PushAxis<std::string>("axis3", std::vector<std::string>{"I", "II"});
  t3.setConstant(12321);

  EXPECT_FLOAT_EQ(t3(0, 0, 2, 1), 12321);
  EXPECT_FLOAT_EQ(t3.At("b0", 33, 3.3, "II"), 12321);
}

TEST(TensorTest, Stack) {
  Tensor<float, 3> t1(3, 3, 3);
  t1.PushAxis<std::string>("axis0", std::vector<std::string>{"a0", "b0", "c0"});
  t1.PushAxis<int>("axis1", std::vector<int>{11, 22, 33});
  t1.PushAxis<float>("axis2", std::vector<float>{44.44, 55.55, 66.66});
  t1.setConstant(33.33);

  Tensor<float, 3> t2(3, 3, 3);
  t2.PushAxis<std::string>("axis0", std::vector<std::string>{"a0", "b0", "c0"});
  t2.PushAxis<int>("axis1", std::vector<int>{44, 55, 66});
  t2.PushAxis<float>("axis2", std::vector<float>{44.44, 55.55, 66.66});
  t2.setConstant(66.66);

  t1.Stack<int>(t2, "axis1");

  Eigen::array<int64_t, 3> dim = t1.dimensions();
  Eigen::array<int64_t, 3> target_dim{3, 6, 3};

  EXPECT_EQ(dim, target_dim);
  EXPECT_FLOAT_EQ(t1.At("a0", 11, 44.44), 33.33);
  EXPECT_FLOAT_EQ(t1.At("a0", 55, 55.55), 66.66);
}

TEST(TensorTest, StackFrom0) {
  Tensor<float, 3> t1(3, 0, 3);
  t1.PushAxis<std::string>("axis0", std::vector<std::string>{"a0", "b0", "c0"});
  t1.PushAxis<int>("axis1", {});
  t1.PushAxis<float>("axis2", std::vector<float>{44.44, 55.55, 66.66});
  t1.setConstant(33.33);

  Tensor<float, 3> t2(3, 3, 3);
  t2.PushAxis<std::string>("axis0", std::vector<std::string>{"a0", "b0", "c0"});
  t2.PushAxis<int>("axis1", std::vector<int>{44, 55, 66});
  t2.PushAxis<float>("axis2", std::vector<float>{44.44, 55.55, 66.66});
  t2.setConstant(66.66);

  t1.Stack<int>(t2, "axis1");

  Eigen::array<int64_t, 3> dim = t1.dimensions();
  Eigen::array<int64_t, 3> target_dim{3, 3, 3};

  EXPECT_EQ(dim, target_dim);
  EXPECT_FLOAT_EQ(t1.At("a0", 44, 44.44), 66.66);
  EXPECT_FLOAT_EQ(t1.At("a0", 55, 55.55), 66.66);
}

TEST(TensorTest, Chip) {
  Tensor<float, 3> t1(3, 4, 5);
  t1.PushAxis<std::string>("axis0", std::vector<std::string>{"a0", "b0", "c0"});
  t1.PushAxis<int>("axis1", std::vector<int>{11, 22, 33, 44});
  t1.PushAxis<float>("axis2", std::vector<float>{44.44, 55.55, 66.66, 77.77, 88.88});
  t1.setConstant(33.33);

  Tensor<float, 2> t2 = t1.Chip(0, "axis1");
  Eigen::array<int64_t, 2> dim2 = t2.dimensions();
  Eigen::array<int64_t, 2> target_dim2{3, 5};

  EXPECT_EQ(dim2, target_dim2);
  EXPECT_FLOAT_EQ(t2.At("a0", 44.44), 33.33);
  EXPECT_FLOAT_EQ(t2.At("c0", 55.55), 33.33);

  Tensor<float, 1> t3 = t2.Chip(0, "axis2");
  Eigen::array<int64_t, 1> dim3 = t3.dimensions();
  Eigen::array<int64_t, 1> target_dim3{3};

  EXPECT_EQ(dim3, target_dim3);
  EXPECT_FLOAT_EQ(t3.At("a0"), 33.33);
}

TEST(TensorTest, Operations) {
  Tensor<float, 3> t1(3, 4, 5);
  t1.PushAxis<std::string>("axis0", std::vector<std::string>{"a0", "b0", "c0"});
  t1.PushAxis<int>("axis1", std::vector<int>{11, 22, 33, 44});
  t1.PushAxis<float>("axis2", std::vector<float>{44.44, 55.55, 66.66, 77.77, 88.88});
  t1.setConstant(33.33);

  Tensor<float, 3> t2(3, 4, 5);
  t2.PushAxis<std::string>("axis0", std::vector<std::string>{"a0", "b0", "c0"});
  t2.PushAxis<int>("axis1", std::vector<int>{11, 22, 33, 44});
  t2.PushAxis<float>("axis2", std::vector<float>{44.44, 55.55, 66.66, 77.77, 88.88});
  t2.setConstant(66.66);

  Tensor<float, 3> t3 = t1 + t2;
  Tensor<float, 3> t4 = t1 - t2;
  Tensor<float, 3> t5 = t1 * t2;
  Tensor<float, 3> t6 = t1 / t2;

  EXPECT_FLOAT_EQ(t3.At("a0", 33, 55.55), 99.99);
  EXPECT_FLOAT_EQ(t4.At("a0", 33, 55.55), -33.33);
  EXPECT_FLOAT_EQ(t5.At("a0", 33, 55.55), 2221.7778);
  EXPECT_FLOAT_EQ(t6.At("a0", 33, 55.55), 0.5);
}

TEST(TensorTest, AxisIndices) {
  Tensor<float, 3> t1(3, 4, 5);
  t1.PushAxis<std::string>("axis0", std::vector<std::string>{"a0", "b0", "c0"});
  t1.PushAxis<int>("axis1", std::vector<int>{11, 22, 33, 44});
  t1.PushAxis<float>("axis2", std::vector<float>{44.44, 55.55, 66.66, 77.77, 88.88});
  t1.setConstant(33.33);

  int index = 0;
  for (const std::string& str : t1.AxisIndices<std::string>("axis0")) {
    if (index == 0) {
      EXPECT_EQ(str, "a0");
    } else if (index == 1) {
      EXPECT_EQ(str, "b0");
    } else if (index ==2) {
      EXPECT_EQ(str, "c0");
    } else {
      CHECK(false);
    }
    index++;
  }
  index = 0;
  for (const float& f : t1.AxisIndices<float>("axis2")) {
    if (index == 0) {
      EXPECT_FLOAT_EQ(f, 44.44);
    } else if (index == 1) {
      EXPECT_FLOAT_EQ(f, 55.55);
    } else if (index ==2) {
      EXPECT_FLOAT_EQ(f, 66.66);
    } else if (index == 3) {
      EXPECT_FLOAT_EQ(f, 77.77);
    } else if (index == 4) {
      EXPECT_FLOAT_EQ(f, 88.88);
    } else {
      CHECK(false);
    }
    index++;
  }
}

TEST(TensorTest, ResetAxis) {
  Tensor<float, 3> t1(3, 4, 5);
  t1.PushAxis<std::string>("axis0", std::vector<std::string>{"a0", "b0", "c0"});
  t1.PushAxis<int>("axis1", std::vector<int>{11, 22, 33, 44});
  t1.PushAxis<float>("axis2", std::vector<float>{44.44, 55.55, 66.66, 77.77, 88.88});
  t1.setConstant(33.33);

  t1.ResetAxis("axis1", std::vector<int>{77, 88, 99});

  Eigen::array<int64_t, 3> dim = t1.dimensions();
  Eigen::array<int64_t, 3> target_dim{3, 3, 5};
  EXPECT_EQ(dim, target_dim);

  std::vector<int> indices = t1.AxisIndices<int>("axis1");
  std::vector<int> target_indices{77, 88, 99};
  EXPECT_EQ(indices, target_indices);
}

TEST(TensorTest, DefaultAt) {
  Tensor<float, 3> t1(3, 4, 5);
  t1.PushAxis<std::string>("axis0", std::vector<std::string>{"a0", "b0", "c0"});
  t1.PushAxis<int>("axis1", std::vector<int>{11, 22, 33, 44});
  t1.PushAxis<float>("axis2", std::vector<float>{44.44, 55.55, 66.66, 77.77, 88.88});
  t1.setConstant(33.33);

  EXPECT_FLOAT_EQ(33.33, t1(2, 3, 4));
}

}  // namespace container
}  // namespace base
