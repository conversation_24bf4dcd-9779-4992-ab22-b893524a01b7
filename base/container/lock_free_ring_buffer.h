// Copyright @2025 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <algorithm>
#include <atomic>
#include <mutex>
#include <vector>

namespace base {

// Multi-producer multi-consumer lock-free ring buffer
template <typename T>
class LockFreeRingBuffer {
 private:
  enum class SlotState : uint8_t {
    Empty,
    Writing,
    Ready,
    Reading,
  };
  struct Slot {
    T data;
    std::atomic<SlotState> state{SlotState::Empty};
  };

 public:
  explicit LockFreeRingBuffer(int capacity)
      : capacity_(RoundToPowerOfTwo(std::max(capacity, 1))),
        mask_(capacity_ - 1),
        buffer_(capacity_),
        head_{0},
        tail_{0} {}

  bool Push(const T& item) {
    int tail = tail_.load(std::memory_order_relaxed);
    while (true) {
      int head_cache = head_.load(std::memory_order_acquire);
      int next_tail = (tail + 1) & mask_;
      if (next_tail == head_cache) {
        return false;
      }
      Slot& slot = buffer_[tail];
      SlotState expected = SlotState::Empty;
      if (slot.state.compare_exchange_strong(
              expected, SlotState::Writing, std::memory_order_acquire, std::memory_order_relaxed)) {
        if (tail_.compare_exchange_strong(
                tail, next_tail, std::memory_order_release, std::memory_order_relaxed)) {
          slot.data = item;
          slot.state.store(SlotState::Ready, std::memory_order_release);
          return true;
        } else {
          slot.state.store(SlotState::Empty, std::memory_order_release);
        }
      }
    }
  }

  bool PopBatch(std::vector<T>* batch, int max_size) {
    if (!batch || max_size <= 0) {
      return false;
    }
    batch->clear();
    int head = head_.load(std::memory_order_relaxed);
    const int tail_val = tail_.load(std::memory_order_acquire);
    const int avail = (tail_val - head + capacity_) & mask_;
    if (avail == 0) {
      return false;
    }
    int count = 0;
    while (count < max_size && count < avail) {
      const int idx = (head + count) & mask_;
      Slot& slot = buffer_[idx];
      SlotState expected = SlotState::Ready;
      if (slot.state.compare_exchange_strong(
              expected, SlotState::Reading, std::memory_order_acquire, std::memory_order_relaxed)) {
        batch->push_back(slot.data);
        slot.state.store(SlotState::Empty, std::memory_order_release);
        count++;
      } else if (expected == SlotState::Empty || expected == SlotState::Writing) {
        break;
      }
    }
    if (count > 0) {
      const int new_head = (head + count) & mask_;
      head_.store(new_head, std::memory_order_release);
      return true;
    }
    return false;
  }

  int GetCapacity() const { return capacity_; }

 private:
  static int RoundToPowerOfTwo(int n) {
    if (n <= 0) return 1;
    n--;
    n |= n >> 1;
    n |= n >> 2;
    n |= n >> 4;
    n |= n >> 8;
    n |= n >> 16;
    return n + 1;
  }

  int capacity_;
  int mask_;
  std::vector<Slot> buffer_;
  alignas(64) std::atomic<int> head_;
  alignas(64) std::atomic<int> tail_;
};

}  // namespace base