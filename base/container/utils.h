// Copyright @2019 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>
//          Guancheng Li (<EMAIL>)

#pragma once

#include <algorithm>
#include <array>
#include <iterator>
#include <utility>
#include <vector>

#include "glog/logging.h"

namespace base {

template <typename T, int N>
constexpr int array_size(const T (&)[N]) {
  return N;
}

template <typename T, uint64_t N>
constexpr int array_size(const std::array<T, N>&) {
  return N;
}

template <class MapType>
bool IsKeySame(const MapType* const map1, const MapType* const map2) {
  if (map1->size() != map2->size()) {
    return false;
  }
  for (const auto& pair : *map1) {
    if (map2->find(pair.first) == map2->end()) {
      return false;
    }
  }
  return true;
}

// NOTE: The following functions are imported from Proto Buffer. Some details are
// modified to fit with the current code style requirements. The origin version is here:
// https://github.com/protocolbuffers/protobuf/blob/main/src/google/protobuf/stubs/map_util.h

//
// Find*()
//

// Returns a const reference to the value associated with the given key if it
// exists. Crashes otherwise.
//
// This is intended as a replacement for operator[] as an rvalue (for reading)
// when the key is guaranteed to exist.
//
// operator[] for lookup is discouraged for several reasons:
//  - It has a side-effect of inserting missing keys
//  - It is not thread-safe (even when it is not inserting, it can still
//      choose to resize the underlying storage)
//  - It invalidates iterators (when it chooses to resize)
//  - It default constructs a value object even if it doesn't need to
//
// This version assumes the key is printable, and includes it in the fatal log
// message.
template <class Collection>
const typename Collection::value_type::second_type& FindOrDie(
    const Collection& collection, const typename Collection::value_type::first_type& key) {
  typename Collection::const_iterator it = collection.find(key);
  CHECK(it != collection.end()) << "Map key not found: " << key;
  return it->second;
}

// Same as above, but consumes a pointer to collection and returns a non-const reference.
template <class Collection>
typename Collection::value_type::second_type* FindOrDie(
    Collection* const collection, const typename Collection::value_type::first_type& key) {
  CHECK(collection != nullptr) << "invalid nullptr";
  typename Collection::iterator it = collection->find(key);
  CHECK(it != collection->end()) << "Map key not found: " << key;
  return &it->second;
}

// Same as FindOrDie above, but doesn't log the key on failure.
template <class Collection>
const typename Collection::value_type::second_type& FindOrDieNoPrint(
    const Collection& collection, const typename Collection::value_type::first_type& key) {
  typename Collection::const_iterator it = collection.find(key);
  CHECK(it != collection.end()) << "Map key not found";
  return it->second;
}

// Same as above, but returns a non-const reference.
template <class Collection>
typename Collection::value_type::second_type& FindOrDieNoPrint(
    Collection* const collection, const typename Collection::value_type::first_type& key) {
  CHECK(collection != nullptr) << "invalid nullptr";
  typename Collection::iterator it = collection->find(key);
  CHECK(it != collection->end()) << "Map key not found";
  return it->second;
}

// Returns a const reference to the value associated with the given key if it
// exists, otherwise returns a const reference to the provided default value.
//
// WARNING: If a temporary object is passed as the default "value,"
// this function will return a reference to that temporary object,
// which will be destroyed at the end of the statement. A common
// example: if you have a map with string values, and you pass a char*
// as the default "value," either use the returned value immediately
// or store it in a string (not string&).
// Details: http://go/findwithdefault
template <class Collection>
const typename Collection::value_type::second_type& FindWithDefault(
    const Collection& collection,
    const typename Collection::value_type::first_type& key,
    const typename Collection::value_type::second_type& value) {
  typename Collection::const_iterator it = collection.find(key);
  if (it == collection.end()) {
    return value;
  }
  return it->second;
}

template <class Collection>
const typename Collection::value_type::second_type& FindWithDefault(
    Collection* const collection,
    const typename Collection::value_type::first_type& key,
    const typename Collection::value_type::second_type& value) {
  CHECK(collection != nullptr) << "invalid nullptr";
  return FindWithDefault(*collection, key, value);
}

// Returns a pointer to the const value associated with the given key if it
// exists, or nullptr otherwise.
template <class Collection>
const typename Collection::value_type::second_type* FindOrNull(
    const Collection& collection, const typename Collection::value_type::first_type& key) {
  typename Collection::const_iterator it = collection.find(key);
  if (it == collection.end()) {
    return nullptr;
  }
  return &it->second;
}

// Same as above but returns a pointer to the non-const value.
template <class Collection>
typename Collection::value_type::second_type* FindOrNull(
    Collection* const collection, const typename Collection::value_type::first_type& key) {
  CHECK(collection != nullptr) << "invalid nullptr";
  typename Collection::iterator it = collection->find(key);
  if (it == collection->end()) {
    return nullptr;
  }
  return &it->second;
}

// Returns the pointer value associated with the given key. If none is found,
// nullptr is returned. The function is designed to be used with a map of keys to
// pointers.
//
// This function does not distinguish between a missing key and a key mapped
// to nullptr.
template <class Collection>
typename Collection::value_type::second_type FindPtrOrNull(
    const Collection& collection, const typename Collection::value_type::first_type& key) {
  typename Collection::const_iterator it = collection.find(key);
  if (it == collection.end()) {
    return typename Collection::value_type::second_type();
  }
  return it->second;
}

// Same as above, except takes non-const reference to collection.
//
// This function is needed for containers that propagate constness to the
// pointee, such as boost::ptr_map.
template <class Collection>
typename Collection::value_type::second_type FindPtrOrNull(
    Collection* const collection, const typename Collection::value_type::first_type& key) {
  CHECK(collection != nullptr) << "invalid nullptr";
  typename Collection::iterator it = collection->find(key);
  if (it == collection->end()) {
    return typename Collection::value_type::second_type();
  }
  return it->second;
}

// Finds the pointer value associated with the given key in a map whose values
// are linked_ptrs. Returns nullptr if key is not found.
template <class Collection>
typename Collection::value_type::second_type::element_type* FindLinkedPtrOrNull(
    const Collection& collection, const typename Collection::value_type::first_type& key) {
  typename Collection::const_iterator it = collection.find(key);
  if (it == collection.end()) {
    return nullptr;
  }
  // Since linked_ptr::get() is a const member returning a non const,
  // we do not need a version of this function taking a non const collection.
  return it->second.get();
}

// Same as above, but dies if the key is not found.
template <class Collection>
typename Collection::value_type::second_type::element_type& FindLinkedPtrOrDie(
    const Collection& collection, const typename Collection::value_type::first_type& key) {
  typename Collection::const_iterator it = collection.find(key);
  CHECK(it != collection.end()) << "Map key not found: " << key;
  // Since linked_ptr::operator*() is a const member returning a non const,
  // we do not need a version of this function taking a non const collection.
  return *it->second;
}

// Finds the value associated with the given key and copies it to *value.
// Returns false if the key was not found, true otherwise.
template <class Collection, class Key, class Value>
bool FindCopy(const Collection& collection, const Key& key, Value* const value) {
  CHECK(value != nullptr) << "invalid nullptr";
  typename Collection::const_iterator it = collection.find(key);
  if (it == collection.end()) {
    return false;
  }
  *value = it->second;
  return true;
}

//
// Contains*()
//

// Returns true if and only if the given collection contains the given key.
template <class Collection, class Key>
bool ContainsKey(const Collection& collection, const Key& key) {
  return collection.find(key) != collection.end();
}

// Returns true if and only if the given collection contains the given key-value
// pair.
template <class Collection, class Key, class Value>
bool ContainsKeyValuePair(const Collection& collection, const Key& key, const Value& value) {
  typedef typename Collection::const_iterator const_iterator;
  std::pair<const_iterator, const_iterator> range = collection.equal_range(key);
  for (const_iterator it = range.first; it != range.second; ++it) {
    if (it->second == value) {
      return true;
    }
  }
  return false;
}

// Utilizing template specialization to check for the existence of a specified function. The design
// also supports containers in customizing their find function, such as enabling a sorted_vector to
// perform binary search, or a vector_shell to support comparison of pointer elements.
template <typename Container, typename Key, typename = void>
struct HasFindMethod : std::false_type {};

template <typename Container, typename Key>
struct HasFindMethod<Container,
                     Key,
                     std::__void_t<decltype(std::declval<Container>().find(std::declval<Key>()))>>
    : std::true_type {};

// Returns true if and only if the given collection contains the given key.
template<class Collection, class Key>
bool Contains(const Collection& collection, const Key& key) {
  if constexpr (HasFindMethod<Collection, Key>::value) {
    return collection.find(key) != collection.end();
  } else {
    return std::find(collection.begin(), collection.end(), key) != collection.end();
  }
}

//
// Insert*()
//

// Inserts the given key-value pair into the collection. Returns true if and
// only if the key from the given pair didn't previously exist. Otherwise, the
// value in the map is replaced with the value from the given pair.
template <class Collection>
bool InsertOrUpdate(Collection* const collection, const typename Collection::value_type& vt) {
  CHECK(collection != nullptr) << "invalid nullptr";
  std::pair<typename Collection::iterator, bool> ret = collection->insert(vt);
  if (!ret.second) {
    // update
    ret.first->second = vt.second;
    return false;
  }
  return true;
}

// Same as above, except that the key and value are passed separately.
template <class Collection>
bool InsertOrUpdate(Collection* const collection,
                    const typename Collection::value_type::first_type& key,
                    const typename Collection::value_type::second_type& value) {
  CHECK(collection != nullptr) << "invalid nullptr";
  return InsertOrUpdate(collection, typename Collection::value_type(key, value));
}

// Inserts/updates all the key-value pairs from the range defined by the
// iterators "first" and "last" into the given collection.
template <class Collection, class InputIterator>
void InsertOrUpdateMany(Collection* const collection, InputIterator first, InputIterator last) {
  CHECK(collection != nullptr) << "invalid nullptr";
  for (; first != last; ++first) {
    InsertOrUpdate(collection, *first);
  }
}

// Inserts the given key and value into the given collection if and only if the
// given key did NOT already exist in the collection. If the key previously
// existed in the collection, the value is not changed. Returns true if the
// key-value pair was inserted; returns false if the key was already present.
template <class Collection>
bool InsertIfNotPresent(Collection* const collection, const typename Collection::value_type& vt) {
  CHECK(collection != nullptr) << "invalid nullptr";
  return collection->insert(vt).second;
}

// Same as above except the key and value are passed separately.
template <class Collection>
bool InsertIfNotPresent(Collection* const collection,
                        const typename Collection::value_type::first_type& key,
                        const typename Collection::value_type::second_type& value) {
  CHECK(collection != nullptr) << "invalid nullptr";
  return InsertIfNotPresent(collection, typename Collection::value_type(key, value));
}

// Same as above except dies if the key already exists in the collection.
template <class Collection>
void InsertOrDie(Collection* const collection, const typename Collection::value_type& vt) {
  CHECK(collection != nullptr) << "invalid nullptr";
  CHECK(InsertIfNotPresent(collection, vt)) << "duplicate key: " << vt.first;
}

// Same as above except doesn't log the value on error.
template <class Collection>
void InsertOrDieNoPrint(Collection* const collection,
                        const typename Collection::value_type& vt) {
  CHECK(collection != nullptr) << "invalid nullptr";
  CHECK(InsertIfNotPresent(collection, vt)) << "duplicate key.";
}

// Inserts the key-value pair into the collection. Dies if key was already
// present.
template <class Collection>
void InsertOrDie(Collection* const collection,
                 const typename Collection::value_type::first_type& key,
                 const typename Collection::value_type::second_type& value) {
  CHECK(collection != nullptr) << "invalid nullptr";
  CHECK(InsertIfNotPresent(collection, key, value)) << "duplicate key: " << key;
}

// Same as above except doesn't log the key on error.
template <class Collection>
void InsertOrDieNoPrint(Collection* const collection,
                        const typename Collection::value_type::first_type& key,
                        const typename Collection::value_type::second_type& value) {
  CHECK(collection != nullptr) << "invalid nullptr";
  CHECK(InsertIfNotPresent(collection, key, value)) << "duplicate key.";
}

// Inserts a new key and default-initialized value. Dies if the key was already
// present. Returns a reference to the value. Example usage:
//
// map<int, SomeProto> m;
// SomeProto& proto = InsertKeyOrDie(&m, 3);
// proto.set_field("foo");
template <class Collection>
typename Collection::value_type::second_type& InsertKeyOrDie(
    Collection* const collection, const typename Collection::value_type::first_type& key) {
  CHECK(collection != nullptr) << "invalid nullptr";
  typedef typename Collection::value_type value_type;
  std::pair<typename Collection::iterator, bool> res =
      collection->insert(value_type(key, typename value_type::second_type()));
  CHECK(res.second) << "duplicate key: " << key;
  return res.first->second;
}

//
// Lookup*()
//

// Looks up a given key and value pair in a collection and inserts the key-value
// pair if it's not already present. Returns a reference to the value associated
// with the key.
template <class Collection>
typename Collection::value_type::second_type& LookupOrInsert(
    Collection* const collection, const typename Collection::value_type& vt) {
  CHECK(collection != nullptr) << "invalid nullptr";
  return collection->insert(vt).first->second;
}

// Same as above except the key-value are passed separately.
template <class Collection>
typename Collection::value_type::second_type& LookupOrInsert(
    Collection* const collection,
    const typename Collection::value_type::first_type& key,
    const typename Collection::value_type::second_type& value) {
  CHECK(collection != nullptr) << "invalid nullptr";
  return LookupOrInsert(collection, typename Collection::value_type(key, value));
}

// Counts the number of equivalent elements in the given "sequence", and stores
// the results in "count_map" with element as the key and count as the value.
//
// Example:
//   vector<string> v = {"a", "b", "c", "a", "b"};
//   map<string, int> m;
//   AddTokenCounts(v, 1, &m);
//   assert(m["a"] == 2);
//   assert(m["b"] == 2);
//   assert(m["c"] == 1);
// TODO(liguancheng02): Add UT for this function.
template <typename Sequence, typename Collection>
void AddTokenCounts(const Sequence& sequence,
                    const typename Collection::value_type::second_type& increment,
                    Collection* const count_map) {
  CHECK(count_map != nullptr) << "invalid nullptr";
  for (typename Sequence::const_iterator it = sequence.begin(); it != sequence.end(); ++it) {
    typename Collection::value_type::second_type& value =
        LookupOrInsert(count_map, *it, typename Collection::value_type::second_type());
    value += increment;
  }
}

//
// Misc Utility Functions
//

// Updates the value associated with the given key. If the key was not already
// present, then the key-value pair are inserted and "previous" is unchanged. If
// the key was already present, the value is updated and "*previous" will
// contain a copy of the old value.
//
// InsertOrReturnExisting has complementary behavior that returns the
// address of an already existing value, rather than updating it.
template <class Collection>
bool UpdateReturnCopy(Collection* const collection,
                      const typename Collection::value_type::first_type& key,
                      const typename Collection::value_type::second_type& value,
                      typename Collection::value_type::second_type* const previous = nullptr) {
  CHECK(collection != nullptr) << "invalid nullptr";
  std::pair<typename Collection::iterator, bool> ret =
      collection->insert(typename Collection::value_type(key, value));
  if (ret.second) {
    return false;
  }
  // return copy
  if (previous) {
    *previous = ret.first->second;
  }
  // update
  ret.first->second = value;
  return true;
}

// Same as above except that the key and value are passed as a pair.
template <class Collection>
bool UpdateReturnCopy(Collection* const collection,
                      const typename Collection::value_type& vt,
                      typename Collection::value_type::second_type* const previous = nullptr) {
  CHECK(collection != nullptr) << "invalid nullptr";
  return UpdateReturnCopy(collection, vt.first, vt.second, previous);
}

// Tries to insert the given key-value pair into the collection. Returns nullptr if
// the insert succeeds. Otherwise, returns a pointer to the existing value.
//
// This complements UpdateReturnCopy in that it allows to update only after
// verifying the old value and still insert quickly without having to look up
// twice. Unlike UpdateReturnCopy this also does not come with the issue of an
// undefined previous* in case new value was inserted.
template <class Collection>
typename Collection::value_type::second_type* InsertOrReturnExisting(
    Collection* const collection, const typename Collection::value_type& vt) {
  CHECK(collection != nullptr) << "invalid nullptr";
  std::pair<typename Collection::iterator, bool> ret = collection->insert(vt);
  // Return nullptr if inserted since no existing previous value, otherwise the ptr of previous.
  return ret.second ? nullptr : &ret.first->second;
}

// Same as above, except for explicit key and value.
template <class Collection>
typename Collection::value_type::second_type* InsertOrReturnExisting(
    Collection* const collection,
    const typename Collection::value_type::first_type& key,
    const typename Collection::value_type::second_type& value) {
  CHECK(collection != nullptr) << "invalid nullptr";
  return InsertOrReturnExisting(collection, typename Collection::value_type(key, value));
}

// Erases the collection item identified by the given key, and returns true
// if the existed key-value pair erased, at the same time assign the existing value
// to the input previous pointer (if its value is not nullptr).
template <class Collection>
bool EraseKeyReturnPreviousValue(
    Collection* const collection,
    const typename Collection::value_type::first_type& key,
    typename Collection::value_type::second_type* const previous = nullptr) {
  CHECK(collection != nullptr) << "invalid nullptr";
  typename Collection::iterator it = collection->find(key);
  if (it == collection->end()) {
    return false;
  }
  if (previous) {
    *previous = it->second;
  }
  collection->erase(it);
  return true;
}

// Inserts all the keys from map_container into key_container, which must
// support insert(MapContainer::key_type).
//
// Note: any initial contents of the key_container are not cleared.
template <class MapContainer, class KeyContainer>
void InsertKeysFromMap(const MapContainer& map_container, KeyContainer* const key_container) {
  CHECK(key_container != nullptr) << "invalid nullptr";
  for (typename MapContainer::const_iterator it = map_container.begin(); it != map_container.end();
       ++it) {
    key_container->insert(it->first);
  }
}

// Inserts all the values from map_container into value_container, which must
// support insert(MapContainer::value_type).
//
// Note: any initial contents of the key_container are not cleared.
//   The MapContainer::value_type must be able to be stored in the new container.
template <class MapContainer, class ValueContainer>
void InsertValuesFromMap(const MapContainer& map_container, ValueContainer* const value_container) {
  CHECK(value_container != nullptr) << "invalid nullptr";
  for (typename MapContainer::const_iterator it = map_container.begin(); it != map_container.end();
       ++it) {
    value_container->insert(it->second);
  }
}

// Appends all the keys from map_container into key_container, which must
// support push_back(MapContainer::key_type).
//
// Note: any initial contents of the key_container are not cleared.
template <class MapContainer, class KeyContainer>
void AppendKeysFromMap(const MapContainer& map_container, KeyContainer* const key_container) {
  CHECK(key_container != nullptr) << "invalid nullptr";
  for (typename MapContainer::const_iterator it = map_container.begin(); it != map_container.end();
       ++it) {
    key_container->push_back(it->first);
  }
}

// A more specialized overload of AppendKeysFromMap to optimize reallocations
// for the common case in which we're appending keys to a vector and hence can
// (and sometimes should) call reserve() first.
//
// (It would be possible to play SFINAE games to call reserve() for any
// container that supports it, but this seems to get us 99% of what we need
// without the complexity of a SFINAE-based solution.)
template <class MapContainer, class KeyType>
void AppendKeysFromMap(const MapContainer& map_container,
                       std::vector<KeyType>* const key_container) {
  CHECK(key_container != nullptr) << "invalid nullptr";
  // We now have the opportunity to call reserve(). Calling reserve() every
  // time is a bad idea for some use cases: libstdc++'s implementation of
  // vector<>::reserve() resizes the vector's backing store to exactly the
  // given size (unless it's already at least that big). Because of this,
  // the use case that involves appending a lot of small maps (total size
  // N) one by one to a vector would be O(N^2). But never calling reserve()
  // loses the opportunity to improve the use case of adding from a large
  // map to an empty vector (this improves performance by up to 33%). A
  // number of heuristics are possible; see the discussion in
  // cl/34081696. Here we use the simplest one.
  if (key_container->empty()) {
    key_container->reserve(map_container.size());
  }
  for (typename MapContainer::const_iterator it = map_container.begin(); it != map_container.end();
       ++it) {
    key_container->push_back(it->first);
  }
}

// Appends all the keys associated with the given value from map_container into key_container, which
// must support push_back(MapContainer::key_type).
//
// Note: any initial contents of the key_container are not cleared.
// Unstable results on unordered_map are expected.
template <class MapContainer, class KeyContainer, class ValueType>
void AppendKeysFromMapByValue(const MapContainer& map_container,
                              const ValueType& value,
                              KeyContainer* const key_container) {
  CHECK(key_container != nullptr) << "invalid nullptr";
  for (typename MapContainer::const_iterator it = map_container.begin(); it != map_container.end();
       ++it) {
    if (it->second == value) {
      key_container->push_back(it->first);
    }
  }
}

// Inserts all the values from map_container into value_container, which must
// support push_back(MapContainer::mapped_type).
//
// Note: any initial contents of the value_container are not cleared.
template <class MapContainer, class ValueContainer>
void AppendValuesFromMap(const MapContainer& map_container, ValueContainer* const value_container) {
  CHECK(value_container != nullptr) << "invalid nullptr";
  for (typename MapContainer::const_iterator it = map_container.begin(); it != map_container.end();
       ++it) {
    value_container->push_back(it->second);
  }
}

// A more specialized overload of AppendValuesFromMap to optimize reallocations
// for the common case in which we're appending values to a vector and hence
// can (and sometimes should) call reserve() first.
//
// (It would be possible to play SFINAE games to call reserve() for any
// container that supports it, but this seems to get us 99% of what we need
// without the complexity of a SFINAE-based solution.)
template <class MapContainer, class ValueType>
void AppendValuesFromMap(const MapContainer& map_container,
                         std::vector<ValueType>* const value_container) {
  CHECK(value_container != nullptr) << "invalid nullptr";
  // See AppendKeysFromMap for why this is done.
  if (value_container->empty()) {
    value_container->reserve(map_container.size());
  }
  for (typename MapContainer::const_iterator it = map_container.begin(); it != map_container.end();
       ++it) {
    value_container->push_back(it->second);
  }
}

}  // namespace base
