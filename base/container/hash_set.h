// Copyright @2024 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include "absl/container/flat_hash_set.h"
#include "absl/container/node_hash_set.h"
#include "absl/hash/hash.h"

namespace base {

template <typename T,
          typename Hash = absl::container_internal::hash_default_hash<T>,
          typename EqualTo = absl::container_internal::hash_default_eq<T>>
using NodeHashSet = absl::node_hash_set<T, Hash, EqualTo>;

template <typename T,
          typename Hash = absl::container_internal::hash_default_hash<T>,
          typename EqualTo = absl::container_internal::hash_default_eq<T>>
using FlatHashSet = absl::flat_hash_set<T, Hash, EqualTo>;

}  // namespace base
