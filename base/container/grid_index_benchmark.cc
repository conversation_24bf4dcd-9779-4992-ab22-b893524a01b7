// Copyright @2025 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>
//          Na<PERSON><PERSON><PERSON> (<EMAIL>)

#include <utility>

#include "benchmark/benchmark.h"

#include "base/container/grid_index.h"

namespace base {
namespace container {
namespace {

struct Cell {
  int i = 0;
  double d = 0.0;
  float f = 0.0;
  int x = 0;
  int y = 0;
  int v = 0;
};

}  // namespace

class GridIndexHashMapBenchmark : public benchmark::Fixture {};

class GridIndexArray2dBenchmark : public benchmark::Fixture {};

BENCHMARK_DEFINE_F(GridIndexHashMapBenchmark, Construct)
(benchmark::State& state) {  // NOLINT
  const math::Vector2d p0(0.0, 0.0);
  const math::Vector2d p1(state.range(0), state.range(0));
  while (state.KeepRunning()) {
    const GridIndexHashMap<Cell> grid_index(p0, p1, 1.0, Cell());
    benchmark::ClobberMemory();
    benchmark::DoNotOptimize(grid_index);
  }
}
BENCHMARK_REGISTER_F(GridIndexHashMapBenchmark, Construct)
    ->RangeMultiplier(2)
    ->Ranges({{100, 400}});

BENCHMARK_DEFINE_F(GridIndexArray2dBenchmark, Construct)
(benchmark::State& state) {  // NOLINT
  const math::Vector2d p0(0.0, 0.0);
  const math::Vector2d p1(state.range(0), state.range(0));
  while (state.KeepRunning()) {
    const GridIndexArray2d<Cell> grid_index(p0, p1, 1.0, Cell());
    benchmark::ClobberMemory();
    benchmark::DoNotOptimize(grid_index);
  }
}
BENCHMARK_REGISTER_F(GridIndexArray2dBenchmark, Construct)
    ->RangeMultiplier(2)
    ->Ranges({{100, 400}});

// TODO(wangnaizheng): refactor this benchmark.
void BM_GRID_INDEX_INSERT(benchmark::State& state,    // NOLINT
                          bool clear_after_insert) {  // NOLINT
  constexpr int kXSize = 500;
  constexpr int kYSize = 500;
  const math::Vector2d min_point(-100.0, -100.0);
  const math::Vector2d max_point(+100.0, +100.0);
  const double resolution = 0.2;
  GridIndex<int> grid_index(min_point, max_point, resolution);
  for (auto s : state) {
    for (int x = 0; x < kXSize; ++x) {
      for (int y = 0; y < kYSize; ++y) {
        int* mutable_cell = grid_index.GetMutableByIndex({x, y});
        CHECK(mutable_cell != nullptr);
      }
    }
    if (clear_after_insert) {
      grid_index.Clear();
    }
  }
}

BENCHMARK_CAPTURE(BM_GRID_INDEX_INSERT, with_clear, true);
BENCHMARK_CAPTURE(BM_GRID_INDEX_INSERT, without_clear, false);

}  // namespace container
}  // namespace base

BENCHMARK_MAIN();

// clang-format off
/*
Run on (16 X 5000 MHz CPU s)
CPU Caches:
  L1 Data 32K (x8)
  L1 Instruction 32K (x8)
  L2 Unified 256K (x8)
  L3 Unified 16384K (x1)
Load Average: 2.06, 4.54, 7.00
***WARNING*** CPU scaling is enabled, the benchmark real time measurements may be noisy and will incur extra overhead.
----------------------------------------------------------------------------------
Benchmark                                        Time             CPU   Iterations
----------------------------------------------------------------------------------
GridIndexHashMapBenchmark/Construct/100       32.3 ns         32.3 ns     22055388
GridIndexHashMapBenchmark/Construct/128       34.2 ns         34.2 ns     22161566
GridIndexHashMapBenchmark/Construct/256       32.4 ns         32.4 ns     21980228
GridIndexHashMapBenchmark/Construct/400       32.4 ns         32.4 ns     21545629
GridIndexArray2dBenchmark/Construct/100       7366 ns         7366 ns        93685
GridIndexArray2dBenchmark/Construct/128      12674 ns        12674 ns        54255
GridIndexArray2dBenchmark/Construct/256      51225 ns        51224 ns        13686
GridIndexArray2dBenchmark/Construct/400     121488 ns       121483 ns         5677
BM_GRID_INDEX_INSERT/with_clear           10217831 ns     10217505 ns           64
BM_GRID_INDEX_INSERT/without_clear         9545018 ns      9544640 ns           78
*/
