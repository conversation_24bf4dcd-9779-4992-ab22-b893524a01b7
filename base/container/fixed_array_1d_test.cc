// Copyright @2024 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "gtest/gtest.h"

#include "base/container/fixed_array_1d.h"

namespace base {
namespace container {
namespace {

constexpr int kSize = 1080;

struct IdWithIteration {
  int id = -1;
  int iteration = -1;
};

struct IdWithIterationTrival {
  int id;
  int iteration;
};

}  // namespace

TEST(FixArray1dTest, BasicTest) {
  {
    FixedArray1d<IdWithIteration> fix_array_1d(kSize);
    fix_array_1d[100] = IdWithIteration{5, 6};
    const IdWithIteration* data = fix_array_1d.data();
    const IdWithIteration& test_value = data[100];
    EXPECT_EQ(5, test_value.id);
    EXPECT_EQ(6, test_value.iteration);
    EXPECT_EQ(kSize, fix_array_1d.size());

    EXPECT_TRUE(fix_array_1d.IsIndexValid(1079));
    EXPECT_FALSE(fix_array_1d.IsIndexValid(1080));
    EXPECT_FALSE(fix_array_1d.IsIndexValid(-1));
  }
  {
    FixedArray1d<IdWithIterationTrival> fix_array_1d(kSize);
    fix_array_1d[100] = IdWithIterationTrival{5, 6};
    const IdWithIterationTrival* data = fix_array_1d.data();
    const IdWithIterationTrival& test_value = data[100];
    EXPECT_EQ(5, test_value.id);
    EXPECT_EQ(6, test_value.iteration);
    InitFixedArray(&fix_array_1d);
    EXPECT_EQ(0, test_value.id);
    EXPECT_EQ(0, test_value.iteration);
  }
}

}  // namespace container
}  // namespace base
