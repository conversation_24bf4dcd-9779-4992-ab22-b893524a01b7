// Copyright @2025 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <algorithm>
#include <deque>
#include <utility>
#include <vector>

#include "glog/logging.h"

#include "base/common/macros.h"
#include "base/common/optional.h"
#include "base/container/hash_map.h"
#include "base/math/packed_index.h"
#include "base/strings/macros.h"

namespace base {
namespace container {

template <typename T>
class GridCellsHashMap final {
 public:
  GridCellsHashMap() = default;
  GridCellsHashMap(int x_size, int y_size);
  GridCellsHashMap(int x_size, int y_size, T default_value);

  const base::FlatHashMap<int64_t, T*>& index_to_cells() const { return cells_; }
  int x_size() const { return x_size_; }
  int y_size() const { return y_size_; }

  void Clear();

  const T* GetByIndex(const math::Vector2i& index) const;
  T* GetMutableByIndex(const math::Vector2i& index);

 private:
  struct Bucket {
    int unused = 0;
    std::vector<T> slots;
  };
  T* NewCell();

  int x_size_ = 0;
  int y_size_ = 0;
  base::FlatHashMap<int64_t, T*> cells_;
  base::Optional<T> default_value_;

  std::deque<Bucket> buckets_;

  DISALLOW_COPY_AND_ASSIGN(GridCellsHashMap);
};

template <typename T>
GridCellsHashMap<T>::GridCellsHashMap(int x_size, int y_size) : x_size_(x_size), y_size_(y_size) {
  CHECK_GE(x_size_, 0);
  CHECK_GE(y_size_, 0);
}

template <typename T>
GridCellsHashMap<T>::GridCellsHashMap(int x_size, int y_size, T default_value)
    : GridCellsHashMap(x_size, y_size) {
  default_value_ = std::move(default_value);
}

template <typename T>
void GridCellsHashMap<T>::Clear() {
  cells_.clear();
  buckets_.clear();
}

template <typename T>
inline const T* GridCellsHashMap<T>::GetByIndex(const math::Vector2i& index) const {
  const int64_t packed_index = math::PackedIndex(index);
  const auto it = cells_.find(packed_index);
  return it != cells_.end() ? it->second : nullptr;
}

template <typename T>
inline T* GridCellsHashMap<T>::GetMutableByIndex(const math::Vector2i& index) {
  const int64_t packed_index = math::PackedIndex(index);
  auto it = cells_.find(packed_index);
  if (it != cells_.end()) {
    return it->second;
  }
  T* p = NewCell();
  cells_[packed_index] = p;
  return p;
}

template <typename T>
T* GridCellsHashMap<T>::NewCell() {
  constexpr int kMinBucketCapacity = 32;
  constexpr int kMaxBucketCapacity = 8192;
  if (buckets_.empty() || buckets_[0].unused == 0) {
    const int capacity =
        (buckets_.empty()
             ? kMinBucketCapacity
             : std::min(2 * static_cast<int>(buckets_[0].slots.size()), kMaxBucketCapacity));
    buckets_.emplace_front();
    buckets_[0].unused = capacity;
    if (default_value_ == base::none) {
      buckets_[0].slots.resize(capacity);
    } else {
      buckets_[0].slots.resize(capacity, *default_value_);
    }
  }
  Bucket& bucket = buckets_[0];
  return &bucket.slots[--bucket.unused];
}

template <typename T>
class GridCellsArray2d final {
 public:
  GridCellsArray2d() = default;
  GridCellsArray2d(int x_size, int y_size);
  GridCellsArray2d(int x_size, int y_size, T default_value);

  const std::vector<T>& cells_array() const { return cells_; }
  int x_size() const { return x_size_; }
  int y_size() const { return y_size_; }

  void Clear();

  const T* GetByIndex(const math::Vector2i& index) const;
  T* GetMutableByIndex(const math::Vector2i& index);

  int PackedOffset(const math::Vector2i& index) const;
  math::Vector2i UnpackOffset(int64_t packed_offset) const;

 private:
  int x_size_ = 0;
  int y_size_ = 0;
  std::vector<T> cells_;
  base::Optional<T> default_value_;

  DISALLOW_COPY_AND_ASSIGN(GridCellsArray2d);
};

template <typename T>
GridCellsArray2d<T>::GridCellsArray2d(int x_size, int y_size) : x_size_(x_size), y_size_(y_size) {
  CHECK_GE(x_size_, 0);
  CHECK_GE(y_size_, 0);
  cells_.resize(x_size_ * y_size_);
}

template <typename T>
GridCellsArray2d<T>::GridCellsArray2d(int x_size, int y_size, T default_value)
    : x_size_(x_size), y_size_(y_size) {
  CHECK_GE(x_size_, 0);
  CHECK_GE(y_size_, 0);
  default_value_ = std::move(default_value);
  cells_.resize(x_size_ * y_size_, *default_value_);
}

template <typename T>
void GridCellsArray2d<T>::Clear() {
  cells_.clear();
  if (default_value_ == base::none) {
    cells_.resize(x_size_ * y_size_);
  } else {
    cells_.resize(x_size_ * y_size_, *default_value_);
  }
}

template <typename T>
inline const T* GridCellsArray2d<T>::GetByIndex(const math::Vector2i& index) const {
  return &cells_[PackedOffset(index)];
}

template <typename T>
inline T* GridCellsArray2d<T>::GetMutableByIndex(const math::Vector2i& index) {
  return &cells_[PackedOffset(index)];
}

template <typename T>
inline int GridCellsArray2d<T>::PackedOffset(const math::Vector2i& index) const {
  return index.x * y_size_ + index.y;
}

template <typename T>
inline math::Vector2i GridCellsArray2d<T>::UnpackOffset(int64_t packed_offset) const {
  return math::Vector2i(packed_offset / y_size_, packed_offset % y_size_);
}

}  // namespace container
}  // namespace base
