// Copyright @2023 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/container/keyed_double_buffer.h"

#include <functional>
#include <memory>
#include <string>
#include <vector>

#include "glog/logging.h"
#include "gtest/gtest.h"

#include "base/strings/format.h"

namespace base {

using TestTask = std::function<void()>;

TEST(KeyedDoubleBufferTest, BasicTest) {
  const std::unordered_map<std::string, int> buffer_size_map = {
      {"buffer1", 10},
  };
  KeyedDoubleBuffer<TestTask> keyed_double_buffer(buffer_size_map);
  int count = 0;
  for (int i = 0; i < 10; ++i) {
    auto task = [&count, i]() {
      if (count == i) {
        count++;
      }
    };
    EXPECT_TRUE(keyed_double_buffer.Push("buffer1", task));
  }
  std::vector<const KeyedDoubleBuffer<TestTask>::Buffer*> task_vec =
      keyed_double_buffer.TakeAllBuffer();
  for (const KeyedDoubleBuffer<TestTask>::Buffer* buffer : task_vec) {
    for (const TestTask& task : *buffer) {
      task();
    }
  }
  EXPECT_EQ(count, 10);
  EXPECT_TRUE(keyed_double_buffer.IsEmpty());
}

TEST(KeyedDoubleBufferTest, MultiBuffer) {
  const std::unordered_map<std::string, int> buffer_size_map = {
      {"buffer1", 200},
      {"buffer2", 200},
      {"buffer3", 200},
  };
  KeyedDoubleBuffer<TestTask> keyed_double_buffer(buffer_size_map);
  int count = 0;
  auto task = [&count]() { count++; };
  for (int i = 0; i < 100; ++i) {
    EXPECT_TRUE(keyed_double_buffer.Push("buffer1", task));
  }
  std::vector<const KeyedDoubleBuffer<TestTask>::Buffer*> task_vec_1 =
      keyed_double_buffer.TakeAllBuffer();
  for (const KeyedDoubleBuffer<TestTask>::Buffer* buffer : task_vec_1) {
    for (const TestTask& task : *buffer) {
      task();
    }
  }
  EXPECT_EQ(count, 100);
  for (int i = 0; i < 50; ++i) {
    EXPECT_TRUE(keyed_double_buffer.Push("buffer2", task));
    EXPECT_TRUE(keyed_double_buffer.Push("buffer3", task));
  }
  std::vector<const KeyedDoubleBuffer<TestTask>::Buffer*> task_vec_2 =
      keyed_double_buffer.TakeAllBuffer();
  for (const KeyedDoubleBuffer<TestTask>::Buffer* buffer : task_vec_2) {
    for (const TestTask& task : *buffer) {
      task();
    }
  }
  EXPECT_EQ(count, 200);
  EXPECT_TRUE(keyed_double_buffer.IsEmpty());
}

TEST(KeyedDoubleBufferTest, StopTest) {
  const std::unordered_map<std::string, int> buffer_size_map = {
      {"buffer1", 200},
      {"buffer2", 200},
      {"buffer3", 200},
  };
  KeyedDoubleBuffer<TestTask> keyed_double_buffer(buffer_size_map);
  int count = 0;
  auto task = [&count]() { count++; };
  for (int i = 0; i < 100; ++i) {
    EXPECT_TRUE(keyed_double_buffer.Push("buffer1", task));
  }
  keyed_double_buffer.Close();
  EXPECT_TRUE(keyed_double_buffer.IsClosed());
  std::vector<const KeyedDoubleBuffer<TestTask>::Buffer*> task_vec_1 =
      keyed_double_buffer.TakeAllBuffer();
  for (const KeyedDoubleBuffer<TestTask>::Buffer* buffer : task_vec_1) {
    for (const TestTask& task : *buffer) {
      task();
    }
  }
  EXPECT_EQ(count, 100);
  for (int i = 0; i < 50; ++i) {
    EXPECT_FALSE(keyed_double_buffer.Push("buffer2", task));
    EXPECT_FALSE(keyed_double_buffer.Push("buffer3", task));
  }
  std::vector<const KeyedDoubleBuffer<TestTask>::Buffer*> task_vec_2 =
      keyed_double_buffer.TakeAllBuffer();
  for (const KeyedDoubleBuffer<TestTask>::Buffer* buffer : task_vec_2) {
    for (const TestTask& task : *buffer) {
      task();
    }
  }
  EXPECT_EQ(count, 100);
  EXPECT_TRUE(keyed_double_buffer.IsEmpty());
}

TEST(KeyedDoubleBufferTest, DefaultBufferTest) {
  const std::unordered_map<std::string, int> buffer_size_map = {
      {"buffer1", 10},
  };
  KeyedDoubleBuffer<TestTask> keyed_double_buffer(
      buffer_size_map,
      KeyedDoubleBuffer<TestTask>::Options{.extra_default_buffer = true,
                                           .default_buffer_name = "default"});
  int count = 0;
  for (int i = 0; i < 10; ++i) {
    auto task = [&count, i]() {
      if (count == i) {
        count++;
      }
    };
    EXPECT_TRUE(keyed_double_buffer.Push("any", task));
  }
  const KeyedDoubleBuffer<TestTask>::Buffer& buffer = keyed_double_buffer.TakeBuffer("default");
  for (const TestTask& task : buffer) {
    task();
  }
  EXPECT_EQ(count, 10);
  EXPECT_TRUE(keyed_double_buffer.IsEmpty());
}

}  // namespace base
