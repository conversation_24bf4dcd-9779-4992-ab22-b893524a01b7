// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/container/grid_index.h"

#include "gmock/gmock.h"
#include "gtest/gtest.h"

#include "base/math/line_segment2d.h"
#include "base/testing/random.h"

namespace base {
namespace container {

using math::LineSegment2d;
using math::OrientedBox2d;
using math::Polygon2d;
using math::Vector2d;

TEST(GridIndexTest, Constructor) {
  {
    const GridIndex<int> grid_index({0.0, 1.0}, {2.0, 5.0}, 0.5);
    ASSERT_NEAR(grid_index.min_x(), 0.0, 1e-6);
    ASSERT_NEAR(grid_index.min_y(), 1.0, 1e-6);
    ASSERT_NEAR(grid_index.max_x(), 2.0, 1e-6);
    ASSERT_NEAR(grid_index.max_y(), 5.0, 1e-6);
    ASSERT_NEAR(grid_index.resolution(), 0.5, 1e-6);
    ASSERT_EQ(grid_index.x_size(), 5);
    ASSERT_EQ(grid_index.y_size(), 9);
  }
  {
    const GridIndex<int> grid_index(Vector2d(0.0, 5.0), Vector2d(2.0, 1.0), 0.5);
    ASSERT_NEAR(grid_index.min_x(), 0.0, 1e-6);
    ASSERT_NEAR(grid_index.min_y(), 1.0, 1e-6);
    ASSERT_NEAR(grid_index.max_x(), 2.0, 1e-6);
    ASSERT_NEAR(grid_index.max_y(), 5.0, 1e-6);
    ASSERT_NEAR(grid_index.resolution(), 0.5, 1e-6);
    ASSERT_EQ(grid_index.x_size(), 5);
    ASSERT_EQ(grid_index.y_size(), 9);
  }
  {
    const GridIndex<int> grid_index({0.0, 1.0}, {2.0, 5.0}, 0.5);
    ASSERT_EQ(grid_index.x_size(), 5);
    ASSERT_EQ(grid_index.y_size(), 9);
    ASSERT_EQ(grid_index.GetIndex(0.0, 1.0), math::Vector2i(0, 0));
    ASSERT_EQ(grid_index.GetIndex(0.0, 1.5), math::Vector2i(0, 1));
    ASSERT_EQ(grid_index.GetIndex(0.0, 0.5), base::none);
    ASSERT_EQ(grid_index.GetIndex(2.0, 5.5), base::none);
  }
}

TEST(GridIndexTest, GridXOrYChecked) {
  const GridIndex<int> grid_index({10.0, 10.0}, {20.0, 20.0}, 1.0);
  ASSERT_EQ(grid_index.GridXChecked(10.0), 0);
  ASSERT_EQ(grid_index.GridYChecked(10.0), 0);
  ASSERT_EQ(grid_index.GridXChecked(15.0), 5);
  ASSERT_EQ(grid_index.GridYChecked(15.0), 5);
  ASSERT_EQ(grid_index.GridXChecked(19.1), 9);
  ASSERT_EQ(grid_index.GridYChecked(10.1), 0);
  ASSERT_EQ(grid_index.GridXChecked(19.9), 9);
  ASSERT_EQ(grid_index.GridYChecked(10.9), 0);
}

TEST(GridIndexTest, GetAndGetMutable1) {
  GridIndex<int> grid_index({-20, -30}, {+20, +30}, 1.0);
  ASSERT_NEAR(grid_index.min_x(), -20.0, 1e-6);
  ASSERT_NEAR(grid_index.min_y(), -30.0, 1e-6);
  ASSERT_NEAR(grid_index.max_x(), +20.0, 1e-6);
  ASSERT_NEAR(grid_index.max_y(), +30.0, 1e-6);
  ASSERT_EQ(grid_index.x_size(), 41);
  ASSERT_EQ(grid_index.y_size(), 61);

  ASSERT_EQ(grid_index.Get(30.0, 0.0), nullptr);
  ASSERT_EQ(grid_index.GetMutable(30, 0.0), nullptr);
  ASSERT_EQ(grid_index.Get(0.0, 50.0), nullptr);
  ASSERT_EQ(grid_index.GetMutable(0.0, 50.0), nullptr);

  ASSERT_EQ(grid_index.Get(0.0, 2.0), nullptr);
  *CHECK_NOTNULL(grid_index.GetMutable(0.0, 2.0)) = 100;
  ASSERT_EQ(*CHECK_NOTNULL(grid_index.Get(0.0, 2.0)), 100);
  ASSERT_EQ(*CHECK_NOTNULL(grid_index.GetMutable(0.0, 2.0)), 100);
  ASSERT_EQ(*CHECK_NOTNULL(grid_index.Get(Vector2d(0.0, 2.0))), 100);
  ASSERT_EQ(*CHECK_NOTNULL(grid_index.GetMutable(Vector2d(0.0, 2.0))), 100);

  *CHECK_NOTNULL(grid_index.GetMutable(-20.0, +30.0)) = 10;
  *CHECK_NOTNULL(grid_index.GetMutable(-20.0, -30.0)) = 11;
  *CHECK_NOTNULL(grid_index.GetMutable(+20.0, +30.0)) = 12;
  *CHECK_NOTNULL(grid_index.GetMutable(+20.0, -30.0)) = 13;

  ASSERT_EQ(*CHECK_NOTNULL(grid_index.Get(-20.0, +30.0)), 10);
  ASSERT_EQ(*CHECK_NOTNULL(grid_index.Get(-20.0, -30.0)), 11);
  ASSERT_EQ(*CHECK_NOTNULL(grid_index.Get(+20.0, +30.0)), 12);
  ASSERT_EQ(*CHECK_NOTNULL(grid_index.Get(+20.0, -30.0)), 13);

  ASSERT_EQ(*CHECK_NOTNULL(grid_index.GetMutable(-20.0, +30.0)), 10);
  ASSERT_EQ(*CHECK_NOTNULL(grid_index.GetMutable(-20.0, -30.0)), 11);
  ASSERT_EQ(*CHECK_NOTNULL(grid_index.GetMutable(+20.0, +30.0)), 12);
  ASSERT_EQ(*CHECK_NOTNULL(grid_index.GetMutable(+20.0, -30.0)), 13);
}

TEST(GridIndexTest, GetAndGetMutable2) {
  GridIndex<int> grid_index({-20, -20}, {+20, +20}, 1.0);
  ASSERT_NEAR(grid_index.min_x(), -20.0, 1e-6);
  ASSERT_NEAR(grid_index.min_y(), -20.0, 1e-6);
  ASSERT_NEAR(grid_index.max_x(), +20.0, 1e-6);
  ASSERT_NEAR(grid_index.max_y(), +20.0, 1e-6);
  for (double x = -19.5; x <= 19.5; x += 1.0) {
    for (double y = -19.5; y <= 19.5; y += 1.0) {
      const int v = static_cast<int>(x * x * 13.0 + y * 7.0);
      *CHECK_NOTNULL(grid_index.GetMutable(x, y)) = v;
      ASSERT_EQ(*CHECK_NOTNULL(grid_index.Get(x, y)), v);
      ASSERT_EQ(*CHECK_NOTNULL(grid_index.GetMutable(x, y)), v);
    }
  }
}

TEST(GridIndexTest, ForEachIndexInRect) {
  GridIndex<int> grid_index({-0.5, -0.5}, {10.5, 10.5}, 1.0);
  const auto get_indices = [&grid_index](const Vector2d& p0, const Vector2d& p1) {
    std::vector<math::Vector2i> indices;
    grid_index.ForEachIndexInRect(p0, p1, [&indices](const math::Vector2i& index) {
      indices.emplace_back(index.x, index.y);
    });
    return indices;
  };
  ASSERT_THAT(get_indices(Vector2d(20.0, 0.0), Vector2d(11.0, 10.0)),
              testing::UnorderedElementsAre());
  ASSERT_THAT(get_indices(Vector2d(0.0, 0.0), Vector2d(2.0, 2.0)),
              testing::UnorderedElementsAreArray(std::vector<math::Vector2i>{
                  {0, 0}, {0, 1}, {0, 2}, {1, 0}, {1, 1}, {1, 2}, {2, 0}, {2, 1}, {2, 2}}));
  ASSERT_THAT(
      get_indices(Vector2d(0.0, 0.0), Vector2d(0.4, 1.5)),
      testing::UnorderedElementsAreArray(std::vector<math::Vector2i>{{0, 0}, {0, 1}, {0, 2}}));
  ASSERT_THAT(
      get_indices(Vector2d(-0.5, 0.0), Vector2d(1.5, -0.5)),
      testing::UnorderedElementsAreArray(std::vector<math::Vector2i>{{0, 0}, {1, 0}, {2, 0}}));
}

TEST(GridIndexTest, GetAndGetMutableInRect) {
  {
    GridIndex<int> grid_index({-0.5, -0.5}, {10.5, 10.5}, 1.0);
    std::vector<const int*> ptrs;
    for (int* ptr : grid_index.GetAllMutableInRect(Vector2d(0.0, 0.0), Vector2d(4.0, 3.0))) {
      *ptr = 0;
      ptrs.push_back(ptr);
    }
    for (int x = 0; x <= 10; ++x) {
      for (int y = 0; y <= 10; ++y) {
        if (x >= 0 && x <= 4 && y >= 0 && y <= 3) {
          ASSERT_EQ(*CHECK_NOTNULL(grid_index.Get(x, y)), 0);
        } else {
          ASSERT_EQ(grid_index.Get(x, y), nullptr);
        }
      }
    }
    ASSERT_THAT(grid_index.GetAllInRect(Vector2d(-10.0, +20.0), Vector2d(+20.0, -10.0)),
                testing::UnorderedElementsAreArray(ptrs));
  }
  {
    GridIndex<int> grid_index({-0.5, -0.5}, {10.5, 10.5}, 1.0);
    grid_index.ForEachMutableInRect(
        Vector2d(3.0, 6.0), Vector2d(2.0, 7.0), [](int* ptr) { *ptr = 3; });
    for (int x = 0; x <= 10; ++x) {
      for (int y = 0; y <= 10; ++y) {
        if (x >= 2 && x <= 3 && y >= 6 && y <= 7) {
          ASSERT_EQ(*CHECK_NOTNULL(grid_index.Get(x, y)), 3);
        } else {
          ASSERT_EQ(grid_index.Get(x, y), nullptr);
        }
      }
    }
    grid_index.ForEachInRect(math::Vector2d(0.0, 10.0),
                             math::Vector2d(10.0, 0.0),
                             [](const int* ptr) { ASSERT_EQ(*ptr, 3); });
  }
}

TEST(GridIndexTest, ForEachIndexOnSegment) {
  const GridIndex<int> grid_index({-0.5, -0.5}, {10.5, 10.5}, 1.0);
  const auto get_indices = [&grid_index](const Vector2d& p0, const Vector2d& p1) {
    std::vector<math::Vector2i> indices;
    grid_index.ForEachIndexOnSegment(p0, p1, [&indices](const math::Vector2i& index) {
      indices.emplace_back(index.x, index.y);
    });
    return indices;
  };
  ASSERT_THAT(get_indices(Vector2d(12.0, 0.0), Vector2d(11.0, 11.0)),
              testing::UnorderedElementsAre());
  ASSERT_THAT(get_indices(Vector2d(-0.5, -0.5), Vector2d(0.5, 3.0)),
              testing::UnorderedElementsAreArray(
                  std::vector<math::Vector2i>{{0, 0}, {0, 1}, {0, 2}, {0, 3}, {1, 3}}));
  ASSERT_THAT(
      get_indices(Vector2d(3.7, 1.5), Vector2d(5.5, 1.7)),
      testing::UnorderedElementsAreArray(std::vector<math::Vector2i>{{4, 2}, {5, 2}, {6, 2}}));
  ASSERT_THAT(get_indices(Vector2d(3.0, 0.0), Vector2d(2.0, 0.4)),
              testing::UnorderedElementsAreArray(std::vector<math::Vector2i>{{2, 0}, {3, 0}}));
}

TEST(GridIndexTest, GetAndGetMutableOnSegment) {
  const std::vector<LineSegment2d> segments = {
      LineSegment2d(Vector2d(0.3, -0.1), Vector2d(11.2, 0.1)),
      LineSegment2d(Vector2d(-5.0, 3.0), Vector2d(3.0, -5.0)),
      LineSegment2d(Vector2d(0.1, 0.1), Vector2d(9.4, 0.0)),
      LineSegment2d(Vector2d(-3.5, 0.4), Vector2d(10.7, -3.3)),
      LineSegment2d(Vector2d(1.2, 2.3), Vector2d(6.9, 2.4)),
      LineSegment2d(Vector2d(0.1, 0.0), Vector2d(9.0, 0.0)),
      LineSegment2d(Vector2d(11.2, 2.3), Vector2d(0.5, 9.6)),
      LineSegment2d(Vector2d(9.7, -6.3), Vector2d(-3.5, 3.9)),
      LineSegment2d(Vector2d(4.7, -0.8), Vector2d(9.5, 12.3)),
      LineSegment2d(Vector2d(3.7, 11.5), Vector2d(9.3, 6.7)),
      LineSegment2d(Vector2d(-1.7, 1.1), Vector2d(11.1, 1.1)),
      LineSegment2d(Vector2d(0.3, 1.2), Vector2d(0.4, 10.5)),
      LineSegment2d(Vector2d(0.2, 0.3), Vector2d(0.3, 0.8)),
  };
  GridIndex<int> grid_index({0.0, 0.0}, {10.0, 10.0}, 1.0);
  for (int i = 0; i < static_cast<int>(segments.size()); ++i) {
    for (int* ptr : grid_index.GetAllMutableOnSegment(segments[i].start(), segments[i].end())) {
      (*ptr) |= (1 << i);
    }
  }
  for (double x = 0.0; x < 10.0; x += 1.0) {
    for (double y = 0.0; y < 10.0; y += 1.0) {
      OrientedBox2d grid = OrientedBox2d::FromAABox(Vector2d(x, y), Vector2d(x + 1.0, y + 1.0));
      int expected = 0;
      for (int i = 0; i < static_cast<int>(segments.size()); ++i) {
        if (grid.HasOverlap(segments[i])) {
          expected |= (1 << i);
        }
      }
      if (expected == 0) {
        ASSERT_EQ(nullptr, grid_index.Get(x + 0.5, y + 0.5));
      } else {
        ASSERT_NE(nullptr, grid_index.Get(x + 0.5, y + 0.5));
        ASSERT_EQ(*CHECK_NOTNULL(grid_index.Get(x + 0.5, y + 0.5)), expected);
      }
    }
  }
}

TEST(GridIndexTest, ForEachIndexInBox) {
  GridIndex<int> grid_index({-0.5, -0.5}, {10.5, 10.5}, 1.0);
  const auto get_indices =
      [&grid_index](const Vector2d& center, double heading, double length, double width) {
        OrientedBox2d box(center, heading, length, width);
        std::vector<math::Vector2i> indices;
        grid_index.ForEachIndexInBox(box, [&indices](const math::Vector2i& index) {
          indices.emplace_back(index.x, index.y);
        });
        return indices;
      };
  ASSERT_THAT(get_indices(Vector2d(0.0, 0.0), 0.0, 0.5, 0.5),
              testing::UnorderedElementsAreArray(std::vector<math::Vector2i>{{0, 0}}));
  ASSERT_THAT(get_indices(Vector2d(1.0, 1.0), 0.0, 1.0, 1.0),
              testing::UnorderedElementsAreArray(
                  std::vector<math::Vector2i>{{1, 1}, {1, 2}, {2, 1}, {2, 2}}));
  ASSERT_THAT(get_indices(Vector2d(1.0, 1.0), 0.0, 1.2, 1.2),
              testing::UnorderedElementsAreArray(std::vector<math::Vector2i>{
                  {1, 1}, {1, 2}, {2, 1}, {2, 2}, {0, 1}, {1, 0}, {0, 0}, {0, 2}, {2, 0}}));
  ASSERT_THAT(get_indices(Vector2d(1.0, 1.0), 0.8, 1.2, 1.2),
              testing::UnorderedElementsAreArray(
                  std::vector<math::Vector2i>{{1, 1}, {1, 2}, {2, 1}, {1, 0}, {0, 1}}));
}

TEST(GridIndexTest, GetAndGetMutableInBox) {
  const std::vector<OrientedBox2d> boxes = {
      OrientedBox2d(Vector2d(6.1, 11.0), 1.1, 6.7, 1.7),
      OrientedBox2d(Vector2d(2.5, 6.8), 0.6, 2.1, 4.0),
      OrientedBox2d(Vector2d(-2.0, 11.0), 0.6, 6.4, 0.6),
      OrientedBox2d(Vector2d(11.8, 11.5), 2.1, 4.6, 0.9),
      OrientedBox2d(Vector2d(7.1, 5.6), 1.5, 3.2, 4.6),
      OrientedBox2d(Vector2d(8.3, 1.1), 2.9, 2.4, 1.9),
      OrientedBox2d(Vector2d(11.5, -0.8), 1.7, 6.6, 5.3),
      OrientedBox2d(Vector2d(11.4, 3.6), 2.3, 6.0, 2.1),
      OrientedBox2d(Vector2d(-1.7, 4.7), 3.0, 5.9, 3.5),
      OrientedBox2d(Vector2d(4.0, -0.8), 0.3, 5.2, 5.8),
      OrientedBox2d(Vector2d(7.1, -1.8), 2.4, 1.4, 6.1),
      OrientedBox2d(Vector2d(-0.8, 7.0), 0.3, 3.8, 4.0),
      OrientedBox2d(Vector2d(11.7, 1.9), 0.7, 0.4, 0.7),
  };
  GridIndex<int> grid_index({0.0, 0.0}, {10.0, 10.0}, 1.0);
  for (int i = 0; i < static_cast<int>(boxes.size()); ++i) {
    for (int* ptr : grid_index.GetAllMutableInBox(boxes[i])) {
      (*ptr) |= (1 << i);
    }
  }
  for (double x = 0.0; x < 10.0; x += 1.0) {
    for (double y = 0.0; y < 10.0; y += 1.0) {
      OrientedBox2d grid = OrientedBox2d::FromAABox(Vector2d(x, y), Vector2d(x + 1.0, y + 1.0));
      int expected = 0;
      for (int i = 0; i < static_cast<int>(boxes.size()); ++i) {
        if (grid.HasOverlap(boxes[i])) {
          expected |= (1 << i);
        }
      }
      if (expected == 0) {
        ASSERT_EQ(nullptr, grid_index.Get(x + 0.5, y + 0.5));
      } else {
        ASSERT_NE(nullptr, grid_index.Get(x + 0.5, y + 0.5));
        ASSERT_EQ(*CHECK_NOTNULL(grid_index.Get(x + 0.5, y + 0.5)), expected);
      }
    }
  }
}

TEST(GridIndexTest, ForEachIndexInCircle) {
  GridIndex<int> grid_index({-0.5, -0.5}, {10.5, 10.5}, 1.0);
  const auto get_indices = [&grid_index](const Vector2d& center, double radius) {
    std::vector<math::Vector2i> indices;
    grid_index.ForEachIndexInCircle(center, radius, [&indices](const math::Vector2i& index) {
      indices.emplace_back(index.x, index.y);
    });
    return indices;
  };
  ASSERT_THAT(get_indices(Vector2d(-0.7, -0.7), 0.1), testing::UnorderedElementsAre());
  ASSERT_THAT(get_indices(Vector2d(-0.7, -0.7), 0.3),
              testing::UnorderedElementsAreArray(std::vector<math::Vector2i>{{0, 0}}));
  ASSERT_THAT(
      get_indices(Vector2d(-0.7, -0.7), 1.3),
      testing::UnorderedElementsAreArray(std::vector<math::Vector2i>{{0, 0}, {0, 1}, {1, 0}}));
  ASSERT_THAT(get_indices(Vector2d(-0.7, -0.7), 1.7),
              testing::UnorderedElementsAreArray(
                  std::vector<math::Vector2i>{{0, 0}, {0, 1}, {1, 0}, {1, 1}}));
}

TEST(GridIndexTest, GetAndGetMutableInCircle) {
  const std::vector<std::pair<Vector2d, double>> circles = {
      {Vector2d(-1.9, 11.3), 1.7},
      {Vector2d(6.0, -0.8), 5.3},
      {Vector2d(4.4, 2.5), 0.6},
      {Vector2d(4.7, 7.5), 5.2},
      {Vector2d(10.3, 5.8), 6.4},
      {Vector2d(1.4, 3.3), 2.7},
      {Vector2d(7.6, 8.0), 1.1},
      {Vector2d(5.9, 10.5), 3.7},
      {Vector2d(5.7, 5.2), 1.5},
      {Vector2d(-0.8, 6.9), 4.0},
      {Vector2d(6.9, 3.1), 3.2},
      {Vector2d(4.5, 6.7), 6.0},
      {Vector2d(-0.7, 4.5), 2.2},
      {Vector2d(5.3, 1.0), 5.2},
      {Vector2d(2.1, 1.5), 2.3},
  };
  GridIndex<int> grid_index({0.0, 0.0}, {10.0, 10.0}, 1.0);
  for (int i = 0; i < static_cast<int>(circles.size()); ++i) {
    for (int* ptr : grid_index.GetAllMutableInCircle(circles[i].first, circles[i].second)) {
      (*ptr) |= (1 << i);
    }
  }
  for (double x = 0.0; x < 10.0; x += 1.0) {
    for (double y = 0.0; y < 10.0; y += 1.0) {
      OrientedBox2d grid = OrientedBox2d::FromAABox(Vector2d(x, y), Vector2d(x + 1.0, y + 1.0));
      int expected = 0;
      for (int i = 0; i < static_cast<int>(circles.size()); ++i) {
        if (grid.DistanceTo(circles[i].first) <= circles[i].second) {
          expected |= (1 << i);
        }
      }
      if (expected == 0) {
        ASSERT_EQ(nullptr, grid_index.Get(x + 0.5, y + 0.5));
      } else {
        ASSERT_NE(nullptr, grid_index.Get(x + 0.5, y + 0.5));
        ASSERT_EQ(*CHECK_NOTNULL(grid_index.Get(x + 0.5, y + 0.5)), expected);
      }
    }
  }
}

TEST(GridIndexTest, ForEachIndexInPolygon) {
  GridIndex<int> grid_index({-0.5, -0.5}, {10.5, 10.5}, 1.0);
  const auto get_indices = [&grid_index](const std::vector<Vector2d>& points) {
    std::vector<math::Vector2i> indices;
    grid_index.ForEachIndexInPolygon(Polygon2d(points), [&indices](const math::Vector2i& index) {
      indices.emplace_back(index.x, index.y);
    });
    return indices;
  };
  ASSERT_THAT(get_indices(std::vector<Vector2d>{{-1.0, -1.0}, {-1.0, -0.5}, {-0.5, -1.0}}),
              testing::UnorderedElementsAre());
  ASSERT_THAT(get_indices(std::vector<Vector2d>{{0.0, 0.0}, {0.0, 1.0}, {1.0, 1.0}, {1.0, 0.0}}),
              testing::UnorderedElementsAreArray(
                  std::vector<math::Vector2i>{{0, 0}, {0, 1}, {1, 0}, {1, 1}}));
  ASSERT_THAT(
      get_indices(
          std::vector<Vector2d>{{0.0, 0.0}, {1.0, 0.0}, {1.0, 0.1}, {0.1, 0.1}, {0.1, 1.0}}),
      testing::UnorderedElementsAreArray(std::vector<math::Vector2i>{{0, 0}, {0, 1}, {1, 0}}));
}

TEST(GridIndexTest, GetAndGetMutableInPolygon) {
  const std::vector<std::vector<Vector2d>> points = {
      {{8.7, 12.0}, {10.1, 1.2}, {4.1, 5.6}},
      {{6.9, 7.2}, {4.8, 9.5}, {-0.3, 0.8}},
      {{0.5, 7.0}, {8.2, -0.5}, {3.3, 8.1}},
      {{6.7, 3.4}, {10.4, 12.5}, {7.6, 9.9}},
      {{9.9, 11.2}, {-2.9, 1.1}, {2.1, 9.6}},
      {{5.7, 5.1}, {1.4, 0.7}, {2.4, -1.7}},
      {{9.2, 7.1}, {4.1, 9.1}, {-2.1, 8.8}},
      {{9.1, 0.3}, {11.2, 4.5}, {1.8, -0.8}},
      {{3.2, 6.5}, {4.3, 7.0}, {1.6, 8.9}},
      {{9.8, 3.9}, {10.5, 7.5}, {5.3, 5.2}},
      {{2.1, 3.2}, {9.8, 5.2}, {3.3, 6.3}, {1.2, 8.3}},
      {{0.3, 0.1}, {10.6, 3.9}, {9.4, 8.7}, {0.4, 8.5}, {3.4, 4.2}},
  };
  std::vector<Polygon2d> polygons;
  for (int i = 0; i < static_cast<int>(points.size()); ++i) {
    polygons.emplace_back(points[i]);
  }
  GridIndex<int> grid_index({0.0, 0.0}, {10.0, 10.0}, 1.0);
  for (int i = 0; i < static_cast<int>(polygons.size()); ++i) {
    for (int* ptr : grid_index.GetAllMutableInPolygon(polygons[i])) {
      (*ptr) |= (1 << i);
    }
  }
  for (double x = 0.0; x < 10.0; x += 1.0) {
    for (double y = 0.0; y < 10.0; y += 1.0) {
      OrientedBox2d grid = OrientedBox2d::FromAABox(Vector2d(x, y), Vector2d(x + 1.0, y + 1.0));
      int expected = 0;
      for (int i = 0; i < static_cast<int>(polygons.size()); ++i) {
        if (polygons[i].HasOverlap(Polygon2d(grid))) {
          expected |= (1 << i);
        }
      }
      if (expected == 0) {
        ASSERT_EQ(nullptr, grid_index.Get(x + 0.5, y + 0.5));
      } else {
        ASSERT_NE(nullptr, grid_index.Get(x + 0.5, y + 0.5));
        ASSERT_EQ(*CHECK_NOTNULL(grid_index.Get(x + 0.5, y + 0.5)), expected);
      }
    }
  }
}

}  // namespace container
}  // namespace base
