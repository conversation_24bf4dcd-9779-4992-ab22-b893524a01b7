// Copyright @2024 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include "absl/container/flat_hash_map.h"
#include "absl/container/node_hash_map.h"
#include "absl/hash/hash.h"

namespace base {

template <typename K,
          typename V,
          typename Hash = absl::container_internal::hash_default_hash<K>,
          typename EqualTo = absl::container_internal::hash_default_eq<K>>
using NodeHashMap = absl::node_hash_map<K, V, Hash, EqualTo>;

template <typename K,
          typename V,
          typename Hash = absl::container_internal::hash_default_hash<K>,
          typename EqualTo = absl::container_internal::hash_default_eq<K>>
using FlatHashMap = absl::flat_hash_map<K, V, Hash, EqualTo>;

}  // namespace base
