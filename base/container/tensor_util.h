// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <string>
#include <unsupported/Eigen/CXX11/Tensor>

#include "base/container/utils.h"

namespace strings {

template<typename ElemType, int N>
inline std::string ToString(const Eigen::DSizes<ElemType, N>& a) {
  return ToStringLinearLike(a);
}

}

namespace base {
namespace container {

} // namespace container
} // namespace base
