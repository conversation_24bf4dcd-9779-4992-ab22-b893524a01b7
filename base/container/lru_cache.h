// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <algorithm>
#include <functional>
#include <limits>
#include <list>
#include <unordered_map>
#include <utility>

#include "glog/logging.h"
#include "gtest/gtest.h"

#include "base/common/macros.h"
#include "base/common/optional.h"

namespace base {

template <typename Key, typename Value, typename KeyHash = std::hash<Key>,
          typename KeyEqualTo = std::equal_to<Key>>
class LruCache {
 public:
  struct KeyValuePair {
    KeyValuePair(const Key& key, Value value) : key(key), value(std::move(value)) {}

    const Key key;
    Value value;
  };

  explicit LruCache(int capacity = std::numeric_limits<int>::max(),
                    const KeyHash& key_hash = KeyHash(),
                    const KeyEqualTo& key_equal_to = KeyEqualTo())
      : capacity_(capacity),
        key_hash_(key_hash),
        key_equal_to_(key_equal_to),
        key_to_iter_map_(kMinBucketCount, key_hash_, key_equal_to_) {
    CHECK_GE(capacity_, 0);
  }

  virtual ~LruCache() = default;

  const std::list<KeyValuePair>& elements() const { return elements_; }

  int capacity() const { return capacity_; }

  int size() const { return elements_.size(); }

  bool IsEmpty() const { return elements_.empty(); }

  bool Contains(const Key& key) const { return key_to_iter_map_.count(&key) != 0; }

  void Clear() {
    key_to_iter_map_.clear();
    elements_.clear();
  }

  const Value& GetOrDie(const Key& key, bool promote = true) {
    return *CHECK_NOTNULL(Find(key, promote));
  }

  Value& GetMutableOrDie(const Key& key, bool promote = true) {
    return *CHECK_NOTNULL(FindMutable(key, promote));
  }

  const Value* Find(const Key& key, bool promote = true) {
    KeyValuePair* p = FindKeyValuePair(key, promote);
    return p != nullptr ? &(p->value) : nullptr;
  }

  Value* FindMutable(const Key& key, bool promote = true) {
    KeyValuePair* p = FindKeyValuePair(key, promote);
    return p != nullptr ? &(p->value) : nullptr;
  }

  // Returns true if a new element was inserted or false if an equivalent key already existed.
  bool Set(const Key& key, Value value, bool promote = true) {
    KeyValuePair* p = FindKeyValuePair(key, promote);
    if (p != nullptr) {
      p->value = std::move(value);
      return false;
    }
    elements_.emplace_back(key, std::move(value));
    auto iter = std::prev(elements_.end());
    CHECK(key_to_iter_map_.insert({&(iter->key), iter}).second);
    EvictElements();
    return true;
  }

  base::Optional<Value> Pop(const Key& key) {
    auto map_iter = key_to_iter_map_.find(&key);
    if (map_iter == key_to_iter_map_.end()) {
      return base::none;
    }
    auto iter = map_iter->second;
    key_to_iter_map_.erase(map_iter);
    Value value = std::move(iter->value);
    elements_.erase(iter);
    return value;
  }

  Value PopFront() {
    CHECK(!elements_.empty());
    auto iter = elements_.begin();
    Value value = std::move(iter->value);
    EvictElements(iter);
    return value;
  }

  Value PopBack() {
    CHECK(!elements_.empty());
    auto iter = std::prev(elements_.end());
    Value value = std::move(iter->value);
    EvictElements(iter);
    return value;
  }

 private:
  using ElementListIterator = typename std::list<KeyValuePair>::iterator;

  struct KeyHashWithPointer {
    explicit KeyHashWithPointer(const KeyHash& key_hash) : key_hash_(key_hash) {}

    size_t operator()(const Key* key) const { return key_hash_(*key); }

    KeyHash key_hash_;
  };

  struct KeyEqualToWithPointer {
    explicit KeyEqualToWithPointer(const KeyEqualTo& key_equal_to) : key_equal_to_(key_equal_to) {}

    bool operator()(const Key* lhs, const Key* rhs) const {
      return lhs == rhs || key_equal_to_(*lhs, *rhs);
    }

    KeyEqualTo key_equal_to_;
  };

  static constexpr int kMinBucketCount = 100;

  void EvictElements() {
    if (capacity_ < static_cast<int>(elements_.size())) {
      EvictElements(static_cast<int>(elements_.size()) - capacity_);
    }
  }

  void EvictElements(ElementListIterator iter) {
    CHECK_EQ(1, key_to_iter_map_.erase(&(iter->key)));
    elements_.erase(iter);
  }

  void EvictElements(int num_items) {
    CHECK_GE(num_items, 0);
    num_items = std::min(num_items, static_cast<int>(elements_.size()));
    for (int i = 0; i < num_items; ++i) {
      EvictElements(elements_.begin());
    }
  }

  KeyValuePair* FindKeyValuePair(const Key& key, bool promote) {
    auto map_iter = key_to_iter_map_.find(&key);
    if (map_iter == key_to_iter_map_.end()) {
      return nullptr;
    }
    auto iter = map_iter->second;
    if (promote) {
      elements_.splice(elements_.end(), elements_, iter);
    }
    return &(*iter);
  }

  int capacity_ = 0;

  KeyHashWithPointer key_hash_;
  KeyEqualToWithPointer key_equal_to_;

  std::list<KeyValuePair> elements_;
  std::unordered_map<const Key*, ElementListIterator, KeyHashWithPointer, KeyEqualToWithPointer>
      key_to_iter_map_;

  FRIEND_TEST(LruCacheTest, EvictElementsTest);

  DISALLOW_COPY_AND_ASSIGN(LruCache);
};

}  // namespace base
