// Copyright @2020 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <algorithm>
#include <vector>

#include "base/container/grid.h"

namespace base {
namespace container {

template <class T>
class RollingGrid : public Grid<T> {
 public:
  RollingGrid(double resolution, int rows, int cols);
  struct Options {
    double resolution;
    int rows;
    int cols;
    int array_r0;
    int array_c0;
    double global_x;
    double global_y;
  };
  RollingGrid(const Options& options, std::vector<T>&& data);
  ~RollingGrid() override = default;

  T* GetMutableValueByGlobalXY(const math::Vector2d& global_xy) override;
  T* GetMutableValueByRowColumn(const math::Vector2i& coord) override;
  math::Vector2i CellToRowColumn(const T* cell) const override;
  void Recenter(const math::Vector2d& global_xy) override;

  const T* GetValueByGlobalXY(const math::Vector2d& global_xy) const override;
  const T* GetValueByRowColumn(const math::Vector2i& coord) const override;

  void CopyFrom(const RollingGrid& other_grid) {
    CHECK(this->resolution() == other_grid.resolution())
        << "Grids with different resolution can NOT be copied";
    CHECK(this->rows() == other_grid.rows())
        << "Grids with different number of rows can NOT be copied";
    CHECK(this->cols() == other_grid.cols())
        << "Grids with different number of columns can NOT be copied";
    r0_ = other_grid.r0_;
    c0_ = other_grid.c0_;
    array_r0_ = other_grid.array_r0_;
    array_c0_ = other_grid.array_c0_;
    std::copy(other_grid.array_.begin(), other_grid.array_.end(), array_.begin());
  }

  void SetParam(int r0, int c0, int array_r0, int array_c0) {
    r0_ = r0;
    c0_ = c0;
    array_r0_ = array_r0;
    array_c0_ = array_c0;
  }

  int array_r0() const { return array_r0_; }
  int array_c0() const { return array_c0_; }

 private:
  inline int wrap(int x, int max) const { return x >= max ? (x - max) : (x < 0 ? (x + max) : x); }

  void AddColumnEast();
  void AddColumnWest();
  void AddRowNorth();
  void AddRowSouth();

  // Position of lower left corner in array.
  int array_r0_ = 0;
  int array_c0_ = 0;

  using Grid<T>::resolution_;
  using Grid<T>::rows_;
  using Grid<T>::cols_;
  using Grid<T>::r0_;
  using Grid<T>::c0_;
  using Grid<T>::array_;

  FRIEND_TEST(RollingGridTest, BuildTest);
  FRIEND_TEST(RollingGridTest, GetValue);
  FRIEND_TEST(RollingGridTest, MoveGrid);

  DISALLOW_COPY_AND_ASSIGN(RollingGrid);
};

template <class T>
RollingGrid<T>::RollingGrid(double resolution, int rows, int cols)
    : Grid<T>(resolution, rows, cols) {}

template <class T>
RollingGrid<T>::RollingGrid(const Options& options, std::vector<T>&& data)
    : Grid<T>(options.resolution, options.rows, options.cols),
      array_r0_(options.array_r0),
      array_c0_(options.array_c0) {
  r0_ = std::floor(options.global_x / resolution_) - rows_ / 2;
  c0_ = std::floor(options.global_y / resolution_) - cols_ / 2;
  array_ = std::move(data);
}

template <class T>
T* RollingGrid<T>::GetMutableValueByGlobalXY(const math::Vector2d& global_xy) {
  const math::Vector2i coord = Grid<T>::GlobalXYToRowColumn(global_xy);
  return GetMutableValueByRowColumn(coord);
}

template <class T>
T* RollingGrid<T>::GetMutableValueByRowColumn(const math::Vector2i& coord) {
  if (!Grid<T>::IsInRange(coord.x, coord.y)) {
    return nullptr;
  }
  const int row = wrap(coord.x + array_r0_, rows_);
  const int col = wrap(coord.y + array_c0_, cols_);
  return &(array_[row * cols_ + col]);
}

template <class T>
math::Vector2i RollingGrid<T>::CellToRowColumn(const T* cell) const {
  const int offset = cell - array_.data();
  math::Vector2i coord(offset / cols_ - array_r0_, offset % cols_ - array_c0_);
  if (coord.x < 0) {
    coord.x += rows_;
  }
  if (coord.y < 0) {
    coord.y += cols_;
  }
  return coord;
}

template <class T>
void RollingGrid<T>::Recenter(const math::Vector2d& global_xy) {
  const int corner_r = std::floor(global_xy.y / resolution_) - rows_ / 2;
  const int corner_c = std::floor(global_xy.x / resolution_) - cols_ / 2;
  const int dr = corner_r - r0_;
  const int dc = corner_c - c0_;
  // Grid is not moved.
  if (dr == 0 && dc == 0) {
    return;
  }
  // New grid has no overlap with the old one.
  if (std::abs(dr) >= rows_ || std::abs(dc) >= cols_) {
    Grid<T>::Fill({});
    r0_ = corner_r;
    c0_ = corner_c;
    array_r0_ = 0;
    array_c0_ = 0;
    return;
  }
  if (dr > 0) {
    for (int i = 0; i < dr; ++i) {
      AddRowNorth();
    }
  } else if (dr < 0) {
    for (int i = 0; i < std::abs(dr); ++i) {
      AddRowSouth();
    }
  }
  if (dc > 0) {
    for (int i = 0; i < dc; ++i) {
      AddColumnEast();
    }
  } else if (dc < 0) {
    for (int i = 0; i < std::abs(dc); ++i) {
      AddColumnWest();
    }
  }
}

template <class T>
void RollingGrid<T>::AddColumnEast() {
  for (int r = 0; r < rows_; ++r) {
    array_[r * cols_ + array_c0_] = {};
  }
  c0_++;
  array_c0_++;
  if (array_c0_ == cols_) {
    array_c0_ = 0;
  }
}

template <class T>
void RollingGrid<T>::AddColumnWest() {
  array_c0_--;
  if (array_c0_ < 0) {
    array_c0_ = cols_ - 1;
  }
  for (int r = 0; r < rows_; ++r) {
    array_[r * cols_ + array_c0_] = {};
  }
  c0_--;
}

template <class T>
void RollingGrid<T>::AddRowNorth() {
  std::fill_n(array_.begin() + array_r0_ * cols_, cols_, T());
  r0_++;
  array_r0_++;
  if (array_r0_ == rows_) {
    array_r0_ = 0;
  }
}

template <class T>
void RollingGrid<T>::AddRowSouth() {
  array_r0_--;
  if (array_r0_ < 0) {
    array_r0_ = rows_ - 1;
  }
  std::fill_n(array_.begin() + array_r0_ * cols_, cols_, T());
  r0_--;
}

template <class T>
const T* RollingGrid<T>::GetValueByGlobalXY(const math::Vector2d& global_xy) const {
  const math::Vector2i coord = Grid<T>::GlobalXYToRowColumn(global_xy);
  return GetValueByRowColumn(coord);
}

template <class T>
const T* RollingGrid<T>::GetValueByRowColumn(const math::Vector2i& coord) const {
  if (!Grid<T>::IsInRange(coord.x, coord.y)) {
    return nullptr;
  }
  const int row = wrap(coord.x + array_r0_, rows_);
  const int col = wrap(coord.y + array_c0_, cols_);
  return &(array_[row * cols_ + col]);
}

}  // namespace container
}  // namespace base
