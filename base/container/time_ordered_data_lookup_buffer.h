// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>
//          <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)

#pragma once

#include <algorithm>
#include <deque>
#include <memory>
#include <string>
#include <utility>

#include "base/common/macros.h"
#include "base/common/optional.h"
#include "base/math/constants.h"
#include "base/math/math_util.h"
#include "base/math/piecewise_linear_function.h"
#include "base/math/transform/transform.h"
#include "base/strings/format.h"
#include "walle/engine/util/time.h"

namespace base {
namespace container {

struct TimeOrderedDataLookupBufferOption {
  base::Optional<absl::Duration> duration;
  std::string name;
};

// A time-ordered buffer of data that supports interpolated lookups.
template <typename T, typename LerpFunc = math::LerpOperator<T, double>>
class TimeOrderedDataLookupBuffer {
 public:
  explicit TimeOrderedDataLookupBuffer(
      const TimeOrderedDataLookupBufferOption& option = TimeOrderedDataLookupBufferOption())
      : duration_(option.duration), name_(std::move(option.name)) {}
  virtual ~TimeOrderedDataLookupBuffer() = default;

  bool IsEmpty() const { return t_samples_.empty(); }

  bool IsInside(const absl::Time& time) const {
    return IsInside(walle::util::DoubleSecondsFromTime(time));
  }
  bool IsInside(double time_in_seconds) const {
    return !t_samples_.empty() &&
           math::IsInside(time_in_seconds, t_samples_.front(), t_samples_.back());
  }

  void Clear() {
    t_samples_.clear();
    v_samples_.clear();
  }

  void ClearExpiredItem(double time_in_seconds) {
    if (!duration_) {
      return;
    }
    const double earliest_time = time_in_seconds - absl::ToDoubleSeconds(*duration_);
    while (!t_samples_.empty() && t_samples_.front() < earliest_time) {
      t_samples_.pop_front();
      v_samples_.pop_front();
    }
  }
  void ClearAfterTime(double time_in_seconds) {
    while (!t_samples_.empty() && t_samples_.back() > time_in_seconds) {
      t_samples_.pop_back();
      v_samples_.pop_back();
    }
  }

  void Push(const absl::Time& time, T data) {
    Push(walle::util::DoubleSecondsFromTime(time), std::move(data));
  }
  void Push(double time_in_seconds, T data);

  base::Optional<T> Lookup(const absl::Time& time, bool extrapolate = true) const {
    return Lookup(walle::util::DoubleSecondsFromTime(time), extrapolate);
  }
  base::Optional<T> Lookup(double time_in_seconds, bool extrapolate = true) const;

  // Interpolated value is supposed to be inaccurate if interpolating inteval is larger than
  // duration_in_seconds.
  base::Optional<T> LookupInDuration(const absl::Time& time,
                                         double duration_in_seconds,
                                         bool extrapolate = true) const {
    return LookupInDuration(
        walle::util::DoubleSecondsFromTime(time), duration_in_seconds, extrapolate);
  }
  base::Optional<T> LookupInDuration(double time_in_seconds,
                                         double duration_in_seconds,
                                         bool extrapolate = true) const;
  // Interpolated valid only in range [t_sample[start] - range_in_second, t_sample[end] + range_in_second]
  base::Optional<T> LookupWithExtrapolateRange(const absl::Time& time,
                                               double range_in_seconds,
                                               bool extrapolate = true) const {
    return LookupWithExtrapolateRange(
        walle::util::DoubleSecondsFromTime(time), range_in_seconds, extrapolate);
  }
  base::Optional<T> LookupWithExtrapolateRange(double time_in_seconds,
                                               double range_in_seconds,
                                               bool extrapolate = true) const;

  base::Optional<T> GetLatestItem() const;

  base::Optional<math::LerpRatio> GetDataLerpRatio(double time_in_seconds,
                                                       bool extrapolate = true) const {
    if (t_samples_.empty()) {
      return base::none;
    }
    if (!extrapolate && !IsInside(time_in_seconds)) {
      return base::none;
    }
    return math::GetLerpRatio(t_samples_, time_in_seconds, &last_index_hint_, extrapolate);
  }

  absl::Time GetEarliestTime() const {
    return walle::util::TimeFromDoubleSeconds(GetEarliestTimeInSeconds());
  }
  double GetEarliestTimeInSeconds() const {
    CHECK(!t_samples_.empty());
    return t_samples_.front();
  }

  absl::Time GetLatestTime() const {
    return walle::util::TimeFromDoubleSeconds(GetLatestTimeInSeconds());
  }
  double GetLatestTimeInSeconds() const {
    CHECK(!t_samples_.empty());
    return t_samples_.back();
  }

  int size() const { return t_samples_.size(); }
  std::string name() const { return name_; }
  void set_name(std::string name) {
    if (!name_.empty()) {
      LOG(ERROR) << strings::Format("Current buffer has name: {}, cannot set name again!", name_);
      return;
    }
    name_ = std::move(name);
  }

  const std::deque<double>& x_samples() const { return t_samples_; }
  const std::deque<T>& y_samples() const { return v_samples_; }

  const std::deque<double>& t_samples() const { return t_samples_; }
  const std::deque<T>& v_samples() const { return v_samples_; }

 private:
  base::Optional<absl::Duration> duration_;
  std::string name_;

  std::deque<double> t_samples_;
  std::deque<T> v_samples_;
  mutable int last_index_hint_ = -1;

  DISALLOW_COPY_AND_ASSIGN(TimeOrderedDataLookupBuffer);
};

template <typename T, typename LerpFunc>
void TimeOrderedDataLookupBuffer<T, LerpFunc>::Push(double time_in_seconds, T data) {
  if (!t_samples_.empty()) {
    CHECK(t_samples_.back() <= time_in_seconds)
        << strings::Format("({:.4f} vs. {:.4f}) New data is older than latest, buffer name: {}.",
                           t_samples_.back(), time_in_seconds, name_);
  }
  // Do NOT add data with duplicate timestamp.
  constexpr double kTimeEpsilon = 1e-4;  // In 0.1ms.
  if (!t_samples_.empty() && time_in_seconds - t_samples_.back() < kTimeEpsilon) {
    t_samples_.pop_back();
    v_samples_.pop_back();
  }
  t_samples_.push_back(time_in_seconds);
  v_samples_.push_back(std::move(data));

  ClearExpiredItem(time_in_seconds);
}

template <typename T, typename LerpFunc>
base::Optional<T> TimeOrderedDataLookupBuffer<T, LerpFunc>::Lookup(double time_in_seconds,
                                                                       bool extrapolate) const {
  base::Optional<math::LerpRatio> lerp_ratio = GetDataLerpRatio(time_in_seconds, extrapolate);
  if (!lerp_ratio.has_value()) {
    return base::none;
  }
  return math::Lerp<std::deque<T>, T, LerpFunc>(v_samples_, *lerp_ratio);
}

template <typename T, typename LerpFunc>
base::Optional<T> TimeOrderedDataLookupBuffer<T, LerpFunc>::LookupInDuration(
    double time_in_seconds, double duration_in_seconds, bool extrapolate) const {
  const base::Optional<math::LerpRatio> lerp_ratio =
      GetDataLerpRatio(time_in_seconds, extrapolate);
  if (!lerp_ratio.has_value()) {
    return base::none;
  }
  const int last_sample_id = v_samples_.size() - 1;
  if (!last_sample_id) {
    if (std::abs(time_in_seconds - t_samples_[0]) > duration_in_seconds) {
      return base::none;
    }
  } else {
    if (std::abs(lerp_ratio->r) > math::kEpsilon) {
      CHECK_GE(lerp_ratio->i, 0);
      CHECK_LE(lerp_ratio->i, last_sample_id);
      if (lerp_ratio->i == last_sample_id) {
        if (t_samples_[lerp_ratio->i] - t_samples_[lerp_ratio->i - 1] > duration_in_seconds) {
          return base::none;
        }
      } else {
        if (t_samples_[lerp_ratio->i + 1] - t_samples_[lerp_ratio->i] > duration_in_seconds) {
          return base::none;
        }
      }
    }
  }
  return math::Lerp<std::deque<T>, T, LerpFunc>(v_samples_, *lerp_ratio);
}

template <typename T, typename LerpFunc>
base::Optional<T> TimeOrderedDataLookupBuffer<T, LerpFunc>::LookupWithExtrapolateRange(
    double time_in_seconds, double range_in_seconds, bool extrapolate) const {
  if (t_samples_.size() == 0) {
    return base::none;
  }
  range_in_seconds = range_in_seconds >= 0.0 ? range_in_seconds : 0.0;
  if (time_in_seconds < t_samples_[0] - range_in_seconds ||
      time_in_seconds > t_samples_[t_samples_.size() - 1] + range_in_seconds) {
    return base::none;
  }
  return Lookup(time_in_seconds, extrapolate);
}

template <typename T, typename LerpFunc>
base::Optional<T> TimeOrderedDataLookupBuffer<T, LerpFunc>::GetLatestItem() const {
  if (t_samples_.empty()) {
    return base::none;
  }
  return v_samples_.back();
}

}  // namespace container
}  // namespace base
