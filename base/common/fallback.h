// Copyright @2024 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <iomanip>
#include <iostream>
#include <string>
#include "glog/logging.h"

#include "absl/debugging/stacktrace.h"
#include "base/common/macros.h"
namespace base {

enum Level {
  DEFAULT = 0,
  FATAL = 1,
  ERROR = 2,
};

void Fallback(const std::string& module,
              const std::string& evalation_alert,
              uint64_t module_code,
              uint64_t inner_code,
              int level);

} // namespace base

class CustomLogSink : public google::LogSink {
 public:
  static CustomLogSink& getInstance() {
    static CustomLogSink instance;
    return instance;
  }

  void send(google::LogSeverity severity,
            const char* full_filename,
            const char* base_filename,
            int line,
            const google::LogMessageTime& time,
            const char* message,
            size_t message_len) override {
    std::cerr << LogSink::ToString(severity, full_filename, line, time, message, message_len)
              << std::endl;
  }

  DISALLOW_COPY_AND_ASSIGN(CustomLogSink);

 private:
  CustomLogSink() {}
};

#define FALLBACK_LOG(fallback_severity) FALLBACK_LOG_##fallback_severity

#define FALLBACK_LOG_FATAL                                                             \
  []() -> google::LogMessage {                                                         \
    base::Fallback("FALLBACK FATAL CALL", std::string(), 0, 0, base::FATAL);           \
    return google::LogMessage(                                                         \
        __FILE__, __LINE__, google::GLOG_ERROR, &CustomLogSink::getInstance(), false); \
  }()                                                                                  \
              .stream()
#define FALLBACK_LOG_ERROR                                                             \
  []() -> google::LogMessage {                                                         \
    base::Fallback("FALLBACK ERROR CALL", std::string(), 0, 0, base::ERROR);           \
    return google::LogMessage(                                                         \
        __FILE__, __LINE__, google::GLOG_ERROR, &CustomLogSink::getInstance(), false); \
  }()                                                                                  \
              .stream()

#define FALLBACK(condition, module, level) \
  (condition) ? (void)0 : base::Fallback(#module, std::string(), 0, 0, level)

#define FALLBACK_GENERAL(condition, module, parking, level) \
  (condition) ? (void)0 : base::Fallback(#module, parking, 0, 0, level)

#define FALLBACK_ERROR(condition, ...) \
  (condition) ? (void)0 : base::Fallback(__VA_ARGS__, std::string(), 0, 0, base::ERROR)

#define FALLBACK_FATAL(condition, ...) \
  (condition) ? (void)0 : base::Fallback(__VA_ARGS__, std::string(), 0, 0, base::FATAL)

#define FALLBACK_FAULTEVENT(condition, module_code, inner_code, level) \
  (condition) ? (void)0                                                \
              : base::Fallback(std::string(), std::string(), module_code, inner_code, level)

#define FALLBACK_ERROR_FAULTEVENT(condition, module_code, inner_code) \
  (condition) ? (void)0                                               \
              : base::Fallback(std::string(), std::string(), module_code, inner_code, base::ERROR)

#define FALLBACK_FATAL_FAULTEVENT(condition, module_code, inner_code) \
  (condition) ? (void)0                                               \
              : base::Fallback(std::string(), std::string(), module_code, inner_code, base::FATAL)

#define FALLBACK_FAULTEVENT_LOG(module_code, inner_code, fallback_severity) \
  FALLBACK_FAULTEVENT_LOG_##fallback_severity(module_code, inner_code)

#define FALLBACK_FAULTEVENT_LOG_FATAL(module_code, inner_code)                                  \
  []() -> google::LogMessage {                                                                  \
    base::Fallback("FALLBACK FATAL CALL", std::string(), module_code, inner_code, base::FATAL); \
    return google::LogMessage(                                                                  \
        __FILE__, __LINE__, google::GLOG_ERROR, &CustomLogSink::getInstance(), false);          \
  }()                                                                                           \
              .stream()
#define FALLBACK_FAULTEVENT_LOG_ERROR(module_code, inner_code)                                  \
  []() -> google::LogMessage {                                                                  \
    base::Fallback("FALLBACK ERROR CALL", std::string(), module_code, inner_code, base::ERROR); \
    return google::LogMessage(                                                                  \
        __FILE__, __LINE__, google::GLOG_ERROR, &CustomLogSink::getInstance(), false);          \
  }()                                                                                           \
              .stream()
