load("//build/bazel_rules:walle_proto_library.bzl", "walle_proto_library")

package(default_visibility = ["//visibility:public"])

exports_files([
    "symlinked_main.cc",
])

cc_library(
    name = "macros",
    hdrs = ["macros.h"],
    deps = [
        "@glog",
    ],
)

cc_test(
    name = "macros_test",
    size = "small",
    srcs = ["macros_test.cc"],
    tags = [
        "ci",
        "ci_cpu",
    ],
    deps = [
        ":macros",
        ":status",
        "//base/testing:test_main",
    ],
)

cc_library(
    name = "optional",
    hdrs = ["optional.h"],
    deps = [
        ":macros",
        "@com_google_absl//absl/types:optional",
    ],
)

cc_test(
    name = "optional_test",
    size = "small",
    srcs = ["optional_test.cc"],
    tags = [
        "ci",
        "ci_cpu",
    ],
    deps = [
        ":optional",
        "//base/testing:test_main",
    ],
)

cc_library(
    name = "debug_optional",
    hdrs = ["debug_optional.h"],
    visibility = [":__pkg__"],
    deps = [
        "//base/common:macros",
        "//base/common/internal:debug_optional_impl",
    ],
)

cc_test(
    name = "debug_optional_test",
    size = "small",
    srcs = ["debug_optional_test.cc"],
    deps = [
        ":debug_optional",
        "//base/testing:test_main",
    ],
)

cc_library(
    name = "numeric",
    hdrs = ["numeric.h"],
)

cc_library(
    name = "failure_logger",
    srcs = ["failure_logger.cc"],
    hdrs = ["failure_logger.h"],
    deps = [
        "@com_google_absl//absl/debugging:failure_signal_handler",
        "@glog",
    ],
)

walle_proto_library(
    name = "error_code_proto",
    srcs = ["error_code.proto"],
)

walle_proto_library(
    name = "status_proto",
    srcs = ["status.proto"],
    deps = [":error_code_proto"],
)

cc_library(
    name = "status",
    hdrs = ["status.h"],
    deps = [
        ":cc_error_code_proto",
        ":cc_status_proto",
        ":macros",
        "//base/synchronization:mutex",
        "@com_google_absl//absl/strings",
    ],
)

cc_library(
    name = "status_test_util",
    testonly = True,
    hdrs = ["status_test_util.h"],
    deps = [
        ":status",
        "@gtest//:testing",
    ],
)

cc_test(
    name = "status_test",
    size = "small",
    srcs = ["status_test.cc"],
    tags = [
        "ci",
        "ci_cpu",
    ],
    deps = [
        ":status",
        ":status_test_util",
        "//base/testing:test_main",
    ],
)

cc_library(
    name = "environment",
    srcs = ["environment.cc"],
    hdrs = ["environment.h"],
    deps = [":macros"],
)

cc_test(
    name = "environment_test",
    size = "small",
    srcs = ["environment_test.cc"],
    tags = [
        "ci",
        "ci_cpu",
    ],
    deps = [
        ":environment",
        "//base/testing:test_main",
    ],
)

cc_library(
    name = "eintr_wrapper",
    hdrs = ["eintr_wrapper.h"],
)

cc_test(
    name = "numeric_test",
    size = "small",
    srcs = ["numeric_test.cc"],
    tags = [
        "ci",
        "ci_cpu",
    ],
    deps = [
        ":numeric",
        "//base/testing:test_main",
    ],
)

cc_library(
    name = "field_macros",
    hdrs = ["field_macros.h"],
    deps = [
        ":macros",
        ":optional",
    ],
)

cc_test(
    name = "field_macros_test",
    srcs = ["field_macros_test.cc"],
    tags = [
        "ci",
        "ci_cpu",
    ],
    deps = [
        ":field_macros",
        "//base/container:time_ordered_data_lookup_buffer",
        "//base/math",
        "//base/testing:test_main",
    ],
)

cc_library(
    name = "cleanup",
    hdrs = [
        "cleanup.h",
    ],
    deps = [
        "//base/common:macros",
    ],
)

cc_test(
    name = "cleanup_test",
    srcs = [
        "cleanup_test.cc",
    ],
    tags = [
        "ci",
        "ci_cpu",
    ],
    deps = [
        ":cleanup",
        "//base/testing:test_main",
    ],
)

cc_binary(
    name = "cleanup_benchmark",
    srcs = [
        "cleanup_benchmark.cc",
    ],
    deps = [
        ":cleanup",
        "@benchmark",
    ],
)

cc_library(
    name = "stacktrace_util",
    srcs = ["stacktrace_util.cc"],
    hdrs = ["stacktrace_util.h"],
    # avoid error: undefined reference to `dladdr'
    linkopts = ["-ldl"],
    deps = [
        "//base/strings:format",
        "@com_google_absl//absl/debugging:stacktrace",
        "@com_google_absl//absl/debugging:symbolize",
        "@com_google_absl//absl/strings",
        "@com_google_absl//absl/synchronization",
    ],
)

cc_test(
    name = "stacktrace_util_test",
    size = "small",
    srcs = ["stacktrace_util_test.cc"],
    deps = [
        ":stacktrace_util",
        "//base/testing:test_main",
        "@glog",
    ],
)

walle_proto_library(
    name = "optional_proto_type_proto",
    srcs = [
        "optional_proto_type.proto",
    ],
)

walle_proto_library(
    name = "fallback_proto",
    srcs = [
        "fallback.proto",
    ],
)

cc_library(
    name = "fallback",
    srcs = [
        "fallback.cc",
    ],
    hdrs = [
        "fallback.h",
    ],
    deps = [
        ":cc_fallback_proto",
        ":stacktrace_util",
        "//base/common:macros",
        "@glog",
        "@gtest",
    ],
)

cc_test(
    name = "fallback_test",
    size = "small",
    srcs = [
        "fallback_test.cc",
    ],
    tags = [
        "ci",
        "ci_cpu",
    ],
    deps = [
        ":fallback",
        "@gtest//:main",
    ],
)

cc_library(
    name = "mallocx",
    srcs = [
        "mallocx.cc",
    ],
    hdrs = [
        "mallocx.h",
    ],
)
