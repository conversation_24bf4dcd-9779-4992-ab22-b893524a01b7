// Copyright @2025 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/common/mallocx.h"

extern "C" {

void __attribute__((weak)) SetThreadSpecifiedArena() { return; }

unsigned int __attribute__((weak)) GetThreadArenaId() { return 0; }

void __attribute__((weak)) SetArenaDirtyDecayMs(ssize_t ms) {
  (void)ms;
  return;
}

void __attribute__((weak)) SetArenaMuzzyDecayMs(ssize_t ms) {
  (void)ms;
  return;
}

}  // extern "C"
