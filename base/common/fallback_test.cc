// Copyright @2024 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/common/fallback.h"

#include <unistd.h>
#include <signal.h>

#include "glog/logging.h"
#include "gtest/gtest.h"

#include "base/common/fallback.pb.h"

namespace base {
namespace {

void SignalHandler1(int signo, siginfo_t* info, void* ctx) {
  if (signo == SIGRTMIN + 20) {
    base::FallbackInfo* fallback_info = reinterpret_cast<base::FallbackInfo*>(info->si_value.sival_ptr);
    EXPECT_TRUE(fallback_info != nullptr);
    EXPECT_EQ(fallback_info->level(), FallbackInfo_Level_ERROR);
    free(fallback_info);
    std::cout << "SignalHandler1 End" << std::endl;
  }
}

void SignalHandler2(int signo, siginfo_t* info, void* ctx) {
  if (signo == SIGRTMIN + 20) {
    base::FallbackInfo* fallback_info = reinterpret_cast<base::FallbackInfo*>(info->si_value.sival_ptr);
    EXPECT_TRUE(fallback_info != nullptr);
    EXPECT_NE(fallback_info->level(), FallbackInfo_Level_ERROR);
    free(fallback_info);
    std::cout << "SignalHandler2 End" << std::endl;
  }
}

} // namespace

TEST(MarcoTest, BasicTest) {
  FALLBACK_ERROR(true, "BasicTest");
  FALLBACK_FATAL(1 == 1, "BasicTest");
}

TEST(MarcoTest, FallbackErrorTest) {
  struct sigaction action;
  memset(&action, 0, sizeof(action));
  sigemptyset(&action.sa_mask);
  action.sa_flags |= SA_SIGINFO;
  action.sa_sigaction = SignalHandler1;
  sigaction(SIGRTMIN + 20, &action, nullptr);
  FALLBACK_ERROR(false, "FallbackErrorTest");
}

TEST(MarcoTest, FallbackFatalTest) {
  struct sigaction action;
  memset(&action, 0, sizeof(action));
  sigemptyset(&action.sa_mask);
  action.sa_flags |= SA_SIGINFO;
  action.sa_sigaction = SignalHandler2;
  sigaction(SIGRTMIN + 20, &action, nullptr);
  FALLBACK_FATAL(1 != 1, "FallbackFatalTest");
}

TEST(MarcoTest, FallbackLogERRORTest) {
  struct sigaction action;
  memset(&action, 0, sizeof(action));
  sigemptyset(&action.sa_mask);
  action.sa_flags |= SA_SIGINFO;
  action.sa_sigaction = SignalHandler1;
  sigaction(SIGRTMIN + 20, &action, nullptr);
  FALLBACK_LOG(ERROR) << " FallbackLogERRORTest";
}

TEST(MarcoTest, FallbackLogFATALTest) {
  struct sigaction action;
  memset(&action, 0, sizeof(action));
  sigemptyset(&action.sa_mask);
  action.sa_flags |= SA_SIGINFO;
  action.sa_sigaction = SignalHandler2;
  sigaction(SIGRTMIN + 20, &action, nullptr);
  FALLBACK_LOG(FATAL) << " FallbackLogFATALTest";
}

TEST(MarcoTest, FallbackFaultEventTest) {
  struct sigaction action;
  memset(&action, 0, sizeof(action));
  sigemptyset(&action.sa_mask);
  action.sa_flags |= SA_SIGINFO;
  action.sa_sigaction = SignalHandler1;
  sigaction(SIGRTMIN + 20, &action, nullptr);
  FALLBACK_FAULTEVENT(false, 0x1, 0x1, base::ERROR);
}

TEST(MarcoTest, FallbackErrorFaultEventTest) {
  struct sigaction action;
  memset(&action, 0, sizeof(action));
  sigemptyset(&action.sa_mask);
  action.sa_flags |= SA_SIGINFO;
  action.sa_sigaction = SignalHandler1;
  sigaction(SIGRTMIN + 20, &action, nullptr);
  FALLBACK_ERROR_FAULTEVENT(false, 0x1, 0x1);
}

TEST(MarcoTest, FallbackFatalFaultEventTest) {
  struct sigaction action;
  memset(&action, 0, sizeof(action));
  sigemptyset(&action.sa_mask);
  action.sa_flags |= SA_SIGINFO;
  action.sa_sigaction = SignalHandler2;
  sigaction(SIGRTMIN + 20, &action, nullptr);
  FALLBACK_FATAL_FAULTEVENT(1 != 1, 0x1, 0x1);
}

TEST(MarcoTest, FallbackFaultEventLogERRORTest) {
  struct sigaction action;
  memset(&action, 0, sizeof(action));
  sigemptyset(&action.sa_mask);
  action.sa_flags |= SA_SIGINFO;
  action.sa_sigaction = SignalHandler1;
  sigaction(SIGRTMIN + 20, &action, nullptr);
  FALLBACK_FAULTEVENT_LOG(0x1, 0x1, ERROR) << " FallbackLogERRORTest";
}

} // namespace base
