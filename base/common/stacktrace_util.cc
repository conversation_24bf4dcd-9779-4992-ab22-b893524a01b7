// Copyright @2023 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/common/stacktrace_util.h"

#include <iostream>

#include "absl/debugging/stacktrace.h"
#include "absl/debugging/symbolize.h"
#include "absl/strings/str_join.h"
#include "absl/synchronization/mutex.h"
#include "boost/stacktrace.hpp"

#include "base/strings/format.h"

namespace base {
namespace {

std::string Symbolize(const void *pc) {
  char tmp[1024] = {'\0'};
  const char *symbol = "Symbolize FAILED\0";
  if (absl::Symbolize(pc, tmp, sizeof(tmp))) {
    symbol = tmp;
  }
  return std::string(symbol);
}

}  // namespace

std::vector<std::string> GetStackTrace(int max_depth, int skip_count) {
  void* result[max_depth];
  const int depth = absl::GetStackTrace(result, max_depth, skip_count);
  if (depth <= 0) {
    return {};
  }
  CHECK_LE(depth, max_depth);
  std::vector<std::string> stack_trace;
  stack_trace.reserve(depth);
  for (int i = 0; i < depth; ++i) {
    stack_trace.emplace_back(Symbolize(result[i]));
  }
  return stack_trace;
}

void PrintStackTrace(int max_depth, int skip_count) {
  const std::vector<std::string>& stack_trace =
      base::GetStackTrace(max_depth, skip_count);
  std::cout << strings::Format("StackTrace:\n{}", absl::StrJoin(stack_trace, "\n\t"))
            << std::endl;
}

std::string GetCallerName() {
  const std::vector<std::string>& stack_trace = base::GetStackTrace(1, 2);
  if (stack_trace.empty()) {
    return "Unknown Caller";
  }
  return stack_trace[0];
}

void PrintCallerName() {
  const std::vector<std::string>& stack_trace = base::GetStackTrace(2, 2);
  if (stack_trace.empty()) {
    std::cout << "Failed to get caller name." << std::endl;
    return;
  }
  std::cout << strings::Format("Current function({}) called by {}",
                               stack_trace[0],
                               stack_trace[1])
            << std::endl;
}

void MutexTraceCallback(const char* msg, const void* obj, int64_t wait_cycles) {
  static volatile void* last_stacktrace_mutex = nullptr;
  const absl::MutexTraceInfo* trace_info = reinterpret_cast<const absl::MutexTraceInfo*>(obj);
  const int64_t backtrace_threshold = (static_cast<int64_t>(trace_info->clkfreq)) >> 4;  // 62.5ms
  // When wait_time > backtrace_threshold, enable output backtrace info.
  if (wait_cycles > backtrace_threshold && trace_info->mutex != last_stacktrace_mutex) {
    last_stacktrace_mutex = trace_info->mutex;
    LOG(ERROR) << "MutexTraceStack:\n" << boost::stacktrace::stacktrace();
  }

  std::string info;
  char hold_name[16] = {0};
  pthread_getname_np(trace_info->hold_tid, hold_name, sizeof(hold_name));
  info.append(strings::Format("MutexTrace: mutex[{}]->hold[{}({})], wait",
                              trace_info->mutex, hold_name, trace_info->hold_tid));
  for (int i = 0; i < trace_info->wait_cnt; ++i) {
    char wait_name[16] = {0};
    pthread_getname_np(trace_info->wait_tid[i], wait_name, sizeof(wait_name));
    info.append(strings::Format("[{}({},{}ms)]",
                                wait_name, trace_info->wait_tid[i],
                                trace_info->wait_cycles[i] * 1000 / trace_info->clkfreq));
  }
  LOG(ERROR) << info;
}

}  // namespace base
