package(default_visibility = ["//visibility:private"])

cc_library(
    name = "debug_bad_optional_access",
    srcs = ["debug_bad_optional_access.cc"],
    hdrs = ["debug_bad_optional_access.h"],
    deps = [
        "@com_google_absl//absl/base:config",
        "@glog",
    ],
)

cc_library(
    name = "debug_optional_internal",
    hdrs = ["debug_optional_internal.h"],
    deps = [
        "@com_google_absl//absl/memory",
        "@com_google_absl//absl/meta:type_traits",
        "@com_google_absl//absl/utility",
    ],
)

cc_library(
    name = "debug_optional_impl",
    hdrs = ["debug_optional_impl.h"],
    visibility = ["//base/common:__pkg__"],
    deps = [
        ":debug_bad_optional_access",
        ":debug_optional_internal",
        "@com_google_absl//absl/base:config",
        "@com_google_absl//absl/base:core_headers",
        "@com_google_absl//absl/meta:type_traits",
        "@com_google_absl//absl/utility",
        "@glog",
    ],
)
