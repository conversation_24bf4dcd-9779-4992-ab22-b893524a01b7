/* Copyright 2016 The TensorFlow Authors. All Rights Reserved.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
==============================================================================*/

#include "benchmark/benchmark.h"

#include <functional>

#include "base/common/cleanup.h"

namespace base {
namespace {

using AnyCleanup = Cleanup<std::function<void()>>;

void Incr(int* p) { (*p)++; }

void Incr() {
  static volatile int i = 0;
  i++;
}

}  // namespace

void BM_Cleanup(benchmark::State& state) {  // NOLINT(runtime/references)
  for (auto s : state) {
    benchmark::DoNotOptimize(MakeCleanup([] { Incr(); }));
  }
}
BENCHMARK(BM_Cleanup);

void BM_AnyCleanup(benchmark::State& state) {  // NOLINT(runtime/references)
  for (auto s : state) {
    AnyCleanup fin = MakeCleanup([] { Incr(); });
    benchmark::DoNotOptimize(fin);
  }
}
BENCHMARK(BM_AnyCleanup);

void BM_AnyCleanupNoFactory(benchmark::State& state) {  // NOLINT(runtime/references)
  for (auto s : state) {
    AnyCleanup fin([] { Incr(); });
    benchmark::DoNotOptimize(fin);
  }
}
BENCHMARK(BM_AnyCleanupNoFactory);

void BM_CleanupBound(benchmark::State& state) {  // NOLINT(runtime/references)
  int i = 0;
  int* p = &i;
  for (auto s : state) {
    auto fin = MakeCleanup([p] { Incr(p); });
    benchmark::DoNotOptimize(fin);
  }
}
BENCHMARK(BM_CleanupBound);

void BM_AnyCleanupBound(benchmark::State& state) {  // NOLINT(runtime/references)
  int i = 0;
  int* p = &i;
  for (auto s : state) {
    AnyCleanup fin = MakeCleanup([p] { Incr(p); });
    benchmark::DoNotOptimize(fin);
  }
}
BENCHMARK(BM_AnyCleanupBound);

void BM_AnyCleanupNoFactoryBound(benchmark::State& state) {  // NOLINT(runtime/references)
  int i = 0;
  int* p = &i;
  for (auto s : state) {
    AnyCleanup fin([p] { Incr(p); });
    benchmark::DoNotOptimize(fin);
  }
}
BENCHMARK(BM_AnyCleanupNoFactoryBound);

}  // namespace base

BENCHMARK_MAIN();

// clang-format off
/*
Run on (16 X 5000 MHz CPU s)
CPU Caches:
  L1 Data 32K (x8)
  L1 Instruction 32K (x8)
  L2 Unified 256K (x8)
  L3 Unified 16384K (x1)
Load Average: 0.85, 0.79, 0.82
***WARNING*** CPU scaling is enabled, the benchmark real time measurements may be noisy and will incur extra overhead.
----------------------------------------------------------------------
Benchmark                            Time             CPU   Iterations
----------------------------------------------------------------------
BM_Cleanup                        1.14 ns         1.14 ns    580063174
BM_AnyCleanup                     2.29 ns         2.29 ns    300643133
BM_AnyCleanupNoFactory            2.67 ns         2.67 ns    259734996
BM_CleanupBound                   1.15 ns         1.15 ns    610346230
BM_AnyCleanupBound                2.33 ns         2.33 ns    296572044
BM_AnyCleanupNoFactoryBound       2.31 ns         2.31 ns    282379481
*/
