// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/common/field_macros.h"

#include "base/container/time_ordered_data_lookup_buffer.h"
#include "base/math/polygon2d.h"
#include "gtest/gtest.h"

namespace base {

class TestUniqueClass {
 public:
  TestUniqueClass() = default;
  ~TestUniqueClass() = default;
};

class TestClass {
 public:
  TestClass() = default;
  ~TestClass() = default;

 private:
  SIMPLE_FIELD(int, id) = 1;
  MUTABLE_FIELD(base::container::TimeOrderedDataLookupBuffer<double>, lookup_buffer);
  OPTIONAL_FIELD(math::Polygon2d, polygon);
  OPTIONAL_FIELD(double, test_value) = 2.0;
  UNIQUE_FIELD(TestUniqueClass, test_unique_class);

  DISALLOW_COPY_AND_ASSIGN(TestClass);
};

TEST(PerceptionFieldMacroTest, BasicTest) {
  TestClass test_class;
  EXPECT_EQ(1, test_class.id());
  EXPECT_FALSE(test_class.has_polygon());
  std::vector<math::Vector2d> points;
  points.push_back(math::Vector2d(0.0, 0.0));
  points.push_back(math::Vector2d(1.0, 0.0));
  points.push_back(math::Vector2d(1.0, 1.0));
  points.push_back(math::Vector2d(0.0, 1.0));
  test_class.set_polygon(math::Polygon2d(points));
  EXPECT_TRUE(test_class.has_polygon());

  EXPECT_TRUE(test_class.has_test_value());
  EXPECT_EQ(2.0, test_class.test_value());

  base::container::TimeOrderedDataLookupBuffer<double>* lookup_buffer =
      test_class.mutable_lookup_buffer();
  lookup_buffer->Push(1.0, 5.0);
  lookup_buffer->Push(2.0, 6.0);
  lookup_buffer->Push(3.0, 7.0);
  lookup_buffer->Push(4.0, 8.0);
  auto find_value = test_class.lookup_buffer().Lookup(3.0, false);
  EXPECT_EQ(7.0, *find_value);

  EXPECT_FALSE(test_class.test_unique_class_is_initialized());
  auto test_unique_class = std::make_unique<TestUniqueClass>();
  test_class.set_test_unique_class(std::move(test_unique_class));
  EXPECT_TRUE(test_class.test_unique_class_is_initialized());
  TestUniqueClass* test_unique_class_ptr = test_class.mutable_test_unique_class();
  EXPECT_TRUE(test_unique_class_ptr != nullptr);
}

}  // namespace base
