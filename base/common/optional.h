// Copyright @2019 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>
//          <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)
//          Guancheng Li (<EMAIL>)

#pragma once

#include <utility>

#include "absl/types/optional.h"

namespace base {

template <typename T>
using Optional = ::absl::optional<T>;

using NoneClassType = ::absl::nullopt_t;

static constexpr const NoneClassType& none = ::absl::nullopt;

inline NoneClassType None() { return none; }

// Returns Optional<T>(v)
template <class T>
inline Optional<std::decay_t<T>> make_optional(T&& v) {
  return Optional<std::decay_t<T>>(std::forward<T>(v));
}

// Returns Optional<T>(args...)
template <class T, class... Args>
inline Optional<T> make_optional(Args&&... args) {
  return Optional<T>(absl::in_place_t(), std::forward<Args>(args)...);
}

// Returns Optional<T>(cond, v)
template <class T>
inline Optional<std::decay_t<T>> make_optional(bool cond, T&& v) {
  return Optional<std::decay_t<T>>(cond, std::forward<T>(v));
}

}  // namespace base
