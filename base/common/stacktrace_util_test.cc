// Copyright @2023 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/common/stacktrace_util.h"

#include <string>

#include "glog/logging.h"
#include "gtest/gtest.h"

namespace base {
namespace test {

int FuncC(std::vector<std::string>* stack, std::string* caller_name) {
  *stack = base::GetStackTrace();
  *caller_name = base::GetCallerName();
  base::PrintStackTrace();
  base::PrintCallerName();
  return 1;
}

int FuncB(std::vector<std::string>* stack, std::string* caller_name) {
  LOG(ERROR) << "Inside FuncB";
  return test::FuncC(stack, caller_name) * 2 + 1;
}

int FuncA(std::vector<std::string>* stack, std::string* caller_name) {
  LOG(ERROR) << "Inside FuncA";
  return test::FuncB(stack, caller_name) + 2;
}

}  // namespace test

TEST(DebugMacrosTest, TestGetStackTrace) {
  std::vector<std::string> stack;
  std::string caller_name;
  EXPECT_EQ(5, test::FuncA(&stack, &caller_name));
  EXPECT_EQ("base::test::FuncC()", caller_name);
  const int stack_size = stack.size();
  EXPECT_GT(stack_size, 2);
  constexpr const char* kExpected[] = {
      "base::test::FuncC()",
      "base::test::FuncA()",
  };
  for (int i = 0; i < 2; ++i) {
    EXPECT_EQ(kExpected[i], stack[i + 1]);
  }
  // LOG(FATAL) << "Make it crash to check print out info.";
}

}  // namespace base
