// Copyright @2019 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/common/environment.h"

#include <stdlib.h>

namespace base {

bool Environment::GetVar(const std::string& variable_name, std::string* result) {
  const char* env_value = getenv(variable_name.c_str());
  if (!env_value) {
    return false;
  }

  // Note that the variable may be defined but empty.
  if (result) {
    *result = env_value;
  }
  return true;
}

bool Environment::HasVar(const std::string& variable_name) {
  return GetVar(variable_name, nullptr);
}

bool Environment::SetVar(const std::string& variable_name, const std::string& new_value) {
  return !setenv(variable_name.c_str(), new_value.c_str(), 1);
}

bool Environment::UnSetVar(const std::string& variable_name) {
  return !unsetenv(variable_name.c_str());
}
}  // namespace base
