// Copyright @2023 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <utility>

#include "base/common/internal/debug_optional_impl.h"


// NOTE:
// This is a debug version of absl::optional, it will make coredump
// when using a variable that is not initialized, this is different
// from the base::optional. At the same time, it will conflict with
// the base::optional. It is using to test if uninitialized values
// in using in our system. Please DO NOT use it in any code in devel.
namespace base {

template <typename T>
using Optional = ::base::optional<T>;

using NoneClassType = ::base::nullopt_t;

static constexpr const NoneClassType& none = ::base::nullopt;

inline NoneClassType None() { return none; }

// Returns Optional<T>(cond, v)
template <class T>
inline Optional<std::decay_t<T>> make_optional(bool cond, T&& v) {
  return Optional<std::decay_t<T>>(cond, std::forward<T>(v));
}

}  // namespace base
