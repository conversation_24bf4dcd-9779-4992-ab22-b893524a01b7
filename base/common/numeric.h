// Copyright @2020 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

namespace base {

// Checking a decimal integer number is a leanding numer or not
// in below example way:
//    1 -> true
//    9 -> true
//    50 -> true
//    240 -> false
//    5000 -> true
//    12000 -> false
inline bool IsLeadingInteger(int64_t num) {
  if (num <= 0) {
    return false;
  } else {
    int64_t dnum = num / 10;
    while (dnum  > 0) {
      if (num % 10 != 0) {
        return false;
      }
      num = dnum;
      dnum /= 10;
    }
    return true;
  }
}
} // namespace base
