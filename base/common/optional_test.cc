// Copyright @2022 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "gtest/gtest.h"

#include "base/common/optional.h"

namespace base {
namespace {

class TestOption {
 public:
  TestOption(int val_1, std::string val_2) : val_1_(val_1), val_2_(val_2) {}
  int val_1_;
  std::string val_2_;
};

}

TEST(OptionalTest, OptionalNoneTest) {
  const auto test = [](bool value) {
    return value ? make_optional<int>(1) : None();
  };
  EXPECT_EQ(none, test(false));
  EXPECT_NE(none, test(true));
}

TEST(OptionalTest, OptionalCreateClassTest) {
  Optional<TestOption> test_optional = make_optional<TestOption>(10, "OptionalTest");
  EXPECT_NE(none, test_optional);
  EXPECT_EQ(10, test_optional->val_1_);
  EXPECT_EQ("OptionalTest", test_optional->val_2_);
}

}  // namespace base
