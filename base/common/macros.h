// Copyright @2023 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <sstream>

#ifdef ATTRIBUTE_UNUSED
// <alsa/global.h> has a same definition as ours
#undef ATTRIBUTE_UNUSED
#endif

#ifdef EXPORT
// "rtk/src/rtklib.h" has a same definition as ours
#undef EXPORT
#endif

// Compiler attributes
#if (defined(__GNUC__) || defined(__APPLE__) || defined(__clang__)) && !defined(SWIG)
// Compiler supports GCC-style attributes
#define ATTRIBUTE_NORETURN __attribute__((noreturn))
#define ATTRIBUTE_ALWAYS_INLINE __attribute__((always_inline))
#define ATTRIBUTE_NOINLINE __attribute__((noinline))
#define ATTRIBUTE_UNUSED __attribute__((unused))
#define ATTRIBUTE_COLD __attribute__((cold))
#define ATTRIBUTE_WEAK __attribute__((weak))
// NOTE: Keep the `MAD_` prefix because it will conflict with cuda library.
#define MAD_PACKED __attribute__((packed))
#define MUST_USE_RESULT __attribute__((warn_unused_result))
#define PRINTF_ATTRIBUTE(string_index, first_to_check) \
  __attribute__((__format__(__printf__, string_index, first_to_check)))
#define SCANF_ATTRIBUTE(string_index, first_to_check) \
  __attribute__((__format__(__scanf__, string_index, first_to_check)))
#elif defined(_MSC_VER)
// Non-GCC equivalents
#define ATTRIBUTE_NORETURN __declspec(noreturn)
#define ATTRIBUTE_ALWAYS_INLINE __forceinline
#define ATTRIBUTE_NOINLINE
#define ATTRIBUTE_UNUSED
#define ATTRIBUTE_COLD
#define ATTRIBUTE_WEAK
#define MUST_USE_RESULT
#define MAD_PACKED
#define PRINTF_ATTRIBUTE(string_index, first_to_check)
#define SCANF_ATTRIBUTE(string_index, first_to_check)
#else
// Non-GCC equivalents
#define ATTRIBUTE_NORETURN
#define ATTRIBUTE_ALWAYS_INLINE
#define ATTRIBUTE_NOINLINE
#define ATTRIBUTE_UNUSED
#define ATTRIBUTE_COLD
#define ATTRIBUTE_WEAK
#define MUST_USE_RESULT
#define MAD_PACKED
#define PRINTF_ATTRIBUTE(string_index, first_to_check)
#define SCANF_ATTRIBUTE(string_index, first_to_check)
#endif

// Control visiblity outside .so
#if defined(_WIN32)
#ifdef COMPILE_LIBRARY
#define EXPORT __declspec(dllexport)
#else
#define EXPORT __declspec(dllimport)
#endif  // COMPILE_LIBRARY
#else
#define EXPORT __attribute__((visibility("default")))
#endif  // _WIN32

#ifdef __has_builtin
#define HAS_BUILTIN(x) __has_builtin(x)
#else
#define HAS_BUILTIN(x) 0
#endif

// Compilers can be told that a certain branch is not likely to be taken
// (for instance, a CHECK failure), and use that information in static
// analysis. Giving it this information can help it optimize for the
// common case in the absence of better information (ie.
// -fprofile-arcs).
//
// We need to disable this for GPU builds, though, since nvcc8 and older
// don't recognize `__builtin_expect` as a builtin, and fail compilation.
#if (!defined(__NVCC__)) && \
    (HAS_BUILTIN(__builtin_expect) || (defined(__GNUC__) && __GNUC__ >= 3))
#define PREDICT_FALSE(x) (__builtin_expect(x, 0))
#define PREDICT_TRUE(x) (__builtin_expect(!!(x), 1))
#else
#define PREDICT_FALSE(x) (x)
#define PREDICT_TRUE(x) (x)
#endif

// A macro to disallow the copy constructor and operator= functions
// This is usually placed in the private: declarations for a class.
#define DISALLOW_COPY_AND_ASSIGN(TypeName) \
  TypeName(const TypeName&) = delete;      \
  void operator=(const TypeName&) = delete

// The ARRAYSIZE(arr) macro returns the # of elements in an array arr.
//
// The expression ARRAYSIZE(a) is a compile-time constant of type
// size_t.
#define ARRAYSIZE(a)            \
  ((sizeof(a) / sizeof(*(a))) / \
    static_cast<size_t>(!(sizeof(a) % sizeof(*(a)))))

#if defined(__GXX_EXPERIMENTAL_CXX0X__) || __cplusplus >= 201103L || \
    (defined(_MSC_VER) && _MSC_VER >= 1900)
// Define this to 1 if the code is compiled in C++11 mode; leave it
// undefined otherwise.  Do NOT define it to 0 -- that causes
// '#ifdef LANG_CXX11' to behave differently from '#if LANG_CXX11'.
#define LANG_CXX11 1
#endif

#if defined(__clang__) && defined(LANG_CXX11) && defined(__has_warning)
#if __has_feature(cxx_attributes) && __has_warning("-Wimplicit-fallthrough")
#define FALLTHROUGH_INTENDED [[clang::fallthrough]]  // NOLINT
#endif
#endif

#ifndef FALLTHROUGH_INTENDED
#define FALLTHROUGH_INTENDED \
  do {                       \
  } while (0)
#endif

// Macro to output the current line info.
#define CURRENT_LINE_INFO()                                                   \
  ({                                                                          \
    std::stringstream _current_line_info;                                     \
    _current_line_info << __FILE__ << ":" << __LINE__ << ":" << __FUNCTION__; \
    _current_line_info.str();                                                 \
  })
