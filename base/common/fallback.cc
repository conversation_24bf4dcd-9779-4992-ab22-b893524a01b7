// Copyright @2024 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/common/fallback.h"

#include <unistd.h>
#include <signal.h>

#include <string>

#include "base/common/fallback.pb.h"
#include "base/common/stacktrace_util.h"
#include "base/strings/format.h"

namespace base {
namespace {
const int kMaxStackFrames = 10;
} // namespace

void Fallback(const std::string& module,
              const std::string& evalation_alert,
              uint64_t module_code,
              uint64_t inner_code,
              int level) {
  FallbackInfo* info = new FallbackInfo();
  switch (level) {
    case DEFAULT:
      info->set_level(FallbackInfo_Level_DEFAULT);
      break;
    case FATAL:
      info->set_level(FallbackInfo_Level_FATAL);
      break;
    case ERROR:
      info->set_level(FallbackInfo_Level_ERROR);
      break;
    default:
      LOG(ERROR) << strings::Format("There is no such level: {}", level);
      break;
  }
  info->set_module_name(module);
  
  // differentiate the fallback interfaces for generic and check operations through parking.
  if (!evalation_alert.empty()) {
    info->set_evaluation_alert(evalation_alert);
  } else {
    void** result = reinterpret_cast<void**>(malloc(kMaxStackFrames * sizeof(void*)));
    absl::GetStackTrace(result, kMaxStackFrames, 0);
    uintptr_t addr = reinterpret_cast<uintptr_t>(result);
    info->set_stacktrace_addr(reinterpret_cast<uint64_t>(addr));
  }
  // Add fault info.
  info->set_module_code(module_code);
  info->set_inner_code(inner_code);

  union sigval sigval;
  sigval.sival_ptr = reinterpret_cast<void*>(info);
  pid_t pid = getpid();
  if (sigqueue(pid, SIGRTMIN + 20, sigval) == -1) {
    if (evalation_alert.empty()) {
      PrintStackTrace();
    } else {
      LOG(ERROR) << "Fallback sigqueue fail.";
    }
  }
}

} // namespace base
