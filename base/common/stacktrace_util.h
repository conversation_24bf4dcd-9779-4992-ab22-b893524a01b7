// Copyright @2023 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <string>
#include <vector>

// Sometimes we lost key logs from remote/onboard and maybe limited with the resources.
// This util provides several methods to catch the stack trace as debug information.
namespace base {

// WARNING: Only for test, DO NOT use in production. It may cause performance issue.

// "skip_count" from current function to the most top function.
// More infomation is here:
//   https://github.com/abseil/abseil-cpp/blob/master/absl/debugging/stacktrace.h
// From current observation, the anonymous namespace cannot be parsed.
// Platform & complier & compile/link parameter may change the result.
std::vector<std::string> GetStackTrace(int max_depth = 10, int skip_count = 0);
void PrintStackTrace(int max_depth = 10, int skip_count = 0);
std::string GetCallerName();
void PrintCallerName();

void MutexTraceCallback(const char* msg, const void* obj, int64_t wait_cycles);

}  // namespace base
