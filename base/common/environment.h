// Copyright @2019 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <string>

#include "base/common/macros.h"

namespace base {

class Environment {
 public:
  Environment() = default;
  ~Environment() = default;

  // Gets an environment variable's value and stores it in |result|.
  // Returns false if the key is unset.
  bool GetVar(const std::string& variable_name, std::string* result);

  // Syntactic sugar for GetVar(variable_name, nullptr);
  bool HasVar(const std::string& variable_name);

  // Returns true on success, otherwise returns false. This method should not
  // be called in a multi-threaded process.
  bool SetVar(const std::string& variable_name, const std::string& new_value);

  // Returns true on success, otherwise returns false. This method should not
  // be called in a multi-threaded process.
  bool UnSetVar(const std::string& variable_name);
};

}  // namespace base
