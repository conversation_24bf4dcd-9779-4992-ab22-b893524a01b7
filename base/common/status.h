// Copyright @2019 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <string>

#include "google/protobuf/descriptor.h"

#include "base/common/error_code.pb.h"
#include "base/common/status.pb.h"

namespace base {

/**
 * @class Status
 *
 * @brief A general class to denote the return status of an API call. It
 * can either be an OK status for success, or a failure status with error
 * message and error code enum.
*/
class Status {
 public:
  /**
   * @brief Create a success status.
   */
  Status() : code_(errors::ErrorCode::OK), msg_() {}
  ~Status() = default;

  /**
   * @brief Create a status with the specified error code and msg as a
   * human-readable string containing more detailed information.
   * @param code the error code.
   * @param msg the message associated with the error.
   */
  Status(errors::ErrorCode code, const std::string &msg) : code_(code), msg_(msg) {}
  explicit Status(const std::string &msg) : code_(errors::ErrorCode::UNKNOWN), msg_(msg) {}

  /**
   * @brief Create a status with the specified error code and empty msg
   * @param code the error code.
   */
  explicit Status(errors::ErrorCode code) : code_(code), msg_("") {}

  /**
   * @brief generate a success status.
   * @returns a success status
   */
  static Status OK() { return Status(); }

  /**
   * @brief check whether the return status is OK.
   * @returns true if the code is ErrorCode::OK
   *          false otherwise
   */
  bool ok() const { return code_ == errors::ErrorCode::OK; }

  /**
   * @brief get the error code
   * @returns the error code
   */
  errors::ErrorCode code() const { return code_; }

  /**
   * @brief defines the logic of testing if two Status are equal
   */
  bool operator==(const Status &rh) const {
    return (this->code_ == rh.code_) && (this->msg_ == rh.msg_);
  }

  /**
   * @brief defines the logic of testing if two Status are unequal
   */
  bool operator!=(const Status &rh) const { return !(*this == rh); }

  /**
   * @brief returns the error message of the status, empty if the status is OK.
   * @returns the error message
   */
  const std::string &error_message() const { return msg_; }

  /**
   * @brief returns a string representation in a readable format.
   * @returns the string "OK" if success.
   *          the internal error message otherwise.
   */
  std::string ToString() const {
    if (ok()) {
      return "OK";
    }
    return ErrorCode_Name(code_) + ": " + msg_;
  }

  /**
   * @brief save the error_code and error message to protobuf
   * @param the Status protobuf that will store the message.
   */
  void Save(StatusProto *status_pb) {
    if (!status_pb) {
      return;
    }
    status_pb->set_error_code(code_);
    if (!msg_.empty()) {
      status_pb->set_msg(msg_);
    }
  }

 private:
  errors::ErrorCode code_;
  std::string msg_;
};

inline std::ostream &operator<<(std::ostream &os, const Status &s) {
  os << s.ToString();
  return os;
}

}  // namespace base

#define CHECK_OK(statement) CHECK_EQ(::base::Status::OK(), (statement))

// Run a command that returns a Status.  If the called code returns an
// error status, return that status up out of this method too.
// Example:
//   RETURN_IF_ERROR(DoThings(4));
// Using _status below to avoid capture problems if expr is "status".
#define RETURN_IF_ERROR(expr)                                                \
  do {                                                                       \
    const ::base::Status _status = (expr);                                   \
    if (__builtin_expect(!_status.ok(), 0))                                  \
      return _status;                                                        \
  } while (0)

// Run a command that returns a Status.  If the called code returns an
// ok status, return that status up out of this method too.
// Example:
//   RETURN_IF_OK(DoThings(4));
// Using _status below to avoid capture problems if expr is "status".
#define RETURN_IF_OK(expr)                                                   \
  do {                                                                       \
    const ::base::Status _status = (expr);                                   \
    if (__builtin_expect(_status.ok(), 0))                                   \
      return _status;                                                        \
  } while (0)
