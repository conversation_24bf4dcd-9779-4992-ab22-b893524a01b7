// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <memory>
#include <string>
#include <utility>
#include <vector>

#include "base/common/macros.h"
#include "base/common/optional.h"

// We define some basic field macros to remove duplicate codes and
// provide template methods.

// CONST_FIELD only used to define read-only types.
#define CONST_FIELD(field_type, field_name)                                                        \
 public:                                                                                           \
  const field_type& field_name(void* p ATTRIBUTE_UNUSED = nullptr) const { return field_name##_; } \
                                                                                                   \
 private:                                                                                          \
  field_type field_name##_

#define PROTECTED_CONST_FIELD(field_type, field_name)                                              \
 public:                                                                                           \
  const field_type& field_name(void* p ATTRIBUTE_UNUSED = nullptr) const { return field_name##_; } \
                                                                                                   \
 protected:                                                                                        \
  field_type field_name##_

// CONST_POINTER_FIELD only used to define read-only pointer types.
#define CONST_POINTER_FIELD(field_type, field_name)                                   \
 public:                                                                              \
  bool field_name##_is_initialized() const { return field_name##_ != nullptr; }       \
  void set_##field_name(const field_type* field_name) { field_name##_ = field_name; } \
  const field_type& field_name(void* p ATTRIBUTE_UNUSED = nullptr) const {            \
    CHECK(field_name##_is_initialized());                                             \
    return *field_name##_;                                                            \
  }                                                                                   \
                                                                                      \
 private:                                                                             \
  const field_type* field_name##_ = nullptr

// SIMPLE_FIELD only used to define primitive types.
#define SIMPLE_FIELD(field_type, field_name)                                                \
 public:                                                                                    \
  field_type field_name(void* p ATTRIBUTE_UNUSED = nullptr) const { return field_name##_; } \
  void set_##field_name(field_type field_name) { field_name##_ = field_name; }              \
                                                                                            \
 private:                                                                                   \
  field_type field_name##_

// FIELD used to define type with default constructor.
#define MUTABLE_FIELD(field_type, field_name)                                                      \
 public:                                                                                           \
  const field_type& field_name(void* p ATTRIBUTE_UNUSED = nullptr) const { return field_name##_; } \
  field_type* mutable_##field_name(void* p ATTRIBUTE_UNUSED = nullptr) { return &field_name##_; }  \
                                                                                                   \
 private:                                                                                          \
  field_type field_name##_

// SIMPLE_FIELD only used to define primitive types.
#define PROTECTED_SIMPLE_FIELD(field_type, field_name)                                      \
 public:                                                                                    \
  field_type field_name(void* p ATTRIBUTE_UNUSED = nullptr) const { return field_name##_; } \
  void set_##field_name(field_type field_name) { field_name##_ = field_name; }              \
                                                                                            \
 protected:                                                                                 \
  field_type field_name##_

// FIELD used to define type with default constructor.
#define PROTECTED_MUTABLE_FIELD(field_type, field_name)                                            \
 public:                                                                                           \
  const field_type& field_name(void* p ATTRIBUTE_UNUSED = nullptr) const { return field_name##_; } \
  field_type* mutable_##field_name(void* p ATTRIBUTE_UNUSED = nullptr) { return &field_name##_; }  \
                                                                                                   \
 protected:                                                                                        \
  field_type field_name##_

#define OPTIONAL_FIELD(field_type, field_name)                                                 \
 public:                                                                                       \
  bool has_##field_name() const { return field_name##_.has_value(); }                          \
  const field_type& field_name(void* p ATTRIBUTE_UNUSED = nullptr) const {                     \
    CHECK(has_##field_name());                                                                 \
    return *field_name##_;                                                                     \
  }                                                                                            \
  const base::Optional<field_type>& field_name##_optional(void* p ATTRIBUTE_UNUSED = nullptr)  \
      const {                                                                                  \
    return field_name##_;                                                                      \
  }                                                                                            \
  void set_##field_name(const field_type& field_name) { field_name##_ = field_name; }          \
  base::Optional<field_type>* mutable_##field_name##_ptr(void* p ATTRIBUTE_UNUSED = nullptr) { \
    return &field_name##_;                                                                     \
  }                                                                                            \
                                                                                               \
 private:                                                                                      \
  base::Optional<field_type> field_name##_

#define PROTECTED_OPTIONAL_FIELD(field_type, field_name)                                       \
 public:                                                                                       \
  bool has_##field_name() const { return field_name##_.has_value(); }                          \
  const field_type& field_name(void* p ATTRIBUTE_UNUSED = nullptr) const {                     \
    CHECK(has_##field_name());                                                                 \
    return *field_name##_;                                                                     \
  }                                                                                            \
  const base::Optional<field_type>& field_name##_optional() const { return field_name##_; }    \
  void set_##field_name(const field_type& field_name) { field_name##_ = field_name; }          \
  base::Optional<field_type>* mutable_##field_name##_ptr(void* p ATTRIBUTE_UNUSED = nullptr) { \
    return &field_name##_;                                                                     \
  }                                                                                            \
                                                                                               \
 protected:                                                                                    \
  base::Optional<field_type> field_name##_

#define UNIQUE_FIELD(field_type, field_name)                                                    \
 public:                                                                                        \
  bool field_name##_is_initialized() const { return field_name##_ != nullptr; }                 \
  const field_type& field_name(void* p ATTRIBUTE_UNUSED = nullptr) const {                      \
    CHECK(field_name##_is_initialized());                                                       \
    return *field_name##_;                                                                      \
  }                                                                                             \
  void set_##field_name(std::unique_ptr<field_type> field_name) {                               \
    field_name##_ = std::move(field_name);                                                      \
  }                                                                                             \
  field_type* mutable_##field_name(void* p ATTRIBUTE_UNUSED = nullptr) {                        \
    CHECK(field_name##_is_initialized());                                                       \
    return field_name##_.get();                                                                 \
  }                                                                                             \
  std::unique_ptr<field_type>* mutable_##field_name##_ptr(void* p ATTRIBUTE_UNUSED = nullptr) { \
    return &field_name##_;                                                                      \
  }                                                                                             \
                                                                                                \
 private:                                                                                       \
  std::unique_ptr<field_type> field_name##_
