// Copyright @2019 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "gtest/gtest.h"

#include "base/common/environment.h"

namespace base {

constexpr char kValidEnvironmentVariable[] = "PATH";

TEST(EnvironmentTest, GetVar) {
  Environment env;
  std::string env_value;
  EXPECT_TRUE(env.GetVar(kValidEnvironmentVariable, &env_value));
  EXPECT_NE(env_value, "");
}

TEST(EnvironmentTest, HasVar) {
  Environment env;
  EXPECT_TRUE(env.HasVar(kValidEnvironmentVariable));
}

TEST(EnvironmentTest, SetVar) {
  Environment env;
  const char kFooUpper[] = "FOO";
  const char kFooLower[] = "foo";
  EXPECT_TRUE(env.SetVar(kFooUpper, kFooLower));

  // Now verify that the environment has the new variable.
  EXPECT_TRUE(env.HasVar(kFooUpper));
  std::string var_value;
  EXPECT_TRUE(env.GetVar(kFooUpper, &var_value));
  EXPECT_EQ(var_value, kFooLower);
}

TEST(EnvironmentTest, UnSetVar) {
  Environment env;
  const char kFooUpper[] = "FOO";
  const char kFooLower[] = "foo";

  // First set some environment variable.
  EXPECT_TRUE(env.SetVar(kFooUpper, kFooLower));
  // Now verify that the environment has the new variable.
  EXPECT_TRUE(env.HasVar(kFooUpper));
  // Finally verify that the environment variable was erased.
  EXPECT_TRUE(env.UnSetVar(kFooUpper));
  // And check that the variable has been unset.
  EXPECT_FALSE(env.HasVar(kFooUpper));
}

}  // namespace base
