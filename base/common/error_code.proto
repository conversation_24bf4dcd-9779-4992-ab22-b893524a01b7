syntax = "proto2";

package errors;

// Error codes enum for API's categorized by modules.
enum ErrorCode {
  UNKNOWN = -1;

  // No error, reutrns on success.
  OK = 0;
  INTERNAL_ERROR = 100;
  NOT_IMPLEMENTED_ERROR = 101;

  // Control module error codes start from here.
  CONTROL_ERROR = 1000;
  CONTROL_INIT_ERROR = 1001;
  CONTROL_COMPUTE_ERROR = 1002;
  CONTROL_FSM_ERROR = 1003;
  CONTROL_FUSION_ERROR = 1004;
  CONTROL_ADS_EVAL_ERROR = 1005;
  // Canbus module error codes start from here.
  CANBUS_ERROR = 2000;
  CAN_CLIENT_ERROR_BASE = 2100;
  CAN_CLIENT_ERROR_OPEN_DEVICE_FAILED = 2101;
  CAN_CLIENT_ERROR_FRAME_NUM = 2102;
  CAN_CLIENT_ERROR_SEND_FAILED = 2103;
  CAN_CLIENT_ERROR_RECV_FAILED = 2104;

  // Localization module error codes start from here.
  LOCALIZATION_ERROR = 3000;
  LOCALIZATION_ERROR_MSG = 3100;
  LOCALIZATION_ERROR_LIDAR = 3200;
  LOCALIZATION_ERROR_INTEG = 3300;
  LOCALIZATION_ERROR_GNSS = 3400;

  // Perception module error codes start from here.
  PERCEPTION_ERROR = 4000;
  PERCEPTION_ERROR_TF = 4001;
  PERCEPTION_ERROR_PROCESS = 4002;
  PERCEPTION_FATAL = 4003;
  PERCEPTION_INSTANT_MAP_ERROR = 4004;

  // Prediction module error codes start from here.
  PREDICTION_ERROR = 5000;

  // Planning module error codes start from here
  PLANNING_ERROR = 6000;
  PLANNING_ERROR_FREESPACE_PERCHECK_FAILED = 6002;
  PLANNING_ERROR_QP_OVER_MAX_ITERATION = 6100;
  PLANNING_ERROR_ILQR_OVER_MAX_ITERATION = 6101;
  PLANNING_ERROR_REFERENCE_LINE_PROJECTION = 6001;
  PLANNING_ERROR_SLT_GET_PATH_POINT_FAILED = 6200;

  // HDMap module error codes start from here
  HDMAP_DATA_ERROR = 7000;

  // Routing module error codes
  ROUTING_ERROR = 8000;
  ROUTING_ERROR_REQUEST = 8001;
  ROUTING_ERROR_RESPONSE = 8002;
  ROUTING_ERROR_NOT_READY = 8003;
  INIT_ROUTING_ERROR_RESPONSE = 8004;
  ROUTING_ERROR_NO_LOCALIZATION = 8005;
  ROUTING_ERROR_BLACKLIST = 8006;
  ROUTING_WAYPOINT_ERROR = 8007;
  ROUTING_ERROR_LOAD_MAP = 8008;
  // Indicates an input has been exhausted.
  END_OF_INPUT = 9000;

  // HTTP request error codes.
  HTTP_LOGIC_ERROR = 10000;
  HTTP_RUNTIME_ERROR = 10001;
  WS_NO_CONNECTION_ERROR = 10002;
  WS_CONNECTION_OFFLINE = 10003;
  HTTP_DOWNLOAD_SIZE_EXCEED_LIMIT = 10004;

  // Relative Map error codes.
  RELATIVE_MAP_ERROR = 11000;  // general relative map error code
  RELATIVE_MAP_NOT_READY = 11001;

  // Gnss Driver error codes
  DRIVER_ERROR_GNSS = 12000;

  // Sim Editor error codes
  SIMEDITOR = 13000;

  // Camera Driver error codes
  DRIVER_ERROR_CAMERA = 14000;
  DRIVER_ERROR_CAMERA_USB = 14001;
  DRIVER_ERROR_CAMERA_USB_INIT = 14002;

  // Integrated pnc error codes
  INTEGRATED_PNC_INIT_ERROR = 15000;
  INTEGRATED_PNC_INPUT_CHECK_ERROR = 15001;
  INTEGRATED_PNC_COMPUTE_ERROR = 15002;

  // MapUpdater error codes
  MAP_LOADER_NOT_ACTION_ERROR = 16000;

  // Onboard mender error codes
  ONBOARD_MENDER_ERROR = 17000;
  ONBOARD_MENDER_ERROR_DATA_DIR_NOT_FOUND = 17001;
  ONBOARD_MENDER_ERROR_DATA_FILE_NOT_FOUND = 17002;
  ONBOARD_MENDER_ERROR_BROKEN_DATA = 17003;

  // RedundantControl error codes
  REDUNDANT_CONTROL_ERROR = 18000;
}
