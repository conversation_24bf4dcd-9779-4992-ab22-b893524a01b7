// Copyright @2020 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "gtest/gtest.h"

#include "base/common/numeric.h"

namespace base {

TEST(NumericTest, IsLeadingIntegerTest) {
  EXPECT_FALSE(IsLeadingInteger(-1));
  EXPECT_FALSE(IsLeadingInteger(0));
  EXPECT_TRUE(IsLeadingInteger(1));
  EXPECT_FALSE(IsLeadingInteger(11));
  EXPECT_TRUE(IsLeadingInteger(50));
  EXPECT_FALSE(IsLeadingInteger(230));
  EXPECT_FALSE(IsLeadingInteger(12000));
  EXPECT_TRUE(IsLeadingInteger(50000));
  EXPECT_FALSE(IsLeadingInteger(123456));
}
}  // namespace base
