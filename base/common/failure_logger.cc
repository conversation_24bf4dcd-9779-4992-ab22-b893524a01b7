// Copyright @2019 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/common/failure_logger.h"

#include <cstdlib>

#include "glog/logging.h"
#include "absl/debugging/failure_signal_handler.h"

namespace base {

void InstallFailureLogger(char* arg0) {
  absl::FailureSignalHandlerOptions options;
  absl::InstallFailureSignalHandler(options);
  google::InstallFailureFunction(reinterpret_cast<google::logging_fail_func_t>(&abort));
}

}  // namespace base
