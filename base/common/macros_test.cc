// Copyright @2023 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include <string>

#include "glog/logging.h"
#include "gtest/gtest.h"

#include "base/common/macros.h"
#include "base/common/status.h"


namespace base {
namespace {

struct Response {
  int code = -1;
  std::string data;
};

MUST_USE_RESULT base::Status ProcessResponse(const Response& resp, std::string* result) {
  CHECK(result != nullptr);
  if (resp.code != 200) {
    return base::Status("invalid input");
  }
  *result = resp.data;
  return Status::OK();
}

std::string GetCurrentLineInfo() {
  return CURRENT_LINE_INFO();
}

}  // namespace

class A {
 public:
  A() = default;
  virtual ~A() = default;

  MUST_USE_RESULT virtual bool Run() { return true; }
  MUST_USE_RESULT virtual bool Run1() { return true; }
  MUST_USE_RESULT virtual bool Run2() { return true; }
  MUST_USE_RESULT bool Execute() { return true; }
  MUST_USE_RESULT bool Execute1() { return true; }
  bool Execute2() { return true; }

 protected:
  MUST_USE_RESULT virtual bool RunProtected() { return true; }
  MUST_USE_RESULT virtual bool RunProtected1() { return true; }
  MUST_USE_RESULT virtual bool RunProtected2() { return true; }
  MUST_USE_RESULT bool ExecuteProtected() { return true; }
  MUST_USE_RESULT bool ExecuteProtected1() { return true; }
  bool ExecuteProtected2() { return true; }

 private:
  MUST_USE_RESULT virtual bool RunPrivate() { return true; }
  MUST_USE_RESULT virtual bool RunPrivate1() { return true; }
  MUST_USE_RESULT virtual bool RunPrivate2() { return true; }
  MUST_USE_RESULT bool ExecutePrivate() { return true; }
  MUST_USE_RESULT bool ExecutePrivate1() { return true; }
  bool ExecutePrivate2() { return true; }

  FRIEND_TEST(MacroTest, TestMustUseResultMacro);

  DISALLOW_COPY_AND_ASSIGN(A);
};

class B : public A {
 public:
  B() = default;
  virtual ~B() = default;

  bool Run() override { return true; }
  bool Execute() { return true; }

 protected:
  bool RunProtected() override { return true; }
  bool ExecuteProtected() { return true; }

 private:
  bool RunPrivate() override { return true; }
  bool ExecutePrivate() { return true; }

  FRIEND_TEST(MacroTest, TestMustUseResultMacro);

  DISALLOW_COPY_AND_ASSIGN(B);
};

TEST(MacroTest, TestMustUseResultMacro) {
  {
    // Basic test.
    Response response;
    std::string ret;
    // ProcessResponse(response, &ret);  // Cannot pass compile.
    EXPECT_NE(Status::OK(), ProcessResponse(response, &ret));
    response.code = 200;
    // ProcessResponse(response, &ret);  // Still cannot pass compile.
    EXPECT_EQ(Status::OK(), ProcessResponse(response, &ret));
  }
  {
    // OOP test.
    A a;
    B b;

    // Public
    // a.Run();  // Cannot pass compile.
    // a.Execute();  // Cannot pass compile.
    EXPECT_TRUE(a.Run());
    EXPECT_TRUE(a.Execute());
    b.Run();  // Able to pass compile.
    // b.Run1();  // Cannot pass compile.
    // b.Run2();  // Cannot pass compile.
    b.Execute();  // Able to pass compile.
    // b.Execute1();  // Cannot pass compile.
    b.Execute2();  // Able to pass compile.

    // Protected
    // a.RunProtected();  // Cannot pass compile.
    // a.ExecuteProtected();  // Cannot pass compile.
    EXPECT_TRUE(a.RunProtected());
    EXPECT_TRUE(a.ExecuteProtected());
    b.RunProtected();  // Able to pass compile.
    // b.RunProtected1();  // Cannot pass compile.
    // b.RunProtected2();  // Cannot pass compile.
    b.ExecuteProtected();  // Able to pass compile.
    // b.ExecuteProtected1();  // Cannot pass compile.
    b.ExecuteProtected2();  // Able to pass compile.

    // Private
    // a.RunPrivate();  // Cannot pass compile.
    // a.ExecutePrivate();  // Cannot pass compile.
    EXPECT_TRUE(a.RunPrivate());
    EXPECT_TRUE(a.ExecutePrivate());
    b.RunPrivate();  // Able to pass compile.
    // b.RunPrivate1();  // Cannot pass compile.
    // b.RunPrivate2();  // Cannot pass compile.
    b.ExecutePrivate();  // Able to pass compile.
    // b.ExecutePrivate1();  // Cannot pass compile.
    b.ExecutePrivate2();  // Able to pass compile.
  }
}

TEST(MacrosTest, TestMacroCURRENT_LINE_INFO) {
  EXPECT_EQ("base/common/macros_test.cc:31:GetCurrentLineInfo", GetCurrentLineInfo());
}

}  // namespace base




