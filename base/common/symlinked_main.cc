// Copyright @2025 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include <string>

#ifdef STRIP_MAIN_SUFFIX
#define TO_FUNC(name) name##_main
#else
#define TO_FUNC(name) name
#endif
#define ITER_MAIN(x) int TO_FUNC(x)(int argc, char** argv);
ITER_ALL_MAINS
#undef ITER_MAIN

namespace {

bool EraseSuffix(const std::string& suffix, std::string* str) {
  if (str->size() < suffix.size() ||
      str->compare(str->size() - suffix.size(), suffix.size(), suffix) != 0) {
    return false;
  }
  str->resize(str->size() - suffix.size());
  return true;
}

}  // namespace

int main(int argc, char** argv) {
  std::string program_name = argv[0];
  size_t pos = program_name.find_last_of('/');
  if (pos == std::string::npos) {
    pos = program_name.find_last_of('\\');
  }
  if (pos != std::string::npos) {
    program_name = program_name.substr(pos + 1);
  }
  std::string key_name = program_name;
  EraseSuffix(".stripped", &key_name);

#define ITER_MAIN(x)               \
  if (key_name == #x) {            \
    return TO_FUNC(x)(argc, argv); \
  }
  ITER_ALL_MAINS
#undef ITER_MAIN

  fprintf(stderr,
          "ERROR: The file named \"%s\" should not be run. Please run as a symlink named:\n%s",
          program_name.c_str(),
#define ITER_MAIN(x) "  - " #x "\n"
          ITER_ALL_MAINS);
#undef ITER_MAIN

  return 1;
}
