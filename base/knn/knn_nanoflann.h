// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <algorithm>
#include <memory>
#include <vector>
#include <utility>

#include "Eigen/Core"
#include "glog/logging.h"

#include "base/common/macros.h"
#include "third_party/nanoflann/include/KDTreeVectorOfVectorsAdaptor.h"
#include "third_party/nanoflann/include/nanoflann.hpp"

namespace base {
namespace knn {

template <typename FloatType>
using EigenVector = Eigen::Matrix<FloatType, Eigen::Dynamic, 1>;

template <typename FloatType>
using EigenMatrixAdaptor =
    nanoflann::KDTreeEigenMatrixAdaptor<Eigen::Matrix<FloatType, Eigen::Dynamic, Eigen::Dynamic>>;

template <typename FloatType>
using VectorAdaptor = KDTreeVectorOfVectorsAdaptor<
    std::vector<EigenVector<FloatType>, Eigen::aligned_allocator<EigenVector<FloatType>>>,
    FloatType>;

template <typename FloatType>
using Vector3AdaptorL2S = KDTreeVectorOfVectorsAdaptor<
    std::vector<EigenVector<FloatType>, Eigen::aligned_allocator<EigenVector<FloatType>>>,
    FloatType, 3, nanoflann::metric_L2_Simple>;

template <typename DataAdaptor, typename FloatType>
class KnnNanoFlann {
 public:
  using MatrixType = Eigen::Matrix<FloatType, Eigen::Dynamic, Eigen::Dynamic>;
  using VectorType =
      std::vector<EigenVector<FloatType>, Eigen::aligned_allocator<EigenVector<FloatType>>>;

  KnnNanoFlann() = default;
  explicit KnnNanoFlann(const MatrixType& points, int leaf_size = 20);
  explicit KnnNanoFlann(const VectorType& points, int leaf_size = 20);

  void SetInputMatrix(const MatrixType& points, int leaf_size = 20);
  void SetInputVector(const VectorType& points, int leaf_size = 20);
  int Search(const EigenVector<FloatType>& point, int k, std::vector<int>* indices,
             std::vector<FloatType>* sqr_distances) const;
  bool SearchNearestWithoutParamCheck(
      const EigenVector<FloatType>& point, int* indice, FloatType* sqr_distance) const;
  // radius search, results given in the form (point_index, distance_squared)
  size_t Search(const EigenVector<FloatType>& point, FloatType radius,
                std::vector<std::pair<size_t, FloatType>>* result) const;

 private:
  const nanoflann::SearchParams KDefaultSearchParam;
  int num_points_ = 0;
  int leaf_size_ = 20;
  int point_dim_ = 0;
  bool is_initialized_ = false;
  std::unique_ptr<DataAdaptor> tree_;

  DISALLOW_COPY_AND_ASSIGN(KnnNanoFlann);
};

template <typename DataAdaptor, typename FloatType>
KnnNanoFlann<DataAdaptor, FloatType>::KnnNanoFlann(const MatrixType& points, int leaf_size) {
  SetInputMatrix(points, leaf_size);
}

template <typename DataAdaptor, typename FloatType>
KnnNanoFlann<DataAdaptor, FloatType>::KnnNanoFlann(const VectorType& points, int leaf_size) {
  SetInputVector(points, leaf_size);
}

template <typename DataAdaptor, typename FloatType>
void KnnNanoFlann<DataAdaptor, FloatType>::SetInputMatrix(const MatrixType& points, int leaf_size) {
  num_points_ = points.cols();
  CHECK_GT(num_points_, 0);
  leaf_size_ = leaf_size;
  point_dim_ = points.rows();
  tree_ = std::make_unique<DataAdaptor>(points, leaf_size_);
  is_initialized_ = true;
}

template <typename DataAdaptor, typename FloatType>
void KnnNanoFlann<DataAdaptor, FloatType>::SetInputVector(const VectorType& points, int leaf_size) {
  num_points_ = points.size();
  CHECK_GT(num_points_, 0);
  leaf_size_ = leaf_size;
  point_dim_ = points[0].rows();
  tree_ = std::make_unique<DataAdaptor>(point_dim_, points, leaf_size_);
  is_initialized_ = true;
}

template <typename DataAdaptor, typename FloatType>
int KnnNanoFlann<DataAdaptor, FloatType>::Search(const EigenVector<FloatType>& point, int k,
                                                 std::vector<int>* indices,
                                                 std::vector<FloatType>* sqr_distances) const {
  CHECK(is_initialized_);
  CHECK_GT(k, 0);
  CHECK_EQ(point_dim_, point.rows());
  CHECK(indices != nullptr);
  CHECK(sqr_distances != nullptr);

  indices->clear();
  sqr_distances->clear();
  const int num_results = std::min(k, num_points_);
  indices->resize(num_results);
  sqr_distances->resize(num_results);
  nanoflann::KNNResultSet<FloatType, int> result_set(num_results);
  result_set.init(&(*indices)[0], &(*sqr_distances)[0]);
  CHECK(tree_->index->findNeighbors(result_set, point.data(), KDefaultSearchParam));
  return result_set.size();
}

// For performance consideration, we skip the input param check. Use with caution!
template <typename DataAdaptor, typename FloatType>
bool KnnNanoFlann<DataAdaptor, FloatType>::SearchNearestWithoutParamCheck(
    const EigenVector<FloatType>& point, int* indice, FloatType* sqr_distance) const {
  nanoflann::KNNResultSet<FloatType, int> result_set(1);
  result_set.init(indice, sqr_distance);
  tree_->index->findNeighbors(result_set, point.data(), KDefaultSearchParam);
  return result_set.size() > 0;
}

template <typename DataAdaptor, typename FloatType>
size_t KnnNanoFlann<DataAdaptor, FloatType>::Search(
    const EigenVector<FloatType>& point, FloatType radius,
    std::vector<std::pair<size_t, FloatType>>* result) const {
  CHECK(is_initialized_);
  CHECK_GT(radius, 0.0);
  CHECK_EQ(point_dim_, point.rows());
  CHECK(result != nullptr);
  result->clear();
  return tree_->index->radiusSearch(point.data(), radius * radius, *result, KDefaultSearchParam);
}

}  // namespace knn
}  // namespace base
