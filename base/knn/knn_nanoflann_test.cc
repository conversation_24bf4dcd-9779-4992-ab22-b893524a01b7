// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/knn/knn_nanoflann.h"

#include <limits>

#include "gtest/gtest.h"

#include "base/math/math_util.h"
#include "base/testing/random.h"

namespace base {
namespace knn {
namespace {

template <typename VectorType>
bool BruteForceSearch(VectorType data, const Eigen::Vector3f& point, int* index, float* distance) {
  size_t min_index = -1;
  float min_distance = std::numeric_limits<float>::infinity();
  for (size_t i = 0; i < data.size(); ++i) {
    const float distance = (data[i] - point).norm();
    if (distance < min_distance) {
      min_distance = distance;
      min_index = i;
    }
  }
  *index = min_index;
  *distance = min_distance;
  return min_index >= 0;
}

template <typename VectorType>
bool BruteForceRadiusSearch(VectorType data, const Eigen::Vector3f& point, float radius,
                            std::vector<int>* index_vec, std::vector<float>* distance_vec) {
  using Distance = std::pair<float, int>;
  std::vector<Distance> distances;
  for (size_t i = 0; i < data.size(); ++i) {
    const float distance = (data[i] - point).norm();
    distances.emplace_back(distance, i);
  }
  std::sort(distances.begin(), distances.end(),
            [](const Distance& a, const Distance& b) { return a.first < b.first; });
  for (size_t i = 0; i < distances.size(); ++i) {
    if (distances[i].first < radius) {
      index_vec->push_back(distances[i].second);
      distance_vec->push_back(distances[i].first);
    }
  }
  return true;
}

TEST(KnnNanoFlannTest, BasicMatrixTest) {
  const int kNumPoint = 10;
  using KdTree = KnnNanoFlann<EigenMatrixAdaptor<float>, float>;
  KdTree::MatrixType data = Eigen::MatrixXf::Zero(3, kNumPoint);
  for (int i = 0; i < kNumPoint; ++i) {
    data.col(i) << i, i, i;
  }

  auto knn = std::make_unique<KnnNanoFlann<EigenMatrixAdaptor<float>, float>>(data);
  std::vector<int> indices;
  std::vector<float> sqr_distances;
  for (int i = 0; i < kNumPoint; ++i) {
    int num_found = knn->Search(Eigen::Vector3f(i, i, i), 1, &indices, &sqr_distances);
    EXPECT_EQ(1, num_found);
    EXPECT_EQ(1, indices.size());
    EXPECT_EQ(1, sqr_distances.size());
    EXPECT_EQ(i, indices[0]);
    EXPECT_NEAR(0.0, sqr_distances[0], 1e-6);
  }
}

TEST(KnnNanoFlannTest, BasicVectorTest) {
  const int kNumPoint = 10;
  using KdTree = KnnNanoFlann<VectorAdaptor<float>, float>;
  KdTree::VectorType data;
  for (int i = 0; i < kNumPoint; ++i) {
    data.push_back(Eigen::Vector3f(i, i, i));
  }

  auto knn = std::make_unique<KnnNanoFlann<VectorAdaptor<float>, float>>(data);
  std::vector<int> indices;
  std::vector<float> sqr_distances;
  for (int i = 0; i < kNumPoint; ++i) {
    int num_found = knn->Search(Eigen::Vector3f(i, i, i), 1, &indices, &sqr_distances);
    EXPECT_EQ(1, num_found);
    EXPECT_EQ(1, indices.size());
    EXPECT_EQ(1, sqr_distances.size());
    EXPECT_EQ(i, indices[0]);
    EXPECT_NEAR(0.0, sqr_distances[0], 1e-6);
  }
}

TEST(KnnNanoFlannTest, RandomTest) {
  const int kNumPoint = 1000;
  using KdTree = KnnNanoFlann<VectorAdaptor<float>, float>;
  KdTree::VectorType data;
  auto gen_random_Vector3f = [] {
    const float x = static_cast<float>(testing::RandomDouble(-10.0, 10.0));
    const float y = static_cast<float>(testing::RandomDouble(-10.0, 10.0));
    const float z = static_cast<float>(testing::RandomDouble(-10.0, 10.0));
    return Eigen::Vector3f(x, y, z);
  };
  for (int i = 0; i < kNumPoint; ++i) {
    data.push_back(gen_random_Vector3f());
  }

  auto knn = std::make_unique<KnnNanoFlann<VectorAdaptor<float>, float>>(data);
  std::vector<int> indices;
  std::vector<float> sqr_distances;
  for (int i = 0; i < kNumPoint; ++i) {
    const Eigen::Vector3f point = gen_random_Vector3f();
    int num_found = knn->Search(point, 1, &indices, &sqr_distances);
    EXPECT_EQ(1, num_found);
    EXPECT_EQ(1, indices.size());
    EXPECT_EQ(1, sqr_distances.size());
    // SearchNearestWithoutParamCheck Test
    int indice = 0;
    float sqr_distance = 0.0f;
    EXPECT_TRUE(knn->SearchNearestWithoutParamCheck(point, &indice, &sqr_distance));

    int index_ref = 0;
    float distance_ref = 0.0f;
    EXPECT_TRUE(BruteForceSearch(data, point, &index_ref, &distance_ref));
    EXPECT_EQ(index_ref, indices[0]);
    EXPECT_NEAR(math::square(distance_ref), sqr_distances[0], 1e-6);
    EXPECT_NEAR(math::square(distance_ref), sqr_distance, 1e-6);
  }
}

TEST(KnnNanoFlannTest, RadiusSearchTest) {
  const int kNumPoint = 1000;
  using KdTree = KnnNanoFlann<VectorAdaptor<float>, float>;
  KdTree::VectorType data;
  auto gen_random_Vector3f = [] {
    const float x = static_cast<float>(testing::RandomDouble(-10.0, 10.0));
    const float y = static_cast<float>(testing::RandomDouble(-10.0, 10.0));
    const float z = static_cast<float>(testing::RandomDouble(-10.0, 10.0));
    return Eigen::Vector3f(x, y, z);
  };
  for (int i = 0; i < kNumPoint; ++i) {
    data.push_back(gen_random_Vector3f());
  }

  auto knn = std::make_unique<KnnNanoFlann<VectorAdaptor<float>, float>>(data);
  for (int i = 0; i < kNumPoint; ++i) {
    const Eigen::Vector3f point = gen_random_Vector3f();
    std::vector<std::pair<size_t, float>> knn_results;
    size_t knn_found = knn->Search(point, 5.1, &knn_results);
    EXPECT_EQ(knn_found, knn_results.size());

    std::vector<int> brute_indices;
    std::vector<float> brute_distances;
    EXPECT_TRUE(BruteForceRadiusSearch(data, point, 5.1, &brute_indices, &brute_distances));
    EXPECT_EQ(brute_indices.size(), brute_distances.size());

    EXPECT_EQ(knn_found, brute_indices.size());
    for (size_t i = 0; i < knn_results.size(); ++i) {
      EXPECT_EQ(knn_results[i].first, brute_indices[i]);
      EXPECT_NEAR(knn_results[i].second, math::square(brute_distances[i]), 1e-4);
    }
  }
}

}  // namespace
}  // namespace knn
}  // namespace base
