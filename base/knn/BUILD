package(default_visibility = ["//visibility:public"])

cc_library(
    name = "knn_nanoflann",
    hdrs = ["knn_nanoflann.h"],
    deps = [
        "//base/common:macros",
        "//third_party/nanoflann",
        "@eigen",
        "@glog",
    ],
)

cc_test(
    name = "knn_nanoflann_test",
    size = "small",
    srcs = ["knn_nanoflann_test.cc"],
    tags = [
        "ci",
        "ci_cpu",
    ],
    deps = [
        ":knn_nanoflann",
        "//base/math",
        "//base/testing:random",
        "//base/testing:test_main",
    ],
)

cc_binary(
    name = "knn_nanoflann_benchmark",
    srcs = [
        "knn_nanoflann_benchmark.cc",
    ],
    deps = [
        ":knn_nanoflann",
        "@benchmark",
    ],
)
