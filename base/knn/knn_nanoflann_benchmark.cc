// Copyright @2023 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/knn/knn_nanoflann.h"

#include "benchmark/benchmark.h"

namespace base {
namespace knn {
namespace {

constexpr int kMaxElementsSize = 1e4;
constexpr int kTestCount = 100;

void BM_VectorSearchNearest(benchmark::State& state) {  // NOLINT(runtime/references)
  using KdTree = base::knn::KnnNanoFlann<base::knn::Vector3AdaptorL2S<float>, float>;
  for (auto _ : state) {
    state.PauseTiming(); // Stop timers. They will not count until they are resumed.
    KdTree::VectorType eig_vec;
    for (int i = 0; i < kMaxElementsSize; ++i) {
      eig_vec.push_back(static_cast<float>(i) * Eigen::Vector3f::Ones());
    }
    KdTree kd_tree;
    kd_tree.SetInputVector(eig_vec);
    state.ResumeTiming(); // And resume timers. They are now counting again.
    int indice;
    float dis_sq;
    for (int i = 0; i < kTestCount; ++i) {
      for (int j = 0; j < kMaxElementsSize; ++j) {
        const Eigen::Vector3f search_point = (static_cast<float>(j) - 0.3) * Eigen::Vector3f::Ones();
        kd_tree.SearchNearestWithoutParamCheck(search_point, &indice, &dis_sq);
      }
    }
  }
}

BENCHMARK(BM_VectorSearchNearest)->Unit(benchmark::kMillisecond);

void BM_VectorSearch(benchmark::State& state) {  // NOLINT(runtime/references)
  using KdTree = base::knn::KnnNanoFlann<base::knn::VectorAdaptor<float>, float>;
  for (auto _ : state) {
    state.PauseTiming(); // Stop timers. They will not count until they are resumed.
    KdTree::VectorType eig_vec;
    for (int i = 0; i < kMaxElementsSize; ++i) {
      eig_vec.push_back(static_cast<float>(i) * Eigen::Vector3f::Ones());
    }
    KdTree kd_tree;
    kd_tree.SetInputVector(eig_vec);
    state.ResumeTiming(); // And resume timers. They are now counting again.
    std::vector<int> indices;
    std::vector<float> dis_sqs;
    for (int i = 0; i < kTestCount; ++i) {
      for (int j = 0; j < kMaxElementsSize; ++j) {
        const Eigen::Vector3f search_point = (static_cast<float>(j) - 0.3) * Eigen::Vector3f::Ones();
        kd_tree.Search(search_point, 1, &indices, &dis_sqs);
      }
    }
  }
}

BENCHMARK(BM_VectorSearch)->Unit(benchmark::kMillisecond);

}  // namespace
}  // namespace knn
}  // namespace base

BENCHMARK_MAIN();

// clang-format off
/*
Run on (16 X 4900 MHz CPU s)
CPU Caches:
  L1 Data 48K (x8)
  L1 Instruction 32K (x8)
  L2 Unified 512K (x8)
  L3 Unified 16384K (x1)
Load Average: 1.87, 0.77, 0.39
***WARNING*** CPU scaling is enabled, the benchmark real time measurements may be noisy and will incur extra overhead.
-----------------------------------------------------------------
Benchmark                       Time             CPU   Iterations
-----------------------------------------------------------------
BM_VectorSearchNearest        283 ms          283 ms            2
BM_VectorSearch               340 ms          340 ms            2
*/
