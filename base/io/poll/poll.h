// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <sys/epoll.h>

#include <algorithm>
#include <vector>

#include "absl/time/time.h"
#include "glog/logging.h"

#include "base/common/optional.h"

namespace base {
namespace io {

class PollEvent {
 public:
  static constexpr int kInEvent = (1 << 1);
  static constexpr int kOutEvent = (1 << 2);
  static constexpr int kErrEvent = (1 << 3);
  static constexpr int kHupEvent = (1 << 4);

  PollEvent(int fd, int event) : fd_(fd), event_(event) { CHECK_GE(fd_, 0); }

  int fd() const { return fd_; }
  int event() const { return event_; }

  bool IsReadable() const {
    static constexpr int kReadableEventMask = (kInEvent | kErrEvent | kHupEvent);
    return (event_ & kReadableEventMask) != 0;
  }

  bool IsWritable() const {
    static constexpr int kWritableEventMask = (kOutEvent | kErrEvent | kHupEvent);
    return (event_ & kWritableEventMask) != 0;
  }

 private:
  int fd_ = -1;
  int event_ = 0;
};

enum class PollEventType {
  kReadableEvent = (1 << 1),
  kWritableEvent = (1 << 2),
  kAllEvent = (kReadableEvent | kWritableEvent),
};

class Epoll {
 public:
  Epoll();

  virtual ~Epoll();

  int size() const { return std::max<int>(0, epoll_events_.size() - 1); }

  bool Add(int fd, PollEventType type);

  bool Delete(int fd);

  bool Wait(std::vector<PollEvent>* fired_events,
            base::Optional<absl::Duration> timeout = base::none);

  void Signal();

 private:
  int epoll_fd_ = -1;
  int event_fd_ = -1;

  std::vector<struct epoll_event> epoll_events_;
};

base::Optional<PollEvent> SyncWait(int fd, PollEventType type,
                                   base::Optional<absl::Duration> timeout = base::none);

}  // namespace io
}  // namespace base
