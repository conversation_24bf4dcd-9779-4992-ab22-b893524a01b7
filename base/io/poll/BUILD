package(default_visibility = ["//visibility:public"])

cc_library(
    name = "poll",
    srcs = [
        "poll.cc",
    ],
    hdrs = [
        "poll.h",
    ],
    deps = [
        "//base/common:eintr_wrapper",
        "//base/common:optional",
        "//base/strings:macros",
        "@com_google_absl//absl/time",
    ],
)

cc_test(
    name = "poll_test",
    srcs = [
        "poll_test.cc",
    ],
    deps = [
        ":poll",
        "//base/testing:test_main",
    ],
)
