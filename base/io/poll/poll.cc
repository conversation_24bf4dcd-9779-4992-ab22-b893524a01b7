// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/io/poll/poll.h"

#include <poll.h>
#include <sys/eventfd.h>
#include <unistd.h>

#include <algorithm>

#include "base/common/eintr_wrapper.h"
#include "base/strings/macros.h"

namespace base {
namespace io {

namespace {

bool IsReadableEventType(PollEventType type) {
  return static_cast<int>(type) & static_cast<int>(PollEventType::kReadableEvent);
}

bool IsWritableEventType(PollEventType type) {
  return static_cast<int>(type) & static_cast<int>(PollEventType::kWritableEvent);
}

}  // namespace

constexpr int PollEvent::kInEvent;
constexpr int PollEvent::kOutEvent;
constexpr int PollEvent::kErrEvent;
constexpr int PollEvent::kHupEvent;

Epoll::Epoll() {
  epoll_fd_ = epoll_create1(0);
  CHECK_GE(epoll_fd_, 0) << "Failed to create epoll_fd_, " << DUMP_TO_STREAM(errno) << ":"
                         << strerror(errno);
  event_fd_ = eventfd(0, EFD_NONBLOCK);
  CHECK_GE(event_fd_, 0) << "Failed to create event_fd_, " << DUMP_TO_STREAM(errno) << ":"
                         << strerror(errno);
  CHECK(Add(event_fd_, PollEventType::kReadableEvent))
      << "Failed to Add " << DUMP_TO_STREAM(event_fd_) << " to " << DUMP_TO_STREAM(epoll_fd_);
}

Epoll::~Epoll() {
  HANDLE_EINTR(close(event_fd_));
  HANDLE_EINTR(close(epoll_fd_));
}

bool Epoll::Add(int fd, PollEventType type) {
  struct epoll_event epoll_event = {
      .events = 0,
      .data = {.fd = fd},
  };
  if (IsReadableEventType(type)) {
    epoll_event.events |= EPOLLIN;
  }
  if (IsWritableEventType(type)) {
    epoll_event.events |= EPOLLOUT;
  }
  if (epoll_ctl(epoll_fd_, EPOLL_CTL_ADD, fd, &epoll_event) != 0) {
    CHECK_EQ(errno, EEXIST) << "Failed to Add event, " << DUMP_TO_STREAM(fd, errno) << ":"
                            << strerror(errno);
    return false;
  }
  epoll_events_.emplace_back();
  return true;
}

bool Epoll::Delete(int fd) {
  struct epoll_event epoll_event = {
      .events = 0,
      .data = {.fd = fd},
  };
  if (epoll_ctl(epoll_fd_, EPOLL_CTL_DEL, fd, &epoll_event) != 0) {
    CHECK_EQ(errno, ENOENT) << "Failed to Delete event, " << DUMP_TO_STREAM(fd, errno) << ":"
                            << strerror(errno);
    return false;
  }
  CHECK_NE(0, epoll_events_.size());
  epoll_events_.pop_back();
  return true;
}

bool Epoll::Wait(std::vector<PollEvent>* fired_events, base::Optional<absl::Duration> timeout) {
  CHECK(fired_events != nullptr);
  int epoll_events_size = static_cast<int>(epoll_events_.size());
  int epoll_timeout =
      (!timeout ? -1 : std::max(0, static_cast<int>(absl::ToInt64Milliseconds(*timeout))));
  int num_fired_events =
      IGNORE_EINTR(epoll_wait(epoll_fd_, epoll_events_.data(), epoll_events_size, epoll_timeout));
  CHECK_GE(num_fired_events, 0) << "Bad epoll_wait, " << DUMP_TO_STREAM(epoll_fd_, errno) << ":"
                                << strerror(errno);
  bool signaled = false;
  for (int i = 0; i < num_fired_events; ++i) {
    const struct epoll_event& epoll_event = epoll_events_[i];
    int event = 0;
    if (epoll_event.events & EPOLLIN) {
      event |= PollEvent::kInEvent;
    }
    if (epoll_event.events & EPOLLOUT) {
      event |= PollEvent::kOutEvent;
    }
    if (epoll_event.events & EPOLLERR) {
      event |= PollEvent::kErrEvent;
    }
    if (epoll_event.events & EPOLLHUP) {
      event |= PollEvent::kHupEvent;
    }
    int fd = epoll_event.data.fd;
    if (fd == event_fd_) {
      uint64_t data = 0;
      int n = read(event_fd_, &data, sizeof(data));
      CHECK_EQ(n, sizeof(data)) << "Bad read, " << DUMP_TO_STREAM(event_fd_, n, errno) << ":"
                                << strerror(errno);
      signaled = true;
      continue;
    }
    fired_events->emplace_back(fd, event);
  }
  return signaled;
}

void Epoll::Signal() {
  uint64_t data = 1;
  int n = write(event_fd_, &data, sizeof(data));
  CHECK_EQ(n, sizeof(data)) << "Bad write, " << DUMP_TO_STREAM(event_fd_, n, errno) << ":"
                            << strerror(errno);
}

base::Optional<PollEvent> SyncWait(int fd, PollEventType type,
                                   base::Optional<absl::Duration> timeout) {
  int poll_timeout =
      (!timeout ? -1 : std::max(0, static_cast<int>(absl::ToInt64Milliseconds(*timeout))));
  struct pollfd pollfd = {
      .fd = fd,
      .events = 0,
      .revents = 0,
  };
  if (IsReadableEventType(type)) {
    pollfd.events |= POLLIN;
  }
  if (IsWritableEventType(type)) {
    pollfd.events |= POLLOUT;
  }
  int num_fired_events = IGNORE_EINTR(poll(&pollfd, 1, poll_timeout));
  CHECK_GE(num_fired_events, 0) << "Bad poll, " << DUMP_TO_STREAM(fd, errno) << ":"
                                << strerror(errno);
  if (num_fired_events == 0) {
    return base::none;
  }
  int event = 0;
  if (pollfd.revents & POLLIN) {
    event |= PollEvent::kInEvent;
  }
  if (pollfd.revents & POLLOUT) {
    event |= PollEvent::kOutEvent;
  }
  if (pollfd.revents & POLLERR) {
    event |= PollEvent::kErrEvent;
  }
  if (pollfd.revents & POLLHUP) {
    event |= PollEvent::kHupEvent;
  }
  return PollEvent(fd, event);
}

}  // namespace io
}  // namespace base
