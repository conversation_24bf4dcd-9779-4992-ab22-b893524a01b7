// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/io/poll/poll.h"

#include <fcntl.h>
#include <unistd.h>

#include <memory>
#include <thread>
#include <utility>
#include <vector>

#include "absl/time/clock.h"
#include "gtest/gtest.h"

namespace base {
namespace io {

namespace {

class Pipe {
 public:
  Pipe() { CHECK_EQ(pipe2(fd_, O_NONBLOCK), 0); }

  ~Pipe() {
    Close(0);
    Close(1);
  }

  int fd(int i) const {
    CHECK_GE(i, 0);
    CHECK_LT(i, 2);
    return fd_[i];
  }

  void Close(int i) {
    CHECK_GE(i, 0);
    CHECK_LT(i, 2);
    if (fd_[i] >= 0) {
      CHECK_EQ(close(fd_[i]), 0);
      fd_[i] = -1;
    }
  }

 private:
  int fd_[2] = {-1, -1};
};

}  // namespace

class PollTest : public ::testing::Test {
 public:
  void SetUp() override {
    epoll_ = std::make_unique<Epoll>();
    ASSERT_EQ(epoll_->size(), 0);
  }

  void TearDown() override { epoll_ = nullptr; }

 protected:
  std::unique_ptr<Epoll> epoll_;
};

TEST_F(PollTest, AddAndDelete) {
  Pipe pipe0;
  ASSERT_TRUE(epoll_->Add(pipe0.fd(0), PollEventType::kReadableEvent));
  ASSERT_EQ(epoll_->size(), 1);
  ASSERT_TRUE(epoll_->Add(pipe0.fd(1), PollEventType::kWritableEvent));
  ASSERT_EQ(epoll_->size(), 2);

  ASSERT_FALSE(epoll_->Add(pipe0.fd(0), PollEventType::kReadableEvent));
  ASSERT_FALSE(epoll_->Add(pipe0.fd(1), PollEventType::kReadableEvent));

  ASSERT_TRUE(epoll_->Delete(pipe0.fd(0)));
  ASSERT_EQ(epoll_->size(), 1);
  ASSERT_TRUE(epoll_->Delete(pipe0.fd(1)));
  ASSERT_EQ(epoll_->size(), 0);

  ASSERT_FALSE(epoll_->Delete(pipe0.fd(0)));
  ASSERT_FALSE(epoll_->Delete(pipe0.fd(1)));
}

TEST_F(PollTest, Wait0) {
  std::vector<PollEvent> fired_events;
  ASSERT_FALSE(epoll_->Wait(&fired_events, absl::ZeroDuration()));

  Pipe pipe0;
  ASSERT_TRUE(epoll_->Add(pipe0.fd(0), PollEventType::kReadableEvent));
  ASSERT_FALSE(epoll_->Wait(&fired_events, absl::ZeroDuration()));
  ASSERT_EQ(fired_events.size(), 0);

  ASSERT_TRUE(epoll_->Add(pipe0.fd(1), PollEventType::kWritableEvent));
  ASSERT_FALSE(epoll_->Wait(&fired_events, absl::ZeroDuration()));
  ASSERT_EQ(fired_events.size(), 1);
  ASSERT_EQ(fired_events[0].fd(), pipe0.fd(1));
  ASSERT_EQ(fired_events[0].event(), PollEvent::kOutEvent);
  fired_events.clear();

  int64_t value = 0xdeadbeaf;
  ASSERT_EQ(sizeof(value), write(pipe0.fd(1), &value, sizeof(value)));
  ASSERT_FALSE(epoll_->Wait(&fired_events, absl::ZeroDuration()));
  ASSERT_EQ(fired_events.size(), 2);
  if (fired_events[0].fd() != pipe0.fd(0)) {
    std::swap(fired_events[0], fired_events[1]);
  }
  ASSERT_EQ(fired_events[0].fd(), pipe0.fd(0));
  ASSERT_EQ(fired_events[0].event(), PollEvent::kInEvent);
  ASSERT_EQ(fired_events[1].fd(), pipe0.fd(1));
  ASSERT_EQ(fired_events[1].event(), PollEvent::kOutEvent);
  ASSERT_EQ(sizeof(value), read(pipe0.fd(0), &value, sizeof(value)));
  ASSERT_EQ(value, 0xdeadbeaf);
  fired_events.clear();

  ASSERT_FALSE(epoll_->Wait(&fired_events, absl::Hours(1)));
  ASSERT_EQ(fired_events.size(), 1);
  ASSERT_EQ(fired_events[0].fd(), pipe0.fd(1));
  ASSERT_EQ(fired_events[0].event(), PollEvent::kOutEvent);
  fired_events.clear();

  ASSERT_TRUE(epoll_->Delete(pipe0.fd(0)));
  ASSERT_TRUE(epoll_->Delete(pipe0.fd(1)));
  ASSERT_FALSE(epoll_->Wait(&fired_events, absl::ZeroDuration()));
  ASSERT_EQ(fired_events.size(), 0);
}

TEST_F(PollTest, Wait1) {
  Pipe pipe0;
  ASSERT_TRUE(epoll_->Add(pipe0.fd(1), PollEventType::kWritableEvent));

  int64_t round = 0;
  while (true) {
    std::vector<PollEvent> fired_events;
    ASSERT_FALSE(epoll_->Wait(&fired_events, absl::ZeroDuration()));
    if (fired_events.empty()) {
      break;
    }
    ASSERT_EQ(fired_events[0].fd(), pipe0.fd(1));
    ASSERT_EQ(sizeof(round), write(pipe0.fd(1), &round, sizeof(round)));
    round++;
  }
  ASSERT_TRUE(epoll_->Delete(pipe0.fd(1)));

  ASSERT_TRUE(epoll_->Add(pipe0.fd(0), PollEventType::kReadableEvent));
  int64_t sum = 0;
  while (true) {
    std::vector<PollEvent> fired_events;
    ASSERT_FALSE(epoll_->Wait(&fired_events, absl::ZeroDuration()));
    if (fired_events.empty()) {
      break;
    }
    ASSERT_EQ(fired_events[0].fd(), pipe0.fd(0));
    int64_t value = 0;
    ASSERT_EQ(sizeof(value), read(pipe0.fd(0), &value, sizeof(value)));
    sum += value;
  }
  ASSERT_EQ(sum, (round - 1) * round / 2);
}

TEST_F(PollTest, Wait3) {
  Pipe pipe0;
  ASSERT_TRUE(epoll_->Add(pipe0.fd(0), PollEventType::kReadableEvent));

  int64_t value = 0;
  ASSERT_EQ(-1, read(pipe0.fd(0), &value, sizeof(value)));
  ASSERT_TRUE(errno == EWOULDBLOCK || errno == EAGAIN);

  pipe0.Close(1);

  std::vector<PollEvent> fired_events;
  ASSERT_FALSE(epoll_->Wait(&fired_events, absl::ZeroDuration()));
  ASSERT_EQ(fired_events.size(), 1);
  ASSERT_EQ(fired_events[0].fd(), pipe0.fd(0));
  ASSERT_EQ(fired_events[0].event(), PollEvent::kHupEvent);
  ASSERT_EQ(0, read(pipe0.fd(0), &value, sizeof(value)));
}

TEST_F(PollTest, Signal) {
  epoll_->Signal();

  std::vector<PollEvent> fired_events;
  ASSERT_TRUE(epoll_->Wait(&fired_events, absl::Hours(1)));
  ASSERT_TRUE(fired_events.empty());

  for (int i = 0; i < 10; ++i) {
    epoll_->Signal();
  }
  ASSERT_TRUE(epoll_->Wait(&fired_events, absl::Hours(1)));
  ASSERT_TRUE(fired_events.empty());

  ASSERT_FALSE(epoll_->Wait(&fired_events, absl::ZeroDuration()));
  ASSERT_TRUE(fired_events.empty());

  std::thread t0([this]() {
    absl::SleepFor(absl::Milliseconds(10));
    epoll_->Signal();
  });

  ASSERT_TRUE(epoll_->Wait(&fired_events, absl::Hours(1)));
  ASSERT_TRUE(fired_events.empty());

  t0.join();

  ASSERT_FALSE(epoll_->Wait(&fired_events, absl::ZeroDuration()));
  ASSERT_TRUE(fired_events.empty());
}

TEST_F(PollTest, SyncWait) {
  Pipe pipe0;
  auto event0 = SyncWait(pipe0.fd(1), PollEventType::kWritableEvent, absl::ZeroDuration());
  ASSERT_TRUE(event0 != base::none);
  ASSERT_EQ(event0->fd(), pipe0.fd(1));
  ASSERT_EQ(event0->event(), PollEvent::kOutEvent);

  auto event1 = SyncWait(pipe0.fd(0), PollEventType::kReadableEvent, absl::ZeroDuration());
  ASSERT_TRUE(event1 == base::none);

  std::thread t0([&pipe0]() {
    int64_t value = 0xdeadbeaf;
    ASSERT_EQ(sizeof(value), write(pipe0.fd(1), &value, sizeof(value)));
    absl::SleepFor(absl::Milliseconds(10));
    pipe0.Close(1);
  });

  auto event2 = SyncWait(pipe0.fd(0), PollEventType::kAllEvent, absl::Hours(1));
  ASSERT_TRUE(event2 != base::none);
  int64_t value = 0;
  ASSERT_EQ(sizeof(value), read(pipe0.fd(0), &value, sizeof(value)));
  ASSERT_EQ(value, 0xdeadbeaf);

  t0.join();

  auto event3 = SyncWait(pipe0.fd(0), PollEventType::kAllEvent, absl::Hours(1));
  ASSERT_TRUE(event3 != base::none);
  ASSERT_EQ(event3->event(), PollEvent::kHupEvent);
}

}  // namespace io
}  // namespace base
