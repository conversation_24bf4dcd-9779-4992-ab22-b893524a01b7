package(default_visibility = ["//visibility:public"])

cc_library(
    name = "base_file_writer",
    srcs = ["base_file_writer.cc"],
    hdrs = ["base_file_writer.h"],
    deps = [
        "//base/common:macros",
        "@com_google_protobuf//:protobuf",
        "@glog",
    ],
)

cc_test(
    name = "base_file_writer_test",
    srcs = ["base_file_writer_test.cc"],
    tags = [
        "ci",
        "ci_cpu",
    ],
    deps = [
        ":base_file_writer",
        "//base/file/proto:cc_test_proto",
        "//base/testing:test_main",
    ],
)

cc_library(
    name = "local_file_writer",
    srcs = ["local_file_writer.cc"],
    hdrs = ["local_file_writer.h"],
    deps = [
        ":base_file_writer",
        "//base/file:file_util",
    ],
)

cc_test(
    name = "local_file_writer_test",
    srcs = ["local_file_writer_test.cc"],
    tags = [
        "ci",
        "ci_cpu",
    ],
    deps = [
        ":local_file_writer",
        "//base/file:file_path_util",
        "//base/file/proto:cc_test_proto",
        "//base/testing:scoped_temp_dir",
        "//base/testing:test_main",
    ],
)

cc_library(
    name = "async_file_writer",
    srcs = ["async_file_writer.cc"],
    hdrs = ["async_file_writer.h"],
    deps = [
        ":local_file_writer",
        "//base/thread:thread_pool",
        "@glog",
    ],
)

cc_test(
    name = "async_file_writer_test",
    srcs = ["async_file_writer_test.cc"],
    tags = [
        "ci",
        "ci_cpu",
    ],
    deps = [
        ":async_file_writer",
        "//base/file:file_path_util",
        "//base/file:file_util",
        "//base/file/proto:cc_test_proto",
        "//base/testing:scoped_temp_dir",
        "//base/testing:test_main",
    ],
)

cc_library(
    name = "proto_to_parquet_file_writer",
    hdrs = ["proto_to_parquet_file_writer.h"],
    deps = select({
        "//:orin_jetson": [],
        "//:use_horizon": [],
        "//:orin_drive": [],
        "//conditions:default": [
            "@arrow",
            "@com_google_protobuf//:protobuf",
            "@glog",
        ],
    }),
)
