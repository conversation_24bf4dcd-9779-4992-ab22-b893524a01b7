// Copyright @2023 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/io/file_writer/local_file_writer.h"

namespace base {
namespace io {

bool LocalFileWriter::WriteProtoInternal(const std::string& path,
                                         const google::protobuf::Message& message) {
  if (!base::WriteBinaryProtoToFile(path, message)) {
    LOG(ERROR) << "Export WriteBinaryProtoToFile to " << path << " failed";
    return false;
  }
  return true;
}

bool LocalFileWriter::WriteContentInternal(const std::string& path, const std::string& contents) {
  if (!base::WriteContentToFile(path, contents)) {
    LOG(ERROR) << "Export WriteContentToFile to " << path << " failed";
    return false;
  }
  return true;
}

}  // namespace io
}  // namespace base
