// Copyright @2023 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/io/file_writer/base_file_writer.h"

#include "google/protobuf/text_format.h"
#include "gtest/gtest.h"

#include "base/file/proto/test.pb.h"

namespace base {
namespace io {

namespace {

class FakeFileWriter : public BaseFileWriter {
 public:
  FakeFileWriter() {}
  ~FakeFileWriter() {}

 private:
  bool WriteProtoInternal(const std::string& path,
                          const google::protobuf::Message& message) override {
    return true;
  }
  bool WriteContentInternal(const std::string& path, const std::string& contents) override {
    return true;
  }

  DISALLOW_COPY_AND_ASSIGN(FakeFileWriter);
};

}  // namespace

TEST(BaseFileWriterTest, OpenTest) {
  FakeFileWriter fake_file_writer;
  const std::string path = "/tmp/test.txt";
  const std::string test_string = "Hello World";
  TestProto test_proto;
  google::protobuf::TextFormat::ParseFromString(test_string, &test_proto);
  EXPECT_TRUE(fake_file_writer.WriteProto(path, test_proto));
  EXPECT_TRUE(fake_file_writer.WriteContent(path, test_string));
}

}  // namespace io
}  // namespace base
