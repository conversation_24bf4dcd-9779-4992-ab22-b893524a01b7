// Copyright @2023 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <string>

#include "google/protobuf/message.h"

#include "base/common/macros.h"

namespace base {
namespace io {

class BaseFileWriter {
 public:
  BaseFileWriter() = default;
  virtual ~BaseFileWriter() = default;

  bool WriteProto(const std::string& path, const google::protobuf::Message& message);
  bool WriteContent(const std::string& path, const std::string& contents);

 private:
  virtual bool WriteProtoInternal(const std::string& path,
                                  const google::protobuf::Message& message) = 0;
  virtual bool WriteContentInternal(const std::string& path, const std::string& contents) = 0;

  DISALLOW_COPY_AND_ASSIGN(BaseFileWriter);
};

}  // namespace io
}  // namespace base
