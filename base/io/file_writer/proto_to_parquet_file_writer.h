#pragma once

#include <assert.h>

#if !defined(USE_HORIZON) && !defined(USE_JETSON_ORIN) && !defined(USE_DRIVE_ORIN)
#include <arrow/api.h>
#include <arrow/array/array_base.h>
#include <arrow/io/api.h>
#include <arrow/result.h>
#include <arrow/status.h>
#include <arrow/util/type_fwd.h>
#include <google/protobuf/message.h>
#include <google/protobuf/text_format.h>
#include <parquet/arrow/schema.h>
#include <parquet/arrow/writer.h>
#include <parquet/exception.h>
#include <parquet/properties.h>
#include "glog/logging.h"
#endif

namespace base {
namespace io {

enum class ParquetCompressionType { UNCOMPRESSED, GZIP, LZ4, ZSTD };

struct ParquetExporterOptions {
  std::string file_path;
  ParquetCompressionType compression_type = ParquetCompressionType::UNCOMPRESSED;
};

template <typename T>
class ParquetExporter {
 public:
  static_assert(std::is_base_of<google::protobuf::Message, T>::value,
                "T must be a descendant of google::protobuf::Message");

  ParquetExporter(const ParquetExporterOptions& options) {
#if !defined(USE_HORIZON) && !defined(USE_JETSON_ORIN) && !defined(USE_DRIVE_ORIN)

    CreateParquetSchema();

    PARQUET_ASSIGN_OR_THROW(outfile_, arrow::io::FileOutputStream::Open(options.file_path));

    parquet::WriterProperties::Builder builder;
    builder.compression(ConvertCompressionType(options.compression_type));
    std::shared_ptr<parquet::ArrowWriterProperties> arrow_props =
        parquet::ArrowWriterProperties::Builder().store_schema()->build();

    auto writer_result = parquet::arrow::FileWriter::Open(
        *arrow_schema_, arrow::default_memory_pool(), outfile_, builder.build(), arrow_props);
    if (!writer_result.ok()) {
      LOG(ERROR) << "Failed to open file writer: " << writer_result.status().message();
      throw std::runtime_error("Init ParquetExporter error");
    }
    writer_ = std::move(writer_result).ValueOrDie();
#else
  throw std::runtime_error("ParquetExporter only support in cloud environment!");
#endif
  };

  void WriteData(const T& record) {
#if !defined(USE_HORIZON) && !defined(USE_JETSON_ORIN) && !defined(USE_DRIVE_ORIN)
    std::vector<std::shared_ptr<arrow::Array>> columns = {};
    for (std::shared_ptr<ParquetNode> child_field : fields_) {
      std::shared_ptr<arrow::Array> array;
      BuildData(record, child_field);
      PARQUET_THROW_NOT_OK(child_field->GetRootBuilder()->Finish(&array));
      columns.push_back(array);
    };
    auto batch = arrow::RecordBatch::Make(arrow_schema_, 1, columns);
    arrow::Status write_status = writer_->WriteRecordBatch(*batch);
    if (!write_status.ok()) {
      LOG(ERROR) << "Failed to write record : " << write_status.message();
      throw std::runtime_error("Failed to write record");
    }
#else
  throw std::runtime_error("ParquetExporter only support in cloud environment!");
#endif
  };

  void WriteBatch(const std::vector<T>& records) {
#if !defined(USE_HORIZON) && !defined(USE_JETSON_ORIN) && !defined(USE_DRIVE_ORIN)
    if (records.empty()) {
      LOG(ERROR) << "Failed to write batch, param \'records\' is empty";
      return;
    }
    std::vector<std::shared_ptr<arrow::Array>> columns = {};
    for (std::shared_ptr<ParquetNode> child_field : fields_) {
      std::shared_ptr<arrow::Array> array;
      for (const T& record : records) {
        BuildData(record, child_field);
      }
      PARQUET_THROW_NOT_OK(child_field->GetRootBuilder()->Finish(&array));
      columns.push_back(array);
    };
    auto batch = arrow::RecordBatch::Make(arrow_schema_, records.size(), columns);
    arrow::Status write_status = writer_->WriteRecordBatch(*batch);
    if (!write_status.ok()) {
      LOG(ERROR) << "Failed to write record : " << write_status.message();
      throw std::runtime_error("Failed to write record");
    }
#else
  throw std::runtime_error("ParquetExporter only support in cloud environment!");
#endif
  };

#if !defined(USE_HORIZON) && !defined(USE_JETSON_ORIN) && !defined(USE_DRIVE_ORIN)
  ~ParquetExporter() noexcept(false) {
    PARQUET_THROW_NOT_OK(writer_->Close());
    PARQUET_THROW_NOT_OK(outfile_->Close());
  }
#else
  ~ParquetExporter() {}
#endif

#if !defined(USE_HORIZON) && !defined(USE_JETSON_ORIN) && !defined(USE_DRIVE_ORIN)
 private:
  std::shared_ptr<arrow::io::FileOutputStream> outfile_;

  std::unique_ptr<parquet::arrow::FileWriter> writer_;

  std::shared_ptr<arrow::Schema> arrow_schema_;

  struct ParquetNode {
    std::shared_ptr<arrow::ListBuilder> list_builder;
    std::shared_ptr<arrow::StructBuilder> struct_builder;
    std::shared_ptr<arrow::ArrayBuilder> primitive_builder;
    const google::protobuf::FieldDescriptor* field_descriptor;
    std::vector<std::shared_ptr<ParquetNode>> child_nodes;

    std::shared_ptr<arrow::ArrayBuilder> GetRootBuilder() {
      if (list_builder) {
        return list_builder;
      }
      if (struct_builder) {
        return struct_builder;
      }
      assert(primitive_builder != nullptr);
      return primitive_builder;
    }
  };

  std::vector<std::shared_ptr<ParquetNode>> fields_ = {};

  void CreateParquetSchema() {
    std::vector<std::shared_ptr<arrow::Field>> arrow_schema_fields = {};
    const google::protobuf::Descriptor* table_descriptor = T::descriptor();
    for (int i = 0; i < table_descriptor->field_count(); ++i) {
      const google::protobuf::FieldDescriptor* f = table_descriptor->field(i);
      std::shared_ptr<ParquetNode> parquet_node;
      std::shared_ptr<arrow::Field> arrow_field_ptr;
      CreateParquetNode(f, parquet_node, arrow_field_ptr);
      if (!parquet_node) {
        continue;
      }
      fields_.push_back(parquet_node);
      arrow_schema_fields.push_back(arrow_field_ptr);
    }
    arrow_schema_ = arrow::schema(arrow_schema_fields);
  }

  void CreateParquetNode(const google::protobuf::FieldDescriptor* f,
                         std::shared_ptr<ParquetNode>& parquet_node,
                         std::shared_ptr<arrow::Field>& arrow_field_ptr) {
    if (f->message_type() != nullptr && f->message_type()->field_count() == 0) {
      return;
    }
    parquet_node = std::make_shared<ParquetNode>();
    std::shared_ptr<arrow::ArrayBuilder> builder;
    std::shared_ptr<arrow::DataType> type;
    if (f->message_type() != nullptr) {
      std::vector<std::shared_ptr<arrow::ArrayBuilder>> child_builders = {};
      std::vector<std::shared_ptr<arrow::Field>> children_fields = {};
      std::vector<std::shared_ptr<ParquetNode>> child_nodes = {};
      for (int i = 0; i < f->message_type()->field_count(); ++i) {
        const google::protobuf::FieldDescriptor* child_field = f->message_type()->field(i);
        std::shared_ptr<ParquetNode> child_parquet_node;
        std::shared_ptr<arrow::Field> child_arrow_field;
        CreateParquetNode(child_field, child_parquet_node, child_arrow_field);
        if (!child_parquet_node) {
          continue;
        }
        child_builders.push_back(child_parquet_node->GetRootBuilder());
        child_nodes.push_back(child_parquet_node);
        children_fields.push_back(child_arrow_field);
      }
      parquet_node->child_nodes = child_nodes;
      type = arrow::struct_(children_fields);
      parquet_node->struct_builder = std::make_shared<arrow::StructBuilder>(
          type, arrow::default_memory_pool(), child_builders);
      builder = parquet_node->struct_builder;
    } else {
      builder = MakePrimitiveArrowArrayBuilder(f);
      type = MakePrimitiveArrowDataType(f);
      parquet_node->primitive_builder = builder;
    }

    parquet_node->field_descriptor = f;
    if (f->is_repeated()) {
      arrow_field_ptr = arrow::field(f->name(), arrow::list(type));
      parquet_node->list_builder =
          std::make_shared<arrow::ListBuilder>(arrow::default_memory_pool(), builder);
    } else {
      arrow_field_ptr = arrow::field(f->name(), type);
    }
  }

  void BuildData(const google::protobuf::Message& message,
                 std::shared_ptr<ParquetNode> parquet_node) {
    const google::protobuf::Reflection* reflection = message.GetReflection();
    if (parquet_node->list_builder != nullptr) {
      PARQUET_THROW_NOT_OK(parquet_node->list_builder->Append());
      if (parquet_node->struct_builder != nullptr) {
        int field_size = reflection->FieldSize(message, parquet_node->field_descriptor);
        for (int i = 0; i < field_size; ++i) {
          const google::protobuf::Message& child_message =
              reflection->GetRepeatedMessage(message, parquet_node->field_descriptor, i);
          if (child_message.ByteSizeLong() == 0) {
            return;
          }
          PARQUET_THROW_NOT_OK(parquet_node->struct_builder->Append());
          for (std::shared_ptr<ParquetNode> child_node : parquet_node->child_nodes) {
            BuildData(child_message, child_node);
          }
        }
      } else {
        BuildPrimitiveRepeatedData(
            parquet_node->primitive_builder, message, reflection, parquet_node->field_descriptor);
      }
    } else {
      if (parquet_node->struct_builder != nullptr) {
        const google::protobuf::Message& child_message =
            reflection->GetMessage(message, parquet_node->field_descriptor);
        if (child_message.ByteSizeLong() == 0) {
          PARQUET_THROW_NOT_OK(parquet_node->struct_builder->AppendNull());
          return;
        }
        PARQUET_THROW_NOT_OK(parquet_node->struct_builder->Append());
        for (std::shared_ptr<ParquetNode> child_node : parquet_node->child_nodes) {
          BuildData(child_message, child_node);
        }
      } else {
        BuildPrimitiveData(
            parquet_node->primitive_builder, message, reflection, parquet_node->field_descriptor);
      }
    }
  }

  parquet::Compression::type ConvertCompressionType(ParquetCompressionType customType) {
    switch (customType) {
      case ParquetCompressionType::GZIP:
        return parquet::Compression::GZIP;
      case ParquetCompressionType::LZ4:
        return parquet::Compression::LZ4;
      case ParquetCompressionType::ZSTD:
        return parquet::Compression::ZSTD;
      case ParquetCompressionType::UNCOMPRESSED:
      default:
        return parquet::Compression::UNCOMPRESSED;
    }
  }

  void BuildPrimitiveRepeatedData(std::shared_ptr<arrow::ArrayBuilder> primitive_builder,
                                  const google::protobuf::Message& message,
                                  const google::protobuf::Reflection* reflection,
                                  const google::protobuf::FieldDescriptor* field) {
    int field_size = reflection->FieldSize(message, field);
    for (int i = 0; i < field_size; ++i) {
      switch (field->type()) {
        case google::protobuf::FieldDescriptor::TYPE_DOUBLE: {
          auto* double_builder = static_cast<arrow::DoubleBuilder*>(primitive_builder.get());
          PARQUET_THROW_NOT_OK(
              double_builder->AppendValues({reflection->GetRepeatedDouble(message, field, i)}));
          break;
        }
        case google::protobuf::FieldDescriptor::TYPE_FLOAT: {
          auto* float_builder = static_cast<arrow::FloatBuilder*>(primitive_builder.get());
          PARQUET_THROW_NOT_OK(
              float_builder->AppendValues({reflection->GetRepeatedFloat(message, field, i)}));
          break;
        }
        case google::protobuf::FieldDescriptor::TYPE_INT64: {
          auto* int64_builder = static_cast<arrow::Int64Builder*>(primitive_builder.get());
          PARQUET_THROW_NOT_OK(
              int64_builder->AppendValues({reflection->GetRepeatedInt64(message, field, i)}));
          break;
        }
        case google::protobuf::FieldDescriptor::TYPE_UINT64: {
          auto* uint64_builder = static_cast<arrow::UInt64Builder*>(primitive_builder.get());
          PARQUET_THROW_NOT_OK(
              uint64_builder->AppendValues({reflection->GetRepeatedUInt64(message, field, i)}));
          break;
        }
        case google::protobuf::FieldDescriptor::TYPE_INT32: {
          auto* int32_builder = static_cast<arrow::Int32Builder*>(primitive_builder.get());
          PARQUET_THROW_NOT_OK(
              int32_builder->AppendValues({reflection->GetRepeatedInt32(message, field, i)}));
          break;
        }
        case google::protobuf::FieldDescriptor::TYPE_FIXED64: {
          auto* uint64_builder = static_cast<arrow::UInt64Builder*>(primitive_builder.get());
          PARQUET_THROW_NOT_OK(
              uint64_builder->AppendValues({reflection->GetRepeatedUInt64(message, field, i)}));
          break;
        }
        case google::protobuf::FieldDescriptor::TYPE_FIXED32: {
          auto* uint32_builder = static_cast<arrow::UInt32Builder*>(primitive_builder.get());
          PARQUET_THROW_NOT_OK(
              uint32_builder->AppendValues({reflection->GetRepeatedUInt32(message, field, i)}));
          break;
        }
        case google::protobuf::FieldDescriptor::TYPE_BOOL: {
          auto* boolean_builder = static_cast<arrow::BooleanBuilder*>(primitive_builder.get());
          std::vector<bool> values = {reflection->GetRepeatedBool(message, field, i)};
          PARQUET_THROW_NOT_OK(boolean_builder->AppendValues(values));
          break;
        }
        case google::protobuf::FieldDescriptor::TYPE_STRING: {
          auto* string_builder = static_cast<arrow::StringBuilder*>(primitive_builder.get());
          PARQUET_THROW_NOT_OK(
              string_builder->AppendValues({reflection->GetRepeatedString(message, field, i)}));
          break;
        }
        case google::protobuf::FieldDescriptor::TYPE_BYTES: {
          auto* binary_builder = static_cast<arrow::BinaryBuilder*>(primitive_builder.get());
          PARQUET_THROW_NOT_OK(
              binary_builder->AppendValues({reflection->GetRepeatedString(message, field, i)}));
          break;
        }
        case google::protobuf::FieldDescriptor::TYPE_UINT32: {
          auto* uint32_builder = static_cast<arrow::UInt32Builder*>(primitive_builder.get());
          PARQUET_THROW_NOT_OK(
              uint32_builder->AppendValues({reflection->GetRepeatedUInt32(message, field, i)}));
          break;
        }
        case google::protobuf::FieldDescriptor::TYPE_ENUM: {
          auto* string_builder = static_cast<arrow::StringBuilder*>(primitive_builder.get());
          PARQUET_THROW_NOT_OK(
              string_builder->AppendValues({reflection->GetRepeatedEnum(message, field, i)->name()}));
          break;
        }
        case google::protobuf::FieldDescriptor::TYPE_SFIXED32: {
          auto* int32_builder = static_cast<arrow::Int32Builder*>(primitive_builder.get());
          PARQUET_THROW_NOT_OK(
              int32_builder->AppendValues({reflection->GetRepeatedInt32(message, field, i)}));
          break;
        }
        case google::protobuf::FieldDescriptor::TYPE_SFIXED64: {
          auto* int64_builder = static_cast<arrow::Int64Builder*>(primitive_builder.get());
          PARQUET_THROW_NOT_OK(
              int64_builder->AppendValues({reflection->GetRepeatedInt64(message, field, i)}));
          break;
        }
        case google::protobuf::FieldDescriptor::TYPE_SINT32: {
          auto* int32_builder = static_cast<arrow::Int32Builder*>(primitive_builder.get());
          PARQUET_THROW_NOT_OK(
              int32_builder->AppendValues({reflection->GetRepeatedInt32(message, field, i)}));
          break;
        }
        case google::protobuf::FieldDescriptor::TYPE_SINT64: {
          auto* int64_builder = static_cast<arrow::Int64Builder*>(primitive_builder.get());
          PARQUET_THROW_NOT_OK(
              int64_builder->AppendValues({reflection->GetRepeatedInt64(message, field, i)}));
          break;
        }
        default:
          throw std::runtime_error("Unsupported field type.");
      }
    }
  }

  void BuildPrimitiveData(std::shared_ptr<arrow::ArrayBuilder> primitive_builder,
                          const google::protobuf::Message& message,
                          const google::protobuf::Reflection* reflection,
                          const google::protobuf::FieldDescriptor* field) {
    if (!reflection->HasField(message, field)) {
      PARQUET_THROW_NOT_OK(primitive_builder->AppendNull());
      return;
    }
    switch (field->type()) {
      case google::protobuf::FieldDescriptor::TYPE_DOUBLE: {
        auto* double_builder = static_cast<arrow::DoubleBuilder*>(primitive_builder.get());
        PARQUET_THROW_NOT_OK(double_builder->AppendValues({reflection->GetDouble(message, field)}));
        break;
      }
      case google::protobuf::FieldDescriptor::TYPE_FLOAT: {
        auto* float_builder = static_cast<arrow::FloatBuilder*>(primitive_builder.get());
        PARQUET_THROW_NOT_OK(float_builder->AppendValues({reflection->GetFloat(message, field)}));
        break;
      }
      case google::protobuf::FieldDescriptor::TYPE_INT64: {
        auto* int64_builder = static_cast<arrow::Int64Builder*>(primitive_builder.get());
        PARQUET_THROW_NOT_OK(int64_builder->AppendValues({reflection->GetInt64(message, field)}));
        break;
      }
      case google::protobuf::FieldDescriptor::TYPE_UINT64: {
        auto* uint64_builder = static_cast<arrow::UInt64Builder*>(primitive_builder.get());
        PARQUET_THROW_NOT_OK(uint64_builder->AppendValues({reflection->GetUInt64(message, field)}));
        break;
      }
      case google::protobuf::FieldDescriptor::TYPE_INT32: {
        auto* int32_builder = static_cast<arrow::Int32Builder*>(primitive_builder.get());
        PARQUET_THROW_NOT_OK(int32_builder->AppendValues({reflection->GetInt32(message, field)}));
        break;
      }
      case google::protobuf::FieldDescriptor::TYPE_FIXED64: {
        auto* uint64_builder = static_cast<arrow::UInt64Builder*>(primitive_builder.get());
        PARQUET_THROW_NOT_OK(uint64_builder->AppendValues({reflection->GetUInt64(message, field)}));
        break;
      }
      case google::protobuf::FieldDescriptor::TYPE_FIXED32: {
        auto* uint32_builder = static_cast<arrow::UInt32Builder*>(primitive_builder.get());
        PARQUET_THROW_NOT_OK(uint32_builder->AppendValues({reflection->GetUInt32(message, field)}));
        break;
      }
      case google::protobuf::FieldDescriptor::TYPE_BOOL: {
        auto* boolean_builder = static_cast<arrow::BooleanBuilder*>(primitive_builder.get());
        std::vector<bool> values = {reflection->GetBool(message, field)};
        PARQUET_THROW_NOT_OK(boolean_builder->AppendValues(values));
        break;
      }
      case google::protobuf::FieldDescriptor::TYPE_STRING: {
        auto* string_builder = static_cast<arrow::StringBuilder*>(primitive_builder.get());
        PARQUET_THROW_NOT_OK(string_builder->AppendValues({reflection->GetString(message, field)}));
        break;
      }
      case google::protobuf::FieldDescriptor::TYPE_BYTES: {
        auto* binary_builder = static_cast<arrow::BinaryBuilder*>(primitive_builder.get());
        PARQUET_THROW_NOT_OK(binary_builder->AppendValues({reflection->GetString(message, field)}));
        break;
      }
      case google::protobuf::FieldDescriptor::TYPE_UINT32: {
        auto* uint32_builder = static_cast<arrow::UInt32Builder*>(primitive_builder.get());
        PARQUET_THROW_NOT_OK(uint32_builder->AppendValues({reflection->GetUInt32(message, field)}));
        break;
      }
      case google::protobuf::FieldDescriptor::TYPE_SFIXED32: {
        auto* int32_builder = static_cast<arrow::Int32Builder*>(primitive_builder.get());
        PARQUET_THROW_NOT_OK(int32_builder->AppendValues({reflection->GetInt32(message, field)}));
        break;
      }
      case google::protobuf::FieldDescriptor::TYPE_SFIXED64: {
        auto* int64_builder = static_cast<arrow::Int64Builder*>(primitive_builder.get());
        PARQUET_THROW_NOT_OK(int64_builder->AppendValues({reflection->GetInt64(message, field)}));
        break;
      }
      case google::protobuf::FieldDescriptor::TYPE_SINT32: {
        auto* int32_builder = static_cast<arrow::Int32Builder*>(primitive_builder.get());
        PARQUET_THROW_NOT_OK(int32_builder->AppendValues({reflection->GetInt32(message, field)}));
        break;
      }
      case google::protobuf::FieldDescriptor::TYPE_SINT64: {
        auto* int64_builder = static_cast<arrow::Int64Builder*>(primitive_builder.get());
        PARQUET_THROW_NOT_OK(int64_builder->AppendValues({reflection->GetInt64(message, field)}));
        break;
      }
      case google::protobuf::FieldDescriptor::TYPE_ENUM: {
        auto* string_builder = static_cast<arrow::StringBuilder*>(primitive_builder.get());
        PARQUET_THROW_NOT_OK(string_builder->AppendValues({reflection->GetEnum(message, field)->name()}));
        break;
      }
      default:
        throw std::runtime_error("Unsupported field type.");
    }
  }

  std::shared_ptr<arrow::DataType> MakePrimitiveArrowDataType(
      const google::protobuf::FieldDescriptor* field) {
    switch (field->type()) {
      case google::protobuf::FieldDescriptor::TYPE_DOUBLE:
        return arrow::float64();
      case google::protobuf::FieldDescriptor::TYPE_FLOAT:
        return arrow::float32();
      case google::protobuf::FieldDescriptor::TYPE_INT64:
        return arrow::int64();
      case google::protobuf::FieldDescriptor::TYPE_UINT64:
        return arrow::uint64();
      case google::protobuf::FieldDescriptor::TYPE_INT32:
        return arrow::int32();
      case google::protobuf::FieldDescriptor::TYPE_FIXED64:
        return arrow::fixed_size_binary(8);
      case google::protobuf::FieldDescriptor::TYPE_FIXED32:
        return arrow::fixed_size_binary(4);
      case google::protobuf::FieldDescriptor::TYPE_BOOL:
        return arrow::boolean();
      case google::protobuf::FieldDescriptor::TYPE_STRING:
        return arrow::utf8();
      case google::protobuf::FieldDescriptor::TYPE_BYTES:
        return arrow::binary();
      case google::protobuf::FieldDescriptor::TYPE_UINT32:
        return arrow::uint32();
      case google::protobuf::FieldDescriptor::TYPE_ENUM:
        return arrow::utf8();
      case google::protobuf::FieldDescriptor::TYPE_SFIXED32:
        return arrow::int32();
      case google::protobuf::FieldDescriptor::TYPE_SFIXED64:
        return arrow::int64();
      case google::protobuf::FieldDescriptor::TYPE_SINT32:
        return arrow::int32();
      case google::protobuf::FieldDescriptor::TYPE_SINT64:
        return arrow::int64();
      default:
        throw std::runtime_error("Unsupported field type.");
    }
  }

  std::shared_ptr<arrow::ArrayBuilder> MakePrimitiveArrowArrayBuilder(
      const google::protobuf::FieldDescriptor* field) {
    switch (field->type()) {
      case google::protobuf::FieldDescriptor::TYPE_DOUBLE:
        return std::make_shared<arrow::DoubleBuilder>();
      case google::protobuf::FieldDescriptor::TYPE_FLOAT:
        return std::make_shared<arrow::FloatBuilder>();
      case google::protobuf::FieldDescriptor::TYPE_INT64:
        return std::make_shared<arrow::Int64Builder>();
      case google::protobuf::FieldDescriptor::TYPE_UINT64:
        return std::make_shared<arrow::UInt64Builder>();
      case google::protobuf::FieldDescriptor::TYPE_INT32:
        return std::make_shared<arrow::Int32Builder>();
      case google::protobuf::FieldDescriptor::TYPE_FIXED64:
        return std::make_shared<arrow::UInt64Builder>();
      case google::protobuf::FieldDescriptor::TYPE_FIXED32:
        return std::make_shared<arrow::UInt32Builder>();
      case google::protobuf::FieldDescriptor::TYPE_BOOL:
        return std::make_shared<arrow::BooleanBuilder>();
      case google::protobuf::FieldDescriptor::TYPE_STRING:
        return std::make_shared<arrow::StringBuilder>();
      case google::protobuf::FieldDescriptor::TYPE_BYTES:
        return std::make_shared<arrow::BinaryBuilder>();
      case google::protobuf::FieldDescriptor::TYPE_UINT32:
        return std::make_shared<arrow::UInt32Builder>();
      case google::protobuf::FieldDescriptor::TYPE_SFIXED32:
        return std::make_shared<arrow::Int32Builder>();
      case google::protobuf::FieldDescriptor::TYPE_SFIXED64:
        return std::make_shared<arrow::Int64Builder>();
      case google::protobuf::FieldDescriptor::TYPE_SINT32:
        return std::make_shared<arrow::Int32Builder>();
      case google::protobuf::FieldDescriptor::TYPE_SINT64:
        return std::make_shared<arrow::Int64Builder>();
      case google::protobuf::FieldDescriptor::TYPE_ENUM:
        return std::make_shared<arrow::StringBuilder>();
      default:
        throw std::runtime_error("Unsupported field type.");
    }
  }
#endif
};

}  // namespace io
}  // namespace base