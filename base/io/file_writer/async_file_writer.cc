// Copyright @2023 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/io/file_writer/async_file_writer.h"

#include "glog/logging.h"

namespace base {
namespace io {

namespace {
constexpr int kMaxNumRetry = 2;
}  // namespace

AsyncFileWriter::AsyncFileWriter(int thread_num,
                                 int64_t max_pending_tasks,
                                 const WriteFailedCallback& write_failed_callback)
    : write_failed_callback_(write_failed_callback) {
  thread_pool_ = std::make_unique<ThreadPool>(thread_num, max_pending_tasks);
  file_writer_ = std::make_unique<LocalFileWriter>();
}

AsyncFileWriter::~AsyncFileWriter() {
  LOG(ERROR) << "Thread pool pending task num: " << thread_pool_->pending_tasks();
  thread_pool_->WaitAllTasksCompleted();
}

bool AsyncFileWriter::WriteProtoInternal(const std::string& path,
                                         const google::protobuf::Message& message) {
  std::string contents;
  // TODO(zhangmanwei02) Try to optimize this part of the time-consuming
  CHECK(message.SerializeToString(&contents))
      << "Failed to write protobuf to binary: " << message.Utf8DebugString();
  return WriteContentInternal(path, contents);
}

bool AsyncFileWriter::WriteContentInternal(const std::string& path, const std::string& contents) {
  thread_pool_->AddTask([this, path, contents]() {
    WriteContentInternalWithRetry(path, contents, write_failed_callback_, 0);
  });
  return true;
}

void AsyncFileWriter::WriteContentInternalWithRetry(
    const std::string& path,
    const std::string& contents,
    const WriteFailedCallback& write_failed_callback,
    int num_retry) {
  if (num_retry > kMaxNumRetry) {
    LOG_FIRST_N(ERROR, 1000) << "Failed to write content to file: " << path;
    LOG_EVERY_N(ERROR, 100) << "No " << google::COUNTER << " write failed path: " << path;
    write_failed_callback(path);
    return;
  }
  if (num_retry > 0) {
    LOG(ERROR) << strings::Format(
        "Retry {}/{} WriteContentFile with path: {}", num_retry, kMaxNumRetry, path);
  }
  if (!file_writer_->WriteContent(path, contents)) {
    LOG(ERROR) << "Export WriteContentFile to " << path << " failed.";
    thread_pool_->AddTask([this, path, contents, num_retry]() {
      WriteContentInternalWithRetry(path, contents, write_failed_callback_, num_retry + 1);
    });
  }
}

}  // namespace io
}  // namespace base
