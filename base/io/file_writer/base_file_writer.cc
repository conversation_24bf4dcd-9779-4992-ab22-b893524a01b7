// Copyright @2023 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/io/file_writer/base_file_writer.h"

namespace base {
namespace io {

bool BaseFileWriter::WriteProto(const std::string& path, const google::protobuf::Message& message) {
  return WriteProtoInternal(path, message);
}

bool BaseFileWriter::WriteContent(const std::string& path, const std::string& contents) {
  return WriteContentInternal(path, contents);
}

}  // namespace io
}  // namespace base
