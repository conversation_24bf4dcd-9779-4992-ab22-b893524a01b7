// Copyright @2023 Sankuai AI Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/io/file_writer/async_file_writer.h"

#include "google/protobuf/text_format.h"
#include "gtest/gtest.h"

#include "base/file/file_path_util.h"
#include "base/file/file_util.h"
#include "base/file/proto/test.pb.h"
#include "base/testing/scoped_temp_dir.h"

namespace base {
namespace io {

class AsyncFileWriterTest : public ::testing::Test {
 public:
  void SetUp() override { async_file_writer = std::make_unique<AsyncFileWriter>(10, 10); }

 protected:
  std::unique_ptr<AsyncFileWriter> async_file_writer = nullptr;
};

TEST_F(AsyncFileWriterTest, Proto1DataWriteTest) {
  const std::string kNodeConfigs =
      "value1: 1, "
      "value2: 2.5, "
      "value3: \"test-proto\", ";

  const base::testing::ScopedTempDir scoped_dir("walle-%%%%");
  LOG(ERROR) << "temp_path: " << scoped_dir.temp_path();
  const std::string proto_path = file_path::Join(scoped_dir.temp_path(), "test_proto.bin");
  TestProto test_proto;
  CHECK(google::protobuf::TextFormat::ParseFromString(kNodeConfigs, &test_proto));
  EXPECT_TRUE(async_file_writer->WriteProto(proto_path, test_proto));
  absl::SleepFor(absl::Seconds(1));

  TestProto test_proto_read;
  EXPECT_TRUE(ReadBinaryProtoFile(proto_path, &test_proto_read));
  EXPECT_EQ(test_proto_read.value1(), 1);
  EXPECT_EQ(test_proto_read.value2(), 2.5);
  EXPECT_EQ(test_proto_read.value3(), "test-proto");
}

TEST_F(AsyncFileWriterTest, ContentsWriteTest) {
  const std::string kNodeConfigsWithUnknownField =
      "value1: 1, "
      "value2: 2.5, "
      "value3: \"test-proto\", "
      "value6: 6, "
      "value4: \"unknown-proto\", "
      "value5: \"value5\", "
      "value7: 7, ";

  const base::testing::ScopedTempDir scoped_dir("walle-%%%%");
  LOG(ERROR) << "temp_path: " << scoped_dir.temp_path();
  const std::string proto_path = file_path::Join(scoped_dir.temp_path(), "test_proto.pb.txt");
  EXPECT_TRUE(async_file_writer->WriteContent(proto_path, kNodeConfigsWithUnknownField));
  absl::SleepFor(absl::Seconds(1));

  TestProto test_proto_read;
  // Even unknown fields is allowed:
  // 1. The result of function is false;
  // 2. The text content behind the unknown fields will not be parsed and lost in the object;
  EXPECT_FALSE(ReadTextProtoFile(proto_path, &test_proto_read, true));
  EXPECT_EQ(test_proto_read.value1(), 1);
  EXPECT_EQ(test_proto_read.value2(), 2.5);
  EXPECT_EQ(test_proto_read.value3(), "test-proto");
  EXPECT_EQ(test_proto_read.value6(), 6);
  EXPECT_TRUE(test_proto_read.value5().empty());
  EXPECT_EQ(test_proto_read.value7(), 0);
}

}  // namespace io
}  // namespace base
