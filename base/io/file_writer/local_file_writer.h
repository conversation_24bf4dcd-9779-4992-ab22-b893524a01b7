// Copyright @2023 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <string>

#include "glog/logging.h"

#include "base/file/file_util.h"
#include "base/io/file_writer/base_file_writer.h"

namespace base {
namespace io {

class LocalFileWriter : public BaseFileWriter {
 public:
  LocalFileWriter() = default;
  ~LocalFileWriter() override = default;

 private:
  bool WriteProtoInternal(const std::string& path,
                          const google::protobuf::Message& message) override;
  bool WriteContentInternal(const std::string& path, const std::string& contents) override;

  DISALLOW_COPY_AND_ASSIGN(LocalFileWriter);
};

}  // namespace io
}  // namespace base
