// Copyright @2023 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <functional>
#include <memory>
#include <string>

#include "base/io/file_writer/local_file_writer.h"
#include "base/thread/thread_pool.h"

namespace base {
namespace io {

class AsyncFileWriter : public BaseFileWriter {
  using WriteFailedCallback = std::function<void(const std::string& failed_path)>;

 public:
  AsyncFileWriter(
      int thread_num,
      int64_t max_pending_tasks,
      const WriteFailedCallback& write_failed_callback = [](const std::string& failed_path) {});
  ~AsyncFileWriter();

 private:
  bool WriteProtoInternal(const std::string& path,
                          const google::protobuf::Message& message) override;
  bool WriteContentInternal(const std::string& path, const std::string& contents) override;
  void WriteContentInternalWithRetry(const std::string& path,
                                     const std::string& contents,
                                     const WriteFailedCallback& write_failed_callback,
                                     int num_retry);

  std::unique_ptr<base::ThreadPool> thread_pool_ = nullptr;
  std::unique_ptr<BaseFileWriter> file_writer_ = nullptr;
  WriteFailedCallback write_failed_callback_;

  DISALLOW_COPY_AND_ASSIGN(AsyncFileWriter);
};

}  // namespace io
}  // namespace base
