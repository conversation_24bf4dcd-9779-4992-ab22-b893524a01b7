// Copyright @2022 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <memory>
#include <string>
#include <vector>

#include <hdfs.h>
#include "glog/logging.h"
#include "gtest/gtest.h"

#include "base/common/macros.h"
#include "base/common/optional.h"

namespace base {
namespace io {

// Predefine HdfsFileReader as HdfsClient friend class.
class HdfsFileReader;

}  // namespace io
}  // namespace base

namespace hdfs {

// TODO(wanglong): Add more file info.
struct HdfsFileInfo {
  std::string name;
  int64_t size = 0;
  bool is_directory = false;
};

class HdfsClient final {
 public:
  HdfsClient();
  ~HdfsClient();

  // Get the current working directory for the given filesystem.
  std::string GetWorkingDirectory() const;

  // Set the working directory. All relative paths will be resolved relative to it.
  bool SetWorkingDirectory(const std::string& path);

  // Checks if a given path exsits on hdfs.
  bool Exists(const std::string& path) const;

  // Get information about a path, return None if path not exists.
  base::Optional<HdfsFileInfo> GetPathInfo(const std::string& path) const;

  // Get list of files/directories for a given directory-path.
  std::vector<HdfsFileInfo> ListDirectory(const std::string& path) const;

 private:
  class HdfsFileHandler {
   public:
    explicit HdfsFileHandler(hdfsFile hdfs_file) : hdfs_file_(hdfs_file) {}
    ~HdfsFileHandler() {
      CHECK(hdfs_file_ == nullptr) << "HdfsFile is not freed before destruct it.";
    }

   private:
    hdfsFile hdfs_file_ = nullptr;
    friend class HdfsClient;

    DISALLOW_COPY_AND_ASSIGN(HdfsFileHandler);
  };

  // Open hdfs file by path.
  //   flags - an | of bits/fcntl.h file flags - supported flags are O_RDONLY, O_WRONLY (meaning
  // create or overwrite i.e., implies O_TRUNCAT), O_WRONLY|O_APPEND. Other flags are generally
  // ignored other than (O_RDWR || (O_EXCL & O_CREAT)) which return NULL and set errno equal
  // ENOTSUP.
  std::unique_ptr<HdfsFileHandler> OpenFile(const std::string& path, int flag);

  // Read data from an open file.
  int Read(HdfsFileHandler* hdfs_file_handler, void* buffer, int buffer_size);

  // Seek to given offset in file. This works only for files opened in read-only mode.
  bool SeekToOffset(HdfsFileHandler* hdfs_file_hanlder, int64_t offset);

  bool CloseFile(HdfsFileHandler* hdfs_file_handler);

  struct hdfs_internal* hdfs_fs_ = nullptr;

  friend class base::io::HdfsFileReader;

  FRIEND_TEST(HdfsClientTest, OpenCloseTest);
  FRIEND_TEST(HdfsClientTest, ReadSeekTest);
  DISALLOW_COPY_AND_ASSIGN(HdfsClient);
};

}  // namespace hdfs
