package(default_visibility = ["//visibility:public"])

cc_library(
    name = "hdfs_client",
    srcs = ["hdfs_client.cc"],
    hdrs = ["hdfs_client.h"],
    deps = [
        "//base/common:optional",
        "//third_party/hdfs",
        "@gtest",
    ],
)

cc_test(
    name = "hdfs_client_test",
    srcs = ["hdfs_client_test.cc"],
    deps = [
        ":hdfs_client",
        "//base/testing:test_main",
    ],
)

cc_library(
    name = "hdfs_sync_client",
    srcs = ["hdfs_sync_client.cc"],
    hdrs = ["hdfs_sync_client.h"],
    deps = [
        "//base/common:eintr_wrapper",
        "//base/common:optional",
        "//third_party/hdfs",
        "@gtest",
    ],
)

# do not add to ci
cc_test(
    name = "hdfs_sync_client_test",
    srcs = ["hdfs_sync_client_test.cc"],
    linkopts = [
        "-lboost_system",
        "-lboost_filesystem",
    ],
    deps = [
        ":hdfs_sync_client",
        "//base/testing:test_main",
    ],
)
