// Copyright @2022 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include <memory>

#include "base/io/hdfs/hdfs_client.h"

#include "glog/logging.h"
#include "gtest/gtest.h"

namespace hdfs {
namespace {
constexpr char kHadoopWalleDir[] = "viewfs://hadoop-meituan/user/hadoop-walle";
constexpr char kHadoopPerceptionDir[] = "viewfs://hadoop-meituan/user/hadoop-perception";
constexpr char kHelloHdfsFilePath[] = "testing/hello_hdfs.txt";
}  // namespace

TEST(HdfsClientTest, GetAndSetWorkingDirectoryTest) {
  HdfsClient hdfs_client;
  hdfs_client.SetWorkingDirectory(kHadoopWalleDir);
  EXPECT_EQ(hdfs_client.GetWorkingDirectory(), kHadoopWalleDir);
  hdfs_client.SetWorkingDirectory(kHadoopPerceptionDir);
  EXPECT_EQ(hdfs_client.GetWorkingDirectory(), kHadoopPerceptionDir);
}

TEST(HdfsClientTest, ExistsTest) {
  HdfsClient hdfs_client;
  hdfs_client.SetWorkingDirectory(kHadoopWalleDir);
  EXPECT_TRUE(hdfs_client.Exists("testing"));
  EXPECT_TRUE(hdfs_client.Exists(kHelloHdfsFilePath));
}

TEST(HdfsClientTest, GetPathInfoTest) {
  HdfsClient hdfs_client;
  hdfs_client.SetWorkingDirectory(kHadoopWalleDir);
  base::Optional<HdfsFileInfo> path_info = hdfs_client.GetPathInfo(kHelloHdfsFilePath);
  EXPECT_TRUE(path_info != base::None());
  EXPECT_EQ(path_info->name, "viewfs://hadoop-meituan/user/hadoop-walle/testing/hello_hdfs.txt");
}

TEST(HdfsClientTest, ListDirectoryTest) {
  HdfsClient hdfs_client;
  hdfs_client.SetWorkingDirectory(kHadoopWalleDir);
  std::vector<HdfsFileInfo> file_infos = hdfs_client.ListDirectory("testing");
  EXPECT_EQ(file_infos.size(), 1);
  EXPECT_EQ(file_infos[0].name, "viewfs://hadoop-meituan/user/hadoop-walle/testing/hello_hdfs.txt");
}

TEST(HdfsClientTest, OpenCloseTest) {
  HdfsClient hdfs_client;
  hdfs_client.SetWorkingDirectory(kHadoopWalleDir);
  std::unique_ptr<HdfsClient::HdfsFileHandler> hdfs_file_handler =
      hdfs_client.OpenFile(kHelloHdfsFilePath, O_RDONLY);
  EXPECT_TRUE(hdfs_client.CloseFile(CHECK_NOTNULL(hdfs_file_handler).get()));
}

TEST(HdfsClientTest, ReadSeekTest) {
  HdfsClient hdfs_client;
  hdfs_client.SetWorkingDirectory(kHadoopWalleDir);
  std::unique_ptr<HdfsClient::HdfsFileHandler> hdfs_file_handler =
      hdfs_client.OpenFile(kHelloHdfsFilePath, O_RDONLY);
  std::string buffer;
  buffer.resize(10);
  int read_size = hdfs_client.Read(hdfs_file_handler.get(), &buffer[0], 10);
  EXPECT_EQ(read_size, 0);
  EXPECT_TRUE(hdfs_client.SeekToOffset(hdfs_file_handler.get(), 0));
  EXPECT_TRUE(hdfs_client.CloseFile(CHECK_NOTNULL(hdfs_file_handler).get()));
}

}  // namespace hdfs
