// Copyright @2022 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/io/hdfs/hdfs_client.h"

#include <errno.h>
#include <cstring>

#include <memory>

namespace hdfs {

namespace {

using HdfsFileInfoInternal = hdfsFileInfo;

constexpr int kMaxWorkingDirectorySize = 200;

HdfsFileInfo ToHdfsFileInfo(const HdfsFileInfoInternal& file_info_internal) {
  return HdfsFileInfo{
      .name = file_info_internal.mName,
      .size = file_info_internal.mSize,
      .is_directory = file_info_internal.mKind == tObjectKind::kObjectKindDirectory,
  };
}

}  // namespace

HdfsClient::HdfsClient() {
  struct hdfsBuilder* hdfs_builder = nullptr;
  hdfs_builder = hdfsNewBuilder();
  if (!hdfs_builder) {
    LOG(FATAL) << "Create  hdfsBuilder failed!";
  }
  hdfsBuilderSetNameNode(hdfs_builder, "default");
  hdfs_fs_ = hdfsBuilderConnect(hdfs_builder);
  if (!hdfs_fs_) {
    LOG(FATAL) << "Connect HDFS failed!";
  }
  if (hdfsExists(hdfs_fs_, "/") != 0) {
    LOG(FATAL) << "Connect HDFS failed!";
  }
}

HdfsClient::~HdfsClient() { CHECK(hdfsDisconnect(hdfs_fs_) == 0) << "Disconnect hdfs failed!"; }

std::string HdfsClient::GetWorkingDirectory() const {
  std::string dir;
  dir.resize(kMaxWorkingDirectorySize);
  char* buffer = hdfsGetWorkingDirectory(hdfs_fs_, &dir[0], kMaxWorkingDirectorySize);
  CHECK(buffer != nullptr)
      << "GetWorkingDirectory failed! The absolute path length of the working directory may exceed "
         "the max working directory size: "
      << kMaxWorkingDirectorySize;
  dir = buffer;
  return dir;
}

bool HdfsClient::SetWorkingDirectory(const std::string& path) {
  int stat = hdfsSetWorkingDirectory(hdfs_fs_, path.c_str());
  return stat == 0;
}

bool HdfsClient::Exists(const std::string& path) const {
  return hdfsExists(hdfs_fs_, path.c_str()) == 0;
}

base::Optional<HdfsFileInfo> HdfsClient::GetPathInfo(const std::string& path) const {
  HdfsFileInfoInternal* file_info_internal = hdfsGetPathInfo(hdfs_fs_, path.c_str());
  if (!file_info_internal) {
    return base::None();
  }
  HdfsFileInfo new_file_info = ToHdfsFileInfo(*file_info_internal);
  hdfsFreeFileInfo(file_info_internal, 1);
  return new_file_info;
}

std::vector<HdfsFileInfo> HdfsClient::ListDirectory(const std::string& path) const {
  int num_infos = 0;
  HdfsFileInfoInternal* file_info_internal = hdfsListDirectory(hdfs_fs_, path.c_str(), &num_infos);
  if (file_info_internal) {
    std::vector<HdfsFileInfo> file_infos;
    file_infos.reserve(num_infos);
    for (int i = 0; i < num_infos; ++i) {
      file_infos.emplace_back(ToHdfsFileInfo(file_info_internal[i]));
    }
    hdfsFreeFileInfo(file_info_internal, num_infos);
    return file_infos;
  }
  return {};
}

std::unique_ptr<HdfsClient::HdfsFileHandler> HdfsClient::OpenFile(const std::string& path,
                                                                  int flags) {
  hdfsFile hdfs_file = hdfsOpenFile(hdfs_fs_, path.c_str(), flags, 0, 0, 0);
  if (hdfs_file) {
    return std::make_unique<HdfsClient::HdfsFileHandler>(hdfs_file);
  }
  return nullptr;
}

int HdfsClient::Read(HdfsClient::HdfsFileHandler* hdfs_file_handler,
                     void* buffer,
                     int buffer_size) {
  int offset = 0;
  while (offset < buffer_size) {
    const int read_size = hdfsRead(hdfs_fs_,
                                   CHECK_NOTNULL(hdfs_file_handler)->hdfs_file_,
                                   static_cast<char*>(buffer) + offset,
                                   buffer_size - offset);
    if (read_size == -1) {
      LOG(ERROR) << "HDFS read failed with error: " << std::strerror(errno);
      if (errno != EINTR) {
        return -1;
      }
    } else if (read_size == 0) {
      break;
    } else {
      offset += read_size;
    }
  }
  return offset;
}

bool HdfsClient::SeekToOffset(HdfsClient::HdfsFileHandler* hdfs_file_handler, int64_t offset) {
  return hdfsSeek(hdfs_fs_, CHECK_NOTNULL(hdfs_file_handler)->hdfs_file_, offset) == 0;
}

bool HdfsClient::CloseFile(HdfsClient::HdfsFileHandler* hdfs_file_handler) {
  CHECK(CHECK_NOTNULL(hdfs_file_handler)->hdfs_file_ != nullptr) << "hdfs file has been freed!";
  int stat = hdfsCloseFile(hdfs_fs_, hdfs_file_handler->hdfs_file_);
  hdfs_file_handler->hdfs_file_ = nullptr;
  if (stat != 0) {
    LOG(ERROR) << "Close file failed with stat: " << stat;
    return false;
  }
  return true;
}

}  // namespace hdfs
