// Copyright @2022 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/io/hdfs/hdfs_sync_client.h"

#include <memory>

namespace hdfs {

HdfsSyncClient::HdfsSyncClient() {
  // setting for remote hdfs file system
  struct hdfsBuilder* hdfs_builder = nullptr;
  hdfs_builder = hdfsNewBuilder();
  if (!hdfs_builder) {
    LOG(FATAL) << "Create hdfsBuilder failed!";
  }
  hdfsBuilderSetNameNode(hdfs_builder, "default");
  hdfsBuilderConfSetStr(hdfs_builder, "dfs.replication", "2");
  hdfs_fs_ = hdfsBuilderConnect(hdfs_builder);
  if (!hdfs_fs_) {
    LOG(FATAL) << "Connect HDFS failed!";
  }
  if (hdfsExists(hdfs_fs_, "/") != 0) {
    LOG(FATAL) << "Connect HDFS failed!";
  }
  // setting for local file system
  struct hdfsBuilder* local_builder = hdfsNewBuilder();
  if (!local_builder) {
    LOG(FATAL) << "Create hdfsBuilder failed!";
  }
  hdfsBuilderSetNameNode(local_builder, NULL);
  local_fs_ = hdfsBuilderConnect(local_builder);
  if (!local_fs_) {
    LOG(FATAL) << "Connect Local failed!";
  }
  if (hdfsExists(local_fs_, "/") != 0) {
    LOG(FATAL) << "Connect Local failed!";
  }
}

HdfsSyncClient::~HdfsSyncClient() {
  CHECK(hdfsDisconnect(hdfs_fs_) == 0) << "Disconnect HDFS failed!";
  CHECK(hdfsDisconnect(local_fs_) == 0) << "Disconnect S3 failed!";
}

bool HdfsSyncClient::HdfsCopyFromLocal(const std::string& local_file,
                                      const std::string& hdfs_path) const {
  return hdfsCopy(local_fs_, local_file.c_str(), hdfs_fs_, hdfs_path.c_str()) == 0;
}

bool HdfsSyncClient::HdfsCopyToLocal(const std::string& hdfs_path,
                                          const std::string& local_file) const {
  return hdfsCopy(hdfs_fs_, hdfs_path.c_str(), local_fs_, local_file.c_str()) == 0;
}

bool HdfsSyncClient::HdfsCopy(const std::string& src_path, const std::string& dst_path) const {
  return hdfsCopy(hdfs_fs_, src_path.c_str(), hdfs_fs_, dst_path.c_str()) == 0;
}

bool HdfsSyncClient::HdfsMove(const std::string& src_path, const std::string& dst_path) const {
  return hdfsMove(hdfs_fs_, src_path.c_str(), hdfs_fs_, dst_path.c_str()) == 0;
}

bool HdfsSyncClient::HdfsExists(const std::string& path) const {
  return hdfsExists(hdfs_fs_, path.c_str()) == 0;
}

bool HdfsSyncClient::HdfsRemove(const std::string& path, bool is_recursive) const {
  return hdfsDelete(hdfs_fs_, path.c_str(), is_recursive) == 0;
}

bool HdfsSyncClient::HdfsCreateDirectory(const std::string& path) const {
  return hdfsCreateDirectory(hdfs_fs_, path.c_str()) == 0;
}

std::vector<std::string> HdfsSyncClient::HdfsListDirectory(const std::string& path) const {
  int num_file = -1;
  hdfsFileInfo* hdfs_file_info_ptr = hdfsListDirectory(hdfs_fs_, path.c_str(), &num_file);
  std::vector<std::string> files;
  if (hdfs_file_info_ptr != nullptr && num_file > 0) {
    files.reserve(num_file);
    for (int idx = 0; idx < num_file; ++idx) {
      files.push_back(hdfs_file_info_ptr->mName);
      ++hdfs_file_info_ptr;
    }
  }
  return files;
}

int64_t HdfsSyncClient::HdfsFileSize(const std::string& path) const {
  hdfsFileInfo* file_info = hdfsGetPathInfo(hdfs_fs_, path.c_str());
  if (!file_info) {
    return -1;
  }
  return file_info->mSize;
}

}  // namespace hdfs
