// Copyright @2022 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/io/hdfs/hdfs_sync_client.h"

#include <boost/filesystem.hpp>
#include <fstream>
#include <memory>

#include "glog/logging.h"
#include "gtest/gtest.h"

namespace hdfs {

TEST(HdfsClientTest, HdfsExistsTest) {
  HdfsSyncClient sync_client;
  std::string test_file = "viewfs://hadoop-meituan/user/hadoop-walle/testing/hello_hdfs.txt";
  std::string test_dir = "viewfs://hadoop-meituan/user/hadoop-walle/testing";
  EXPECT_TRUE(sync_client.HdfsExists(test_dir));
  EXPECT_TRUE(sync_client.HdfsExists(test_file));
}

TEST(HdfsClientTest, HdfsRemoveTest) {
  HdfsSyncClient sync_client;
  std::string source_file = "viewfs://hadoop-meituan/user/hadoop-walle/testing/hello_hdfs.txt";
  std::string remove_file = "viewfs://hadoop-meituan/user/hadoop-walle/testing/remove_hdfs.txt";
  if (!sync_client.HdfsExists(remove_file)) {
    EXPECT_TRUE(sync_client.HdfsCopy(source_file, remove_file));
  }
  EXPECT_TRUE(sync_client.HdfsExists(remove_file));
  EXPECT_TRUE(sync_client.HdfsRemove(remove_file, false));
  EXPECT_FALSE(sync_client.HdfsExists(remove_file));
}

TEST(HdfsClientTest, HdfsCreateDirectoryTest) {
  HdfsSyncClient sync_client;
  std::string test_dir = "viewfs://hadoop-meituan/user/hadoop-walle/testing/ut_dir";
  if (sync_client.HdfsExists(test_dir)) {
    EXPECT_TRUE(sync_client.HdfsRemove(test_dir, true));
  }
  EXPECT_FALSE(sync_client.HdfsExists(test_dir));
  EXPECT_TRUE(sync_client.HdfsCreateDirectory(test_dir));
  EXPECT_TRUE(sync_client.HdfsExists(test_dir));
  EXPECT_TRUE(sync_client.HdfsRemove(test_dir, true));
}

TEST(HdfsClientTest, HdfsListDirectoryTest) {
  HdfsSyncClient sync_client;
  std::string test_dir = "viewfs://hadoop-meituan/user/hadoop-walle/testing";
  std::vector<std::string> files = sync_client.HdfsListDirectory(test_dir);
  EXPECT_EQ(files.size(), 1);
  EXPECT_EQ(files[0], "viewfs://hadoop-meituan/user/hadoop-walle/testing/hello_hdfs.txt");
}

TEST(HdfsClientTest, HdfsCopyTest) {
  HdfsSyncClient sync_client;
  std::string source_file = "viewfs://hadoop-meituan/user/hadoop-walle/testing/hello_hdfs.txt";
  std::string remove_file = "viewfs://hadoop-meituan/user/hadoop-walle/testing/remove_hdfs.txt";
  if (sync_client.HdfsExists(remove_file)) {
    EXPECT_TRUE(sync_client.HdfsRemove(remove_file, false));
  }
  EXPECT_FALSE(sync_client.HdfsExists(remove_file));
  EXPECT_TRUE(sync_client.HdfsCopy(source_file, remove_file));
  EXPECT_TRUE(sync_client.HdfsExists(remove_file));
  EXPECT_TRUE(sync_client.HdfsRemove(remove_file, false));
}

TEST(HdfsClientTest, HdfsCopyFromLocalTest) {
  HdfsSyncClient sync_client;
  std::string local_file = "/tmp/local_upload.txt";
  std::ofstream output(local_file);
  std::string msg = "text message";
  output.write(msg.c_str(), msg.length());
  output.close();
  std::string hdfs_file = "viewfs://hadoop-meituan/user/hadoop-walle/testing/local_upload.txt";
  if (sync_client.HdfsExists(hdfs_file)) {
    EXPECT_TRUE(sync_client.HdfsRemove(hdfs_file, false));
  }
  EXPECT_FALSE(sync_client.HdfsExists(hdfs_file));
  EXPECT_TRUE(sync_client.HdfsCopyFromLocal(local_file, hdfs_file));
  EXPECT_TRUE(sync_client.HdfsExists(hdfs_file));
  EXPECT_TRUE(sync_client.HdfsRemove(hdfs_file, false));
}

TEST(HdfsClientTest, HdfsFileSizeTest) {
  HdfsSyncClient sync_client;
  std::string test_file = "viewfs://hadoop-meituan/user/hadoop-walle/testing/hello_hdfs.txt";
  EXPECT_TRUE(sync_client.HdfsExists(test_file));
  EXPECT_EQ(sync_client.HdfsFileSize(test_file), 13);
}

TEST(HdfsClientTest, HdfsCopyToLocalTest) {
  HdfsSyncClient sync_client;
  std::string source_file = "viewfs://hadoop-meituan/user/hadoop-walle/testing/hello_hdfs.txt";
  std::string local_file = "/tmp/local_download.txt";
  boost::filesystem::path path(local_file);
  if (boost::filesystem::exists(path)) {
    boost::filesystem::remove(path);
  }
  EXPECT_TRUE(sync_client.HdfsCopyToLocal(source_file, local_file));
  EXPECT_TRUE(boost::filesystem::exists(path));
}

}  // namespace hdfs
