// Copyright @2022 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <string>
#include <vector>

#include <hdfs.h>
#include "glog/logging.h"
#include "gtest/gtest.h"

#include "base/common/macros.h"

namespace hdfs {

class HdfsSyncClient final {
 public:
  HdfsSyncClient();
  ~HdfsSyncClient();

  // sync local file to HDFS file.
  bool HdfsCopyFromLocal(const std::string& local_file, const std::string& hdfs_path) const;

  // sync HDFS file to local file.
  bool HdfsCopyToLocal(const std::string& hdfs_file, const std::string& local_path) const;

  // Copy src_path to dst_path.
  bool HdfsCopy(const std::string& src_path, const std::string& dst_path) const;

  // Move src_path to dst_path.
  bool HdfsMove(const std::string& src_path, const std::string& dst_path) const;

  // Remove path.
  bool HdfsRemove(const std::string& path, bool is_recursive) const;

  // Checks if a given path exsits on hdfs.
  bool HdfsExists(const std::string& path) const;

  // Create directory.
  bool HdfsCreateDirectory(const std::string& path) const;

  // List files/directories in directory.
  std::vector<std::string> HdfsListDirectory(const std::string& path) const;

  // Get file size.
  int64_t HdfsFileSize(const std::string& path) const;

 private:
  struct hdfs_internal* hdfs_fs_ = nullptr;

  struct hdfs_internal* local_fs_ = nullptr;

  DISALLOW_COPY_AND_ASSIGN(HdfsSyncClient);
};

}  // namespace hdfs
