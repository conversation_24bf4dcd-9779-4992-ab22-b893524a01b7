// Copyright @2022 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/io/file_reader/base_file_reader.h"
#include <cstdint>

#include "gtest/gtest.h"

namespace base {
namespace io {

namespace {

class FakeFileReader : public BaseFileReader {
 public:
  FakeFileReader() {}
  ~FakeFileReader() {}

 private:
  bool OpenInternal(const std::string& path) override { return true; }
  bool SeekToOffsetInternal(int64_t offset) override { return true; }
  int ReadInternal(void* buffer, int buffer_size) override { return 0; }
  bool CloseInternal() override { return true; }

  int64_t GetFileSize() override { return 0; }

  DISALLOW_COPY_AND_ASSIGN(FakeFileReader);
};

}  // namespace

TEST(BaseFileReaderTest, OpenTest) {
  FakeFileReader fake_file_reader;
  EXPECT_TRUE(fake_file_reader.Open("/tmp/test.txt"));
  EXPECT_EQ(fake_file_reader.Read(nullptr, 0), 0);
  EXPECT_TRUE(fake_file_reader.SeekToOffset(100));
  EXPECT_TRUE(fake_file_reader.Close());
}

}  // namespace io
}  // namespace base
