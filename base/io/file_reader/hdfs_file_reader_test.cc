// Copyright @2022 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/io/file_reader/hdfs_file_reader.h"

#include <string>

#include "gtest/gtest.h"

namespace base {
namespace io {
namespace {
constexpr char kHadoopWalleDir[] = "viewfs://hadoop-meituan/user/hadoop-walle";
}  // namespace

TEST(HdfsFileReaderTest, BasicTest) {
  hdfs::HdfsClient hdfs_client;
  hdfs_client.SetWorkingDirectory(kHadoopWalleDir);
  HdfsFileReader hdfs_file_reader(&hdfs_client);
  EXPECT_TRUE(hdfs_file_reader.Open("testing/hello_hdfs.txt"));
  std::string buffer;
  buffer.resize(10);
  EXPECT_EQ(hdfs_file_reader.Read(&buffer[0], 10), 0);
  EXPECT_TRUE(hdfs_file_reader.SeekToO<PERSON>et(0));
  EXPECT_TRUE(hdfs_file_reader.Close());
}

}  // namespace io
}  // namespace base
