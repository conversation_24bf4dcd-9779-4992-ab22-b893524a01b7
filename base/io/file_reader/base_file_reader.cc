// Copyright @2022 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/io/file_reader/base_file_reader.h"

#include <algorithm>

#include "glog/logging.h"

#include "base/strings/format.h"

namespace base {
namespace io {

BaseFileReader::BaseFileReader(bool cache_all) : cache_all_(cache_all) {}

BaseFileReader::~BaseFileReader() {
  if (is_opened_) {
    LOG(ERROR) << strings::Format("Reader not closed, path: {}", path_);
  }
}

bool BaseFileReader::Open(const std::string& path) {
  if (is_opened_) {
    if (path == path_) {
      return SeekToOffset(0);
    }
    CHECK(Close()) << strings::Format(
        "Cannot open file: {}, since previous file: {} close failed!", path, path_);
  }
  path_ = path;
  current_offset_ = 0;
  is_opened_ = OpenInternal(path_);
  if (!is_opened_) {
    LOG(ERROR) << "Open file failed: " << path_;
    return false;
  }
  file_size_ = GetFileSize();
  if (file_size_ == -1) {
    LOG(ERROR) << "Get file size failed: " << path_;
    CloseInternal();
    is_opened_ = false;
    return false;
  }
  if (is_opened_ && cache_all_) {
    cache_data_.resize(file_size_);
    const int read_size = ReadInternal(&cache_data_[0], file_size_);
    CHECK_EQ(read_size, file_size_);
    CHECK(CloseInternal()) << strings::Format("{} close failed!", path_);
    CHECK(SeekToOffset(0)) << "Go back to the head failed!";
  }
  return is_opened_;
}

int BaseFileReader::Read(void* buffer, int buffer_size) {
  CHECK(is_opened_) << "No file opened!";
  int read_size = 0;
  if (cache_all_) {
    read_size = std::min(buffer_size, static_cast<int>(file_size_ - current_offset_));
    memcpy(buffer, &cache_data_[current_offset_], read_size);
  } else {
    read_size = ReadInternal(buffer, buffer_size);
  }
  if (read_size >= 0) {
    current_offset_ += read_size;
  }
  return read_size;
}

bool BaseFileReader::SeekToOffset(int64_t offset) {
  CHECK(is_opened_) << "No file opened!";
  if (cache_all_) {
    if (offset <= file_size_) {
      current_offset_ = offset;
      return true;
    }
    return false;
  }
  if (SeekToOffsetInternal(offset)) {
    current_offset_ = offset;
    return true;
  }
  return false;
}

bool BaseFileReader::Close() {
  if (cache_all_) {
    is_opened_ = false;
    cache_data_.clear();
    return true;
  }
  if (is_opened_) {
    is_opened_ = !CloseInternal();
  } else {
    LOG(ERROR) << "No file opened!";
  }
  return !is_opened_;
}

}  // namespace io
}  // namespace base
