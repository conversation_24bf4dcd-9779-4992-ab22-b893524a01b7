package(default_visibility = ["//visibility:public"])

cc_library(
    name = "base_file_reader",
    srcs = ["base_file_reader.cc"],
    hdrs = ["base_file_reader.h"],
    deps = [
        "//base/common:macros",
        "//base/strings:format",
        "@glog",
    ],
)

cc_test(
    name = "base_file_reader_test",
    srcs = ["base_file_reader_test.cc"],
    tags = [
        "ci",
        "ci_cpu",
    ],
    deps = [
        ":base_file_reader",
        "//base/testing:test_main",
    ],
)

cc_library(
    name = "hdfs_file_reader",
    srcs = ["hdfs_file_reader.cc"],
    hdrs = ["hdfs_file_reader.h"],
    deps = [
        ":base_file_reader",
        "//base/io/hdfs:hdfs_client",
    ],
)

cc_test(
    name = "hdfs_file_reader_test",
    srcs = ["hdfs_file_reader_test.cc"],
    deps = [
        ":hdfs_file_reader",
        "//base/testing:test_main",
    ],
)

cc_library(
    name = "local_file_reader",
    srcs = ["local_file_reader.cc"],
    hdrs = ["local_file_reader.h"],
    deps = [
        ":base_file_reader",
        "//base/common:eintr_wrapper",
    ],
)

cc_test(
    name = "local_file_reader_test",
    srcs = ["local_file_reader_test.cc"],
    tags = [
        "ci",
        "ci_cpu",
    ],
    deps = [
        ":local_file_reader",
        "//base/file:file_util",
        "//base/testing:scoped_temp_dir",
        "//base/testing:test_main",
    ],
)

cc_library(
    name = "mock_file_reader",
    srcs = ["mock_file_reader.cc"],
    hdrs = ["mock_file_reader.h"],
    deps = [
        ":base_file_reader",
        "//base/common:eintr_wrapper",
    ],
)
