// Copyright @2022 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <cstdint>
#include <string>

#include "base/io/file_reader/base_file_reader.h"

namespace base {
namespace io {
class LocalFileReader : public BaseFileReader {
 public:
  explicit LocalFileReader(bool cache_all = false);
  ~LocalFileReader() override = default;

 private:
  bool OpenInternal(const std::string& path) override;
  int ReadInternal(void* buffer, int buffer_size) override;
  bool SeekToOffsetInternal(int64_t offset) override;
  bool CloseInternal() override;

  int64_t GetFileSize() override;

  int file_descriptor_ = 0;

  DISALLOW_COPY_AND_ASSIGN(LocalFileReader);
};
}  // namespace io
}  // namespace base
