// Copyright @2023 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <cstdint>
#include <string>

#include "base/io/file_reader/base_file_reader.h"

namespace base {
namespace io {
class MockFileReader : public BaseFileReader {
 public:
  explicit MockFileReader(bool cache_all = false);
  ~MockFileReader() override = default;

 private:
  bool OpenInternal(const std::string& path) override;
  int ReadInternal(void* buffer, int buffer_size) override;
  bool SeekToOffsetInternal(int64_t offset) override;
  bool CloseInternal() override;

  int64_t GetFileSize() override;

  int file_descriptor_ = 0;

  DISALLOW_COPY_AND_ASSIGN(MockFileReader);
};
}  // namespace io
}  // namespace base
