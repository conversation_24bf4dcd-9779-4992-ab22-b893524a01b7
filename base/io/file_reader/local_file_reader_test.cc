// Copyright @2022 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/io/file_reader/local_file_reader.h"

#include <string>

#include "gtest/gtest.h"

#include "base/file/file_util.h"
#include "base/testing/scoped_temp_dir.h"

namespace base {
namespace io {

TEST(LocalFileReaderTest, BasicTest) {
  ::base::testing::ScopedTempDir temp_dir("local_file_reader_test");
  constexpr char kTestBinFile[] = "test.bin";
  constexpr char kTestStr[] = "Hello World!";
  const std::string file_path = file_path::Join(temp_dir.temp_path(), kTestBinFile);
  WriteContentToFile(file_path, kTestStr);

  {
    LocalFileReader local_file_reader;
    CHECK(local_file_reader.Open(file_path));
    std::string read_str;
    read_str.resize(20);
    int read_size = local_file_reader.Read(&read_str[0], 20);
    read_str.resize(read_size);
    EXPECT_EQ(read_str, kTestStr);

    EXPECT_TRUE(local_file_reader.SeekToOffset(1));
    read_str.resize(20);
    read_size = local_file_reader.Read(&read_str[0], 20);
    read_str.resize(read_size);
    EXPECT_EQ(read_str, "ello World!");

    EXPECT_TRUE(local_file_reader.Close());
  }
  {
    LocalFileReader local_file_reader(true);
    CHECK(local_file_reader.Open(file_path));
    std::string read_str;
    read_str.resize(20);
    int read_size = local_file_reader.Read(&read_str[0], 20);
    read_str.resize(read_size);
    EXPECT_EQ(read_str, kTestStr);

    EXPECT_TRUE(local_file_reader.SeekToOffset(1));
    read_str.resize(20);
    read_size = local_file_reader.Read(&read_str[0], 20);
    read_str.resize(read_size);
    EXPECT_EQ(read_str, "ello World!");

    EXPECT_TRUE(local_file_reader.Close());
  }
}

}  // namespace io
}  // namespace base
