// Copyright @2022 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <memory>
#include <string>

#include "base/io/file_reader/base_file_reader.h"
#include "base/io/hdfs/hdfs_client.h"

namespace base {
namespace io {

class HdfsFileReader : public BaseFileReader {
 public:
  explicit HdfsFileReader(hdfs::HdfsClient* hdfs_client, bool cache_all = false);
  ~HdfsFileReader() override = default;

 private:
  bool OpenInternal(const std::string& path) override;
  int ReadInternal(void* buffer, int buffer_size) override;
  bool SeekToOffsetInternal(int64_t offset) override;
  bool CloseInternal() override;

  int64_t GetFileSize() override;

  hdfs::HdfsClient* hdfs_client_ = nullptr;  // Not owned
  std::unique_ptr<hdfs::HdfsClient::HdfsFileHandler> hdfs_file_handler_;

  DISALLOW_COPY_AND_ASSIGN(HdfsFileReader);
};

}  // namespace io
}  // namespace base
