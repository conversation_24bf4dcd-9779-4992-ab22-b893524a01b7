// Copyright @2023 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/io/file_reader/mock_file_reader.h"

#include <fcntl.h>
#include <sys/stat.h>
#include <unistd.h>
#include <cstdlib>
#include <random>

#include "glog/logging.h"

#include "base/common/eintr_wrapper.h"

namespace base {
namespace io {
namespace {
inline bool RandomFailed() {
  constexpr double kRandomFailedRatio = 0.01;
  return std::rand() * 1.0 / RAND_MAX < kRandomFailedRatio;
}
}  // namespace

MockFileReader::MockFileReader(bool cache_all) : BaseFileReader(cache_all), file_descriptor_(-1) {}

bool MockFileReader::OpenInternal(const std::string& path) {
  if (RandomFailed()) {
    return false;
  }
  file_descriptor_ = HANDLE_EINTR(open(path.c_str(), O_RDONLY));
  return file_descriptor_ != -1;
}

int MockFileReader::ReadInternal(void* buffer, int buffer_size) {
  if (RandomFailed()) {
    return -1;
  }
  return read(file_descriptor_, buffer, buffer_size);
}

bool MockFileReader::SeekToOffsetInternal(int64_t offset) {
  if (RandomFailed()) {
    return false;
  }
  return HANDLE_EINTR(lseek(file_descriptor_, offset, SEEK_SET)) != -1;
}

bool MockFileReader::CloseInternal() {
  if (RandomFailed()) {
    return false;
  }
  return HANDLE_EINTR(close(file_descriptor_)) != -1;
}

int64_t MockFileReader::GetFileSize() {
  struct stat statbuf;
  int ret = stat(path_.c_str(), &statbuf);
  CHECK_EQ(ret, 0) << "Failed to get file size: " << path_;
  return statbuf.st_size;
}

}  // namespace io
}  // namespace base
