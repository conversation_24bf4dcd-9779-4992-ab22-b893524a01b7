// Copyright @2022 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/io/file_reader/hdfs_file_reader.h"

namespace base {
namespace io {

HdfsFileReader::HdfsFileReader(hdfs::HdfsClient* hdfs_client, bool cache_all)
    : BaseFileReader(cache_all), hdfs_client_(CHECK_NOTNULL(hdfs_client)) {}

bool HdfsFileReader::OpenInternal(const std::string& path) {
  hdfs_file_handler_ = hdfs_client_->OpenFile(path, O_RDONLY);
  return hdfs_file_handler_ != nullptr;
}

int HdfsFileReader::ReadInternal(void* buffer, int buffer_size) {
  return hdfs_client_->Read(hdfs_file_handler_.get(), buffer, buffer_size);
}

bool HdfsFileReader::SeekToOffsetInternal(int64_t offset) {
  return hdfs_client_->SeekToOffset(hdfs_file_handler_.get(), offset);
}

bool HdfsFileReader::CloseInternal() {
  if (hdfs_file_handler_) {
    return hdfs_client_->CloseFile(hdfs_file_handler_.get());
  }
  return true;
}

int64_t HdfsFileReader::GetFileSize() {
  base::Optional<hdfs::HdfsFileInfo> hdfs_file_info = hdfs_client_->GetPathInfo(path_);
  if (hdfs_file_info == base::None()) {
    LOG(ERROR) << "Get file info failed: " << path_;
    return -1;
  }
  return hdfs_file_info->size;
}

}  // namespace io
}  // namespace base
