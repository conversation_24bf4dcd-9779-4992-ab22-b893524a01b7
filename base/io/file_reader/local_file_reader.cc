// Copyright @2022 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/io/file_reader/local_file_reader.h"

#include <fcntl.h>
#include <sys/stat.h>
#include <unistd.h>

#include "glog/logging.h"

#include "base/common/eintr_wrapper.h"

namespace base {
namespace io {

LocalFileReader::LocalFileReader(bool cache_all)
    : BaseFileReader(cache_all), file_descriptor_(-1) {}

bool LocalFileReader::OpenInternal(const std::string& path) {
  file_descriptor_ = HANDLE_EINTR(open(path.c_str(), O_RDONLY));
  return file_descriptor_ != -1;
}

int LocalFileReader::ReadInternal(void* buffer, int buffer_size) {
  return read(file_descriptor_, buffer, buffer_size);
}

bool LocalFileReader::SeekToOffsetInternal(int64_t offset) {
  return HANDLE_EINTR(lseek(file_descriptor_, offset, SEEK_SET)) != -1;
}

bool LocalFileReader::CloseInternal() { return HANDLE_EINTR(close(file_descriptor_)) != -1; }

int64_t LocalFileReader::GetFileSize() {
  struct stat statbuf;
  int ret = stat(path_.c_str(), &statbuf);
  CHECK_EQ(ret, 0) << "Failed to get file size: " << path_;
  return statbuf.st_size;
}

}  // namespace io
}  // namespace base
