// Copyright @2022 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <cstdint>
#include <string>

#include "base/common/macros.h"

namespace base {
namespace io {

class BaseFileReader {
 public:
  explicit BaseFileReader(bool cache_all = false);
  virtual ~BaseFileReader();

  MUST_USE_RESULT bool Open(const std::string& path);
  MUST_USE_RESULT int Read(void* buffer, int buffer_size);
  MUST_USE_RESULT bool SeekToOffset(int64_t offset);
  MUST_USE_RESULT bool Close();

  int64_t file_size() const { return file_size_; }
  int64_t current_offset() const { return current_offset_; }
  std::string uri() const { return path_; }
  bool is_opened() const { return is_opened_; }

 protected:
  std::string path_;

 private:
  virtual bool OpenInternal(const std::string& path) = 0;
  virtual int ReadInternal(void* buffer, int buffer_size) = 0;
  virtual bool SeekToOffsetInternal(int64_t offset) = 0;
  virtual bool CloseInternal() = 0;
  virtual int64_t GetFileSize() = 0;

  bool is_opened_ = false;
  bool cache_all_ = false;
  int64_t current_offset_ = 0;
  int64_t file_size_ = 0;
  std::string cache_data_;

  DISALLOW_COPY_AND_ASSIGN(BaseFileReader);
};

}  // namespace io
}  // namespace base
