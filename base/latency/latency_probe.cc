// Copyright @2019 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/latency/latency_probe.h"

#include <string>

#include "glog/logging.h"

#include "absl/time/clock.h"
#include "absl/time/time.h"
#include "base/latency/latency_manager.h"
#include "base/strings/format.h"

namespace base {

LatencyProbe::LatencyProbe(const std::string& group_name, const std::string& probe_name)
    : group_name_(group_name), probe_name_(probe_name) {}

LatencyProbe::~LatencyProbe() {
  if (manager_ != nullptr) {
    manager_->Update(this);
  }
}

const absl::Time& LatencyProbe::begin_time() const {
  CHECK(begin_time_  != base::none);
  return *begin_time_;
}

const absl::Time& LatencyProbe::end_time() const {
  CHECK(end_time_  != base::none);
  return *end_time_;
}

void LatencyProbe::Begin(const std::string& location) {
  CHECK(begin_time_  == base::none);
  begin_time_ = absl::Now();
  begin_location_ = location;
}

void LatencyProbe::End(const std::string& location) {
  CHECK(end_time_  == base::none);
  end_time_ = absl::Now();
  end_location_ = location;
}

absl::Duration LatencyProbe::duration() const {
  CHECK(begin_time_ != base::none);
  CHECK(end_time_ != base::none);
  return *end_time_ - *begin_time_;
}

std::string LatencyProbe::name() const {
  return strings::Format("{}@{}-{}", probe_id_, group_name_, probe_name_);
}

std::string LatencyProbe::ToString() const {
  if (begin_time_ != base::none && end_time_ != base::none) {
    return strings::Format("[{}]: {:.4f}", name(), absl::ToDoubleSeconds(duration()));
  } else {
    return strings::Format("[{}]: Not completed", name());
  }
}

std::string LatencyProbe::Report() const {
  auto func = [](const absl::Time& time) {
    return absl::FormatTime("%Y%m%d%H%M%E4S", time, absl::UTCTimeZone());
  };
  CHECK(begin_time_ != base::none);
  CHECK(end_time_ != base::none);
  return strings::Format("[{}]: {:.4f}\n\t begin: {}@{}\n\t end: {}@{}",
                         name(), absl::ToDoubleSeconds(duration()),
                         begin_location_, func(*begin_time_),
                         end_location_, func(*end_time_));
}
}  // namespace base
