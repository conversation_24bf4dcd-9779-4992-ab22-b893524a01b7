package(default_visibility = ["//visibility:public"])

cc_library(
    name = "latency",
    srcs = [
        "latency_manager.cc",
        "latency_probe.cc",
    ],
    hdrs = [
        "latency.h",
        "latency_manager.h",
        "latency_probe.h",
    ],
    deps = [
        "//base/common:optional",
        "//base/strings:format",
        "//base/synchronization:mutex",
        "@com_google_absl//absl/time",
    ],
)
