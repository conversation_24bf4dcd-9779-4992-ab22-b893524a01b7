// Copyright @2019 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <cstdint>
#include <string>
#include <map>

#include "absl/time/time.h"
#include "base/common/macros.h"
#include "base/common/optional.h"

namespace base {

class LatencyManager;
class LatencyProbe {
 public:
  LatencyProbe(const std::string& group_name, const std::string& probe_name);
  ~LatencyProbe();

  std::string name() const;
  int64_t probe_id() const { return probe_id_; }

  const std::string& group_name() const { return group_name_; }
  const std::string& probe_name() const { return probe_name_; }

  const absl::Time& begin_time() const;
  const absl::Time& end_time() const;
  const std::string& begin_location() const { return begin_location_; }
  const std::string& end_location() const { return end_location_; }

  void Begin(const std::string& location);
  void End(const std::string& location);

  absl::Duration duration() const;

  std::string ToString() const;
  std::string Report() const;

 private:
  std::string group_name_;
  std::string probe_name_;
  std::string begin_location_;
  std::string end_location_;
  base::Optional<absl::Time> begin_time_;
  base::Optional<absl::Time> end_time_;
  LatencyManager* manager_ = nullptr;
  int64_t probe_id_ = -1;
  friend class LatencyManager;

  DISALLOW_COPY_AND_ASSIGN(LatencyProbe);
};

}  // namespace base
