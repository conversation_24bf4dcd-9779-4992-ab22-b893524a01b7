// Copyright @2019 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <map>
#include <deque>
#include <memory>
#include <string>

#include "absl/time/time.h"
#include "base/common/macros.h"
#include "base/latency/latency_probe.h"
#include "base/synchronization/mutex.h"

namespace base {

class LatencyManager {
 public:
  explicit LatencyManager(const std::string& group_name);
  ~LatencyManager();

  std::unique_ptr<LatencyProbe> Create(const std::string& probe_name);

  void Update(LatencyProbe* probe);
  std::string Report();

 private:
  struct ProbeItem {
    int id = 0;
    absl::Duration duration;
    absl::Time begin;
    absl::Time end;
  };
  struct ProbeData {
    std::string name;
    absl::Duration total;
    std::deque<ProbeItem> durations;
    base::Mutex lock;
    std::string source_location_begin;
    std::string source_location_end;
  };

  ProbeData* GetProbeData(const std::string& key);
  int64_t AllocateId(const std::string& key);

  mutable base::Mutex mutex_;
  std::map<std::string, std::unique_ptr<ProbeData>> probe_data_;
  std::map<std::string, int64_t> allocate_id_;

  const std::string group_name_;
  static constexpr int kMaxBufferSize = 128;

  DISALLOW_COPY_AND_ASSIGN(LatencyManager);
};

}  // namespace base
