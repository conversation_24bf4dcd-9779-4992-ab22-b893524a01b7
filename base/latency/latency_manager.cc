// Copyright @2019 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/latency/latency_manager.h"

#include <iomanip>
#include <sstream>
#include <string>
#include <utility>
#include <vector>

#include "glog/logging.h"

#include "base/common/macros.h"
#include "base/latency/latency_probe.h"
#include "base/strings/format.h"

namespace base {

constexpr int LatencyManager::kMaxBufferSize;

LatencyManager::LatencyManager(const std::string& group_name) : group_name_(group_name) {}

LatencyManager::~LatencyManager() {}

std::unique_ptr<LatencyProbe> LatencyManager::Create(const std::string& probe_name) {
  auto probe = std::make_unique<LatencyProbe>(group_name_, probe_name);
  probe->manager_ = this;
  probe->probe_id_ = AllocateId(probe_name);
  return probe;
}

LatencyManager::ProbeData* LatencyManager::GetProbeData(const std::string& key) {
  base::MutexLock scoped_lock(&mutex_);
  auto iter = probe_data_.find(key);
  if (iter != probe_data_.end()) {
    return iter->second.get();
  }

  ProbeData* data_ptr = nullptr;
  auto data = std::make_unique<ProbeData>();
  data_ptr = data.get();
  data->name = key;
  probe_data_[key] = std::move(data);
  return data_ptr;
}

int64_t LatencyManager::AllocateId(const std::string& key) {
  base::MutexLock scoped_lock(&mutex_);
  return allocate_id_[key]++;
}

void LatencyManager::Update(LatencyProbe* probe) {
  ProbeData* data = GetProbeData(probe->probe_name());
  base::MutexLock scoped_lock(&data->lock);
  if (data->durations.size() > kMaxBufferSize) {
    const ProbeItem& item = data->durations.front();
    data->total -= item.duration;
    data->durations.pop_front();
  }

  ProbeItem item;
  item.begin = probe->begin_time();
  item.end = probe->end_time();
  item.duration = probe->duration();
  data->total += item.duration;
  data->durations.push_back(item);

  LOG_IF(FATAL, !data->source_location_begin.empty() &&
         data->source_location_begin != probe->begin_location())
      << strings::Format("Multiple probe with same name {}", data->name);

  data->source_location_begin = probe->begin_location();
  data->source_location_end = probe->end_location();
}

std::string LatencyManager::Report() {
  std::vector<ProbeData*> probe_data;
  {
    base::MutexLock scoped_lock(&mutex_);
    for (auto& item : probe_data_) {
      probe_data.emplace_back(item.second.get());
    }
  }

  std::stringstream ss;
  ss << std::fixed << "\n\n"
     << "Report: " << group_name_ << "\n"
     << std::setw(30) << "Name"
     << std::setw(12) << "Num"
     << std::setw(12) << "Average(s)"
     << std::setw(12) << "Max(s)"
     << std::setw(12) << "Min(s)"
     << std::setw(60) << "Location"
     << std::endl;
  ss << std::string(160, '-') << "\n";
  for (const ProbeData* data : probe_data) {
    ss << std::setw(30) << data->name
       << std::setw(12) << data->durations.size()
       << std::setw(12) << absl::ToDoubleSeconds(data->total) / data->durations.size()
       << std::setw(12) << "0.0"
       << std::setw(12) << "0.0"
       << std::setw(60) << data->source_location_begin
       << std::endl;
  }
  ss << std::string(160, '-') << "\n";
  return ss.str();
}
}  // namespace base
