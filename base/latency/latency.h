// Copyright @2019 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <memory>
#include <string>
#include <utility>

#include "glog/logging.h"

#include "absl/time/time.h"
#include "base/common/macros.h"
#include "base/strings/format.h"
#include "base/latency/latency_manager.h"
#include "base/latency/latency_probe.h"

// base::LatencyProbe probe(group_name, probe_name, __LINE__ __FILE__);
// {
//   base::LatencyProbe();
//   your code
// }
// VLOG() << probe.ToString();

#ifndef DISABLE_LATENCY
#define LATENCY_GROUP_BEGIN(probe_name, manager)     {                  \
    auto probe = manager->Create(probe_name);                           \
    probe->Begin(strings::Format("{}:{}", __FILE__, __LINE__));

#define LATENCY_GROUP_END(vlog_level)                                   \
  probe->End(strings::Format("{}:{}", __FILE__, __LINE__));             \
}

#define LATENCY_BEGIN_SCOPE(group_name, probe_name)     {               \
    base::LatencyProbe probe(group_name, probe_name);                   \
    probe.Begin(strings::Format("{}:{}", __FILE__, __LINE__));          \

#define LATENCY_END_SCOPE(vlog_level)                                   \
  probe.End(strings::Format("{}:{}", __FILE__, __LINE__));              \
  VLOG(vlog_level) << probe.Report();                                   \
}

#else

#define LATENCY_GROUP_BEGIN(probe_name, manager)
#define LATENCY_GROUP_END(vlog_level)

#define LATENCY_BEGIN_SCOPE(group_name, probe_name)
#define LATENCY_END_SCOPE(vlog_level)

#endif
