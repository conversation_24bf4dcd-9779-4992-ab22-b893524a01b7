package(default_visibility = ["//visibility:public"])

cc_library(
    name = "base_index",
    hdrs = ["base_index.h"],
    linkopts = [
        "-ldl",
    ],
    deps = [
        "//base/common:macros",
        "//base/strings:format",
        "@glog",
    ],
)

cc_library(
    name = "sqlite_index",
    srcs = ["sqlite_index.cc"],
    hdrs = ["sqlite_index.h"],
    deps = [
        ":base_index",
        "//base/common:macros",
        "//base/file:file_path_util",
        "//base/file:file_util",
        "@sqlite3",
        "@sqlite_orm",
    ],
)

cc_test(
    name = "sqlite_index_test",
    srcs = ["sqlite_index_test.cc"],
    deps = [
        ":sqlite_index",
        "@glog",
        "@gtest//:main",
    ],
)

cc_library(
    name = "data_pack",
    srcs = ["data_pack.cc"],
    hdrs = ["data_pack.h"],
    deps = [
        ":base_index",
        ":sqlite_index",
        "//base/common:macros",
        "//base/file:file_path_util",
        "//base/file:file_util",
        "@glog",
        "@gtest",
    ],
)

cc_test(
    name = "data_pack_test",
    srcs = ["data_pack_test.cc"],
    deps = [
        ":data_pack",
        "@gtest//:main",
    ],
)
