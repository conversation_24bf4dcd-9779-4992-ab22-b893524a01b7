// Copyright @2024 Sankuai AI Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/data_pack/data_pack.h"

#include <memory>
#include <utility>

namespace base {

namespace {
constexpr int kBytesPerGB = 1024 * 1024 * 1024;
constexpr char kDataPackMainPartitionId[] = "main";
}  // namespace

DataPack::DataPack(const Options& options) : options_(options) {
  packaged_file_folder_ = strings::Format("{}/packaged_file", options_.data_pack_folder);
  index_folder_ = strings::Format("{}/index", options_.data_pack_folder);
  if (!file_path::CreateDirectoryIfNotExists(packaged_file_folder_, true)) {
    LOG(FATAL) << "Create Packaged File folder failed!";
  }
  if (!file_path::CreateDirectoryIfNotExists(index_folder_, true)) {
    LOG(FATAL) << "Create Index folder failed!";
  }
  if (options_.partition_id && *options_.partition_id != kDataPackMainPartitionId) {
    current_partition_id_ = *options_.partition_id;
    std::string main_index_file_path =
        strings::Format("{}/index_{}.db", index_folder_, kDataPackMainPartitionId);
    readonly_main_index_ = std::make_unique<SqliteIndex>(main_index_file_path);
  } else {
    current_partition_id_ = kDataPackMainPartitionId;
  }
  index_file_path_ = strings::Format("{}/index_{}.db", index_folder_, current_partition_id_);
  index_ = std::make_unique<SqliteIndex>(index_file_path_);

  InitPackagedFileHandleMap();

  current_file_size_ = packaged_file_handle_map_[GetCurrentPackagedFileName()]->tellg();
}

DataPack::~DataPack() { Close(); }

std::string DataPack::GetCurrentPackagedFileName() const {
  return strings::Format("{}_{}", current_partition_id_, current_block_id_);
}

void DataPack::InitPackagedFileHandleMap() {
  boost::filesystem::directory_iterator iter(packaged_file_folder_);
  while (iter != boost::filesystem::directory_iterator()) {
    std::string packaged_file_name = iter->path().stem().string();
    // TODO(wangjiawei43): convert this part into function
    int pos = 0;
    while (pos < packaged_file_name.size() && packaged_file_name[pos] != '_') {
      pos++;
    }
    std::string partition_id = packaged_file_name.substr(0, pos);
    int block_id =
        std::stoi(packaged_file_name.substr(pos + 1, packaged_file_name.size() - pos - 1));
    if (partition_id == current_partition_id_ && block_id > current_block_id_) {
      current_block_id_ = block_id;
    }
    std::unique_ptr<std::fstream> file_ptr = std::make_unique<std::fstream>();
    file_ptr->open(strings::Format("{}/{}", packaged_file_folder_, packaged_file_name),
                   std::ios::ate | std::ios::app | std::ios::binary | std::ios::in | std::ios::out);

    packaged_file_handle_map_[packaged_file_name] = std::move(file_ptr);
    ++iter;
  }
  if (!packaged_file_handle_map_.count(GetCurrentPackagedFileName())) {
    std::unique_ptr<std::fstream> current_packaged_file_ptr = std::make_unique<std::fstream>();
    current_packaged_file_ptr->open(
        strings::Format("{}/{}", packaged_file_folder_, GetCurrentPackagedFileName()),
        std::ios::app | std::ios::binary | std::ios::in | std::ios::out);
    packaged_file_handle_map_[GetCurrentPackagedFileName()] = std::move(current_packaged_file_ptr);
  }
}

void DataPack::RolloverBlockIfExceeds(size_t size) {
  if (current_file_size_ + size >= options_.block_size_gb * kBytesPerGB) {
    packaged_file_handle_map_[GetCurrentPackagedFileName()]->flush();
    current_file_size_ = 0;
    current_block_id_++;
    std::unique_ptr<std::fstream> current_packaged_file_ptr = std::make_unique<std::fstream>();
    current_packaged_file_ptr->open(
        strings::Format("{}/{}", packaged_file_folder_, GetCurrentPackagedFileName()),
        std::ios::app | std::ios::binary | std::ios::in | std::ios::out);
    packaged_file_handle_map_[GetCurrentPackagedFileName()] = std::move(current_packaged_file_ptr);
  }
}

void DataPack::Flush() {
  packaged_file_handle_map_[GetCurrentPackagedFileName()]->flush();
  packaged_file_handle_map_[GetCurrentPackagedFileName()]->seekg(0, std::ios::end);
  CHECK_EQ(current_file_size_, packaged_file_handle_map_[GetCurrentPackagedFileName()]->tellg());
  index_->Flush();
}

void DataPack::Close() {
  if (is_closed_) {
    return;
  }
  Flush();
  for (auto iter = packaged_file_handle_map_.begin(); iter != packaged_file_handle_map_.end();) {
    iter->second->close();
    iter = packaged_file_handle_map_.erase(iter);
  }
  index_->Close();
  is_closed_ = true;
}

void DataPack::Remove(const std::string& key) { index_->Remove(key); }

bool DataPack::Exist(const std::string& key) const {
  if (readonly_main_index_ && readonly_main_index_->QueryOne(key) != nullptr) {
    return true;
  }
  return index_->QueryOne(key) != nullptr;
}

DataPackToken DataPack::Write(const std::string& key, const std::string& data) {
  RolloverBlockIfExceeds(data.size());
  const std::string& current_packaged_file_name = GetCurrentPackagedFileName();
  DataPackToken token{current_packaged_file_name, current_file_size_, data.size()};
  packaged_file_handle_map_[current_packaged_file_name]->write(&data[0], data.size());
  current_file_size_ += data.size();
  index_->Insert(key, token);
  return token;
}

std::string DataPack::Read(const std::string& key) {
  if (readonly_main_index_ && readonly_main_index_->QueryOne(key) != nullptr) {
    return Read(readonly_main_index_->QueryOne(key));
  }
  return Read(index_->QueryOne(key));
}

std::string DataPack::Read(std::unique_ptr<DataPackToken> token_ptr) {
  std::string content;
  content.resize(token_ptr->size);
  packaged_file_handle_map_[token_ptr->uri]->seekg(token_ptr->offset, std::ios::beg);
  packaged_file_handle_map_[token_ptr->uri]->read(&content[0], token_ptr->size);
  return content;
}

std::unordered_map<std::string, DataPackToken> DataPack::ListPrefix(const std::string& key) const {
  return index_->QueryByPrefix(key);
}

std::unordered_map<std::string, DataPackToken> DataPack::ListAll() const {
  return index_->QueryAll();
}

}  // namespace base
