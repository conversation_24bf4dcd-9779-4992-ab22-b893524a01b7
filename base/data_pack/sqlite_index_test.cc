// Copyright @2024 Sankuai AI Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/data_pack/sqlite_index.h"

#include <memory>

#include "gtest/gtest.h"

namespace base {
namespace {

constexpr char kTestDir[] = "/tmp/data_pack/index";

}  // namespace

class SqliteIndexTest : public ::testing::Test {
 public:
  void SetUp() override {
    if (file_path::IsDirectory(kTestDir)) {
      file_path::RemovePath(kTestDir, true);
    }
    file_path::CreateDirectory(kTestDir, true);
    sqlite_index = std::make_unique<SqliteIndex>("/tmp/data_pack/index/index.db");
  }

 protected:
  std::unique_ptr<SqliteIndex> sqlite_index = nullptr;
};

TEST_F(SqliteIndexTest, BasicTest) {
  sqlite_index->Insert("123", DataPackToken{"0_0", 0, 3});
  sqlite_index->Insert("456", DataPackToken{"1_0", 2, 4});
  std::unique_ptr<DataPackToken> value = sqlite_index->QueryOne("123");
  EXPECT_EQ(value->uri, "0_0");
  EXPECT_EQ(value->offset, 0);
  EXPECT_EQ(value->size, 3);
  sqlite_index->Insert("123", DataPackToken{"0_1", 3, 6});
  std::unique_ptr<DataPackToken> new_value = sqlite_index->QueryOne("123");
  EXPECT_EQ(new_value->uri, "0_1");
  EXPECT_EQ(new_value->offset, 3);
  EXPECT_EQ(new_value->size, 6);
  EXPECT_FALSE(sqlite_index->Exist("789"));
  std::unordered_map<std::string, DataPackToken> prefix_query_values =
      sqlite_index->QueryByPrefix("123");
  EXPECT_EQ(prefix_query_values.size(), 1);
  std::unordered_map<std::string, DataPackToken> all_values = sqlite_index->QueryAll();
  EXPECT_EQ(all_values.size(), 2);
  sqlite_index->Remove("123");
  all_values = sqlite_index->QueryAll();
  EXPECT_EQ(all_values.size(), 1);
}

}  // namespace base
