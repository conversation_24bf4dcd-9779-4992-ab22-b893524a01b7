// Copyright @2024 Sankuai AI Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <iostream>
#include <memory>
#include <string>
#include <unordered_map>

#include "base/data_pack/base_index.h"
#include "base/file/file_path_util.h"
#include "base/file/file_util.h"

#include "glog/logging.h"
#include "sqlite3.h"

#include <sqlite_orm/sqlite_orm.h>

namespace base {

class SqliteIndex : public DataPackIndex {
 public:
  struct DataPackIndexTable {
    size_t id = 0;
    std::string key;
    std::string uri;
    size_t offset = 0;
    size_t size = 0;
  };
  using StorageType = decltype(sqlite_orm::make_storage(
      std::string(),
      sqlite_orm::make_index("idx_key", &DataPackIndexTable::key),
      sqlite_orm::make_table(
          "data_pack_index_table",
          sqlite_orm::make_column(
              "id", &DataPackIndexTable::id, sqlite_orm::primary_key().autoincrement()),
          sqlite_orm::make_column("key", &DataPackIndexTable::key),
          sqlite_orm::make_column("uri", &DataPackIndexTable::uri),
          sqlite_orm::make_column("offset", &DataPackIndexTable::offset),
          sqlite_orm::make_column("size", &DataPackIndexTable::size))));

  explicit SqliteIndex(const std::string& db_file_path, size_t cache_size = 0);
  virtual ~SqliteIndex();

  void Insert(const std::string& key, const DataPackToken& token);
  void Remove(const std::string& key);
  void Flush();
  void Close();
  bool Exist(const std::string& key);
  std::unique_ptr<DataPackToken> QueryOne(const std::string& key);
  std::unordered_map<std::string, DataPackToken> QueryByPrefix(const std::string& prefix);
  std::unordered_map<std::string, DataPackToken> QueryAll();

 private:
  StorageType storage_;
  bool is_closed_ = false;

  DISALLOW_COPY_AND_ASSIGN(SqliteIndex);
};

}  // namespace base
