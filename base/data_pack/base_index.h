// Copyright @2024 Sankuai AI Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <memory>
#include <string>
#include <unordered_map>
#include <vector>

#include "glog/logging.h"

#include "base/common/macros.h"
#include "base/strings/format.h"

namespace base {

struct DataPackToken {
  std::string uri;
  size_t offset;
  size_t size;

  DataPackToken(const std::string& uri, size_t offset, size_t size)
      : uri(uri), offset(offset), size(size) {}
};

class DataPackIndex {
 public:
  DataPackIndex() = default;
  virtual ~DataPackIndex() = default;

  virtual void Insert(const std::string& key, const DataPackToken& token) = 0;
  virtual void Remove(const std::string& key) = 0;
  virtual void Flush() = 0;
  virtual void Close() = 0;
  virtual bool Exist(const std::string& key) = 0;
  virtual std::unique_ptr<DataPackToken> QueryO<PERSON>(const std::string& key) = 0;
  virtual std::unordered_map<std::string, DataPackToken> QueryByPrefix(
      const std::string& prefix) = 0;
  virtual std::unordered_map<std::string, DataPackToken> QueryAll() = 0;

  DISALLOW_COPY_AND_ASSIGN(DataPackIndex);
};

}  // namespace base
