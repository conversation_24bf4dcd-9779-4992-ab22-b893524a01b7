// Copyright @2024 Sankuai AI Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <fstream>
#include <memory>
#include <string>
#include <unordered_map>

#include "gtest/gtest.h"

#include "base/data_pack/base_index.h"
#include "base/data_pack/sqlite_index.h"
#include "base/file/file_path_util.h"
#include "base/file/file_util.h"

namespace base {

class DataPack {
 public:
  struct Options {
    std::string data_pack_folder;
    base::Optional<std::string> partition_id;
    int max_memory_mb = 1024;
    int block_size_gb = 1024;
    bool enable_profiling = false;
    explicit Options(const std::string& data_pack_folder) : data_pack_folder(data_pack_folder) {}
  };

  explicit DataPack(const Options& options);
  virtual ~DataPack();

  void Flush();
  void Close();
  void Remove(const std::string& key);
  bool Exist(const std::string& key) const;
  DataPackToken Write(const std::string& key, const std::string& data);
  std::string Read(const std::string& key);
  std::string Read(std::unique_ptr<DataPackToken> token_ptr);
  std::unordered_map<std::string, DataPackToken> ListPrefix(const std::string& key) const;
  std::unordered_map<std::string, DataPackToken> ListAll() const;
  const Options& GetOptions() const { return options_; }

 private:
  const Options options_;
  std::string packaged_file_folder_;
  std::string index_folder_;
  std::string index_file_path_;
  std::string current_partition_id_;
  int current_block_id_ = 0;
  size_t current_file_size_ = 0;
  bool is_closed_ = false;
  std::unordered_map<std::string, std::unique_ptr<std::fstream>> packaged_file_handle_map_;
  std::unique_ptr<DataPackIndex> index_ = nullptr;
  std::unique_ptr<DataPackIndex> readonly_main_index_ = nullptr;

  std::string GetCurrentPackagedFileName() const;
  void InitPackagedFileHandleMap();
  void RolloverBlockIfExceeds(size_t size);

  FRIEND_TEST(DataPackTest, BasicTest);
  FRIEND_TEST(DataPackTest, GetExportDataPackTest);

  DISALLOW_COPY_AND_ASSIGN(DataPack);
};

}  // namespace base
