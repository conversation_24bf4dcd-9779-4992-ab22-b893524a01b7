// Copyright @2024 Sankuai AI Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/data_pack/sqlite_index.h"

#include <memory>
#include <string>
#include <unordered_map>

namespace base {

namespace {
// TODO(wangjiawei43): Considering whether to support non-ascii characters in the future.
constexpr char IllegalCharacter = '|';
} // namespace

SqliteIndex::SqliteIndex(const std::string& db_file_path, size_t cache_size)
    : storage_(sqlite_orm::make_storage(
          db_file_path,
          sqlite_orm::make_index("idx_key", &DataPackIndexTable::key),
          sqlite_orm::make_table(
              "data_pack_index_table",
              sqlite_orm::make_column(
                  "id", &DataPackIndexTable::id, sqlite_orm::primary_key().autoincrement()),
              sqlite_orm::make_column("key", &DataPackIndexTable::key),
              sqlite_orm::make_column("uri", &DataPackIndexTable::uri),
              sqlite_orm::make_column("offset", &DataPackIndexTable::offset),
              sqlite_orm::make_column("size", &DataPackIndexTable::size)))) {
  storage_.sync_schema();
  storage_.begin_transaction();
}

SqliteIndex::~SqliteIndex() { Close(); }

void SqliteIndex::Insert(const std::string& key, const DataPackToken& token) {
  CHECK(key.find(IllegalCharacter) == std::string::npos);
  storage_.insert(DataPackIndexTable{0, key, token.uri, token.offset, token.size});
}

void SqliteIndex::Remove(const std::string& key) {
  storage_.remove_all<DataPackIndexTable>(
      sqlite_orm::where(sqlite_orm::c(&DataPackIndexTable::key) == key));
}

void SqliteIndex::Flush() {
  storage_.commit();
  storage_.begin_transaction();
}

void SqliteIndex::Close() {
  if (is_closed_) {
    return;
  }
  Flush();
  storage_.commit();
  is_closed_ = true;
}

bool SqliteIndex::Exist(const std::string& key) {
  const std::vector<DataPackIndexTable> results = storage_.get_all<DataPackIndexTable>(
      sqlite_orm::where(sqlite_orm::c(&DataPackIndexTable::key) == key));
  return results.size() > 0;
}

std::unique_ptr<DataPackToken> SqliteIndex::QueryOne(const std::string& key) {
  const std::vector<DataPackIndexTable> results = storage_.get_all<DataPackIndexTable>(
      sqlite_orm::where(sqlite_orm::c(&DataPackIndexTable::key) == key));
  if (results.size() > 0) {
    DataPackIndexTable value = results.back();
    return std::make_unique<DataPackToken>(value.uri, value.offset, value.size);
  }
  return nullptr;
}

std::unordered_map<std::string, DataPackToken> SqliteIndex::QueryByPrefix(
    const std::string& prefix) {
  const std::vector<DataPackIndexTable> values = storage_.get_all<DataPackIndexTable>(
      sqlite_orm::where(sqlite_orm::between(&DataPackIndexTable::key, prefix, prefix + IllegalCharacter)));
  std::unordered_map<std::string, DataPackToken> result;
  for (const auto& value : values) {
    result.insert_or_assign(value.key, DataPackToken{value.uri, value.offset, value.size});
  }
  return result;
}

std::unordered_map<std::string, DataPackToken> SqliteIndex::QueryAll() {
  const std::vector<DataPackIndexTable> values = storage_.get_all<DataPackIndexTable>();
  std::unordered_map<std::string, DataPackToken> result;
  for (const auto& value : values) {
    result.insert_or_assign(value.key, DataPackToken{value.uri, value.offset, value.size});
  }
  return result;
}

}  // namespace base
