# Copyright @2024 Sankuai Technology Inc. All rights reserved.
# Authors: <AUTHORS>

import time

from common.python.utils.walle_logger import get_logger

logger = get_logger("data_pack")


def thread_safe(func):
    '''
    A decorator to ensure method safety in a multi-threaded environment.
    If the object has thread safety mode enabled, this decorator will synchronize method calls using a lock.
    
    Note: The user must ensure that the passed 'self' has '_thread_safe_mode' and '_lock' attributes.
    
    :param func: The method being decorated
    :return: The wrapped method
    '''

    def wrapper(self, *args, **kwargs):
        if self._thread_safe_mode:
            with self._lock:
                return func(self, *args, **kwargs)
        else:
            return func(self, *args, **kwargs)

    return wrapper


def profiler(func):
    '''
    A decorator for measuring the execution time of functions.

    Note: The user must ensure that the 'self' object passed to the decorated function
    has an '_enable_profiling' attribute to control profiling behavior.

    Parameters:
    func -- the function to be decorated

    Returns:
    wrapper -- the wrapped function with added execution time measurement capability
    '''

    def wrapper(self, *args, **kwargs):
        if self._enable_profiling:
            start_time = time.time() * 1000
            result = func(self, *args, **kwargs)
            end_time = time.time() * 1000
            logger.error(
                f'[Profiling] func_name: {func.__name__}, cost_time: {end_time - start_time} ms')
            return result
        else:
            return func(self, *args, **kwargs)

    return wrapper
