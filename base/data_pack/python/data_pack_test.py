# Copyright @2024 Sankuai Technology Inc. All rights reserved.
# Authors: <AUTHORS>

import multiprocessing
import os
import shutil
import unittest

from concurrent.futures import ThreadPoolExecutor
from tqdm import tqdm

from base.data_pack.python.data_pack import DataPack, get_data_pack
from modules.planning.proto.common.math.oriented_point2d_pb2 import OrientedPoint2d

TEST_WORK_DIR = '/tmp/data_pack'
TEST_SIZE = 128 * 1024

TEST_MULTI_THREAD_DIR = '/tmp/multi_thread_data_pack'
TEST_MULTI_THREAD_SIZE = 16
TEST_MULTI_THREAD_FILE_SIZE = 100

TEST_PARTITION_ID = 'test_partition'
TEST_PARTITION_DIR = '/tmp/data_pack_partition'
TEST_PARTITION_FILE_SIZE = 10
TEST_PARTITION_PROCESS_SIZE = 10

TEST_CPP_DIR = '/tmp/test_data_pack'
TEST_CPP_KEY = 'test_datapack_key'
TEST_CPP_HEADING = 1.0


def _test_partition(process_id):
    print(f"start {process_id}")
    test_str = ''
    for i in range(TEST_SIZE):
        test_str += 'x'
    data_pack = DataPack(TEST_PARTITION_DIR, mode="rw", partition_id=process_id)
    for i in range(TEST_PARTITION_FILE_SIZE):
        key = f'/tmp/test_partition_{process_id}_{i}.bin'
        data_pack.write(key, data=test_str.encode())
        data_pack.flush()
        data = data_pack.read(key)
        assert data == test_str.encode()
    data_pack.close()
    print(f"end {process_id}")


class DataPackTest(unittest.TestCase):

    def setUp(self):
        if os.path.exists(TEST_WORK_DIR):
            shutil.rmtree(TEST_WORK_DIR)
        if os.path.exists(TEST_MULTI_THREAD_DIR):
            shutil.rmtree(TEST_MULTI_THREAD_DIR)
        if os.path.exists(TEST_PARTITION_DIR):
            shutil.rmtree(TEST_PARTITION_DIR)

    def test_direct_read(self):
        data_pack_writer = DataPack(TEST_WORK_DIR, enable_profiling=True)
        test_str1 = ''
        test_str2 = ''
        test_str3 = ''
        for _ in range(TEST_SIZE):
            test_str1 += 'x'
            test_str2 += 'y'
            test_str3 += 'z'
        token1 = data_pack_writer.write('/tmp/test1.bin', data=test_str1.encode())
        token2 = data_pack_writer.write('/tmp/test2.bin', data=test_str2.encode())
        token3 = data_pack_writer.write('/tmp/test3.bin', data=test_str3.encode())
        self.assertTrue(data_pack_writer.exist('/tmp/test1.bin'))
        data_pack_writer.delete('/tmp/test1.bin')
        self.assertFalse(data_pack_writer.exist('/tmp/test1.bin'))
        self.assertTrue(data_pack_writer.exist('/tmp/test2.bin'))
        data_pack_writer.close()

        data_pack_noindex = get_data_pack(TEST_WORK_DIR)
        self.assertEqual(DataPack.experiemental_direct_read(TEST_WORK_DIR, token=token1).decode(), test_str1)
        self.assertEqual(DataPack.experiemental_direct_read(TEST_WORK_DIR, token=token2).decode(), test_str2)
        self.assertEqual(DataPack.experiemental_direct_read(TEST_WORK_DIR, token=token3).decode(), test_str3)
    
    def test_noindex(self):
        data_pack_writer = DataPack(TEST_WORK_DIR, enable_profiling=True)
        test_str1 = ''
        test_str2 = ''
        test_str3 = ''
        for _ in range(TEST_SIZE):
            test_str1 += 'x'
            test_str2 += 'y'
            test_str3 += 'z'
        token1 = data_pack_writer.write('/tmp/test1.bin', data=test_str1.encode())
        token2 = data_pack_writer.write('/tmp/test2.bin', data=test_str2.encode())
        token3 = data_pack_writer.write('/tmp/test3.bin', data=test_str3.encode())
        self.assertTrue(data_pack_writer.exist('/tmp/test1.bin'))
        data_pack_writer.delete('/tmp/test1.bin')
        self.assertFalse(data_pack_writer.exist('/tmp/test1.bin'))
        self.assertTrue(data_pack_writer.exist('/tmp/test2.bin'))
        data_pack_writer.close()

        data_pack_noindex = get_data_pack(TEST_WORK_DIR)
        self.assertEqual(data_pack_noindex.read(token=token1).decode(), test_str1)
        self.assertEqual(data_pack_noindex.read(token=token2).decode(), test_str2)
        self.assertEqual(data_pack_noindex.read(token=token3).decode(), test_str3)

    def test_write_and_read(self):
        data_pack_writer = DataPack(TEST_WORK_DIR, enable_profiling=True)
        test_str1 = ''
        test_str2 = ''
        test_str3 = ''
        for _ in range(TEST_SIZE):
            test_str1 += 'x'
            test_str2 += 'y'
            test_str3 += 'z'
        data_pack_writer.write('/tmp/test1.bin', data=test_str1.encode())
        data_pack_writer.write('/tmp/test2.bin', data=test_str2.encode())
        data_pack_writer.write('/tmp/test3.bin', data=test_str3.encode())
        self.assertTrue(data_pack_writer.exist('/tmp/test1.bin'))
        data_pack_writer.delete('/tmp/test1.bin')
        self.assertFalse(data_pack_writer.exist('/tmp/test1.bin'))
        self.assertTrue(data_pack_writer.exist('/tmp/test2.bin'))
        data_pack_writer.close()

        data_pack_reader = DataPack(TEST_WORK_DIR, enable_profiling=True)
        data = data_pack_reader.read(key='/tmp/test2.bin')
        self.assertEqual(data.decode(), test_str2)

        file_paths = data_pack_reader.list_prefix('/tmp/')
        self.assertEqual(len(file_paths), 2)
        self.assertIn('/tmp/test2.bin', file_paths)
        all_keys = data_pack_reader.list_all()
        self.assertEqual(len(all_keys), 2)
        self.assertIn('/tmp/test2.bin', all_keys)
        data_pack_reader.close()

    def test_datapack_partition(self):
        test_str = ''
        for i in range(TEST_SIZE):
            test_str += 'x'
        print("start test")

        pool = multiprocessing.Pool(processes=4)

        process_ids = [i for i in range(TEST_PARTITION_PROCESS_SIZE)]

        with tqdm(total=len(process_ids), desc="Processing") as pbar:
            results = []
            for process_id in process_ids:
                result = pool.apply_async(_test_partition, args=(process_id,))
                results.append(result)

            while not all(result.ready() for result in results):
                pbar.update(sum(result.ready() for result in results) - pbar.n)

            results = [result.get() for result in results]

        pool.close()
        pool.join()
        DataPack.merge_index(TEST_PARTITION_DIR)
        data_pack = DataPack(TEST_PARTITION_DIR, mode='r', partition_id=TEST_PARTITION_ID)
        for process_id in range(TEST_PARTITION_FILE_SIZE):
            for file_id in range(TEST_PARTITION_FILE_SIZE):
                key = f'/tmp/test_partition_{process_id}_{file_id}.bin'
                data = data_pack.read(key)
                assert data == test_str.encode()

    def test_multi_thread(self):
        test_str = ''
        for i in range(TEST_SIZE):
            test_str += 'x'
        write_executor = ThreadPoolExecutor(max_workers=TEST_MULTI_THREAD_SIZE)
        read_executor = ThreadPoolExecutor(max_workers=TEST_MULTI_THREAD_SIZE)
        data_pack = DataPack(TEST_MULTI_THREAD_DIR, mode='rw', enable_profiling=True,
                             thread_safe_mode=True)

        def test_mult_write(data_pack, thread_id):
            for i in range(TEST_MULTI_THREAD_FILE_SIZE):
                data_pack.write(f'/tmp/test{thread_id}_{i}.bin', data=test_str.encode())
            data_pack.flush()

        def test_mult_read(thread_id):
            data_pack = DataPack(TEST_MULTI_THREAD_DIR, mode='r')
            for i in range(TEST_MULTI_THREAD_FILE_SIZE):
                data = data_pack.read(f'/tmp/test{thread_id}_{i}.bin')
                assert data == test_str.encode()

        for i in range(TEST_MULTI_THREAD_SIZE):
            write_executor.submit(test_mult_write, data_pack, i)
        write_executor.shutdown(wait=True)

        for i in range(TEST_MULTI_THREAD_SIZE):
            read_executor.submit(test_mult_read, i)
        read_executor.shutdown(wait=True)

    def test_datapack_from_c_plus_plus(self):
        # TODO(wangjiawei): need to be implemented
        pass


if __name__ == '__main__':
    unittest.main()
