# Copyright @2024 Sankuai Technology Inc. All rights reserved.
# Authors: <AUTHORS>

import logging
import time
import threading
from typing import List, Union, Tuple, Dict

from sqlalchemy import create_engine, Column, Integer, String
from sqlalchemy.dialects.sqlite import insert as sqlite_insert
from sqlalchemy.exc import OperationalError
from sqlalchemy.orm import sessionmaker
from sqlalchemy.orm import declarative_base

from base.data_pack.python.base_index import BaseIndex, Token
from base.data_pack.python.utils import thread_safe, profiler

Base = declarative_base()


class IndexTable(Base):
    __tablename__ = 'data_pack_index_table'
    id = Column(Integer, primary_key=True, autoincrement=True)
    key = Column(String, index=True)
    uri = Column(String)
    offset = Column(Integer)
    size = Column(Integer)


class SqliteIndex(BaseIndex):
    # TODO(wangjiawei43): Considering whether to support non-ascii characters in the future.
    illegal_character = '|'
    def __init__(self, db_file_path, mode: str = 'rw', cache_size: int = 8 * 1024,
                 enable_profiling: bool = False, thread_safe_mode: bool = False):
        self._enable_profiling = enable_profiling
        self._lock = threading.Lock()
        self._thread_safe_mode = thread_safe_mode

        engine = create_engine(
            f'sqlite:///{db_file_path}?cache=shared&mode={mode}&cache_size={cache_size}',
            echo=False)
        try:
            Base.metadata.create_all(engine)
        except OperationalError as e:
            if "already exists" in str(e):
                pass
            else:
                raise e
        Session = sessionmaker(bind=engine)
        self._session = Session()
        self._session.begin()

    @thread_safe
    @profiler
    def insert(self, key: str, token: Token):
        assert SqliteIndex.illegal_character not in key 
        self._session.add(IndexTable(key=key, uri=token.uri, offset=token.offset, size=token.size))

    @thread_safe
    def sqlite_batch_insert(self, data):
        stmt = sqlite_insert(IndexTable).values(data)
        self._session.execute(stmt)

    @thread_safe
    def flush(self):
        self._session.commit()
        self._session.begin()

    @thread_safe
    def close(self):
        self._session.commit()
        self._session.close()

    @thread_safe
    def delete(self, key: str):
        values = self._session.query(IndexTable).filter(IndexTable.key == key)
        for value in values:
            self._session.delete(value)

    @profiler
    def query_one(self, key: str) -> Union[Token, None]:
        value = self._session.query(IndexTable).filter(IndexTable.key == key).order_by(
            IndexTable.id.desc()).first()
        if value is None:
            return None
        return Token(value.uri, value.offset, value.size)

    @profiler
    def query_all(self) -> Dict[str, Token]:
        results = {}
        values = self._session.query(IndexTable).order_by(IndexTable.id.desc()).all()
        for value in values:
            results[value.key] = Token(value.uri, value.offset, value.size)
        return results

    def query_by_prefix(self, prefix: str) -> Dict[str, Token]:
        results = {}
        values = self._session.query(IndexTable).filter(IndexTable.key.between(prefix, prefix + SqliteIndex.illegal_character)).all()
        for value in values:
            results[value.key] = Token(value.uri, value.offset, value.size)
        return results
