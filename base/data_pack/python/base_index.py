# Copyright @2024 Sankuai Technology Inc. All rights reserved.
# Authors: <AUTHORS>

from abc import ABCMeta, abstractmethod
from typing import List, Union, Tuple, Dict


class Token(object):

    def __init__(self, uri: str, offset: int, size: int):
        '''
        :param uri: The URI (often a relative file path) where the Token is located.
        :param offset: The offset of the Token within the resource.
        :param size: The size of the Token.
        '''
        self.uri = uri
        self.offset = offset
        self.size = size


class BaseIndex(metaclass=ABCMeta):

    @abstractmethod
    def insert(self, key: str, token: Token):
        raise NotImplementedError

    @abstractmethod
    def flush(self):
        raise NotImplementedError

    @abstractmethod
    def close(self):
        raise NotImplementedError

    @abstractmethod
    def delete(self, key: str):
        raise NotImplementedError

    @abstractmethod
    def query_one(self, key: str) -> Union[Token, None]:
        raise NotImplementedError

    @abstractmethod
    def query_all(self) -> Dict[str, Token]:
        raise NotImplementedError

    @abstractmethod
    def query_by_prefix(self, prefix: str) -> Dict[str, Token]:
        raise NotImplementedError
