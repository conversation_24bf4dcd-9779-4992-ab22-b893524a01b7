# Copyright @2024 Sankuai Technology Inc. All rights reserved.
# Authors: <AUTHORS>

import functools
import os
import pickle as pkl
import shutil
import threading

from glob import glob
from tqdm import tqdm
from typing import Dict, List, Tuple, Union

from base.data_pack.python.sqlite_index import SqliteIndex, Token
from base.data_pack.python.utils import profiler, thread_safe, logger

BYTES_PER_GIGEBYTE = 1024 * 1024 * 1024
FILE_MODE = {'r': 'rb', 'rw': 'ab+', 'noindex': 'rb'}
MAIN_PARTITION_ID = "main"


class DataPack(object):

    def __init__(self, data_pack_folder: str, partition_id: Union[int, str] = None,
                 mode: str = 'rw', max_memory_mb: int = 1024, block_size_gb: int = 1024,
                 enable_profiling: bool = False, thread_safe_mode: bool = False):
        '''
        data_pack_folder: data and index save path
        partition_id: partition id
        mode: ['r' , 'rw', 'noindex']
        max_memory_mb: max memory used for cache
        block_size_gb: partition size
        enable_profiling: enable profiling or not
        thread_safe_mode: thread safe mode or not
        '''
        self._data_pack_folder = data_pack_folder
        self._packaged_file_folder = f'{self._data_pack_folder}/packaged_file'
        self._index_folder = f'{self._data_pack_folder}/index'
        if not os.path.exists(self._packaged_file_folder):
            os.makedirs(self._packaged_file_folder, exist_ok=True)
        if not os.path.exists(self._index_folder):
            os.makedirs(self._index_folder, exist_ok=True)

        assert mode in FILE_MODE, f'invalid mode: {mode}, support {FILE_MODE.keys()}'
        self._file_mode = FILE_MODE[mode]

        noindex = mode == 'noindex'
        self._is_partition_mode = partition_id is not None
        assert not (self._is_partition_mode and noindex), "partition mode must have index"

        if self._is_partition_mode:
            self._partition_id = partition_id
            main_index_file_path = f"{self._index_folder}/index_{MAIN_PARTITION_ID}.db"
            self._readonly_main_index = SqliteIndex(main_index_file_path, "r",
                                                    cache_size=1024 * max_memory_mb * 1024)
        else:
            self._partition_id = MAIN_PARTITION_ID
            self._readonly_main_index = None

        index_file_path = f'{self._index_folder}/index_{self._partition_id}.db'
        if noindex:
            self._index = None
        else:
            self._index = SqliteIndex(index_file_path, mode, cache_size=1024 * max_memory_mb * 1024)
        self._thread_safe_mode = thread_safe_mode
        self._lock = threading.Lock()
        self._max_memory_mb = max_memory_mb
        self._enable_profiling = enable_profiling
        self._block_size_gb = block_size_gb
        self._packaged_file_handle_dict = {}
        self._block_id = 0
        self._file_bytes = 0
        self._init_packaged_file_handle_dict()

    def __exit__(self, exc_type, exc_value, traceback):
        self.close()

    def _init_packaged_file_handle_dict(self):
        packaged_files = os.listdir(self._packaged_file_folder)
        for packaged_file_name in packaged_files:
            if len(packaged_file_name.split('_')) != 2:
                logger.error(f'invalid partition name: {packaged_file_name}')
                continue
            partition_id, block_id = packaged_file_name.split('_')

            if partition_id == self._partition_id:
                self._block_id = max(self._block_id, int(block_id))

            self._packaged_file_handle_dict[packaged_file_name] = open(
                f'{self._packaged_file_folder}/{packaged_file_name}', self._file_mode)

        if self._is_write_mode():
            if self._packaged_file_handle_dict.get(self._packaged_file_name) is None:
                self._packaged_file_handle_dict[self._packaged_file_name] = \
                    open(self._packaged_file_path, self._file_mode)
            self._file_bytes = os.fstat(
                self._packaged_file_handle_dict[self._packaged_file_name].fileno()).st_size

    def _rollover_block_if_exceeds(self, size: int):
        if self._file_bytes + size > self._block_size_gb * BYTES_PER_GIGEBYTE:
            self._packaged_file_handle_dict[self._packaged_file_name].flush()
            self._file_bytes = 0
            self._block_id += 1
            self._packaged_file_handle_dict[self._packaged_file_name] = open(
                self._packaged_file_path, self._file_mode)

    @property
    def _packaged_file_name(self) -> str:
        return f'{self._partition_id}_{self._block_id}'

    @property
    def _packaged_file_path(self) -> str:
        return f'{self._packaged_file_folder}/{self._packaged_file_name}'

    @property
    def _packaged_file_handle(self) -> str:
        return self._packaged_file_handle_dict[self._packaged_file_name]

    def _is_write_mode(self) -> bool:
        return self._file_mode == FILE_MODE['rw']

    @thread_safe
    def close(self):
        self.flush()
        for packaged_file_handle in self._packaged_file_handle_dict.values():
            packaged_file_handle.close()
        self._packaged_file_handle_dict.clear()
        if self._index is not None:
            self._index.close()

    @thread_safe
    def flush(self):
        '''
        flush cache to disk
        '''
        if not self._is_write_mode():
            return
        self._packaged_file_handle.flush()
        packaged_file_bytes = os.fstat(self._packaged_file_handle.fileno()).st_size
        assert self._file_bytes == packaged_file_bytes, f"self._file_bytes : {self._file_bytes}, packaged_file_bytes: {packaged_file_bytes}"
        self._index.flush()

    @thread_safe
    def delete(self, key: str):
        assert self._is_write_mode(), 'delete only support in write mode'
        self._index.delete(key)

    @thread_safe
    @profiler
    def write(self, key: str, data: bytes = None) -> Token:
        '''
        write bytes to data pack
        return Token
        '''
        assert self._is_write_mode(), 'write only support in write mode'
        assert isinstance(data, bytes), f"write only support bytes data which type is {type(data)}"
        current_written_bytes = len(data)
        self._rollover_block_if_exceeds(current_written_bytes)
        token = Token(self._packaged_file_name, self._file_bytes, current_written_bytes)
        self._packaged_file_handle_dict[self._packaged_file_name].write(data)
        self._file_bytes += current_written_bytes
        self._index.insert(key, token)
        return token

    # TODO(wangjiawei43): merge list_prefix and list_all in the future
    @profiler
    def list_prefix(self, key: str) -> List[str]:
        '''
        find matched file path by prefix
        '''
        assert self._index is not None, 'list_prefix only support index mode'
        return self._index.query_by_prefix(key)

    def exist(self, key: str) -> bool:
        assert self._index is not None, 'exist only support index mode'
        if self._readonly_main_index is not None and self._readonly_main_index.query_one(
                key) is not None:
            return True
        return self._index.query_one(key) is not None

    @profiler
    def read(self, key: str = None, token: Token = None) -> Union[bytes, None]:
        '''
        retrun bytes from data pack
        '''
        index = None
        if key:
            if self._readonly_main_index is not None and self._readonly_main_index.query_one(
                    key) is not None:
                index = self._readonly_main_index.query_one(key)
            elif self._index is not None:
                index = self._index.query_one(key)
            else:
                logger.error('key read only support in index mode')
                return None
        elif token:
            index = token
        else:
            logger.error('key and token are all None')
            return None
        if index is None:
            logger.error(f'read failed, key is not exist, key: {key}')
            return None
        data = os.pread(self._packaged_file_handle_dict[index.uri].fileno(), index.size,
                        index.offset)
        return data

    @profiler
    def list_all(self) -> Dict[str, Token]:
        '''
        return all token in data pack
        '''
        assert self._index is not None, 'list_prefix only support index mode'
        return self._index.query_all()

    @staticmethod
    def experiemental_direct_read(data_pack_folder: str, token: Token) -> bytes:
        '''
        TODO(wangjiawei43): This function is deprecated and will be removed in future.
        direct read token file like normal file read.
        '''
        filepath = f"{data_pack_folder}/packaged_file/{token.uri}"
        file_handle = open(filepath, 'rb')
        data = os.pread(file_handle.fileno(), token.size, token.offset)
        file_handle.close()
        return data
    
    
    @staticmethod
    def merge_index(data_pack_folder) -> bool:
        '''
        merge index db file
        '''
        index_folder = f'{data_pack_folder}/index'
        index_files = glob(f'{index_folder}/index_*')
        merged_index_file_path = f"{index_folder}/index_{MAIN_PARTITION_ID}.db"

        target_file = f'{data_pack_folder}/index/index_{MAIN_PARTITION_ID}_copy.db'
        if os.path.exists(merged_index_file_path):
            shutil.copy2(merged_index_file_path, target_file)
        merged_index = SqliteIndex(target_file, 'rw', cache_size=BYTES_PER_GIGEBYTE,
                                   thread_safe_mode=False, enable_profiling=False)
        total_count = 0

        for index_file in tqdm(index_files):
            if index_file == merged_index_file_path:
                continue
            if not index_file.endswith('.db'):
                logger.error(f'invalid index file name: {index_file}')
                continue
            index = SqliteIndex(index_file, 'r', thread_safe_mode=False, enable_profiling=False)
            values = index.query_all()
            total_count += len(values)
            for value in values.items():
                merged_index.insert(value[0], value[1])
            merged_index.flush()
            os.remove(index_file)

        logger.info(f'total {total_count}')
        merged_index.close()
        shutil.move(target_file, merged_index_file_path)
        return True


@functools.lru_cache(maxsize=None)
def get_data_pack(data_pack_folder, mode='noindex'):
    data_pack = DataPack(data_pack_folder, mode=mode)
    return data_pack
