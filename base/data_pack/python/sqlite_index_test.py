# Copyright @2024 Sankuai Technology Inc. All rights reserved.
# Authors: <AUTHORS>

import os
import unittest

from concurrent.futures import ThreadPoolExecutor

from base.data_pack.python.base_index import Token
from base.data_pack.python.sqlite_index import SqliteIndex

TEST_DB_FILE_PATH = '/tmp/test_datapack/index.db'
CPP_DB_FILE_PATH = '/tmp/data_pack02/index/index.db'
THREAD_POOL_NUM = 10
TEST_MULTI_THREAD_JOB_SIZE = 100


class DataPackIndexTest(unittest.TestCase):

    def setUp(self):
        if os.path.exists(TEST_DB_FILE_PATH):
            os.remove(TEST_DB_FILE_PATH)
        os.makedirs(os.path.dirname(TEST_DB_FILE_PATH), exist_ok=True)
        self._index = SqliteIndex(TEST_DB_FILE_PATH, mode='rw', enable_profiling=True,
                                  thread_safe_mode=True)

    def test_all(self):
        self._index.insert('123', Token('0_0', 0, 3))
        self._index.insert('456', Token('1_0', 0, 3))
        result = self._index.query_one('123')
        self.assertEqual(result.uri, '0_0')
        self.assertEqual(result.offset, 0)
        self.assertEqual(result.size, 3)
        self._index.insert('123', Token('0_1', 0, 3))
        self._index.flush()
        result = self._index.query_one('123')
        self.assertEqual(result.uri, '0_1')
        result = self._index.query_one('789')
        self.assertIsNone(result)

        results = self._index.query_by_prefix('1')
        self.assertEqual(len(results), 1)
        
        results = self._index.query_all()
        self.assertEqual(len(results), 2)
        self._index.delete('123')
        results = self._index.query_all()
        self.assertEqual(len(results), 1)
        self._index.close()

    def test_multithread(self):
        thread_executor = ThreadPoolExecutor(max_workers=THREAD_POOL_NUM)

        def _thread_func(sqlite_index, idx):
            # insert idx
            sqlite_index.insert(idx, Token(idx, 0, 0))
            sqlite_index.flush()
            result = sqlite_index.query_one(idx)
            assert result.uri == idx, f"query_one failed: {result.uri} {idx}"

        for i in range(TEST_MULTI_THREAD_JOB_SIZE):
            thread_executor.submit(_thread_func, self._index, str(i))
        thread_executor.shutdown(wait=True)

        for idx in range(TEST_MULTI_THREAD_JOB_SIZE):
            idx = str(idx)
            result = self._index.query_one(idx)
            assert result.uri == idx, f"query_one failed: {result.uri} {idx}"


if __name__ == '__main__':
    unittest.main()
