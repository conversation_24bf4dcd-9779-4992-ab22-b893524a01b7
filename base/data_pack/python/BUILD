package(default_visibility=["//visibility:public"])

load("//build/bazel_rules:pkg.bzl", "pkg_tar")
load("@python3_deps//:requirements.bzl", "requirement")

py_library(
    name="base_index",
    srcs=["base_index.py"],
    deps=[],
)

py_library(
    name="utils",
    srcs=["utils.py"],
    deps=[
        "//common/python/utils:walle_logger",
    ]
)

py_library(
    name="sqlite_index",
    srcs=["sqlite_index.py"],
    deps=[
        ":base_index",
        ":utils",
        requirement("sqlalchemy"),
    ],
)

py_test(
    name="sqlite_index_test",
    srcs=["sqlite_index_test.py"],
    deps=[
        ":sqlite_index",
    ],
)

py_library(
    name="data_pack",
    srcs=["data_pack.py"],
    deps=[
        ":sqlite_index",
        ":utils",
    ],
)

py_test(
    name="data_pack_test",
    srcs=["data_pack_test.py"],
    deps=[
        ":data_pack",
        "//modules/planning/proto/common/math:py_oriented_point2d_proto",
    ],
)
