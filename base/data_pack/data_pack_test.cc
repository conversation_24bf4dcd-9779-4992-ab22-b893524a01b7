// Copyright @2024 Sankuai AI Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/data_pack/data_pack.h"

#include <memory>
#include <unordered_map>

#include "gtest/gtest.h"

namespace base {
namespace {
constexpr char kTestDir[] = "/tmp/data_pack";
constexpr char kTestPartitionId[] = "111";
constexpr int kTestSize = 128 * 1024 * 1024;
}  // namespace

class DataPackTest : public ::testing::Test {
 public:
  void SetUp() override {
    if (file_path::IsDirectory(kTestDir)) {
      file_path::RemovePath(kTestDir, true);
    }
    file_path::CreateDirectory(kTestDir, true);
    DataPack::Options options(kTestDir);
    data_pack_ = std::make_unique<DataPack>(options);
  }

 protected:
  std::unique_ptr<DataPack> data_pack_ = nullptr;
};

TEST_F(DataPackTest, BasicTest) {
  std::string test_string1, test_string2, test_string3;
  test_string1.resize(kTestSize);
  test_string2.resize(kTestSize);
  test_string3.resize(kTestSize);
  for (int i = 0; i < kTestSize; i++) {
    test_string1 += 'x';
    test_string2 += 'y';
    test_string3 += 'z';
  }
  data_pack_->Write("/tmp/0.bin", test_string1);
  data_pack_->Write("/tmp/1.bin", test_string2);
  data_pack_->Write("/tmp/2.bin", test_string3);
  EXPECT_TRUE(data_pack_->Exist("/tmp/0.bin"));
  data_pack_->Remove("/tmp/0.bin");
  EXPECT_FALSE(data_pack_->Exist("/tmp/0.bin"));
  std::string data2 = data_pack_->Read("/tmp/1.bin");
  EXPECT_EQ(data2, test_string2);

  std::string data3 = data_pack_->Read("/tmp/2.bin");
  EXPECT_EQ(data3, test_string3);

  std::unordered_map<std::string, DataPackToken> answers = data_pack_->ListPrefix("/tmp");
  EXPECT_EQ(answers.size(), 2);
  EXPECT_EQ(answers.count("/tmp/2.bin"), 1);
  std::unordered_map<std::string, DataPackToken> all_key_token_map = data_pack_->ListAll();
  EXPECT_EQ(all_key_token_map.count("/tmp/2.bin"), 1);
}

TEST_F(DataPackTest, ParitionTest) {
  std::string test_string1;
  for (int i = 0; i < kTestSize; i++) {
    test_string1 += 'x';
  }
  data_pack_->Write("/tmp/0.bin", test_string1);
  data_pack_->Close();

  DataPack::Options options(kTestDir);
  options.partition_id = kTestPartitionId;
  DataPack data_pack(options);

  data_pack.Write("/tmp/3.bin", test_string1);
  EXPECT_TRUE(data_pack.Exist("/tmp/3.bin"));
  std::string data3 = data_pack.Read("/tmp/3.bin");
  EXPECT_EQ(data3, test_string1);
  EXPECT_TRUE(data_pack.Exist("/tmp/0.bin"));
  std::string data0 = data_pack.Read("/tmp/0.bin");
  EXPECT_EQ(data0, test_string1);
}

}  // namespace base
