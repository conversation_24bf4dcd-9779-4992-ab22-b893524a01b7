// Copyright @2025 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/cuda/torch_inference_engine.h"

#include <utility>

#include "base/cuda/torchscript/torch_script_runtime_model.h"
#include "common/tools/performance/profiler_shared_resources.h"
#include "walle/common/tracing/tracing.h"

namespace base {

TorchInferenceEngine::TorchInferenceEngine(const walle::ModelConfig& model_config,
                                           cuda::DeviceContext* context)
    : device_context_(CHECK_NOTNULL(context)) {
  TRACE_EVENT(walle::kTraceCategoryInference, "TorchInferenceEngine::TorchInferenceEngine");
  config_manager_ = std::make_unique<walle::ModelConfigManager>(model_config);
  model_ = std::make_unique<base::TorchScriptRuntimeModel>(model_config, context);
  InitializeBuffers();
  recorder_ = std::make_unique<walle::TimeRecorder>();
}

void TorchInferenceEngine::InitializeBuffers() {
  bindings_.clear();
  bindings_.resize(model_->input_binding_infos().size() + model_->output_binding_infos().size());
  host_bindings_.clear();
  host_bindings_.resize(model_->input_binding_infos().size() +
                        model_->output_binding_infos().size());

  for (const auto& input_entry : model_->input_binding_infos()) {
    const std::string& binding_name = input_entry.first;
    const ModelRuntime::BindingInfo& binding_info = input_entry.second;
    input_device_buffers_.emplace(std::piecewise_construct,
                                  std::make_tuple(binding_name),
                                  std::make_tuple(binding_info.buffer_size));
    input_host_buffers_.emplace(std::piecewise_construct,
                                std::make_tuple(binding_name),
                                std::make_tuple(binding_info.buffer_size));
    input_device_buffers_.at(binding_name).Memset(0, device_context_);
    input_host_buffers_.at(binding_name).Memset(0);
    bindings_[binding_info.binding_index] = input_device_buffers_.at(binding_name).mutable_data();
    host_bindings_[binding_info.binding_index] =
        input_host_buffers_.at(binding_name).mutable_data();
  }
  for (const auto& output_entry : model_->output_binding_infos()) {
    const std::string& binding_name = output_entry.first;
    const ModelRuntime::BindingInfo& binding_info = output_entry.second;
    output_device_buffers_.emplace(std::piecewise_construct,
                                   std::make_tuple(binding_name),
                                   std::make_tuple(binding_info.buffer_size));
    output_device_buffers_.at(binding_name).Memset(0, device_context_);
    bindings_[binding_info.binding_index] = output_device_buffers_.at(binding_name).mutable_data();
  }
  if (config_manager_->output_names().empty() || config_manager_->input_names().empty()) {
    LOG(FATAL) << "Can not get input/output node name from model config file.";
  }
  for (const auto& output_name : config_manager_->output_names()) {
    CHECK(model_->output_binding_infos().count(output_name))
        << "Can not find " << output_name
        << " in binding info, please check output node in model config is updated";
    const ModelRuntime::BindingInfo& binding_info = model_->output_binding_infos().at(output_name);
    output_host_buffers_.emplace(std::piecewise_construct,
                                 std::make_tuple(output_name),
                                 std::make_tuple(binding_info.buffer_size));
    output_host_buffers_.at(output_name).Memset(0);
    host_bindings_[binding_info.binding_index] =
        output_host_buffers_.at(output_name).mutable_data();
  }
  model_->AssignBindings(bindings_);
}

void TorchInferenceEngine::RegisterBindingBuffer(const std::string& binding_name, void* data) {
  CHECK(cuda::GetInferMode() == cuda::InferMode::kNativeGPU)
      << "TorchInferenceEngine::RegisterBindingBuffer should only be used in kNativeGPU InferMode.";
  int binding_index = -1;
  const std::unordered_map<std::string, ModelRuntime::BindingInfo>& input_binding_infos =
      model_->input_binding_infos();
  const std::unordered_map<std::string, ModelRuntime::BindingInfo>& output_binding_infos =
      model_->output_binding_infos();
  if (input_binding_infos.count(binding_name)) {
    binding_index = input_binding_infos.at(binding_name).binding_index;
  } else if (output_binding_infos.count(binding_name)) {
    binding_index = output_binding_infos.at(binding_name).binding_index;
  } else {
    LOG(FATAL) << "Fail to find binding index with name " << binding_name;
  }
  CHECK_GE(binding_index, 0);
  bindings_[binding_index] = CHECK_NOTNULL(data);
  model_->AssignBindings(bindings_);
}

void TorchInferenceEngine::Run(int batch_size, bool sync) {
  TRACE_EVENT(walle::kTraceCategoryInference, "TorchInferenceEngine::Run");
  if (sync) {
    recorder_->Start();
  }
  model_->Enqueue(batch_size, false);
  if (sync) {
    device_context_->Sync();
    recorder_->End(model_->name());
  }
}

void TorchInferenceEngine::AsyncCopyOutputBuffersD2H(cuda::DeviceContext* context) {
  if (d2h_copy_output_binding_names_.empty()) {
    for (const auto& output_name : config_manager_->output_names()) {
      cuda::DeviceBuffer* output_buffer_ptr = base::FindOrDie(&output_device_buffers_, output_name);
      void* output_host_buffer_raw_ptr = GetMutableOutputHostBufferRawPtrOrDie(output_name);
      output_buffer_ptr->ToCpu(output_host_buffer_raw_ptr, output_buffer_ptr->size(), context);
    }
  } else {
    for (const auto& output_name : d2h_copy_output_binding_names_) {
      cuda::DeviceBuffer* output_buffer_ptr = base::FindOrDie(&output_device_buffers_, output_name);
      void* output_host_buffer_raw_ptr = GetMutableOutputHostBufferRawPtrOrDie(output_name);
      output_buffer_ptr->ToCpu(output_host_buffer_raw_ptr, output_buffer_ptr->size(), context);
    }
  }
}

}  // namespace base
