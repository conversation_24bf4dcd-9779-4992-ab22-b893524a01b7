// Copyright @2022 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "gtest/gtest.h"

#include <memory>

#include "base/file/file_util.h"

#include "base/cuda/mock_model_runtime.h"

namespace base {
namespace {

constexpr char kModelConfigFile[] =
    "modules/perception/model/model_configs/test_model_config.pb.txt";
}

TEST(MockModelRuntimeTest, BasicTest) {
  cuda::DeviceContext ctx;
  walle::ModelConfig model_config;
  CHECK(base::ReadTextProtoFile(kModelConfigFile, &model_config))
      << kModelConfigFile << " not found!";
  walle::ModelConfigManager config_manager(model_config);
  std::unique_ptr<base::MockModelRuntime> model =
      std::make_unique<base::MockModelRuntime>(config_manager, &ctx);
  EXPECT_TRUE(model->input_binding_infos().count("test_input_node"));
  EXPECT_EQ(model->input_binding_infos().at("test_input_node").buffer_size, 1638400);
  MockData mock_data;
  (*mock_data.mutable_output_binding_infos())["mock_test_output_node"] = {};
  EXPECT_FALSE(model->output_binding_infos().count("mock_test_output_node"));
  model->UpdateMockData(mock_data);
  EXPECT_TRUE(model->output_binding_infos().count("mock_test_output_node"));
}

}  // namespace base
