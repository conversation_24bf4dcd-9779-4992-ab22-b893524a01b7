// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <string>

#include "glog/logging.h"

#ifndef CUDA_CHECK
#define CUDA_CHECK(status) CHECK_EQ(cudaSuccess, status) << cudaGetErrorString(status) << "."
#endif

#define CUDA_KERNEL_LAUNCH_CHECK()                                           \
  do {                                                                       \
    const cudaError_t err = cudaGetLastError();                              \
    CHECK_EQ(err, cudaSuccess) << "CUDA Error: " << cudaGetErrorString(err); \
  } while (0)

#define REGESTER_AS_OP_PARAMS(OpParams) \
  static const std::string name() { return #OpParams; }

#ifndef NVML_CHECK
#define NVML_CHECK(result) CHECK_EQ(NVML_SUCCESS, result) << nvmlErrorString(result) << "."
#endif
