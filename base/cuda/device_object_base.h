// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>
//          <PERSON><PERSON> (<EMAIL>)

#pragma once

#include <memory>
#include <utility>

#include "base/cuda/device_buffer.h"

namespace cuda {

class DeviceObjectBase {
 public:
  DeviceObjectBase(void* dev_ptr, size_t size)
      : dev_ptr_(dev_ptr), dev_ptr_size_(size), field_offset_(0) {}
  DeviceObjectBase(const void* dev_ptr, size_t size)
      : const_dev_ptr_(dev_ptr), dev_ptr_size_(size), field_offset_(0) {}

  virtual ~DeviceObjectBase() = default;

  const void* data() const {
    if (IsConstObject()) {
      return const_dev_ptr_;
    } else {
      return dev_ptr_;
    }
  }

 protected:
  void CreateFieldCheck(size_t field_bytes_size) {
    field_offset_ += field_bytes_size;
    CHECK_LE(field_offset_, dev_ptr_size_);
  }
  bool IsConstObject() const { return const_dev_ptr_ != nullptr; }
  void* dev_ptr_ = nullptr;
  const void* const_dev_ptr_ = nullptr;
  size_t field_offset() const { return field_offset_; }

 private:
  size_t dev_ptr_size_ = 0;
  size_t field_offset_ = 0;
  DISALLOW_COPY_AND_ASSIGN(DeviceObjectBase);
};

#define DEVICE_OBJECT_FIELD(FieldType, field_name)                                               \
                                                                                                 \
 public:                                                                                         \
  const cuda::DeviceBufferView<FieldType>& field_name() const {                                  \
    if (IsConstObject()) {                                                                       \
      return CHECK_NOTNULL(field_name##_const_)->View();                                         \
    }                                                                                            \
    return *CHECK_NOTNULL(field_name##_).get();                                                  \
  }                                                                                              \
  cuda::DeviceBufferView<FieldType>* mutable_##field_name() {                                    \
    CHECK(!IsConstObject());                                                                     \
    return CHECK_NOTNULL(field_name##_).get();                                                   \
  }                                                                                              \
                                                                                                 \
 private:                                                                                        \
  void create_##field_name(size_t size) {                                                        \
    const size_t num_bytes = size * sizeof(FieldType);                                           \
    if (!IsConstObject()) {                                                                      \
      field_name##_ = std::make_unique<cuda::DeviceBufferView<FieldType>>(                       \
          reinterpret_cast<FieldType*>(static_cast<uint8_t*>(dev_ptr_) + field_offset()), size); \
    } else {                                                                                     \
      field_name##_const_ = std::make_unique<cuda::ConstDeviceBufferView<FieldType>>(            \
          static_cast<const uint8_t*>(const_dev_ptr_) + field_offset(), num_bytes);              \
    }                                                                                            \
    CreateFieldCheck(size * sizeof(FieldType));                                                  \
  }                                                                                              \
  std::unique_ptr<cuda::ConstDeviceBufferView<FieldType>> field_name##_const_ = nullptr;         \
  std::unique_ptr<cuda::DeviceBufferView<FieldType>> field_name##_ = nullptr

}  // namespace cuda
