// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <memory>
#include <string>
#include <unordered_map>
#include <unordered_set>
#include <vector>

#include "base/cuda/flags.h"
#include "base/cuda/host_buffer.h"
#include "base/cuda/mock_model_runtime.h"
#include "base/cuda/model_runtime.h"
#include "base/cuda/model_serving/model_serving_runtime.h"
#include "base/cuda/tensorrt/trt_onnx_parser.h"
#include "base/cuda/tensorrt/trt_runtime_model.h"
#include "base/cuda/triton/triton_runtime.h"
#include "common/execution_unit_framework/utils/time_recorder.h"
#include "common/model/model_config_manager.h"

namespace base {

class TrtInferenceEngine {
 public:
  TrtInferenceEngine(const walle::ModelConfig& model_config,
                     cuda::DeviceContext* context,
                     bool use_model_serving = false);
  virtual ~TrtInferenceEngine() = default;

  void UpdateDeviceContext(cuda::DeviceContext* context);
  void GenerateEngine();
  void UpdateMockData(const MockData& mock_data);

  void Run(int batch_size = 1, bool sync = false);

  void ConfigureOutputBufferNamesForAsyncCopy(const std::string& output_name);
  void AsyncCopyOutputBuffersD2H(cuda::DeviceContext* context);
  // device buffer api
  const cuda::DeviceBuffer& GetInputDeviceBufferOrDie(const std::string& input_buffer_name) const {
    return base::FindOrDie(input_device_buffers_, input_buffer_name);
  }
  cuda::DeviceBuffer* GetMutableInputDeviceBufferOrDie(const std::string& input_buffer_name) {
    return base::FindOrDie(&input_device_buffers_, input_buffer_name);
  }
  const cuda::DeviceBuffer& GetOutputDeviceBufferOrDie(const std::string& output_buffer_name) const {
    return base::FindOrDie(output_device_buffers_, output_buffer_name);
  }
  cuda::DeviceBuffer* GetMutableOutputDeviceBufferOrDie(const std::string& output_buffer_name) {
    return base::FindOrDie(&output_device_buffers_, output_buffer_name);
  }
  /*TODO yuanxu : remove old api*/
  const cuda::DeviceBuffer& GetInputBufferOrDie(const std::string& input_buffer_name) const {
    return GetInputDeviceBufferOrDie(input_buffer_name);
  }
  cuda::DeviceBuffer* GetMutableInputBufferOrDie(const std::string& input_buffer_name) {
    return GetMutableInputDeviceBufferOrDie(input_buffer_name);
  }
  const cuda::DeviceBuffer& GetOutputBufferOrDie(const std::string& output_buffer_name) const {
    return GetOutputDeviceBufferOrDie(output_buffer_name);
  }
  cuda::DeviceBuffer* GetMutableOutputBufferOrDie(const std::string& output_buffer_name) {
    return GetMutableOutputDeviceBufferOrDie(output_buffer_name);
  }

  // host buffer api
  const cuda::HostBuffer& GetInputHostBufferOrDie(const std::string& input_buffer_name) const {
    return base::FindOrDie(input_host_buffers_, input_buffer_name);
  }
  cuda::HostBuffer* GetMutableInputHostBufferOrDie(const std::string& input_buffer_name) {
    return base::FindOrDie(&input_host_buffers_, input_buffer_name);
  }
  const cuda::HostBuffer& GetOutputHostBufferOrDie(const std::string& output_buffer_name) const {
    return base::FindOrDie(output_host_buffers_, output_buffer_name);
  }
  cuda::HostBuffer* GetMutableOutputHostBufferOrDie(const std::string& output_buffer_name) {
    return base::FindOrDie(&output_host_buffers_, output_buffer_name);
  }

  // raw ptr api
  void* GetMutableInputDeviceBufferRawPtrOrDie(const std::string& input_buffer_name) {
    return base::FindOrDie(&input_device_buffers_, input_buffer_name)->mutable_data();
  }
  void* GetMutableInputHostBufferRawPtrOrDie(const std::string& input_buffer_name) {
    return base::FindOrDie(&input_host_buffers_, input_buffer_name)->mutable_data();
  }
  void* GetMutableOutputDeviceBufferRawPtrOrDie(const std::string& output_buffer_name) {
    return base::FindOrDie(&output_device_buffers_, output_buffer_name)->mutable_data();
  }
  void* GetMutableOutputHostBufferRawPtrOrDie(const std::string& output_buffer_name) {
    return base::FindOrDie(&output_host_buffers_, output_buffer_name)->mutable_data();
  }

  const std::unordered_map<std::string, TrtRuntimeModel::BindingInfo>& GetInputBindingInfo() const {
    return model_->input_binding_infos();
  }

  const std::unordered_map<std::string, TrtRuntimeModel::BindingInfo>& GetOutputBindingInfo()
      const {
    return model_->output_binding_infos();
  }

  const std::vector<std::string>& output_with_marked_node_names() const { return model_->outputs_name_list(); }

  const std::vector<std::string>& output_names() const { return CHECK_NOTNULL(config_manager_)->output_names(); }

  cuda::DeviceContext* GetEngineContext() { return CHECK_NOTNULL(device_context_); }

  const std::vector<std::string>& input_names() const {
    return CHECK_NOTNULL(config_manager_)->input_names();
  }

  const std::vector<std::vector<int>>& input_shapes() const {
    return CHECK_NOTNULL(config_manager_)->input_shapes();
  }

  int max_batch_size() const {
    return config_manager_ == nullptr ? model_->max_batch_size()
                                      : config_manager_->max_batch_size();
  }

  bool use_triton_runtime() const { return use_triton_runtime_; }

  void RegisterBindingBuffer(const std::string& binding_name,
                             void* data,
                             bool release_original_device_buffer = false);

  bool HasEnabledValidityChecker() { return validity_checker_ != nullptr; }

  // should only be called after `device_context_->Sync()`
  bool CheckTensorValidity();

 protected:
  ModelRuntime* GetMutableModelPtr() { return model_.get(); }

 private:
  class ValidityChecker {
   public:
    typedef std::unordered_map<std::string, const TrtRuntimeModel::BindingInfo*>
        BindingInfoMap;  // Not owned
    static bool DataTypeNeedCheck(nvinfer1::DataType data_type);

    ValidityChecker(const std::string& name,
                    const std::vector<void*>& bindings,
                    BindingInfoMap&& check_map,
                    cuda::DeviceContext* device_context);
    void AssertLastResult();
    void CheckAsync();
    bool GetResult();
    void UpdateDeviceContext(cuda::DeviceContext* context);

    const std::string name_;
    const BindingInfoMap check_map_;
    cuda::DeviceBuffer validity_device_buffer_;
    cuda::HostBuffer validity_host_buffer_;
    const std::vector<void*>& bindings_;         // Not owned
    cuda::DeviceContext* device_context_;        // Not owned.
    int first_failed_passed_frame_cnt_ = 0;
  };

  void InitializeBuffers();
  void ClearBuffers();

  bool use_cuda_graph_ = false;
  bool use_model_serving_ = false;
  bool use_triton_runtime_ = false;
  std::unique_ptr<ModelRuntime> model_ = nullptr;
  std::unique_ptr<walle::ModelConfigManager> config_manager_ = nullptr;
  cuda::DeviceContext* device_context_ = nullptr;  // Not owned.
  std::unordered_map<std::string, cuda::DeviceBuffer> input_device_buffers_;
  std::unordered_map<std::string, cuda::DeviceBuffer> output_device_buffers_;
  std::unordered_map<std::string, cuda::HostBuffer> input_host_buffers_;
  std::unordered_map<std::string, cuda::HostBuffer> output_host_buffers_;
  std::unique_ptr<walle::TimeRecorder> recorder_;
  std::unique_ptr<ValidityChecker> validity_checker_;
  std::vector<void*> bindings_;
  std::vector<void*> host_bindings_;
  std::unordered_set<std::string> d2h_copy_output_binding_names_;

  DISALLOW_COPY_AND_ASSIGN(TrtInferenceEngine);
};

using TrtInferenceEnginePtr = TrtInferenceEngine*;

}  // namespace base
