// Copyright @2022 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <memory>
#include <string>
#include <unordered_map>
#include <vector>

#include "base/common/field_macros.h"
#include "base/cuda/host_buffer.h"
#include "walle/engine/env/trt_inference_engine_resource_pool.h"

namespace base {

class BaseModelManager {
 public:
  BaseModelManager() = default;
  BaseModelManager(const std::string& engine_file,
                   const std::string& model_config_file,
                   cuda::DeviceContext* device_context,
                   walle::TrtInferenceEngineResourcePool* engine_resource_pool = nullptr);
  BaseModelManager(const std::string& engine_file,
                   const std::string& model_config_file,
                   cuda::DeviceContext* device_context,
                   bool process_input_bindings,
                   walle::TrtInferenceEngineResourcePool* engine_resource_pool = nullptr);
  virtual ~BaseModelManager() = default;

  void InitializeInferenceEngine(const std::string& engine_file);

  float* GetMutableBuffer(const std::string& buffer_name) {
    CHECK(buffers_.count(buffer_name));
    return reinterpret_cast<float*>(buffers_.at(buffer_name)->mutable_data());
  }

  float* GetMutableBufferInBatch(const std::string& buffer_name, int batch_idx) {
    CHECK_LT(batch_idx, max_batch_size());
    float* buffer_ptr = GetMutableBuffer(buffer_name);
    CHECK(value_num_per_batchs_.count(buffer_name));
    return buffer_ptr + value_num_per_batchs_.at(buffer_name) * batch_idx;
  }

  const float* GetBuffer(const std::string& buffer_name) const {
    CHECK(buffers_.count(buffer_name));
    return reinterpret_cast<const float*>(buffers_.at(buffer_name)->data());
  }

  const int GetInputBufferDim(const std::string& buffer_name, int dim_idx) const {
    const base::TrtRuntimeModel::BindingInfo& target_obstacle_binding_info =
        base::FindOrDie(mutable_model_engine_->GetInputBindingInfo(), buffer_name);
    return target_obstacle_binding_info.dims.d[dim_idx];
  }

  bool HasBuffer(const std::string& buffer_name) const {
    return buffers_.count(buffer_name) != 0;
  }

  const float* GetBufferInBatch(const std::string& buffer_name, int batch_idx) const {
    const float* buffer_ptr = GetBuffer(buffer_name);
    CHECK(value_num_per_batchs_.count(buffer_name));
    return buffer_ptr + value_num_per_batchs_.at(buffer_name) * batch_idx;
  }

  const std::unordered_map<std::string, int>& value_num_per_batchs() const {
    return value_num_per_batchs_;
  }

  int max_batch_size() const { return CHECK_NOTNULL(mutable_model_engine_)->max_batch_size(); }

  void Run(int batch_size);

  void Sync(int batch_size);

  void ClearBuffer(float default_value = 0.0f, bool only_input_buffer = true);

  void ClearSingleBuffer(const std::string& buffer_name, float default_value = 0.0f);

 protected:
  std::unique_ptr<base::TrtInferenceEngine> model_engine_;
  base::TrtInferenceEngine* mutable_model_engine_;
  cuda::DeviceContext* device_context_ = nullptr;  // Not owned
  std::unordered_map<std::string, cuda::HostBuffer*> buffers_;
  std::unordered_map<std::string, int> value_num_per_batchs_;

 private:
  void ProcessBindingInfos(
      const std::vector<std::string>& node_names,
      const std::unordered_map<std::string, base::TrtRuntimeModel::BindingInfo>& binding_infos);

  DISALLOW_COPY_AND_ASSIGN(BaseModelManager);
};

}  // namespace base
