// Copyright @2022 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/cuda/base_model_manager.h"

namespace base {
BaseModelManager::BaseModelManager(const std::string& engine_file,
                                   const std::string& model_config_file,
                                   cuda::DeviceContext* device_context,
                                   walle::TrtInferenceEngineResourcePool* engine_resource_pool) {
  // TODO(liucheng28): remove engine_file.
  walle::ModelConfig model_config;
  CHECK(base::ReadTextProtoFile(model_config_file, &model_config))
      << model_config_file << " not found!";
  device_context_ = device_context;
  if (engine_resource_pool == nullptr) {
    model_engine_ = std::make_unique<base::TrtInferenceEngine>(model_config, device_context_);
    mutable_model_engine_ = model_engine_.get();
  } else {
    mutable_model_engine_ =
        engine_resource_pool->GetOrCreateTrtInferenceEngine(model_config, device_context_);
  }
  ProcessBindingInfos(mutable_model_engine_->input_names(),
                      mutable_model_engine_->GetInputBindingInfo());
  ProcessBindingInfos(mutable_model_engine_->output_names(),
                      mutable_model_engine_->GetOutputBindingInfo());
}

BaseModelManager::BaseModelManager(const std::string& engine_file,
                                   const std::string& model_config_file,
                                   cuda::DeviceContext* device_context,
                                   bool process_input_bindings,
                                   walle::TrtInferenceEngineResourcePool* engine_resource_pool) {
  walle::ModelConfig model_config;
  CHECK(base::ReadTextProtoFile(model_config_file, &model_config))
      << model_config_file << " not found!";
  device_context_ = device_context;
  if (engine_resource_pool == nullptr) {
    model_engine_ = std::make_unique<base::TrtInferenceEngine>(model_config, device_context_);
    mutable_model_engine_ = model_engine_.get();
  } else {
    mutable_model_engine_ =
        engine_resource_pool->GetOrCreateTrtInferenceEngine(model_config, device_context_);
  }
  if (process_input_bindings) {
    ProcessBindingInfos(mutable_model_engine_->input_names(),
                        mutable_model_engine_->GetInputBindingInfo());
  }
  ProcessBindingInfos(mutable_model_engine_->output_names(),
                      mutable_model_engine_->GetOutputBindingInfo());
}

void BaseModelManager::ProcessBindingInfos(
    const std::vector<std::string>& node_names,
    const std::unordered_map<std::string, base::TrtRuntimeModel::BindingInfo>& binding_infos) {
  const int max_batch_size = mutable_model_engine_->max_batch_size();
  CHECK_GT(max_batch_size, 0);
  for (const auto& buffer_name : node_names) {
    if (mutable_model_engine_->GetInputBindingInfo().count(buffer_name)) {
      buffers_[buffer_name] = mutable_model_engine_->GetMutableInputHostBufferOrDie(buffer_name);
    } else if (mutable_model_engine_->GetOutputBindingInfo().count(buffer_name)) {
      buffers_[buffer_name] = mutable_model_engine_->GetMutableOutputHostBufferOrDie(buffer_name);
    } else {
      LOG(FATAL) << "Cannot find " << buffer_name << " in input or output binding info.";
    }
    const base::TrtRuntimeModel::BindingInfo& binding_info = binding_infos.at(buffer_name);
    // Current we all use float to save data, this is not necessary correct.
    const int total_value_num = binding_info.buffer_size / sizeof(float);
    const int value_num_per_batch = total_value_num / max_batch_size;
    value_num_per_batchs_[buffer_name] = value_num_per_batch;
  }
}

void BaseModelManager::Run(int batch_size) {
  if (!batch_size) {
    return;
  }
  for (const auto& entry : mutable_model_engine_->GetInputBindingInfo()) {
    const std::string& input_name = entry.first;
    const int value_num = base::FindOrDie(value_num_per_batchs_, input_name);
    cuda::DeviceBuffer* device_buffer =
        mutable_model_engine_->GetMutableInputDeviceBufferOrDie(input_name);
    device_buffer->FromCpu(
        buffers_.at(input_name)->data(), value_num * batch_size * sizeof(float), device_context_);
  }
  mutable_model_engine_->Run(batch_size);
}

void BaseModelManager::Sync(int batch_size) {
  if (!batch_size) {
    return;
  }
  for (const auto& output_name : mutable_model_engine_->output_names()) {
    const int value_num = base::FindOrDie(value_num_per_batchs_, output_name);
    const cuda::DeviceBuffer& device_buffer =
        mutable_model_engine_->GetOutputDeviceBufferOrDie(output_name);
    device_buffer.ToCpu(buffers_.at(output_name)->mutable_data(),
                        value_num * batch_size * sizeof(float),
                        device_context_);
  }
  device_context_->Sync();
}

void BaseModelManager::ClearBuffer(float default_value, bool only_input_buffer) {
  const auto& input_info = mutable_model_engine_->GetInputBindingInfo();
  for (auto& entry : buffers_) {
    const std::string& buffer_name = entry.first;
    if (only_input_buffer && input_info.find(buffer_name) == input_info.end()) {
      continue;
    }
    ClearSingleBuffer(buffer_name, default_value);
  }
}

void BaseModelManager::ClearSingleBuffer(const std::string& buffer_name, float default_value) {
  const int total_value_num =
      value_num_per_batchs_.at(buffer_name) * mutable_model_engine_->max_batch_size();
  auto& buffer = buffers_.at(buffer_name);
  CHECK_EQ(buffer->size(), total_value_num * sizeof(float));
  float* host_buffer = reinterpret_cast<float*>(buffer->mutable_data());
  std::fill(host_buffer, host_buffer + total_value_num, default_value);
}

}  // namespace base
