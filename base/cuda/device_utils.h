// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <cuda_runtime.h>
#include <cstring>
#include <memory>
#include <utility>
#include <vector>

#include "glog/logging.h"

#include "base/common/macros.h"
#include "base/cuda/flags.h"
#include "base/cuda/macros.h"

namespace cuda {

enum class InferMode {
  kNativeGPU = 0,
  kLocalModelServing = 1,
  kRemoteModelServing = 2,
  kUnknown = 3
};

InferMode GetInferMode(bool use_triton_runtime = false);

class DeviceStream {
 public:
  DeviceStream() {
    if (GetInferMode() != InferMode::kRemoteModelServing) {
      CUDA_CHECK(cudaStreamCreateWithFlags(&stream_, cudaStreamNonBlocking));
    } else {
      is_own_cuda_stream_ = false;
    }
  }
  explicit DeviceStream(int priority) {
    if (GetInferMode() != InferMode::kRemoteModelServing) {
      CHECK_GE(priority, kHighestPriorityNum);
      CHECK_LE(priority, kLowestPriorityNum);
      CUDA_CHECK(cudaStreamCreateWithPriority(&stream_, cudaStreamNonBlocking, priority));
    } else {
      is_own_cuda_stream_ = false;
    }
  }
  explicit DeviceStream(cudaStream_t stream) : stream_(stream) { is_own_cuda_stream_ = false; }

  virtual ~DeviceStream() {
    if (is_own_cuda_stream_) {
      CUDA_CHECK(cudaStreamDestroy(stream_));
    }
  }

  cudaStream_t stream() const { return stream_; }

  void Sync() {
    if (stream_) CUDA_CHECK(cudaStreamSynchronize(stream_));
  }

  static constexpr int kHighestPriorityNum = -5;
  static constexpr int kLowestPriorityNum = 0;

 private:
  bool is_own_cuda_stream_ = true;
  cudaStream_t stream_ = 0;
};

class DeviceContext {
 public:
  static constexpr DeviceContext* kDefault = nullptr;

  explicit DeviceContext(int gpu_id = 0, bool use_triton_runtime = false)
      : gpu_id_(gpu_id), use_triton_runtime_(use_triton_runtime), stream_() {
    if (GetInferMode(use_triton_runtime) != InferMode::kRemoteModelServing)
      cudaSetDevice(gpu_id);
  }
  explicit DeviceContext(int gpu_id, int priority, bool use_triton_runtime = false)
      : gpu_id_(gpu_id), use_triton_runtime_(use_triton_runtime), stream_(priority) {
    if (GetInferMode(use_triton_runtime) != InferMode::kRemoteModelServing)
      cudaSetDevice(gpu_id);
  }
  // Please pay attention, if upstream's stream has been deconstructed, operations
  // relevanted to this context may fail.
  DeviceContext(int gpu_id, cudaStream_t stream, bool use_triton_runtime = false)
      : gpu_id_(gpu_id), use_triton_runtime_(use_triton_runtime), stream_(stream) {
    if (GetInferMode(use_triton_runtime) != InferMode::kRemoteModelServing)
      cudaSetDevice(gpu_id);
  }

  virtual ~DeviceContext() = default;

  void Sync() {
    if (use_triton_runtime_) return;
    stream_.Sync();
  }

  int gpu_id() const { return gpu_id_; }
  bool use_triton_runtime() { return use_triton_runtime_; }
  bool reuse_device_memory() { return reuse_device_memory_; }
  void set_reuse_device_memory(bool reuse) { reuse_device_memory_ = reuse; }
  cudaStream_t stream() const {
    if (use_triton_runtime_) return 0;
    return stream_.stream();
  }

 private:
  int gpu_id_ = 0;
  bool use_triton_runtime_ = false;
  bool reuse_device_memory_ = false;
  DeviceStream stream_;

  DISALLOW_COPY_AND_ASSIGN(DeviceContext);
};

// DeviceTimer of a given DeviceContext
class DeviceTimer {
 public:
  explicit DeviceTimer(const DeviceContext& ctx) : stream_(ctx.stream()) {
    CUDA_CHECK(cudaEventCreate(&start_));
    CUDA_CHECK(cudaEventCreate(&stop_));
  }
  virtual ~DeviceTimer() {
    CUDA_CHECK(cudaEventDestroy(start_));
    CUDA_CHECK(cudaEventDestroy(stop_));
  }

  void Start() { CUDA_CHECK(cudaEventRecord(start_, stream_)); }

  void Stop() { CUDA_CHECK(cudaEventRecord(stop_, stream_)); }

  float ms() {
    CUDA_CHECK(cudaEventSynchronize(stop_));
    CUDA_CHECK(cudaEventElapsedTime(&ms_, start_, stop_));
    return ms_;
  }

 private:
  cudaEvent_t start_, stop_;
  cudaStream_t stream_;
  float ms_ = 0.0f;

  DISALLOW_COPY_AND_ASSIGN(DeviceTimer);
};

// ceil(x / y)
inline int DivUp(int x, int y) {
  CHECK_NE(y, 0);
  return (x + y - 1) / y;
}

inline void DeviceToHost(const void* device_ptr,
                         void* host_ptr,
                         size_t num_bytes,
                         DeviceContext* ctx) {
  CHECK(host_ptr != nullptr);
  CHECK(device_ptr != nullptr);
  if (GetInferMode() != InferMode::kRemoteModelServing) {
    if (ctx) {
      CUDA_CHECK(
          cudaMemcpyAsync(host_ptr, device_ptr, num_bytes, cudaMemcpyDeviceToHost, ctx->stream()));
    } else {
      CUDA_CHECK(cudaMemcpy(host_ptr, device_ptr, num_bytes, cudaMemcpyDeviceToHost));
    }
  } else {
    std::memcpy(host_ptr, device_ptr, num_bytes);
  }
}

inline void HostToDevice(const void* host_ptr,
                         void* device_ptr,
                         size_t num_bytes,
                         DeviceContext* ctx) {
  CHECK(host_ptr != nullptr);
  CHECK(device_ptr != nullptr);
  if (GetInferMode() != InferMode::kRemoteModelServing) {
    if (ctx) {
      CUDA_CHECK(
          cudaMemcpyAsync(device_ptr, host_ptr, num_bytes, cudaMemcpyHostToDevice, ctx->stream()));
    } else {
      CUDA_CHECK(cudaMemcpy(device_ptr, host_ptr, num_bytes, cudaMemcpyHostToDevice));
    }
  } else {
    std::memcpy(device_ptr, host_ptr, num_bytes);
  }
}

inline void DeviceToDevice(const void* dev_ptr1,
                           void* dev_ptr2,
                           size_t num_bytes,
                           DeviceContext* ctx) {
  CHECK(dev_ptr1 != nullptr);
  CHECK(dev_ptr2 != nullptr);
  if (GetInferMode() != InferMode::kRemoteModelServing) {
    if (ctx) {
      CUDA_CHECK(
          cudaMemcpyAsync(dev_ptr2, dev_ptr1, num_bytes, cudaMemcpyDeviceToDevice, ctx->stream()));
    } else {
      CUDA_CHECK(cudaMemcpy(dev_ptr2, dev_ptr1, num_bytes, cudaMemcpyDeviceToDevice));
    }
  } else {
    std::memcpy(dev_ptr2, dev_ptr1, num_bytes);
  }
}

inline void Fill(void* dev_ptr, uint8_t value, size_t num_bytes, DeviceContext* ctx) {
  CHECK(dev_ptr != nullptr);
  if (GetInferMode() != InferMode::kRemoteModelServing) {
    if (ctx) {
      CUDA_CHECK(cudaMemsetAsync(dev_ptr, value, num_bytes, ctx->stream()));
    } else {
      CUDA_CHECK(cudaMemset(dev_ptr, value, num_bytes));
    }
  } else {
    std::memset(dev_ptr, value, num_bytes);
  }
}

inline bool IsCpuMode(const void* ptr) {
  cudaPointerAttributes attributes;
  cudaError_t err = cudaPointerGetAttributes(&attributes, ptr);
  if (err == cudaSuccess &&
      (attributes.type == cudaMemoryTypeDevice || attributes.type == cudaMemoryTypeManaged)) {
    return false;
  } else {
    return true;
  }
}

}  // namespace cuda
