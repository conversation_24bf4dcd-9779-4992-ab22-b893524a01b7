load("//build/bazel_rules:walle_platform_library.bzl", "walle_platform_select")

package(default_visibility = ["//visibility:public"])

exports_files(["cu_pre_include.h"])

cc_library(
    name = "macros",
    hdrs = ["macros.h"],
    deps = ["@glog"],
)

cc_library(
    name = "device_utils",
    srcs = ["device_utils.cc"],
    hdrs = ["device_utils.h"],
    deps = [
        ":flags",
        ":macros",
        "//base/common:macros",
        "@cuda",
        "@glog",
    ],
)

cc_test(
    name = "device_utils_test",
    srcs = ["device_utils_test.cc"],
    tags = [
        "ci",
        "ci_gpu",
    ],
    deps = [
        ":device_buffer",
        ":device_utils",
        ":host_buffer",
        "//base/testing:test_main",
        "//common/execution_unit_framework/utils:time_recorder",
    ],
)

cc_library(
    name = "device_buffer",
    hdrs = ["device_buffer.h"],
    # TODO(wanglong): enable device_buffer.cc after all cuda_library removed dependencies of device_buffer.
    # srcs = ["device_buffer.cc"],
    deps = [
        ":device_utils",
        "//base/common:macros",
        "@cuda",
    ],
)

cc_test(
    name = "device_buffer_test",
    srcs = ["device_buffer_test.cc"],
    tags = [
        "ci",
        "ci_gpu",
    ],
    deps = [
        ":device_buffer",
        "//base/testing:test_main",
    ],
)

cc_library(
    name = "host_buffer",
    hdrs = ["host_buffer.h"],
    deps = [
        ":device_utils",
        ":macros",
        "//base/common:macros",
        "//base/container:span",
        "@cuda",
        "@glog",
    ],
)

cc_test(
    name = "host_buffer_test",
    srcs = ["host_buffer_test.cc"],
    tags = [
        "ci",
        "ci_gpu",
    ],
    deps = [
        ":device_buffer",
        ":host_buffer",
        "//base/testing:test_main",
    ],
)

cc_library(
    name = "mirror_buffer",
    hdrs = ["mirror_buffer.h"],
    deps = [
        ":device_buffer",
        ":device_utils",
        ":host_buffer",
        "//base/common:macros",
        "@cuda",
    ],
)

cc_test(
    name = "mirror_buffer_test",
    srcs = ["mirror_buffer_test.cc"],
    tags = [
        "ci",
        "ci_gpu",
    ],
    deps = [
        ":device_buffer",
        ":host_buffer",
        ":mirror_buffer",
        "//base/testing:test_main",
    ],
)

cc_library(
    name = "cuda_channel_manager",
    srcs = ["cuda_channel_manager.cc"],
    hdrs = ["cuda_channel_manager.h"],
    deps = [
        ":device_buffer",
        "//base/container:utils",
    ],
)

cc_test(
    name = "cuda_channel_manager_test",
    srcs = ["cuda_channel_manager_test.cc"],
    tags = [
        "ci",
        "ci_gpu",
    ],
    deps = [
        ":cuda_channel_manager",
        "//base/testing:test_main",
    ],
)

cc_library(
    name = "tensor",
    hdrs = ["tensor.h"],
    deps = [
        ":device_buffer",
        ":device_utils",
        "//base/common:macros",
        "@cuda",
        "@glog",
    ],
)

cc_test(
    name = "tensor_test",
    srcs = ["tensor_test.cc"],
    tags = [
        "ci",
        "ci_gpu",
    ],
    deps = [
        ":tensor",
        "//base/testing:test_main",
    ],
)

cc_library(
    name = "device_object_base",
    hdrs = ["device_object_base.h"],
    deps = [
        ":device_buffer",
    ],
)

cc_test(
    name = "device_object_base_test",
    srcs = ["device_object_base_test.cc"],
    deps = [
        ":device_object_base",
        "//base/testing:test_main",
    ],
)

cc_library(
    name = "flags",
    srcs = ["flags.cc"],
    hdrs = ["flags.h"],
    deps = [
        "@gflags",
    ],
)

cc_library(
    name = "model_runtime",
    srcs = ["model_runtime.cc"],
    hdrs = ["model_runtime.h"],
    deps = [
        "//base/container:utils",
        "//base/cuda:device_buffer",
        "//base/file:file_path_util",
        "//base/file:file_util",
        "//base/strings:format",
        "@tensorrt",
    ],
)

cc_library(
    name = "mock_model_runtime",
    srcs = ["mock_model_runtime.cc"],
    hdrs = ["mock_model_runtime.h"],
    deps = [
        ":model_runtime",
        "//base/common:field_macros",
        "//base/cuda/tensorrt:trt_utils",
        "//common/model:model_config_manager",
        "//walle/common/proto/model:cc_model_config_proto",
    ],
)

cc_test(
    name = "mock_model_runtime_test",
    srcs = ["mock_model_runtime_test.cc"],
    data = [
        "//common/test:resources",
        "//modules/perception/model/model_configs",
    ],
    tags = [
        "ci",
        "ci_gpu",
        "no_asan",
    ],
    deps = [
        ":mock_model_runtime",
        "//base/testing:test_main",
    ],
)

cc_library(
    name = "trt_inference_engine",
    srcs = ["trt_inference_engine.cc"],
    hdrs = ["trt_inference_engine.h"],
    implementation_deps = [
        "//common/tools/performance:profiler_shared_resources",
    ],
    deps = [
        ":flags",
        ":mock_model_runtime",
        ":model_runtime",
        "//base/cuda:mirror_buffer",
        "//base/cuda/model_serving:model_serving_runtime",
        "//base/cuda/operators:tensor_validity_cuda",
        "//base/cuda/tensorrt:trt_onnx_parser",
        "//base/cuda/tensorrt:trt_runtime_model",
        "//base/cuda/triton:triton_runtime",
        "//common/execution_unit_framework/utils:time_recorder",
        "//common/model:model_config_manager",
        "//walle/common/tracing",
    ],
)

cc_test(
    name = "trt_inference_engine_build_and_run_test",
    srcs = ["trt_inference_engine_build_and_run_test.cc"],
    data = [
        "//common/test:resources",
    ],
    tags = [
        "ci",
        "ci_gpu",
        "no_asan",
    ],
    deps = [
        ":trt_inference_engine",
        "//base/cuda/proto:cc_calib_param_proto",
        "//base/cuda/tensorrt:trt_onnx_parser",
        "//base/testing:scoped_temp_dir",
        "//base/testing:test_main",
    ],
)

cc_library(
    name = "torch_inference_engine",
    srcs = walle_platform_select(
        "x86_64_with_cuda",
        ["torch_inference_engine.cc"],
        [],
    ),
    hdrs = ["torch_inference_engine.h"],
    implementation_deps = [
        "//common/tools/performance:profiler_shared_resources",
    ] + walle_platform_select(
        "x86_64_with_cuda",
        ["//base/cuda/torchscript:torch_script_runtime_model"],
        [],
    ),
    deps = [
        ":flags",
        ":model_runtime",
        "//base/cuda:mirror_buffer",
        "//common/execution_unit_framework/utils:time_recorder",
        "//common/model:model_config_manager",
        "//walle/common/tracing",
    ],
)

cc_library(
    name = "base_model_manager",
    srcs = ["base_model_manager.cc"],
    hdrs = ["base_model_manager.h"],
    deps = [
        ":host_buffer",
        "//base/common:field_macros",
        "//walle/engine/env:trt_inference_engine_resource_pool",
    ],
)

cc_test(
    name = "base_model_manager_test",
    srcs = ["base_model_manager_test.cc"],
    data = [
        "//modules/prediction/model/mock_model_configs",
    ],
    tags = [
        "ci",
        "ci_gpu",
        "no_asan",
    ],
    deps = [
        ":base_model_manager",
        "//base/cuda/tensorrt:trt_onnx_parser",
        "//base/testing:test_main",
    ],
)

cc_library(
    name = "device_memory_recorder",
    srcs = ["device_memory_recorder.cc"],
    hdrs = ["device_memory_recorder.h"],
    deps = [
        ":macros",
        "//base/common:macros",
        "//base/file:file_path_util",
        "//base/file:file_util",
        "//base/strings:format",
        "@com_google_absl//absl/time",
        "@gtest",
    ] + walle_platform_select(
        "x86_64_with_cuda",
        [
            ":device_buffer",
            "@cuda//:nvml",
        ],
        [],
    ),
)

cc_test(
    name = "device_memory_recorder_test",
    timeout = "short",
    srcs = ["device_memory_recorder_test.cc"],
    tags = [],
    deps = [
        ":device_memory_recorder",
        "//base/testing:test_main",
    ] + walle_platform_select(
        "x86_64_with_cuda",
        [":device_buffer"],
        [],
    ),
)

cc_library(
    name = "device_context_manager",
    srcs = [
        "device_context_manager.cc",
    ],
    hdrs = [
        "device_context_manager.h",
    ],
    deps = [
        ":device_utils",
        "//base/container:utils",
        "//base/synchronization:mutex",
        "//common/execution_unit_framework/common:eu_schedule_type",
    ],
)

cc_test(
    name = "device_context_manager_test",
    srcs = ["device_context_manager_test.cc"],
    tags = [
        "ci",
        "ci_gpu",
    ],
    deps = [
        ":device_buffer",
        ":device_context_manager",
        "//base/testing:test_main",
    ],
)

cc_library(
    name = "vlm_inference_engine",
    srcs = ["vlm_inference_engine.cc"],
    hdrs = ["vlm_inference_engine.h"],
    implementation_deps = [
        "//common/tools/performance:profiler_shared_resources",
    ],
    deps = [
        ":trt_inference_engine",
        "//base/container:utils",
    ],
)
