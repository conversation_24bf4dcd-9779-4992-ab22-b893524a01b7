// Copyright @2023 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <cuda_runtime.h>
#include <memory>
#include <utility>

#include "base/common/macros.h"
#include "base/cuda/device_buffer.h"
#include "base/cuda/device_utils.h"
#include "base/cuda/host_buffer.h"

namespace cuda {

class DiscreteBuffer {
 public:
  explicit DiscreteBuffer(size_t size) : size_(size) {
    host_data_ = std::make_unique<cuda::HostBuffer>(size);
    device_data_ = std::make_unique<cuda::DeviceBuffer>(size);
  }

  virtual ~DiscreteBuffer() = default;

  void Memset(int value, DeviceContext* ctx) {
    host_data_->Memset(value);
    device_data_->Memset(value, ctx);
  }

  size_t size() const { return size_; }
  void* mutable_host_data() const { return host_data_->mutable_data(); }
  void* mutable_device_data() const { return device_data_->mutable_data(); }
  const void* host_data() const { return host_data_->data(); }
  const void* device_data() const { return device_data_->data(); }
  cuda::HostBuffer* GetHostBuffer() const { return host_data_.get(); }
  cuda::DeviceBuffer* GetDeviceBuffer() const { return device_data_.get(); }
  void HostToDevice(DeviceContext* ctx) const {
    device_data_->FromCpu(host_data_->data(), size_, ctx);
  }
  void DeviceToHost(DeviceContext* ctx) const {
    device_data_->ToCpu(host_data_->mutable_data(), size_, ctx);
  }

 private:
  size_t size_ = 0;
  std::unique_ptr<cuda::HostBuffer> host_data_;
  std::unique_ptr<cuda::DeviceBuffer> device_data_;

  DISALLOW_COPY_AND_ASSIGN(DiscreteBuffer);
};

class UnifiedBuffer {
 public:
  explicit UnifiedBuffer(size_t size) : size_(size) {
    host_data_ = std::make_unique<cuda::HostBuffer>(size);
    CUDA_CHECK(cudaHostGetDevicePointer(&device_ptr_, host_data_->mutable_data(), 0));
    device_data_ = std::make_unique<cuda::DeviceBuffer>(device_ptr_, size);
  }

  virtual ~UnifiedBuffer() = default;

  void Memset(int value, DeviceContext* /* ctx */) { host_data_->Memset(value); }

  size_t size() const { return size_; }
  void* mutable_host_data() const { return host_data_->mutable_data(); }
  void* mutable_device_data() const { return device_ptr_; }
  const void* host_data() const { return host_data_->data(); }
  const void* device_data() const { return device_ptr_; }
  cuda::HostBuffer* GetHostBuffer() const { return host_data_.get(); }
  cuda::DeviceBuffer* GetDeviceBuffer() const { return device_data_.get(); }
  void HostToDevice(DeviceContext* /* ctx */) const {
    // Unified memory, Zero Copy
    return;
  }
  void DeviceToHost(DeviceContext* /* ctx */) const {
    // Unified memory, Zero Copy
    return;
  }

 private:
  size_t size_ = 0;
  void* device_ptr_ = nullptr;
  std::unique_ptr<cuda::HostBuffer> host_data_;
  std::unique_ptr<cuda::DeviceBuffer> device_data_;
  DISALLOW_COPY_AND_ASSIGN(UnifiedBuffer);
};

#ifdef __x86_64__
  using MirrorBuffer = DiscreteBuffer;
#else
  using MirrorBuffer = UnifiedBuffer;
#endif

}  // namespace cuda
