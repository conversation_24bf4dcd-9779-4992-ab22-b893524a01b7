// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

// Allocate page-locked memory on host by CUDA API.
// API details refer to: https://docs.nvidia.com/cuda/cuda-runtime-api/group__CUDART__MEMORY.html

#pragma once

#include <cuda_runtime.h>
#include <memory>
#include <utility>

#include "glog/logging.h"

#include "base/common/macros.h"
#include "base/container/span.h"
#include "base/cuda/device_utils.h"
#include "base/cuda/macros.h"

namespace cuda {

class HostBuffer {
 public:
  explicit HostBuffer(size_t size, unsigned int flag = cudaHostAllocDefault) : size_(size) {
    if (GetInferMode() != InferMode::kRemoteModelServing) {
      CUDA_CHECK(cudaHostAlloc(&data_, size_, flag));
    } else {
      data_ = malloc(size_);
    }
    own_memory_ = true;
  }

  HostBuffer(void* host_ptr, size_t size) : data_(CHECK_NOTNULL(host_ptr)), size_(size) {
    own_memory_ = false;
  }

  HostBuffer(HostBuffer&& other) {
    std::swap(data_, other.data_);
    std::swap(size_, other.size_);
    std::swap(own_memory_, other.own_memory_);
  }

  virtual ~HostBuffer() { this->Destroy(); }
  bool own_memory() const { return own_memory_; }

  template <typename T>
  base::Span<T> Span() {
    CHECK_EQ(size_ % sizeof(T), 0);
    T* head_ptr = reinterpret_cast<T*>(data_);
    return base::MakeSpan(head_ptr, size_ / sizeof(T));
  }

  template <typename T>
  base::ConstSpan<T> ConstSpan() const {
    CHECK_EQ(size_ % sizeof(T), 0);
    const T* head_ptr = reinterpret_cast<const T*>(data_);
    return base::MakeConstSpan(head_ptr, size_ / sizeof(T));
  }

  template <typename T>
  base::Span<T> Span(int start_index, int num_elements) {
    CHECK_EQ(size_ % sizeof(T), 0);
    CHECK_LE((start_index + num_elements) * sizeof(T), size_);
    T* head_ptr = reinterpret_cast<T*>(data_);
    return base::MakeSpan(head_ptr + start_index, num_elements);
  }

  template <typename T>
  base::ConstSpan<T> ConstSpan(int start_index, int num_elements) const {
    CHECK_EQ(size_ % sizeof(T), 0);
    CHECK_LE((start_index + num_elements) * sizeof(T), size_);
    const T* head_ptr = reinterpret_cast<const T*>(data_);
    return base::MakeConstSpan(head_ptr + start_index, num_elements);
  }

  void Memset(int value) { CHECK(memset(data_, value, size_)); }

  size_t size() const { return size_; }
  const void* data() const { return data_; }
  void* mutable_data() { return data_; }

 private:
  void Destroy() {
    if (own_memory_) {
      if (GetInferMode() != InferMode::kRemoteModelServing) {
        CUDA_CHECK(cudaFreeHost(data_));
      } else {
        free(data_);
        data_ = nullptr;
      }
    }
  }

  void* data_ = nullptr;
  size_t size_ = 0;
  bool own_memory_ = false;

  DISALLOW_COPY_AND_ASSIGN(HostBuffer);
};

}  // namespace cuda
