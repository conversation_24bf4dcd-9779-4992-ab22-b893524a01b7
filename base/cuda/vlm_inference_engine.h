// Copyright @2025 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <string>
#include <unordered_map>
#include <vector>

#include "base/container/utils.h"
#include "base/cuda/trt_inference_engine.h"

namespace base {

class VlmInferenceEngine : public TrtInferenceEngine {
 public:
  VlmInferenceEngine(const walle::ModelConfig& model_config,
                     bool use_model_serving,
                     cuda::DeviceContext* context);
  virtual ~VlmInferenceEngine() = default;

  // string input/output api
  const std::vector<std::string>& GetStringInputVectorPtrOrDie(
      const std::string& string_input_name) const {
    return base::FindOrDie(string_input_map_, string_input_name);
  }
  std::vector<std::string>* GetMutableStringInputVectorPtrOrDie(
      const std::string& string_input_name) {
    return base::FindOrDie(&string_input_map_, string_input_name);
  }
  const std::vector<std::string>& GetStringOutputVectorPtrOrDie(
      const std::string& string_output_name) const {
    return base::FindOrDie(string_output_map_, string_output_name);
  }
  std::vector<std::string>* GetMutableStringOutputVectorPtrOrDie(
      const std::string& string_output_name) {
    return base::FindOrDie(&string_output_map_, string_output_name);
  }

 private:
  void InitializeStringInputOutputMap(const walle::ModelConfig& model_config);

  std::unordered_map<std::string, std::vector<std::string>> string_input_map_;   // owned
  std::unordered_map<std::string, std::vector<std::string>> string_output_map_;  // owned

  DISALLOW_COPY_AND_ASSIGN(VlmInferenceEngine);
};

}  // namespace base