// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <algorithm>
#include <memory>
#include <string>
#include <utility>

#include <cuda_runtime.h>
#include "glog/logging.h"

#include "base/cuda/tensor.h"
#include "base/cuda/device_utils.h"

namespace cuda {

/*
 * CropAndResize
 * Given a list of images, a list of box coordinates and a mapping from box idx to image idx,
 * apply each box crop on corresponding image & resize into target crops.
 * 
 * inputs
 *   - images: 4D tensor [max_num_images, in_height, in_width, channels],
 *   - boxes: 2D tensor [max_num_boxes, 4],
 *   - boxes_idx: 1D tensor [max_num_boxes, 1],
 *   - num_images is allowed to be less than max_num_images (to avoid runtime allocation),
 *   - num_boxes is allowed to be less than max_num_boxes.
 * outputs
 *   - crops: 4D tensor [max_num_boxes, out_height, out_width, channels].
 * 
 */
template <typename T0, typename T1>
void CropAndResize(
  cudaStream_t stream, int num_images, int num_boxes,
  const DeviceTensor<T0, 4>& images,
  const DeviceTensor<float, 2>& boxes,
  const DeviceTensor<int, 1>& boxes_idx,
  DeviceTensor<T1, 4>* crops);

}  // namespace cuda
