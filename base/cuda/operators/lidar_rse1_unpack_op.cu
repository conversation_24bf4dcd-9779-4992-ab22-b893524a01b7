// Copyright @2025 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/cuda/operators/lidar_rse1_unpack_op.h"

#include <cuda_runtime.h>

#include "base/cuda/macros.h"
#include "sensor/lidar/data_types_device.h"
#include "sensor/lidar/robosense/robosense_data_types.h"

namespace walle {
namespace sensor {
namespace lidar {
namespace robosense {
namespace {

constexpr float kDistanceResolution = 0.005f;

} // namespace

__device__ uint16_t cuda_ntohs(uint16_t netshort) {
  return (netshort >> 8) | (netshort << 8);
}

__device__ float3 ComputeLocalPosition(const RSE1Channel& channel,
                                       int laser_id,
                                       float distance) {
  if (laser_id >= kRSE1LaserCount) {
    printf("ERROR: laser id %d", laser_id);
    return make_float3(0, 0, 0);
  }
  const int16_t vector_x = cuda_ntohs(channel.x);
  const int16_t vector_y = cuda_ntohs(channel.y);
  const int16_t vector_z = cuda_ntohs(channel.z);

  const float x = vector_x * distance / kVectorBase;
  const float y = vector_y * distance / kVectorBase;
  const float z = vector_z * distance / kVectorBase;
  return make_float3(x, y, z);
}

__global__ void UnpackKernel(const RSE1Channel* device_rse1_channel,
                             float min_range,
                             float max_range,
                             lidar::Pixel* pixels) {
  const int block_idx = blockIdx.x;
  if (block_idx >= kRSE1PacketSize) {
    return;
  }
  const int laser_id = threadIdx.x;
  if (laser_id >= kRSE1LaserCount) {
    return;
  }
  const int index = laser_id + block_idx * kRSE1LaserCount;
  const RSE1Channel& channel = device_rse1_channel[index];
  const float distance = cuda_ntohs(channel.distance) * kDistanceResolution;
  if (distance < min_range || distance > max_range) {
    return;
  }
  const float3 position = ComputeLocalPosition(channel, laser_id, distance);
  pixels[index].position[0] = position.x;
  pixels[index].position[1] = position.y;
  pixels[index].position[2] = position.z;
  pixels[index].intensity = channel.intensity;
}

void UnpackOnGpu(const RSE1Channel* device_rse1_channel,
                             float min_range,
                             float max_range,
                             cuda::DeviceBuffer* device_range_image_buffer,
                             cuda::DeviceContext* device_context
                             ) {
  lidar::Pixel* pixels = static_cast<lidar::Pixel*>(device_range_image_buffer->mutable_data());
  dim3 blockDim(kRSE1LaserCount);
  dim3 gridDim(kRSE1PacketSize);
  UnpackKernel<<<gridDim, blockDim, 0, device_context->stream()>>>(
      device_rse1_channel, min_range, max_range, pixels);
  CUDA_KERNEL_LAUNCH_CHECK();
}

}  // namespace robosense
}  // namespace lidar
}  // namespace sensor
}  // namespace walle
