// Copyright @2023 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>
//          <PERSON> (<EMAIL>)

#include "base/cuda/operators/pillar_scatter_op.h"

#include <random>
#include <vector>

#include <cuda_fp16.h>
#include "gtest/gtest.h"

#include "base/cuda/device_buffer.h"

namespace cuda {
namespace {

constexpr uint32_t kBatchSize = 1;
constexpr uint32_t kMaxPillarNum = 15092;
const std::vector<uint32_t> kParamsData{kMaxPillarNum};
constexpr uint32_t kNumFeatures = 128;
constexpr uint32_t kInputDataSize = kBatchSize * kMaxPillarNum * kNumFeatures;
constexpr uint32_t kFeatureX = 1920;
constexpr uint32_t kFeatureY = 1080;
constexpr uint32_t kCoordsDim = 4;
constexpr uint32_t kCoordsDataSize = kBatchSize * kMaxPillarNum * kCoordsDim;
constexpr uint32_t kOutputDataSize = kBatchSize * kFeatureX * kFeatureY * kNumFeatures;
constexpr float kNearEpsilon = 1e-6f;

}  // namespace

template <typename Data>
void test() {
  CHECK_EQ(kBatchSize, 1);
  std::vector<Data> host_pillar_features_data(kInputDataSize, 0.0f);
  std::default_random_engine generator;
  std::normal_distribution<float> distribution(0, 1);
  cuda::DeviceContext context(0);
  for (uint32_t i = 0; i < kInputDataSize; ++i) {
    host_pillar_features_data.at(i) = distribution(generator);
  }
  cuda::DeviceBuffer device_pillar_features_data(kInputDataSize * sizeof(Data));
  device_pillar_features_data.FromCpu(host_pillar_features_data.data(), &context);
  cuda::DeviceBuffer device_spatial_features_data(kOutputDataSize * sizeof(Data));
  std::vector<Data> host_spatial_features_data(kOutputDataSize, 0.0f);
  device_spatial_features_data.FromCpu(host_spatial_features_data.data(), &context);
  std::vector<uint32_t> host_coords_data;
  CHECK_LT(kMaxPillarNum, kFeatureX * kFeatureY);
  for (uint32_t y = 0; y < kFeatureY; ++y) {
    for (uint32_t x = 0; x < kFeatureX; ++x) {
      host_coords_data.push_back(0u);
      host_coords_data.push_back(0u);
      host_coords_data.push_back(y);
      host_coords_data.push_back(x);
    }
  }
  host_coords_data.resize(kCoordsDataSize);
  cuda::DeviceBuffer device_coords_data(kCoordsDataSize * sizeof(uint32_t));
  device_coords_data.FromCpu(host_coords_data.data(), &context);
  cuda::DeviceBuffer device_param_data(kBatchSize * sizeof(uint32_t));
  device_param_data.FromCpu(kParamsData.data(), &context);
  PillarScatterWrapper<Data>(context.stream(),
                             device_pillar_features_data.ConstView<Data>().data(),
                             device_coords_data.ConstView<int>().data(),
                             device_param_data.ConstView<int>().data(),
                             kBatchSize,
                             kMaxPillarNum,
                             kNumFeatures,
                             kFeatureX,
                             kFeatureY,
                             false,
                             device_spatial_features_data.View<Data>().mutable_data());
  device_spatial_features_data.ToCpu(
      host_spatial_features_data.data(), kOutputDataSize * sizeof(Data), &context);
  context.Sync();
  for (uint32_t i = 0; i < kMaxPillarNum; ++i) {
    const uint32_t y = host_coords_data.at(i * kCoordsDim + 2);
    const uint32_t x = host_coords_data.at(i * kCoordsDim + 3);
    for (uint32_t feat_i = 0; feat_i < kNumFeatures; ++feat_i) {
      const float p_feat = host_pillar_features_data.at(kNumFeatures * i + feat_i);
      const uint32_t spatial_index = (feat_i * kFeatureX * kFeatureY) + y * kFeatureX + x;
      const float host_p_feat = host_spatial_features_data.at(spatial_index);
      CHECK_NEAR(p_feat, host_p_feat, kNearEpsilon);
    }
  }
  device_spatial_features_data.Clean(&context);
  PillarScatterWrapper<Data>(context.stream(),
                             device_pillar_features_data.ConstView<Data>().data(),
                             device_coords_data.ConstView<int>().data(),
                             device_param_data.ConstView<int>().data(),
                             kBatchSize,
                             kMaxPillarNum,
                             kNumFeatures,
                             kFeatureX,
                             kFeatureY,
                             true,
                             device_spatial_features_data.View<Data>().mutable_data());
  device_spatial_features_data.ToCpu(
      host_spatial_features_data.data(), kOutputDataSize * sizeof(Data), &context);
  context.Sync();
  for (uint32_t i = 0; i < kMaxPillarNum; ++i) {
    const uint32_t y = host_coords_data.at(i * kCoordsDim + 2);
    const uint32_t x = host_coords_data.at(i * kCoordsDim + 3);
    for (uint32_t feat_i = 0; feat_i < kNumFeatures; ++feat_i) {
      const float p_feat = host_pillar_features_data.at(kNumFeatures * i + feat_i);
      const uint32_t spatial_index = (y * kFeatureX + x) * kNumFeatures + feat_i;
      const float host_p_feat = host_spatial_features_data.at(spatial_index);
      CHECK_NEAR(p_feat, host_p_feat, kNearEpsilon);
    }
  }
}

TEST(PillarScatterKernelTest, BasicFloatTest) { test<float>(); }

TEST(PillarScatterKernelTest, BasicHalfTest) { test<half>(); }

}  // namespace cuda
