load("//build/bazel_rules:cc_rules.bzl", "cc_filegroup")
load("//build/bazel_rules:cuda_library.bzl", "cuda_library")

package(default_visibility = ["//visibility:public"])

cuda_library(
    name = "cub_ops_cuda",
    srcs = [
        "cub_ops.cu",
    ],
    hdrs = [
        "cub_ops.h",
    ],
    # TODO(xuzhaoliang): remove this flag after upgrading to pytorch 2.0
    flags = [
        "-Xcompiler",
        "-fno-gnu-unique",
    ],
    deps = [
        "//base/cuda:device_buffer",
    ],
)

cc_test(
    name = "cub_ops_test",
    srcs = [
        "cub_ops_test.cc",
    ],
    tags = [
        "ci",
        "ci_gpu",
    ],
    deps = [
        ":cub_ops_cuda",
        "//base/testing:test_main",
    ],
)

cc_filegroup(
    name = "lib",
    srcs = [
        ":cub_ops_cuda",
    ],
)
