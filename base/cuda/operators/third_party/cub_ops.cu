// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/cuda/operators/third_party/cub_ops.h"

#include <cub/device/device_histogram.cuh>
#include <cub/device/device_partition.cuh>
#include <cub/device/device_radix_sort.cuh>
#include <cub/device/device_scan.cuh>
#include <cub/device/device_select.cuh>

namespace cuda {
namespace cub_ops {

template <typename KeyType, typename ValueType>
void SortPairs(const ConstDeviceBufferViewPair<KeyType, ValueType>& src_pair, int num_items,
               DeviceBufferViewPair<KeyType, ValueType>* sorted_pair, DeviceBuffer* workspace,
               DeviceContext* context, bool debug_synchronous) {
  size_t temp_storage_bytes = CHECK_NOTNULL(workspace)->size();
  CHECK_GE(src_pair.key->size(), num_items);
  CHECK_GE(src_pair.value->size(), num_items);
  CHECK_GE(CHECK_NOTNULL(sorted_pair)->key->size(), num_items);
  CHECK_GE(sorted_pair->value->size(), num_items);

  CUDA_CHECK(cub::DeviceRadixSort::SortPairs(
      workspace->mutable_data(), temp_storage_bytes, src_pair.key->data(),
      sorted_pair->key->mutable_data(), src_pair.value->data(), sorted_pair->value->mutable_data(),
      num_items, 0, static_cast<int>(sizeof(KeyType) * 8), CHECK_NOTNULL(context)->stream(),
      debug_synchronous));
}

template void SortPairs<int, int>(const ConstDeviceBufferViewPair<int, int>& src_pair,
                                  int num_items, DeviceBufferViewPair<int, int>* sorted_pair,
                                  DeviceBuffer* workspace, DeviceContext* context,
                                  bool debug_synchronous);
template void SortPairs<float, int>(const ConstDeviceBufferViewPair<float, int>& src_pair,
                                    int num_items, DeviceBufferViewPair<float, int>* sorted_pair,
                                    DeviceBuffer* workspace, DeviceContext* context,
                                    bool debug_synchronous);

template <typename KeyType, typename ValueType>
size_t GetSortPairsWorkspaceSize(int num_items) {
  size_t temp_storage_bytes = 0;
  CUDA_CHECK(cub::DeviceRadixSort::SortPairs(
      static_cast<void*>(nullptr), temp_storage_bytes, static_cast<const KeyType*>(nullptr),
      static_cast<KeyType*>(nullptr), static_cast<const ValueType*>(nullptr),
      static_cast<ValueType*>(nullptr), num_items));
  return temp_storage_bytes;
}

template size_t GetSortPairsWorkspaceSize<int, int>(int num_items);
template size_t GetSortPairsWorkspaceSize<float, int>(int num_items);

template <typename KeyType, typename ValueType>
void SortPairsDescending(const ConstDeviceBufferViewPair<KeyType, ValueType>& src_pair, int num_items,
                         DeviceBufferViewPair<KeyType, ValueType>* sorted_pair, DeviceBuffer* workspace,
                         DeviceContext* context, bool debug_synchronous) {
  size_t temp_storage_bytes = CHECK_NOTNULL(workspace)->size();
  CHECK_GE(src_pair.key->size(), num_items);
  CHECK_GE(src_pair.value->size(), num_items);
  CHECK_GE(CHECK_NOTNULL(sorted_pair)->key->size(), num_items);
  CHECK_GE(sorted_pair->value->size(), num_items);

  CUDA_CHECK(cub::DeviceRadixSort::SortPairsDescending(
      workspace->mutable_data(), temp_storage_bytes, src_pair.key->data(),
      sorted_pair->key->mutable_data(), src_pair.value->data(), sorted_pair->value->mutable_data(),
      num_items, 0, static_cast<int>(sizeof(KeyType) * 8), CHECK_NOTNULL(context)->stream(),
      debug_synchronous));
}

template void SortPairsDescending<int, int>(const ConstDeviceBufferViewPair<int, int>& src_pair,
                                            int num_items, DeviceBufferViewPair<int, int>* sorted_pair,
                                            DeviceBuffer* workspace, DeviceContext* context,
                                            bool debug_synchronous);
template void SortPairsDescending<float, int>(const ConstDeviceBufferViewPair<float, int>& src_pair,
                                              int num_items, DeviceBufferViewPair<float, int>* sorted_pair,
                                              DeviceBuffer* workspace, DeviceContext* context,
                                              bool debug_synchronous);

template <typename KeyType, typename ValueType>
size_t GetSortPairsDescendingWorkspaceSize(int num_items) {
  size_t temp_storage_bytes = 0;
  CUDA_CHECK(cub::DeviceRadixSort::SortPairsDescending(
      static_cast<void*>(nullptr), temp_storage_bytes, static_cast<const KeyType*>(nullptr),
      static_cast<KeyType*>(nullptr), static_cast<const ValueType*>(nullptr),
      static_cast<ValueType*>(nullptr), num_items));
  return temp_storage_bytes;
}

template size_t GetSortPairsDescendingWorkspaceSize<int, int>(int num_items);
template size_t GetSortPairsDescendingWorkspaceSize<float, int>(int num_items);

template <typename ValueType>
void SortKeys(const DeviceBufferView<ValueType>& input,
              int num_items,
              DeviceBufferView<ValueType>* output,
              DeviceBuffer* workspace,
              DeviceContext* context,
              bool debug_synchronous) {
  size_t temp_storage_bytes = CHECK_NOTNULL(workspace)->size();
  CHECK_GE(input.size(), num_items);
  CHECK_GE(CHECK_NOTNULL(output)->size(), num_items);
  CUDA_CHECK(cub::DeviceRadixSort::SortKeys(workspace->mutable_data(),
                                            temp_storage_bytes,
                                            input.data(),
                                            output->mutable_data(),
                                            num_items,
                                            0,
                                            static_cast<int>(sizeof(ValueType) * 8),
                                            CHECK_NOTNULL(context)->stream(),
                                            debug_synchronous));
}
template void SortKeys<int>(const DeviceBufferView<int>& input,
                            int num_items,
                            DeviceBufferView<int>* output,
                            DeviceBuffer* workspace,
                            DeviceContext* context,
                            bool debug_synchronous);

template <typename ValueType>
size_t GetSortKeysWorkspaceSize(int num_items) {
  size_t temp_storage_bytes = 0;
  CUDA_CHECK(cub::DeviceRadixSort::SortKeys(static_cast<void*>(nullptr),
                                            temp_storage_bytes,
                                            static_cast<const ValueType*>(nullptr),
                                            static_cast<ValueType*>(nullptr),
                                            num_items));
  return temp_storage_bytes;
}
template size_t GetSortKeysWorkspaceSize<int>(int num_items);

// Given an input sequence d_in having runs of consecutive equal-valued keys, only the first key
// from each run is selectively copied to d_out. The total number of items selected is written to
// d_num_selected_out.
//  - The == equality operator is used to determine whether keys are equivalent
//  - Copies of the selected items are compacted into d_out and maintain their original relative
//  ordering.
template <typename ValueType>
void Unique(const DeviceBufferView<ValueType>& input, DeviceBufferView<ValueType>* output,
            DeviceBufferView<int>* num_unique_value, DeviceBuffer* workspace,
            DeviceContext* context, bool debug_synchronous) {
  size_t temp_storage_bytes = CHECK_NOTNULL(workspace)->size();
  CHECK_EQ(CHECK_NOTNULL(num_unique_value)->size(), 1);
  CUDA_CHECK(cub::DeviceSelect::Unique(workspace->mutable_data(), temp_storage_bytes, input.data(),
                                       CHECK_NOTNULL(output)->mutable_data(),
                                       num_unique_value->mutable_data(), input.size(),
                                       context->stream(), debug_synchronous));
}

template void Unique<int>(const DeviceBufferView<int>& input, DeviceBufferView<int>* output,
                          DeviceBufferView<int>* num_unique_value, DeviceBuffer* workspace,
                          DeviceContext* context, bool debug_synchronous);

template <typename ValueType>
size_t GetUniqueWorkspaceSize(int num_items) {
  size_t temp_storage_bytes = 0;
  CUDA_CHECK(cub::DeviceSelect::Unique(
      static_cast<void*>(nullptr), temp_storage_bytes, static_cast<const ValueType*>(nullptr),
      static_cast<ValueType*>(nullptr), static_cast<int*>(nullptr), num_items));
  return temp_storage_bytes;
}
template size_t GetUniqueWorkspaceSize<int>(int num_items);

// Computes an intensity histogram from a sequence of data samples using the specified bin boundary
// levels.
//  - The number of histogram bins is (num_levels - 1)
//  - The value range for bin_i is [level[i], level[i+1])
template <typename ValueType>
void HistogramRange(const DeviceBufferView<ValueType>& sample,
                    const DeviceBufferView<ValueType>& levels,
                    DeviceBufferView<int>* histogram, DeviceBuffer* workspace,
                    DeviceContext* context, bool debug_synchronous) {
  size_t temp_storage_bytes = CHECK_NOTNULL(workspace)->size();
  const int num_levels = levels.size();
  const int sample_size = sample.size();
  const int histogram_size = histogram->size();
  CHECK_EQ(num_levels, histogram_size + 1);
  CUDA_CHECK(cub::DeviceHistogram::HistogramRange(
      workspace->mutable_data(), temp_storage_bytes, sample.data(),
      CHECK_NOTNULL(histogram)->mutable_data(), num_levels, levels.data(), sample_size,
      CHECK_NOTNULL(context)->stream(), debug_synchronous));
}
template void HistogramRange<int>(const DeviceBufferView<int>& sample,
                                  const DeviceBufferView<int>& levels,
                                  DeviceBufferView<int>* histogram, DeviceBuffer* workspace,
                                  DeviceContext* context, bool debug_synchronous);

template <typename ValueType>
size_t GetHistogramWorkspaceSize(int num_items) {
  size_t temp_storage_bytes = 0;
  CUDA_CHECK(cub::DeviceHistogram::HistogramRange(
      static_cast<void*>(nullptr), temp_storage_bytes, static_cast<ValueType*>(nullptr),
      static_cast<ValueType*>(nullptr), num_items, static_cast<int*>(nullptr), num_items));
  return temp_storage_bytes;
}

template size_t GetHistogramWorkspaceSize<int>(int num_items);

// Computes a device-wide exclusive prefix sum. The value of 0 is applied as the initial value, and
// is assigned to *d_out.
//
//  - Supports non-commutative sum operators.
//  - Provides "run-to-run" determinism for pseudo-associative reduction (e.g., addition of floating
//    point types) on the same GPU device. However, results for pseudo-associative reduction may be
//    inconsistent from one device to a another device of a different compute-capability because CUB
//    can employ different tile-sizing for different architectures.
//  e.g.
//    input: [8, 6,  7,  5,  3,  0,  9]
//    sum  : [0, 8, 14, 21, 26, 29, 29]
template <typename ValueType>
void ExclusiveSum(const DeviceBufferView<ValueType>& input, DeviceBufferView<ValueType>* sum,
                  DeviceBuffer* workspace, DeviceContext* context, bool debug_synchronous) {
  size_t temp_storage_bytes = CHECK_NOTNULL(workspace)->size();
  const int num_value = input.size();
  CUDA_CHECK(cub::DeviceScan::ExclusiveSum(workspace->mutable_data(), temp_storage_bytes,
                                           input.data(), CHECK_NOTNULL(sum)->mutable_data(),
                                           num_value, CHECK_NOTNULL(context)->stream(),
                                           debug_synchronous));
}
template void ExclusiveSum<int>(const DeviceBufferView<int>& input,
                                DeviceBufferView<int>* sum,
                                DeviceBuffer* workspace,
                                DeviceContext* context,
                                bool debug_synchronous);

template <typename ValueType>
size_t GetExclusiveSumWorkspaceSize(int num_items) {
  size_t temp_storage_bytes = 0;
  CUDA_CHECK(cub::DeviceScan::ExclusiveSum(static_cast<void*>(nullptr), temp_storage_bytes,
                                           static_cast<ValueType*>(nullptr),
                                           static_cast<ValueType*>(nullptr), num_items));
  return temp_storage_bytes;
}
template size_t GetExclusiveSumWorkspaceSize<int>(int num_items);

// Computes a device-wide inclusive prefix sum.
//
//  - Supports non-commutative sum operators.
//  - Provides "run-to-run" determinism for pseudo-associative reduction (e.g., addition of floating
//    point types) on the same GPU device. However, results for pseudo-associative reduction may be
//    inconsistent from one device to a another device of a different compute-capability because CUB
//    can employ different tile-sizing for different architectures.
//  e.g.
//    input: [8, 6,  7,  5,  3,  0,  9]
//    sum  : [8, 14, 21, 26, 29, 29, 38]
template <typename ValueType>
void InclusiveSum(const DeviceBufferView<ValueType>& input, DeviceBufferView<ValueType>* sum,
                  DeviceBuffer* workspace, DeviceContext* context, bool debug_synchronous) {
  size_t temp_storage_bytes = CHECK_NOTNULL(workspace)->size();
  const int num_value = input.size();
  CUDA_CHECK(cub::DeviceScan::InclusiveSum(workspace->mutable_data(), temp_storage_bytes,
                                           input.data(), CHECK_NOTNULL(sum)->mutable_data(),
                                           num_value, CHECK_NOTNULL(context)->stream(),
                                           debug_synchronous));
}
template void InclusiveSum<int>(const DeviceBufferView<int>& input,
                                DeviceBufferView<int>* sum,
                                DeviceBuffer* workspace,
                                DeviceContext* context,
                                bool debug_synchronous);
template void InclusiveSum<float>(const DeviceBufferView<float>& input,
                                  DeviceBufferView<float>* sum,
                                  DeviceBuffer* workspace,
                                  DeviceContext* context,
                                  bool debug_synchronous);
template void InclusiveSum<double>(const DeviceBufferView<double>& input,
                                   DeviceBufferView<double>* sum,
                                   DeviceBuffer* workspace,
                                   DeviceContext* context,
                                   bool debug_synchronous);

template <typename ValueType>
size_t GetInclusiveSumWorkspaceSize(int num_items) {
  size_t temp_storage_bytes = 0;
  CUDA_CHECK(cub::DeviceScan::InclusiveSum(static_cast<void*>(nullptr), temp_storage_bytes,
                                           static_cast<ValueType*>(nullptr),
                                           static_cast<ValueType*>(nullptr), num_items));
  return temp_storage_bytes;
}
template size_t GetInclusiveSumWorkspaceSize<int>(int num_items);
template size_t GetInclusiveSumWorkspaceSize<float>(int num_items);
template size_t GetInclusiveSumWorkspaceSize<double>(int num_items);

// Uses the flags sequence to split the corresponding items from input into a partitioned
// sequence output. The total number of items copied into the first partition is written
// to num_selected.
//  e.g.
//    input: [1, 2, 3, 4, 5, 6, 7, 8]
//    flags: [1, 0, 0, 1, 0, 1, 1, 0]
//    output: [1, 4, 6, 7, 8, 5, 3, 2]
//    num_selected: [4]
template <typename ValueType>
void Flagged(const ConstDeviceBufferView<ValueType>& input,
             const ConstDeviceBufferView<int>& flags, int num_items,
             DeviceBufferView<ValueType>* output, DeviceBufferView<int>* num_selected,
             DeviceBuffer* workspace, DeviceContext* context, bool debug_synchronous) {
  size_t temp_storage_bytes = CHECK_NOTNULL(workspace)->size();
  CUDA_CHECK(cub::DevicePartition::Flagged(
      workspace->mutable_data(), temp_storage_bytes, input.data(), flags.data(),
      CHECK_NOTNULL(output)->mutable_data(), CHECK_NOTNULL(num_selected)->mutable_data(), num_items,
      CHECK_NOTNULL(context)->stream(), debug_synchronous));
}

template <typename ValueType>
size_t GetFlaggedWorkspaceSize(int num_items) {
  size_t temp_storage_bytes = 0;
  CUDA_CHECK(cub::DevicePartition::Flagged(
      static_cast<void*>(nullptr), temp_storage_bytes, static_cast<ValueType*>(nullptr),
      static_cast<int*>(nullptr), static_cast<ValueType*>(nullptr),
      static_cast<int*>(nullptr), num_items));
  return temp_storage_bytes;
}

#define DEFINE_FLAGGED_PRIMARY(T)                                   \
  template size_t GetFlaggedWorkspaceSize<T>(int num_items);        \
  template void Flagged<T>(const ConstDeviceBufferView<T>& input,   \
                           const ConstDeviceBufferView<int>& flags, \
                           int num_items,                           \
                           DeviceBufferView<T>* output,             \
                           DeviceBufferView<int>* num_selected,     \
                           DeviceBuffer* workspace,                 \
                           DeviceContext* context,                  \
                           bool debug_synchronous)
DEFINE_FLAGGED_PRIMARY(int);
DEFINE_FLAGGED_PRIMARY(uint32_t);
DEFINE_FLAGGED_PRIMARY(float);
DEFINE_FLAGGED_PRIMARY(double);
#undef DEFINE_FLAGGED_PRIMARY

#define DEFINE_FLAGGED_CUBVECTOR(T, N)                                                        \
  template size_t GetFlaggedWorkspaceSize<CubVector<T, N>>(int num_items);                    \
  template void Flagged<CubVector<T, N>>(const ConstDeviceBufferView<CubVector<T, N>>& input, \
                                         const ConstDeviceBufferView<int>& flags,             \
                                         int num_items,                                       \
                                         DeviceBufferView<CubVector<T, N>>* output,           \
                                         DeviceBufferView<int>* num_selected,                 \
                                         DeviceBuffer* workspace,                             \
                                         DeviceContext* context,                              \
                                         bool debug_synchronous)
DEFINE_FLAGGED_CUBVECTOR(float, 2);
DEFINE_FLAGGED_CUBVECTOR(float, 3);
DEFINE_FLAGGED_CUBVECTOR(float, 4);
DEFINE_FLAGGED_CUBVECTOR(float, 5);
DEFINE_FLAGGED_CUBVECTOR(double, 2);
DEFINE_FLAGGED_CUBVECTOR(double, 3);
DEFINE_FLAGGED_CUBVECTOR(double, 4);
DEFINE_FLAGGED_CUBVECTOR(double, 5);
#undef DEFINE_FLAGGED_CUBVECTOR

}  // namespace cub_ops
}  // namespace cuda
