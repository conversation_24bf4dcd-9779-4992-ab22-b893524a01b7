// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include <algorithm>
#include <vector>

#include "base/cuda/device_utils.h"
#include "base/cuda/operators/third_party/cub_ops.h"

#include "gtest/gtest.h"

namespace cuda {
namespace cub_ops {

TEST(CubOpsTest, TestSortPairs) {
  const int kTestNum = 100000;
  const int kNumBytes = kTestNum * sizeof(int);
  const size_t workspace_size = GetSortPairsWorkspaceSize<int, int>(kTestNum);

  DeviceBuffer workspace(workspace_size);

  std::vector<int> key(kTestNum);
  std::vector<int> key_result(kTestNum);
  std::vector<int> value(kTestNum);
  for (int i = 0; i < kTestNum; ++i) {
    value[i] = i;
    key[i] = std::rand();
  }
  DeviceContext context(0);

  <PERSON>ceBuffer key_dev(kNumBytes);
  DeviceBuffer value_dev(kNumBytes);
  DeviceBuffer key1_dev(kNumBytes);
  DeviceBuffer value1_dev(kNumBytes);
  DeviceBufferView<int> key_dev_view = key_dev.View<int>();
  DeviceBufferView<int> value_dev_view = value_dev.View<int>();
  DeviceBufferView<int> key1_dev_view = key1_dev.View<int>();
  DeviceBufferView<int> value1_dev_view = value1_dev.View<int>();

  key_dev_view.FromCpu(key.data(), &context);
  value_dev_view.FromCpu(value.data(), &context);

  ConstDeviceBufferViewPair<int, int> src_pair(&key_dev_view, &value_dev_view);
  DeviceBufferViewPair<int, int> sorted_pair(&key1_dev_view, &value1_dev_view);
  SortPairs<int, int>(src_pair, kTestNum, &sorted_pair, &workspace, &context);
  key1_dev_view.ToCpu(key_result.data(), &context);
  value1_dev_view.ToCpu(value.data(), &context);

  context.Sync();
  for (int i = 0; i + 1 < kTestNum; ++i) {
    EXPECT_LE(key_result[i], key_result[i + 1]);
    EXPECT_EQ(key[value[i]], key_result[i]);
  }
}

TEST(CubOpsTest, TestUnique) {
  const int kTestNum = 100000;
  const int kNumBytes = kTestNum * sizeof(int);
  const size_t workspace_size = GetUniqueWorkspaceSize<int>(kTestNum);
  DeviceBuffer workspace(workspace_size);
  std::vector<int> x(kTestNum, 0);
  for (int i = 0; i < kTestNum; ++i) {
    x[i] = std::rand() % 2000;
  }
  std::sort(x.begin(), x.end());

  DeviceContext context(0);

  DeviceBuffer dev_input(kNumBytes);
  dev_input.FromCpu(x.data(), kNumBytes, &context);

  DeviceBuffer dev_num_unique_value(4);
  DeviceBufferView<int> dev_input_view = dev_input.View<int>();
  DeviceBufferView<int> dev_num_unique_value_view = dev_num_unique_value.View<int>();
  Unique<int>(dev_input.ConstView<int>().View(), &dev_input_view, &dev_num_unique_value_view,
              &workspace, &context);

  int num_unique_value = 0;
  dev_num_unique_value.ToCpu(&num_unique_value, sizeof(int), &context);
  context.Sync();
  CHECK_LE(num_unique_value, x.size());
  std::vector<int> y(num_unique_value, 0);
  dev_input.ToCpu(y.data(), num_unique_value * sizeof(int), &context);
  context.Sync();

  int j = 0;
  for (int i = 0; i < num_unique_value; ++i) {
    if (i + 1 < num_unique_value) {
      EXPECT_NE(y[i], y[i + 1]);
    }
    EXPECT_LT(j, kTestNum);
    EXPECT_EQ(x[j], y[i]);
    while (j < kTestNum && x[j] == y[i]) {
      ++j;
    }
  }
  EXPECT_EQ(j, kTestNum);
}

TEST(CubOpsTest, TestHistogramRange) {
  const int kTestNum = 100000;
  const int kNumBytes = kTestNum * sizeof(int);
  const size_t workspace_size = GetHistogramWorkspaceSize<int>(kTestNum);
  DeviceBuffer workspace(workspace_size);

  const int kMaxLevel = 2000;
  std::vector<int> sample(kTestNum, 0);
  for (int i = 0; i < kTestNum; ++i) {
    sample[i] = std::rand() % kMaxLevel;
  }
  std::vector<int> level(kMaxLevel + 1, 0);
  for (int i = 0; i <= kMaxLevel; ++i) {
    level[i] = i;
  }

  DeviceContext context(0);
  DeviceBuffer dev_sample(kNumBytes);
  DeviceBuffer dev_level((kMaxLevel + 1) * sizeof(int));
  DeviceBuffer dev_histogram(kMaxLevel * sizeof(int));

  DeviceBufferView<int> dev_sample_view = dev_sample.View<int>();
  DeviceBufferView<int> dev_level_view = dev_level.View<int>();
  DeviceBufferView<int> dev_histogram_view = dev_histogram.View<int>();

  dev_sample_view.FromCpu(sample.data(), &context);
  dev_level_view.FromCpu(level.data(), &context);
  context.Sync();
  HistogramRange<int>(dev_sample_view, dev_level_view, &dev_histogram_view, &workspace, &context);

  std::vector<int> histogram(kMaxLevel, 0);
  dev_histogram_view.ToCpu(histogram.data(), &context);
  context.Sync();

  for (int i = 0; i < static_cast<int>(sample.size()); ++i) {
    if (sample[i] >= 0 && sample[i] <= kMaxLevel) {
      --histogram[sample[i]];
    }
  }
  for (int i = 0; i < static_cast<int>(histogram.size()); ++i) {
    EXPECT_EQ(histogram[i], 0);
  }
}

TEST(CubOpsTest, TestCubExclusiveSum) {
  const int kTestNum = 100000;
  const int kNumBytes = kTestNum * sizeof(int);
  const size_t workspace_size = GetExclusiveSumWorkspaceSize<int>(kTestNum);
  DeviceBuffer workspace(workspace_size);

  const int kMaxInputValue = 2000;
  std::vector<int> input(kTestNum, 0);
  std::vector<int> sum(kTestNum, 0);
  for (int i = 0; i < kTestNum; ++i) {
    input[i] = std::rand() % kMaxInputValue;
  }

  DeviceContext context(0);
  DeviceBuffer dev_input(kNumBytes);
  DeviceBuffer dev_sum(kNumBytes);
  dev_input.FromCpu(input.data(), kNumBytes, &context);
  DeviceBufferView<int> dev_sum_view = dev_sum.View<int>();
  ExclusiveSum<int>(dev_input.ConstView<int>().View(), &dev_sum_view, &workspace, &context);
  dev_sum_view.ToCpu(sum.data(), &context);
  context.Sync();
  int sum_cpu = 0;
  for (int i = 0; i < static_cast<int>(input.size()); ++i) {
    EXPECT_EQ(sum_cpu, sum[i]);
    sum_cpu += input[i];
  }
}

TEST(CubOpsTest, TestFlagged) {
  const int kTestNum = 100000;
  const int kNumBytes = kTestNum * sizeof(float);
  const int kNumBytesFlags = kTestNum * sizeof(int);
  const size_t workspace_size = GetFlaggedWorkspaceSize<float>(kTestNum);
  DeviceBuffer workspace(workspace_size);

  const int kMaxInputValue = 2000;
  std::vector<float> input(kTestNum, 0);
  std::vector<int> flags(kTestNum, 0);
  std::vector<float> output(kTestNum, 0);
  std::vector<float> output_cpu;
  output_cpu.reserve(kTestNum);
  int num_selected = 0;
  for (int i = 0; i < kTestNum; ++i) {
    input[i] = std::rand() % kMaxInputValue;
    flags[i] = static_cast<char>(std::rand() % 2);
    if (flags[i] == 1) {
      output_cpu.push_back(input[i]);
    }
  }

  DeviceContext context(0);
  DeviceBuffer dev_input(kNumBytes);
  DeviceBuffer dev_flags(kNumBytesFlags);
  DeviceBuffer dev_output(kNumBytes);
  DeviceBuffer dev_num_selected(sizeof(int));
  dev_flags.FromCpu(flags.data(), kNumBytesFlags, &context);
  dev_input.FromCpu(input.data(), kNumBytes, &context);
  DeviceBufferView<float> dev_output_view = dev_output.View<float>();
  DeviceBufferView<int> dev_num_selected_view = dev_num_selected.View<int>();
  Flagged<float>(dev_input.ConstView<float>(),
                 dev_flags.ConstView<int>(),
                 kTestNum,
                 &dev_output_view,
                 &dev_num_selected_view,
                 &workspace,
                 &context);
  dev_output_view.ToCpu(output.data(), &context);
  dev_num_selected_view.ToCpu(&num_selected, &context);
  context.Sync();

  EXPECT_EQ(output_cpu.size(), num_selected);
  for (size_t i = 0; i < output_cpu.size(); ++i) {
    EXPECT_EQ(output[i], output_cpu[i]);
  }
}

TEST(CubOpsTest, TestSortKeys) {
  const int kTestNum = 100000;
  const int kNumBytes = kTestNum * sizeof(int);
  const size_t workspace_size = GetSortKeysWorkspaceSize<int>(kTestNum);
  DeviceBuffer workspace(workspace_size);
  DeviceContext context(0);
  std::vector<int> key(kTestNum);
  std::vector<int> key_result(kTestNum);
  for (int i = 0; i < kTestNum; ++i) {
    key[i] = std::rand();
    key_result[i] = key[i];
  }
  std::sort(key_result.begin(), key_result.end());
  DeviceBuffer key_in_dev(kNumBytes);
  DeviceBuffer key_out_dev(kNumBytes);
  DeviceBufferView<int> key_in_dev_view = key_in_dev.View<int>();
  DeviceBufferView<int> key_out_dev_view = key_out_dev.View<int>();
  key_in_dev_view.FromCpu(key.data(), &context);
  SortKeys(key_in_dev_view, kTestNum, &key_out_dev_view, &workspace, &context);
  key_out_dev_view.ToCpu(key.data(), &context);
  context.Sync();
  for (int i = 0; i < kTestNum; ++i) {
    EXPECT_EQ(key[i], key_result[i]);
  }
}

TEST(CubOpsTest, TestInclusiveSum) {
  const int kTestNum = 100000;
  const int kNumBytes = kTestNum * sizeof(int);
  const size_t workspace_size = GetInclusiveSumWorkspaceSize<int>(kTestNum);
  DeviceBuffer workspace(workspace_size);
  const int kMaxInputValue = 2000;
  std::vector<int> input(kTestNum, 0);
  std::vector<int> sum(kTestNum, 0);
  for (int i = 0; i < kTestNum; ++i) {
    input[i] = std::rand() % kMaxInputValue;
  }
  DeviceContext context(0);
  DeviceBuffer dev_input(kNumBytes);
  DeviceBuffer dev_sum(kNumBytes);
  dev_input.FromCpu(input.data(), kNumBytes, &context);
  DeviceBufferView<int> dev_sum_view = dev_sum.View<int>();
  InclusiveSum<int>(dev_input.ConstView<int>().View(), &dev_sum_view, &workspace, &context);
  dev_sum_view.ToCpu(sum.data(), &context);
  context.Sync();
  int sum_cpu = 0;
  for (int i = 0; i < static_cast<int>(input.size()); ++i) {
    sum_cpu += input[i];
    EXPECT_EQ(sum_cpu, sum[i]);
  }
}

}  // namespace cub_ops
}  // namespace cuda
