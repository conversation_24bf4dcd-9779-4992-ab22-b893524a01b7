// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <iostream>
#include <string>

#include "base/cuda/device_buffer.h"
#include "base/cuda/macros.h"

namespace cuda {
namespace cub_ops {

template <typename T, int N>
struct CubVector {
  T data[N];
};

template <typename KeyType, typename ValueType>
struct CubSortPair {
  CubSortPair(KeyType key_buffer, ValueType value_buffer) : key(key_buffer), value(value_buffer) {}
  KeyType key;
  ValueType value;
};

template <typename KeyType, typename ValueType>
using DeviceBufferViewPair = CubSortPair<DeviceBufferView<KeyType>*, DeviceBufferView<ValueType>*>;
template <typename KeyType, typename ValueType>
using ConstDeviceBufferViewPair =
    CubSortPair<const DeviceBufferView<KeyType>*, const DeviceBufferView<ValueType>*>;

// Sorts key-value pairs into ascending order. (~2N auxiliary storage required)
//
//  - The contents of the input data are not altered by the sorting operation
//  - An optional bit subrange [begin_bit, end_bit) of differentiating key bits can be specified.
//  This can reduce overall sorting overhead and yield a corresponding performance improvement.
//  - This operation requires an allocation of temporary device storage that is O(N+P), where N is
//  the length of the input and P is the number of streaming multiprocessors on the device. For
//  sorting using only O(P) temporary storage, see the sorting interface using DoubleBuffer wrappers
//  below.
template <typename KeyType, typename ValueType>
void SortPairs(const ConstDeviceBufferViewPair<KeyType, ValueType>& src_pair, int num_items,
               DeviceBufferViewPair<KeyType, ValueType>* sorted_pair, DeviceBuffer* workspace,
               DeviceContext* context, bool debug_synchronous = false);
template <typename KeyType, typename ValueType>
size_t GetSortPairsWorkspaceSize(int num_items);

template <typename KeyType, typename ValueType>
void SortPairsDescending(const ConstDeviceBufferViewPair<KeyType, ValueType>& src_pair,
                         int num_items,
                         DeviceBufferViewPair<KeyType, ValueType>* sorted_pair,
                         DeviceBuffer* workspace,
                         DeviceContext* context,
                         bool debug_synchronous = false);

template <typename KeyType, typename ValueType>
size_t GetSortPairsDescendingWorkspaceSize(int num_items);

template <typename ValueType>
void SortKeys(const DeviceBufferView<ValueType>& input,
              int num_items,
              DeviceBufferView<ValueType>* output,
              DeviceBuffer* workspace,
              DeviceContext* context,
              bool debug_synchronous = false);
template <typename ValueType>
size_t GetSortKeysWorkspaceSize(int num_items);

// Given an input sequence d_in having runs of consecutive equal-valued keys, only the first key
// from each run is selectively copied to d_out. The total number of items selected is written to
// d_num_selected_out.
//  - The == equality operator is used to determine whether keys are equivalent
//  - Copies of the selected items are compacted into d_out and maintain their original relative
//  ordering.
template <typename ValueType>
void Unique(const DeviceBufferView<ValueType>& input, DeviceBufferView<ValueType>* output,
            DeviceBufferView<int>* num_unique_value, DeviceBuffer* workspace,
            DeviceContext* context, bool debug_synchronous = false);
template <typename ValueType>
size_t GetUniqueWorkspaceSize(int num_items);

// Computes an intensity histogram from a sequence of data samples using the specified bin boundary
// levels.
//  - The number of histogram bins is (num_levels - 1)
//  - The value range for bin_i is [level[i], level[i+1])
template <typename ValueType>
void HistogramRange(const DeviceBufferView<ValueType>& sample,
                    const DeviceBufferView<ValueType>& levels,
                    DeviceBufferView<int>* histogram, DeviceBuffer* workspace,
                    DeviceContext* context, bool debug_synchronous = false);
template <typename ValueType>
size_t GetHistogramWorkspaceSize(int num_items);

// Computes a device-wide exclusive prefix sum. The value of 0 is applied as the initial value, and
// is assigned to *d_out.
//
//  - Supports non-commutative sum operators.
//  - Provides "run-to-run" determinism for pseudo-associative reduction (e.g., addition of floating
//    point types) on the same GPU device. However, results for pseudo-associative reduction may be
//    inconsistent from one device to a another device of a different compute-capability because CUB
//    can employ different tile-sizing for different architectures.
//  e.g.
//    input: [8, 6,  7,  5,  3,  0,  9]
//    sum  : [0, 8, 14, 21, 26, 29, 29]
template <typename ValueType>
void ExclusiveSum(const DeviceBufferView<ValueType>& input, DeviceBufferView<ValueType>* sum,
                  DeviceBuffer* workspace, DeviceContext* context, bool debug_synchronous = false);
template <typename ValueType>
size_t GetExclusiveSumWorkspaceSize(int num_items);

// Computes a device-wide inclusive prefix sum.
//
//  - Supports non-commutative sum operators.
//  - Provides "run-to-run" determinism for pseudo-associative reduction (e.g., addition of floating
//    point types) on the same GPU device. However, results for pseudo-associative reduction may be
//    inconsistent from one device to a another device of a different compute-capability because CUB
//    can employ different tile-sizing for different architectures.
//  e.g.
//    input: [8, 6,  7,  5,  3,  0,  9]
//    sum  : [8, 14, 21, 26, 29, 29, 38]
template <typename ValueType>
void InclusiveSum(const DeviceBufferView<ValueType>& input,
                  DeviceBufferView<ValueType>* sum,
                  DeviceBuffer* workspace,
                  DeviceContext* context,
                  bool debug_synchronous = false);
template <typename ValueType>
size_t GetInclusiveSumWorkspaceSize(int num_items);

// Uses the flags sequence to split the corresponding items from input into a partitioned sequence
// output. The total number of items copied into the first partition is written to num_selected..
//  e.g.
//    input: [1, 2, 3, 4, 5, 6, 7, 8]
//    flags: [1, 0, 0, 1, 0, 1, 1, 0]
//    output: [1, 4, 6, 7, 8, 5, 3, 2]
//    num_selected: [4]
template <typename ValueType>
void Flagged(const ConstDeviceBufferView<ValueType>& input,
             const ConstDeviceBufferView<int>& flags,
             int num_items,
             DeviceBufferView<ValueType>* output,
             DeviceBufferView<int>* num_selected,
             DeviceBuffer* workspace,
             DeviceContext* context,
             bool debug_synchronous = false);
template <typename ValueType>
size_t GetFlaggedWorkspaceSize(int num_items);

}  // namespace cub_ops
}  // namespace cuda
