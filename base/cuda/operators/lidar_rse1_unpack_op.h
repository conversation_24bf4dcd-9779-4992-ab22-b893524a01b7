// Copyright @2025 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include "base/cuda/device_buffer.h"
#include "base/cuda/device_utils.h"
#include "sensor/lidar/robosense/robosense_data_types.h"

namespace walle {
namespace sensor {
namespace lidar {
namespace robosense {

void UnpackOnGpu(const RSE1Channel* device_rse1_channel,
                 float min_range,
                 float max_range,
                 cuda::DeviceBuffer* device_range_image_buffer,
                 cuda::DeviceContext* device_context);

}  // namespace robosense
}  // namespace lidar
}  // namespace sensor
}  // namespace walle
