// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include <chrono>
#include <numeric>

#include "benchmark/benchmark.h"

#include "base/cuda/device_buffer.h"
#include "base/cuda/device_utils.h"
#include "base/cuda/operators/transpose_op.h"

namespace cuda {
namespace {

void BM_TransposeNaive(benchmark::State& state) {  // NOLINT(runtime/references)
  const int kBatch = 1;
  const int kDimX = state.range(0);
  const int kDimY = state.range(1);
  const int kNumElements = kBatch * kDimX * kDimY;
  std::vector<float> src(kNumElements);
  std::iota(src.begin(), src.end(), 0.0f);

  DeviceContext ctx;
  DeviceTimer timer(ctx);
  DeviceBuffer src_dev_buf(kNumElements * sizeof(float));
  DeviceBuffer dst_dev_buf(kNumElements * sizeof(float));
  src_dev_buf.FromCpu(src.data(), kNumElements * sizeof(float), &ctx);

  // benchmark
  while (state.KeepRunning()) {
    Transpose2DNaiveWrapper(
      ctx.stream(),
      kBatch,
      kDimX,
      kDimY,
      reinterpret_cast<const float*>(src_dev_buf.data()),
      reinterpret_cast<float*>(dst_dev_buf.mutable_data()));
    ctx.Sync();
  }
}

void BM_TransposeTiled(benchmark::State& state) {  // NOLINT(runtime/references)
  const int kBatch = 1;
  const int kDimX = state.range(0);
  const int kDimY = state.range(1);
  const int kNumElements = kBatch * kDimX * kDimY;
  std::vector<float> src(kNumElements);
  std::iota(src.begin(), src.end(), 0.0f);

  DeviceContext ctx;
  DeviceTimer timer(ctx);
  DeviceBuffer src_dev_buf(kNumElements * sizeof(float));
  DeviceBuffer dst_dev_buf(kNumElements * sizeof(float));
  src_dev_buf.FromCpu(src.data(), kNumElements * sizeof(float), &ctx);

  // benchmark
  while (state.KeepRunning()) {
    Transpose2DTiledWrapper(
      ctx.stream(),
      kBatch,
      kDimX,
      kDimY,
      reinterpret_cast<const float*>(src_dev_buf.data()),
      reinterpret_cast<float*>(dst_dev_buf.mutable_data()));
    ctx.Sync();
  }
}

void BM_Transpose(benchmark::State& state) {  // NOLINT(runtime/references)
  const int kBatch = 1;
  const int kDimX = state.range(0);
  const int kDimY = state.range(1);
  const int kNumElements = kBatch * kDimX * kDimY;
  std::vector<float> src(kNumElements);
  std::iota(src.begin(), src.end(), 0.0f);

  DeviceContext ctx;
  DeviceTimer timer(ctx);
  DeviceBuffer src_dev_buf(kNumElements * sizeof(float));
  DeviceBuffer dst_dev_buf(kNumElements * sizeof(float));
  src_dev_buf.FromCpu(src.data(), kNumElements * sizeof(float), &ctx);

  // benchmark
  while (state.KeepRunning()) {
    Transpose2DWrapper(
      ctx.stream(),
      kBatch,
      kDimX,
      kDimY,
      reinterpret_cast<const float*>(src_dev_buf.data()),
      reinterpret_cast<float*>(dst_dev_buf.mutable_data()));
    ctx.Sync();
  }
}

}  // namespace

BENCHMARK(BM_TransposeNaive)->Unit(benchmark::kMicrosecond)
  ->RangeMultiplier(4)->Ranges({{1e6, 1e6}, {1, 128}})
  ->Args({1920 * 1080, 3});

BENCHMARK(BM_TransposeTiled)->Unit(benchmark::kMicrosecond)
  ->RangeMultiplier(4)->Ranges({{1e6, 1e6}, {1, 128}})
  ->Args({1920 * 1080, 3});

BENCHMARK(BM_Transpose)->Unit(benchmark::kMicrosecond)
  ->RangeMultiplier(4)->Ranges({{1e6, 1e6}, {1, 128}})
  ->Args({1920 * 1080, 3});

}  // namespace cuda

BENCHMARK_MAIN();

/*
Run on (16 X 5000 MHz CPU s)
CPU Caches:
  L1 Data 32K (x8)
  L1 Instruction 32K (x8)
  L2 Unified 256K (x8)
  L3 Unified 16384K (x1)
Load Average: 0.36, 0.22, 0.10
------------------------------------------------------------------------
Benchmark                              Time             CPU   Iterations
------------------------------------------------------------------------
BM_TransposeNaive/1000000/1         21.8 us         21.8 us        27203
BM_TransposeNaive/1000000/4         71.5 us         71.5 us         9861
BM_TransposeNaive/1000000/16         327 us          327 us         2146
BM_TransposeNaive/1000000/64        4837 us         4837 us          145
BM_TransposeNaive/1000000/128      10266 us        10267 us           68
BM_TransposeNaive/2073600/3          108 us          108 us         6532

BM_TransposeTiled/1000000/1         93.3 us         93.3 us         7530
BM_TransposeTiled/1000000/4          106 us          106 us         6655
BM_TransposeTiled/1000000/16         246 us          246 us         2841
BM_TransposeTiled/1000000/64         984 us          984 us          712
BM_TransposeTiled/1000000/128       2005 us         2006 us          350
BM_TransposeTiled/2073600/3          209 us          209 us         3363

BM_Transpose/1000000/1              21.6 us         21.6 us        32064
BM_Transpose/1000000/4              71.9 us         71.9 us         9804
BM_Transpose/1000000/16              247 us          247 us         2841
BM_Transpose/1000000/64              984 us          984 us          710
BM_Transpose/1000000/128            2008 us         2008 us          349
BM_Transpose/2073600/3               109 us          109 us         6481
*/
