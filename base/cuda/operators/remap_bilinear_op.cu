// Copyright @2023 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/cuda/operators/remap_bilinear_op.h"

#include "base/cuda/macros.h"

namespace cuda {

/**
 * task ~ output volume
 * tensor format: NHWC
 */

template <typename T0, typename T1>
__global__ void remap_bilinear_kernel(int n,
                                      int in_height,
                                      int in_width,
                                      int out_height,
                                      int out_width,
                                      int channels,
                                      const float* map_x,
                                      const float* map_y,
                                      const T0* idata,
                                      T1* odata) {
  int tid = threadIdx.x + blockIdx.x * blockDim.x;
  if (tid >= n) return;
  int idx = tid;
  const int c = idx % channels;
  idx /= channels;
  const int x = idx % out_width;
  idx /= out_width;
  const int y = idx % out_height;
  const int b = idx / out_height;
  const int map_idx = (b * out_height + y) * out_width + x;

  const float in_y = map_y[map_idx];
  const float in_x = map_x[map_idx];
  if (in_y < 0 || in_y > (in_height - 1) || in_x < 0 || in_x > (in_width - 1)) {
    odata[tid] = 0;
    return;
  }
  const int top_y_index = in_y > 0.0f ? floorf(in_y) : 0;
  const int bottom_y_index = (in_y < in_height - 1) ? ceilf(in_y) : in_height - 1;
  const float y_lerp = in_y - floorf(in_y);

  const int left_x_index = in_x > 0.0f ? floorf(in_x) : 0;
  const int right_x_index = (in_x < in_width - 1) ? ceilf(in_x) : in_width - 1;
  const float x_lerp = in_x - left_x_index;

  const float top_left =
      idata[((b * in_height + top_y_index) * in_width + left_x_index) * channels + c];
  const float top_right =
      idata[((b * in_height + top_y_index) * in_width + right_x_index) * channels + c];
  const float bottom_left =
      idata[((b * in_height + bottom_y_index) * in_width + left_x_index) * channels + c];
  const float bottom_right =
      idata[((b * in_height + bottom_y_index) * in_width + right_x_index) * channels + c];

  const float top = top_left + (top_right - top_left) * x_lerp;
  const float bottom = bottom_left + (bottom_right - bottom_left) * x_lerp;
  odata[tid] = static_cast<T1>(top + (bottom - top) * y_lerp);
  return;
}

template <typename T0, typename T1>
void RemapBilinearWrapper(cudaStream_t stream,
                          int batch_size,
                          int input_height,
                          int input_width,
                          int output_height,
                          int output_width,
                          int num_channels,
                          const float* map_x,
                          const float* map_y,
                          const T0* idata,
                          T1* odata) {
  int output_volume = batch_size * output_height * output_width * num_channels;
  constexpr int kNumThreads = 512;
  int num_blocks = (output_volume + kNumThreads - 1) / kNumThreads;
  remap_bilinear_kernel<T0, T1><<<num_blocks, kNumThreads, 0, stream>>>(output_volume,
                                                                        input_height,
                                                                        input_width,
                                                                        output_height,
                                                                        output_width,
                                                                        num_channels,
                                                                        map_x,
                                                                        map_y,
                                                                        idata,
                                                                        odata);
  CUDA_KERNEL_LAUNCH_CHECK();
}

template void RemapBilinearWrapper(cudaStream_t stream,
                                   int batch_size,
                                   int input_height,
                                   int input_width,
                                   int output_height,
                                   int output_width,
                                   int num_channels,
                                   const float* map_x,
                                   const float* map_y,
                                   const uint8_t* idata,
                                   uint8_t* odata);

}  // namespace cuda
