// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <algorithm>
#include <memory>
#include <string>
#include <utility>

#include <cuda_runtime.h>
#include "glog/logging.h"

namespace cuda {

template <typename T0, typename T1>
void ResizeBilinearWrapper(
  cudaStream_t stream, int batch_size, int input_height, int input_width,
  int output_height, int output_width, int num_channels, const T0* idata, T1* odata);

}  // namespace cuda
