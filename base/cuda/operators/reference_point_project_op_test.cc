// Copyright @2023 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include <numeric>
#include <vector>

#include "gtest/gtest.h"

#include "base/cuda/device_buffer.h"
#include "base/cuda/device_utils.h"
#include "base/cuda/operators/reference_point_project_op.h"

namespace cuda {
namespace {

TEST(ReferencePointProjectOpTest, BasicTest) {
  const float min_x = -5.0f;
  const float max_x = 5.0f;
  const float min_y = -5.0f;
  const float max_y = 5.0f;
  const float min_z = -4.0f;
  const float max_z = 2.0f;
  const float resolution_x = 0.5f;
  const float resolution_y = 0.5f;
  const float resolution_z = 0.5f;
  const int num_sampled_points_in_pillar = 4;
  const int num_camera = 2;
  const std::vector<uint8_t> is_camera_valid(num_camera, 1);
  const std::vector<int> image_width(num_camera, 1024);
  const std::vector<int> image_height(num_camera, 576);
  const std::vector<uint8_t> is_fisheye(num_camera, 1);
  const std::vector<float> transform_matrix{1.0f, 0.0f, 0.0f, 0.0f, 0.0f, 1.0f, 0.0f, 0.0f,
                                            0.0f, 0.0f, 1.0f, 0.0f, 0.0f, 0.0f, 0.0f, 1.0f,
                                            1.0f, 0.0f, 0.0f, 0.0f, 0.0f, 1.0f, 0.0f, 0.0f,
                                            0.0f, 0.0f, 1.0f, 0.0f, 0.0f, 0.0f, 0.0f, 1.0f};
  const std::vector<float> intrinsic_matrix(9 * num_camera, 0.0f);
  const int grid_width = static_cast<int>((max_x - min_x) / resolution_x);
  const int grid_length = static_cast<int>((max_y - min_y) / resolution_y);
  const float grid_center_x = grid_width * (std::abs(min_x) / (max_x - min_x));
  const float grid_center_y = grid_length * (std::abs(min_y) / (max_y - min_y));
  const float z_step = (max_z - min_z - resolution_z) / (num_sampled_points_in_pillar - 1);
  std::vector<float> z_value(num_sampled_points_in_pillar);
  for (int i = 0; i < num_sampled_points_in_pillar; ++i) {
    z_value[i] = min_z + i * z_step;
  }
  const int output_volume = num_camera * grid_width * grid_length * num_sampled_points_in_pillar;
  std::vector<float> projection_result(output_volume * 2, 0);
  std::vector<uint8_t> mask(output_volume, 0);
  DeviceContext ctx;
  DeviceBuffer device_is_camera_valid(is_camera_valid.size() * sizeof(bool));
  DeviceBuffer device_image_width(image_width.size() * sizeof(int));
  DeviceBuffer device_image_height(image_height.size() * sizeof(int));
  DeviceBuffer device_is_fisheye(is_fisheye.size() * sizeof(bool));
  DeviceBuffer device_z_value(z_value.size() * sizeof(float));
  DeviceBuffer device_intrinsic_matrix(intrinsic_matrix.size() * sizeof(float));
  DeviceBuffer device_transform_matrix(transform_matrix.size() * sizeof(float));
  DeviceBuffer device_projection_result(projection_result.size() * sizeof(float));
  DeviceBuffer device_mask(mask.size() * sizeof(bool));
  device_z_value.FromCpu(z_value.data(), z_value.size() * sizeof(float), &ctx);
  device_intrinsic_matrix.FromCpu(
      intrinsic_matrix.data(), intrinsic_matrix.size() * sizeof(float), &ctx);
  device_transform_matrix.FromCpu(
      transform_matrix.data(), transform_matrix.size() * sizeof(float), &ctx);
  ReferencePointProjectWrapper(ctx.stream(),
                               1,
                               grid_width,
                               grid_length,
                               resolution_x,
                               resolution_y,
                               grid_center_x,
                               grid_center_y,
                               num_sampled_points_in_pillar,
                               num_camera,
                               reinterpret_cast<const bool*>(device_is_camera_valid.data()),
                               reinterpret_cast<const int*>(device_image_width.data()),
                               reinterpret_cast<const int*>(device_image_height.data()),
                               reinterpret_cast<const bool*>(device_is_fisheye.data()),
                               reinterpret_cast<const float*>(device_z_value.data()),
                               reinterpret_cast<const float*>(device_intrinsic_matrix.data()),
                               reinterpret_cast<const float*>(device_transform_matrix.data()),
                               reinterpret_cast<float*>(device_projection_result.mutable_data()),
                               reinterpret_cast<bool*>(device_mask.mutable_data()));
  device_projection_result.ToCpu(projection_result.data(), device_projection_result.size(), &ctx);
  device_mask.ToCpu(mask.data(), device_mask.size(), &ctx);
  ctx.Sync();
  std::vector<int> expected_projection_result(output_volume * 2, 0);
  std::vector<int> expected_mask(output_volume, 0);
  for (size_t i = 0; i < output_volume; ++i) {
    ASSERT_FLOAT_EQ(projection_result[i * 2], expected_projection_result[i]);
    ASSERT_FLOAT_EQ(projection_result[i * 2 + 1], expected_projection_result[i * 2 + 1]);
    ASSERT_FLOAT_EQ(mask[i], expected_mask[i]);
  }
}

}  // namespace
}  // namespace cuda
