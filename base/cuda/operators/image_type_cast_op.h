// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <algorithm>
#include <memory>
#include <string>
#include <utility>

#include <cuda_runtime.h>
#include "glog/logging.h"

namespace cuda {

template <typename T0, typename T1>
void ImageTypeCastWrapper(
  cudaStream_t stream, int batch_size, int image_height, int image_width,
  int num_channels, float convert_factor, const T0* idata, T1* odata);

}  // namespace cuda
