// Copyright @2022 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <algorithm>
#include <memory>
#include <string>
#include <utility>

#include <cuda_runtime.h>
#include "glog/logging.h"

#include "base/cuda/device_utils.h"
#include "base/cuda/tensor.h"

namespace cuda {

/*
 * MaskImageInplace
 * Given an image and a list of box coordinates,
 * set image pixel in boxes to (mask_value, mask_value, mask_value).
 *
 * inputs
 *   - num_mask_boxes: the number of mask_boxes.
 *   - mask_value: pixel value to be masked on device image.
 *   - mask_boxes: 2D tensor [max_num_boxes, 4],
 *   - image_tensor: 3D tensor [in_height, in_width, channels],
 * outputs
 *   - image_tensor: 3D tensor [in_height, in_width, channels].
 *
 */
template <typename T>
void MaskImageInplace(cudaStream_t stream,
                      int num_mask_box,
                      int mask_value,
                      const ConstDeviceTensorView<float, 2>& mask_boxes,
                      DeviceTensorView<T, 3>* image_tensor);

}  // namespace cuda
