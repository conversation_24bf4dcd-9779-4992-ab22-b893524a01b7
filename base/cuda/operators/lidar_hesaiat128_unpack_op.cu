// Copyright @2025 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/cuda/operators/lidar_hesaiat128_unpack_op.h"

#include <cuda_runtime.h>
#include <cmath>

#include "base/cuda/macros.h"
#include "sensor/lidar/data_types_device.h"
#include "sensor/lidar/hesai/hesai_data_types.h"

namespace walle {
namespace sensor {
namespace lidar {
namespace hesai {
namespace {

constexpr double kDegreeToRadian = M_PI / 180.0;
constexpr int kNumOfBitsInOneByte = 8;

}  // namespace

__device__ double Lerp(double left, double right, double r) { return left + r * (right - left); }

__device__ double GetAdjust(const double* adjust_angle_lookup_table, int laser_id, double azimuth) {
  const double half_azimuth = azimuth * 0.5;
  const int left_id = static_cast<int>(floorf(half_azimuth));
  const int right_id = (left_id == kHesaiAT128AngleAdjustColumns - 1) ? 0 : left_id + 1;
  const double r = half_azimuth - floorf(half_azimuth);
  const int index_left = laser_id * kHesaiAT128AngleAdjustColumns + left_id;
  const int index_right = laser_id * kHesaiAT128AngleAdjustColumns + right_id;
  return Lerp(adjust_angle_lookup_table[index_left], adjust_angle_lookup_table[index_right], r);
}

__device__ double horizontal_angle(const double* start_azimuth_per_frame,
                                   const double* horizontal_angle_offset,
                                   const double* horizontal_angle_adjust,
                                   int horizontal_azimuth,
                                   int vertical_laser_id) {
  const double azimuth = horizontal_azimuth / kHesaiAT128AzimuthResolutionUnit;
  int frame_id = 0;
  if ((start_azimuth_per_frame[0] > azimuth) || (start_azimuth_per_frame[2] <= azimuth)) {
    frame_id = 2;
  } else if (start_azimuth_per_frame[1] <= azimuth) {
    frame_id = 1;
  }
  const double adjust = GetAdjust(horizontal_angle_adjust, vertical_laser_id, azimuth);
  return kDegreeToRadian * ((azimuth - start_azimuth_per_frame[frame_id]) * 2) -
         horizontal_angle_offset[vertical_laser_id] + adjust;
}

__device__ double vertical_angle(const double* vertical_angle_offset,
                                 const double* vertical_angle_adjust,
                                 int horizontal_azimuth,
                                 int vertical_laser_id) {
  const double azimuth = horizontal_azimuth / kHesaiAT128AzimuthResolutionUnit;
  return vertical_angle_offset[vertical_laser_id] +
         GetAdjust(vertical_angle_adjust, vertical_laser_id, azimuth);
}

__device__ float3 ComputeLocalPosition(const double* start_azimuth_per_frame,
                                       const double* end_azimuth_per_frame,
                                       const double* vertical_angle_offset,
                                       const double* horizontal_angle_offset,
                                       const double* vertical_angle_adjust,
                                       const double* horizontal_angle_adjust,
                                       int laser_id,
                                       int rotation,
                                       double distance) {
  const double h_angle = horizontal_angle(start_azimuth_per_frame,
                                          horizontal_angle_offset,
                                          horizontal_angle_adjust,
                                          rotation,
                                          laser_id);
  const double v_angle =
      vertical_angle(vertical_angle_offset, vertical_angle_adjust, rotation, laser_id);
  const double xy_distance = distance * cos(v_angle);
  const double x = xy_distance * sin(h_angle);
  const double y = xy_distance * cos(h_angle);
  const double z = distance * sin(v_angle);
  // Origin AT128 coordinate is: X-Front, Y-Left, Z-Top.
  // We change it to Hesai64 coordinate: X-Left, Y-Back, Z-Top.
  // https://km.sankuai.com/collabpage/546000932
  return make_float3(y, -x, z);
}

__global__ void UnpackKernel(const HesaiAT128RawBlock* blocks,
                             const uint8_t* dis_units,
                             const double* start_azimuth_per_frame,
                             const double* end_azimuth_per_frame,
                             const double* vertical_angle_offset,
                             const double* horizontal_angle_offset,
                             const double* vertical_angle_adjust,
                             const double* horizontal_angle_adjust,
                             float min_range,
                             float max_range,
                             lidar::Pixel* pixels) {
  const int angle_idx = blockIdx.x;
  const int laser_id = threadIdx.x;
  if (angle_idx >= kHesaiAT128NumScanPerRotation || laser_id >= kHesaiAT128LaserCount) {
    return;
  }

  const HesaiAT128RawBlock& block = blocks[blockIdx.x];
  const HesaiAT128RawUnit& unit = block.unit[laser_id];
  const uint8_t dis_unit = dis_units[blockIdx.x];
  if (dis_unit <= 0) {
    printf("* dist unit value error:%d,%d,%d ", laser_id, angle_idx, dis_unit);
  }

  const double distance = static_cast<double>(unit.distance) * dis_unit * 0.001;
  if (distance < min_range || distance > max_range) {
    return;
  }
  const int rotation = (block.azimuth << kNumOfBitsInOneByte | block.fine_azimuth);
  const float3 position = ComputeLocalPosition(start_azimuth_per_frame,
                                               end_azimuth_per_frame,
                                               vertical_angle_offset,
                                               horizontal_angle_offset,
                                               vertical_angle_adjust,
                                               horizontal_angle_adjust,
                                               laser_id,
                                               rotation,
                                               distance);

  const int index = laser_id + angle_idx * kHesaiAT128LaserCount;
  pixels[index].position[0] = position.x;
  pixels[index].position[1] = position.y;
  pixels[index].position[2] = position.z;
  pixels[index].intensity = unit.reflectivity;
}

void UnpackOnGpu(const cuda::DeviceBuffer& lidar_blocks_buffer,
                             const cuda::DeviceBuffer& lidar_dis_unit_buffer,
                             const cuda::DeviceBuffer& start_azimuth_per_frame_buffer,
                             const cuda::DeviceBuffer& end_azimuth_per_frame_buffer,
                             const cuda::DeviceBuffer& vertical_angle_offset_buffer,
                             const cuda::DeviceBuffer& horizontal_angle_offset_buffer,
                             const cuda::DeviceBuffer& vertical_angle_adjust_buffer,
                             const cuda::DeviceBuffer& horizontal_angle_adjust_buffer,
                             float min_range,
                             float max_range,
                             cuda::DeviceBuffer* device_range_image_buffer,
                             cuda::DeviceContext* device_context) {
  const HesaiAT128RawBlock* blocks =
      static_cast<const HesaiAT128RawBlock*>(lidar_blocks_buffer.data());
  const uint8_t* dis_units = static_cast<const uint8_t*>(lidar_dis_unit_buffer.data());

  const double* start_azimuth_per_frame =
      static_cast<const double*>(start_azimuth_per_frame_buffer.data());
  const double* end_azimuth_per_frame =
      static_cast<const double*>(end_azimuth_per_frame_buffer.data());
  const double* vertical_angle_offset =
      static_cast<const double*>(vertical_angle_offset_buffer.data());
  const double* horizontal_angle_offset =
      static_cast<const double*>(horizontal_angle_offset_buffer.data());
  const double* vertical_angle_adjust =
      static_cast<const double*>(vertical_angle_adjust_buffer.data());
  const double* horizontal_angle_adjust =
      static_cast<const double*>(horizontal_angle_adjust_buffer.data());

  device_range_image_buffer->Memset(0, device_context);
  lidar::Pixel* pixels = static_cast<lidar::Pixel*>(device_range_image_buffer->mutable_data());

  dim3 blockDim(kHesaiAT128LaserCount);
  dim3 gridDim(kHesaiAT128NumScanPerRotation);

  UnpackKernel<<<gridDim, blockDim, 0, device_context->stream()>>>(blocks,
                                                                   dis_units,
                                                                   start_azimuth_per_frame,
                                                                   end_azimuth_per_frame,
                                                                   vertical_angle_offset,
                                                                   horizontal_angle_offset,
                                                                   vertical_angle_adjust,
                                                                   horizontal_angle_adjust,
                                                                   min_range,
                                                                   max_range,
                                                                   pixels);
  CUDA_KERNEL_LAUNCH_CHECK();
}

}  // namespace hesai
}  // namespace lidar
}  // namespace sensor
}  // namespace walle
