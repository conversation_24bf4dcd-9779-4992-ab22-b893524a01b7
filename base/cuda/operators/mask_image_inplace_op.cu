// Copyright @2022 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/cuda/operators/mask_image_inplace_op.h"

namespace cuda {
namespace {

constexpr int kNumThreads = 512;

}  // namespace

template <typename T>
__global__ void mask_image_inplace_kernel(int num_pixel,
                                          int num_mask_box,
                                          int image_height,
                                          int image_width,
                                          int channels,
                                          int mask_value,
                                          const float* mask_boxes,
                                          T* image) {
  const int pixel_idx = threadIdx.x + blockIdx.x * blockDim.x;
  if (pixel_idx >= num_pixel) {
    return;
  }
  const int x = pixel_idx % image_width;
  const int y = pixel_idx / image_width % image_height;
  if (x < 0 || x > image_width || y < 0 || y > image_height) {
    return;
  }
  for (int box_idx = 0; box_idx < num_mask_box; ++box_idx) {
    const float x0 = mask_boxes[box_idx * 4 + 0];
    const float y0 = mask_boxes[box_idx * 4 + 1];
    const float x1 = mask_boxes[box_idx * 4 + 2];
    const float y1 = mask_boxes[box_idx * 4 + 3];
    if (x >= x0 && x < x1 && y >= y0 && y < y1) {
      for (int ic = 0; ic < channels; ++ic) {
        image[channels * pixel_idx + ic] = static_cast<T>(mask_value);
      }
      return;
    }
  }
}

template <typename T>
void MaskImageInplace(cudaStream_t stream,
                      int num_mask_box,
                      int mask_value,
                      const ConstDeviceTensorView<float, 2>& mask_boxes,
                      DeviceTensorView<T, 3>* image) {
  // inputs
  CHECK(image != nullptr);
  CHECK_EQ(num_mask_box, mask_boxes.Dimension(0));
  const int image_height = image->Dimension(0);
  const int image_width = image->Dimension(1);
  const int channels = image->Dimension(2);
  const int output_volume = image_height * image_width;
  const int num_blocks = DivUp(output_volume, kNumThreads);
  mask_image_inplace_kernel<T><<<num_blocks, kNumThreads, 0, stream>>>(output_volume,
                                                                       num_mask_box,
                                                                       image_height,
                                                                       image_width,
                                                                       channels,
                                                                       mask_value,
                                                                       mask_boxes.raw_data(),
                                                                       image->mutable_raw_data());
  CUDA_KERNEL_LAUNCH_CHECK();
}

#define MASK_IMAGE_INPLACE(T)        \
  template void MaskImageInplace<T>( \
      cudaStream_t, int, int, const ConstDeviceTensorView<float, 2>&, DeviceTensorView<T, 3>*);
MASK_IMAGE_INPLACE(uint8_t)
MASK_IMAGE_INPLACE(float)
#undef MASK_IMAGE_INPLACE

}  // namespace cuda
