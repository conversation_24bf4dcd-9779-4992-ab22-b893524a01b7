// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>
//          <PERSON> (<EMAIL>)
//          <PERSON><PERSON> (<EMAIL>)

#pragma once

#include <vector>

#include "glog/logging.h"

#include "base/cuda/device_utils.h"
#include "base/cuda/tensor.h"

namespace cuda {

/*
 * CropResizePadding
 * Given a list of images, a list of box coordinates and a mapping from box idx to image idx,
 * apply each box crop on corresponding image & resize image keep aspect ratio & padding
 * keep the image in the top-left corner
 *
 * inputs
 *   - tensorlist: 3D tensor list, each tensor _x has shape [_x_height, _x_width, channels],
 *   - boxes: 2D tensor [max_num_boxes, 4],
 *   - boxes_idx: 1D tensor [max_num_boxes, 1],
 *   - num_boxes is allowed to be less than max_num_boxes.
 *   - is_padding: whether to padding output images
 *   - is_channel_first: output NCHW data if true, else NHWC
 * outputs
 *   - crops: 4D tensor [max_num_boxes, out_height, out_width, channels] or
 *                      [max_num_boxes, channels, out_height, out_width].
 */
template <typename T0, typename T1>
void CropResizePadding(const std::vector<ConstDeviceTensorView<T0, 3>>& tensorlist,
                       const ConstDeviceTensorView<float, 2>& boxes,
                       const ConstDeviceTensorView<int, 1>& boxes_idx,
                       cudaStream_t stream,
                       int num_boxes,
                       bool is_padding,
                       bool is_channel_first,
                       DeviceTensorView<T1, 4>* crops);

}  // namespace cuda
