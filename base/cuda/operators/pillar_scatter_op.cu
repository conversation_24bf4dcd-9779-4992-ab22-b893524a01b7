// Copyright @2023 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>
//          <PERSON> (<EMAIL>)

#include "base/cuda/operators/pillar_scatter_op.h"

#include <algorithm>

#include <cuda_fp16.h>
#include <cuda_runtime_api.h>
#include "glog/logging.h"

#include "base/cuda/macros.h"

namespace cuda {
namespace {

constexpr int kNumThreadsNHWC = 512;

template <typename Element>
int GetPillarsPerBlock(int max_points) {
  constexpr int kMaxThreadNum = 512;
  const int min_blocks = (max_points + kMaxThreadNum - 1) / kMaxThreadNum;
  return min_blocks < kMaxThreadNum / 8 ? kMaxThreadNum / 2 : kMaxThreadNum;
}

template <typename Element>
struct PackElement;

template <>
struct PackElement<float> {
  typedef uint4 Element4;
};

template <>
struct PackElement<half> {
  typedef ushort4 Element4;
};

}  // namespace

template <typename Element, int kPillarFeatureSize>
__global__ void PillarScatterNHWCKernel(const Element* pillar_features_data,
                                        const int* coords_data,
                                        const int* pillar_num_data,
                                        int feature_x,
                                        int feature_y,
                                        Element* spatial_feature_data) {
  __shared__ int pillar_offsets[kNumThreadsNHWC];
  const int num_pillars = pillar_num_data[0];
  {
    const int pillar_index = blockIdx.x * blockDim.x + threadIdx.x;
    int feat_offset = 0;
    if (pillar_index < num_pillars) {
      const int y = coords_data[pillar_index * 4 + 2];
      const int x = coords_data[pillar_index * 4 + 3];
      feat_offset = (y * feature_x + x) * kPillarFeatureSize * sizeof(Element);
    };
    pillar_offsets[threadIdx.x] = feat_offset;
  }
  __syncthreads();
  {
    typedef int4 PackedUnit;
    constexpr int kFeatureSizePacked = kPillarFeatureSize * sizeof(Element) / sizeof(PackedUnit);
    const int num_threads = blockDim.x;
    const int block_offset = blockDim.x * blockIdx.x;
    const PackedUnit* const in_feat = reinterpret_cast<const PackedUnit*>(pillar_features_data) +
                                      block_offset * kFeatureSizePacked;
    const int max_idx = min(num_threads, num_pillars - block_offset) * kFeatureSizePacked;
    const int channel = threadIdx.x % kFeatureSizePacked;
    for (int idx = threadIdx.x; idx < max_idx; idx += blockDim.x) {
      const int local_pillar_index = idx / kFeatureSizePacked;
      const int feat_offset = pillar_offsets[local_pillar_index];
      int8_t* const out_feat = reinterpret_cast<int8_t*>(spatial_feature_data) + feat_offset;
      reinterpret_cast<PackedUnit*>(out_feat)[channel] = in_feat[idx];
    }
  }
}

template <typename Element, int kPillarFeatureSize>
__global__ void PillarScatterNCHWKernel(const Element* pillar_features_data,
                                        const int* coords_data,
                                        const int* pillar_num_data,
                                        int feature_x,
                                        int feature_y,
                                        Element* spatial_feature_data) {
  typedef typename PackElement<Element>::Element4 PackedUnit;
  typedef decltype(PackedUnit::x) SingleUnit;

  const int num_pillars = pillar_num_data[0];
  const int pillar_index = blockIdx.x * blockDim.x + threadIdx.x;
  if (pillar_index >= num_pillars) {
    return;
  };
  const int4 coord = ((const int4*)coords_data)[pillar_index];
  const int x = coord.w;
  const int y = coord.z;

  constexpr int kPackageNum = kPillarFeatureSize / 4;
  PackedUnit cache_line[kPackageNum];
  const PackedUnit* pillar_feature_line =
      reinterpret_cast<const PackedUnit*>(pillar_features_data + pillar_index * kPillarFeatureSize);
  for (int i = 0; i < kPackageNum; i++) {
    cache_line[i] = pillar_feature_line[i];
  }
  SingleUnit* dest = reinterpret_cast<SingleUnit*>(spatial_feature_data);
  for (int i = 0; i < kPackageNum; i++) {
    dest[(i * 4 + 0) * feature_y * feature_x + y * feature_x + x] = cache_line[i].x;
    dest[(i * 4 + 1) * feature_y * feature_x + y * feature_x + x] = cache_line[i].y;
    dest[(i * 4 + 2) * feature_y * feature_x + y * feature_x + x] = cache_line[i].z;
    dest[(i * 4 + 3) * feature_y * feature_x + y * feature_x + x] = cache_line[i].w;
  }
}

template <typename Element>
size_t PillarScatterWrapper(cudaStream_t stream,
                            const Element* pillar_features_data,
                            const int* coords_data,
                            const int* pillar_num_data,
                            int batch_size,
                            int max_pillar_num,
                            int num_features,
                            int feature_x,
                            int feature_y,
                            bool output_nhwc,
                            Element* spatial_feature_data) {
  if (num_features != 32 && num_features != 64 && num_features != 128) {
    LOG(FATAL) << "unsupported num_features: " << num_features;
    return 2;
  }
  const int max_points = std::min(feature_x * feature_y, max_pillar_num);
  const int num_threads = output_nhwc ? kNumThreadsNHWC : GetPillarsPerBlock<Element>(max_points);
  const dim3 blocks((max_points + num_threads - 1) / num_threads);
  const dim3 threads(num_threads);
  for (int b = 0; b < batch_size; ++b) {
#define Kernel(FeatureSize, Layout)                                                    \
  PillarScatter##Layout##Kernel<Element, FeatureSize><<<blocks, threads, 0, stream>>>( \
      pillar_features_data + b * max_pillar_num * num_features,                        \
      coords_data + b * max_pillar_num * 4,                                            \
      pillar_num_data + b,                                                             \
      feature_x,                                                                       \
      feature_y,                                                                       \
      spatial_feature_data + b * num_features * feature_x * feature_y);                \
      CUDA_KERNEL_LAUNCH_CHECK()

    if (num_features == 32) {
      if (output_nhwc) {
        Kernel(32, NHWC);
      } else {
        Kernel(32, NCHW);
      }
    } else if (num_features == 64) {
      if (output_nhwc) {
        Kernel(64, NHWC);
      } else {
        Kernel(64, NCHW);
      }
    } else {
      if (output_nhwc) {
        Kernel(128, NHWC);
      } else {
        Kernel(128, NCHW);
      }
    }
#undef Kernel
  }
  return 0;
}

template size_t PillarScatterWrapper<half>(cudaStream_t stream,
                                           const half* pillar_features_data,
                                           const int* coords_data,
                                           const int* pillar_num_data,
                                           int batch_size,
                                           int max_pillar_num,
                                           int num_features,
                                           int feature_x,
                                           int feature_y,
                                           bool output_nhwc,
                                           half* spatial_feature_data);

template size_t PillarScatterWrapper<float>(cudaStream_t stream,
                                            const float* pillar_features_data,
                                            const int* coords_data,
                                            const int* pillar_num_data,
                                            int batch_size,
                                            int max_pillar_num,
                                            int num_features,
                                            int feature_x,
                                            int feature_y,
                                            bool output_nhwc,
                                            float* spatial_feature_data);

}  // namespace cuda
