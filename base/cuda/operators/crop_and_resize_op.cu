// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/cuda/operators/crop_and_resize_op.h"

#include <cuda_fp16.h>

namespace cuda {
namespace {

constexpr int kNumLoop = 4;
constexpr int kNumThreads = 128;

}  // namespace
/**
 * Crop and resize according to given boxes and boxes_idx.
 *
 * task ~ num_boxes * out_height * out_width * channels
 * tensor format: NHWC
 * use half pixel center
 * box format: top-left & bottom-right vertex (reverse to flip image)
 * invalid box will be processed as black image
 * 
 * inputs
 *     idata: [num_images, in_height, in_width, channels]
 *     boxes: [num_boxes, 4], box coordinate (x0, y0, x1, y1) on idata
 *     boxes_idx: [num_boxes, 1], stores the image idx of each box
 * output
 *     odata: [num_boxes, out_height, out_width, channels]
 */

template <typename T>
__device__ const T* get_image_ptr(const T* const* idata, int image_id, int image_size) {
  return idata[image_id];
}

template <typename T>
__device__ const T* get_image_ptr(const T* idata, int image_id, int image_size) {
  return idata + image_id * image_size;
}

template <typename T0, typename T1>
__global__ void crop_and_resize_kernel(
  int num_task, int num_images, int in_height, int in_width,
  int out_height, int out_width, int channels, int num_boxes,
  const T0* idata, const float* boxes, const int* boxes_idx, T1* odata) {
  for (int tid = threadIdx.x + blockIdx.x * blockDim.x; tid < num_task; tid += blockDim.x * gridDim.x) {
    int idx = tid;
    const float x = idx % out_width;
    idx /= out_width;
    const float y = idx % out_height;
    const int b = idx / out_height;
    const int image_id = boxes_idx[b];
    if (image_id < 0 || image_id >= num_images) {
#pragma unroll
      for (int c = 0; c < channels; c++) {
        odata[channels * tid + c] = static_cast<T1>(0);
      }
      continue;
    }
    const float x0 = boxes[b * 4 + 0];
    const float y0 = boxes[b * 4 + 1];
    const float x1 = boxes[b * 4 + 2];
    const float y1 = boxes[b * 4 + 3];
    const float height_scale = (y1 - y0) / out_height;
    const float width_scale = (x1 - x0) / out_width;
    const float in_y = y0 + (y + 0.5f) * height_scale - 0.5f;
    const float in_x = x0 + (x + 0.5f) * width_scale - 0.5f;
    if (in_y < 0 || in_y > in_height - 1 || in_x < 0 || in_x > in_width - 1) {
#pragma unroll
      for (int c = 0; c < channels; c++) {
        odata[channels * tid + c] = static_cast<T1>(0);
      }
      continue;
    }
    const int top_y_index = floorf(in_y);
    const int bottom_y_index = ceilf(in_y);
    const float y_lerp = in_y - top_y_index;
    const int left_x_index = floorf(in_x);
    const int right_x_index = ceilf(in_x);
    const float x_lerp = in_x - left_x_index;
    const int top_left_index = (top_y_index * in_width + left_x_index) * channels;
    const int top_right_index = (top_y_index * in_width + right_x_index) * channels;
    const int bottom_left_index = (bottom_y_index * in_width + left_x_index) * channels;
    const int bottom_right_index = (bottom_y_index * in_width + right_x_index) * channels;
    const int image_size = in_height * in_width * channels;
#pragma unroll
    for (int c = 0; c < channels; c++) {
      const float top_left = get_image_ptr(idata, image_id, image_size)[top_left_index + c];
      const float top_right = get_image_ptr(idata, image_id, image_size)[top_right_index + c];
      const float bottom_left = get_image_ptr(idata, image_id, image_size)[bottom_left_index + c];
      const float bottom_right = get_image_ptr(idata, image_id, image_size)[bottom_right_index + c];
      const float top = top_left + (top_right - top_left) * x_lerp;
      const float bottom = bottom_left + (bottom_right - bottom_left) * x_lerp;
      odata[channels * tid + c] = static_cast<T1>(top + (bottom - top) * y_lerp);
    }
  }
}

template <typename T0, typename T1>
void CropAndResize(
  cudaStream_t stream, int num_images, int num_boxes,
  const DeviceTensor<T0, 4>& images,
  const DeviceTensor<float, 2>& boxes,
  const DeviceTensor<int, 1>& boxes_idx,
  DeviceTensor<T1, 4>* crops) {
  // inputs
  CHECK_GT(num_images, 0);
  CHECK_LE(num_images, images.Dimension(0));
  int in_height = images.Dimension(1);
  int in_width = images.Dimension(2);
  int channels = images.Dimension(3);
  CHECK_GT(num_boxes, 0);
  CHECK_LE(num_boxes, boxes.Dimension(0));
  CHECK_LE(num_boxes, boxes_idx.Dimension(0));
  // outputs
  CHECK(crops != nullptr);
  CHECK_LE(num_boxes, crops->Dimension(0));
  int out_height = crops->Dimension(1);
  int out_width = crops->Dimension(2);
  CHECK_EQ(crops->Dimension(3), channels);

  const int output_volume = num_boxes * out_height * out_width * channels;
  const int num_blocks = DivUp(output_volume, kNumThreads * kNumLoop);
  crop_and_resize_kernel<T0, T1><<<num_blocks, kNumThreads, 0, stream>>>(output_volume / channels,
    num_images, in_height, in_width, out_height, out_width, channels, num_boxes,
    images.raw_data(), boxes.raw_data(), boxes_idx.raw_data(), crops->mutable_raw_data());
  CUDA_KERNEL_LAUNCH_CHECK();
}

#define CROP_AND_RESIZE(T0, T1)                                      \
  template void CropAndResize<T0, T1>(cudaStream_t,                  \
                                      int,                           \
                                      int,                           \
                                      const DeviceTensor<T0, 4>&,    \
                                      const DeviceTensor<float, 2>&, \
                                      const DeviceTensor<int, 1>&,   \
                                      DeviceTensor<T1, 4>*);
CROP_AND_RESIZE(uint8_t, half)
CROP_AND_RESIZE(uint8_t, float)
CROP_AND_RESIZE(uint8_t, uint8_t)
CROP_AND_RESIZE(float, half)
CROP_AND_RESIZE(float, float)
CROP_AND_RESIZE(half, half)
#undef CROP_AND_RESIZE

}  // namespace cuda
