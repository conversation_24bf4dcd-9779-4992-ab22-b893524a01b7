// Copyright @2025 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include "base/cuda/device_buffer.h"
#include "sensor/lidar/hesai/hesai_data_types.h"

namespace walle {
namespace sensor {
namespace lidar {
namespace hesai {

void UnpackOnGpu(const cuda::<PERSON>ceBuffer& lidar_blocks_buffer,
                 const cuda::<PERSON>ceBuffer& lidar_dis_unit_buffer,
                 const cuda::<PERSON><PERSON><PERSON><PERSON><PERSON>& start_azimuth_per_frame_buffer,
                 const cuda::<PERSON><PERSON><PERSON><PERSON><PERSON>& end_azimuth_per_frame_buffer,
                 const cuda::<PERSON><PERSON><PERSON><PERSON><PERSON>& vertical_angle_offset_buffer,
                 const cuda::<PERSON>ceBuffer& horizontal_angle_offset_buffer,
                 const cuda::<PERSON>ceBuffer& vertical_angle_adjust_buffer,
                 const cuda::<PERSON>ceBuffer& horizontal_angle_adjust_buffer,
                 float min_range,
                 float max_range,
                 cuda::<PERSON>ceBuffer* device_range_image_buffer,
                 cuda::DeviceContext* device_context);

}  // namespace hesai
}  // namespace lidar
}  // namespace sensor
}  // namespace walle
