// Copyright @2023 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/cuda/operators/undistort_image_op.h"

namespace cuda {
namespace {

constexpr int kBlockNum = 32;
constexpr int kDefaultPixelValue = 0;
constexpr float kEpsilon = 1e-6f;

}  // namespace

__global__ void build_pinhole_map_kernel(int out_image_w,
                                         int out_image_h,
                                         const float* cam_k,
                                         const float* distort,
                                         const float* new_cam_inv,
                                         float* map_x,
                                         float* map_y) {
  const int tidx = blockDim.x * blockIdx.x + threadIdx.x;
  const int tidy = blockDim.y * blockIdx.y + threadIdx.y;
  const int map_idx = tidy * out_image_w + tidx;
  if (tidx >= out_image_w || tidy >= out_image_h) {
    return;
  }
  const float k1 = distort[0];
  const float k2 = distort[1];
  const float p1 = distort[2];
  const float p2 = distort[3];
  const float k3 = distort[4];
  const float fx = cam_k[0];
  const float fy = cam_k[4];
  const float cx = cam_k[2];
  const float cy = cam_k[5];
  const float x_cam = tidx * new_cam_inv[0] + tidy * new_cam_inv[1] + new_cam_inv[2];
  const float y_cam = tidx * new_cam_inv[3] + tidy * new_cam_inv[4] + new_cam_inv[5];
  float w_cam = tidx * new_cam_inv[6] + tidy * new_cam_inv[7] + new_cam_inv[8];
  if (w_cam <= kEpsilon) {
    map_x[map_idx] = -1.0f;
    map_y[map_idx] = -1.0f;
    return;
  }
  w_cam += kEpsilon;
  const float w = 1.0f / w_cam;
  const float x = x_cam * w;
  const float y = y_cam * w;
  const float x2 = x * x;
  const float y2 = y * y;
  const float r2 = x2 + y2;
  const float xy = x * y;
  const float _2xy = 2.0f * xy;
  const float kr = 1.0f + ((k3 * r2 + k2) * r2 + k1) * r2;
  const float xd = x * kr + p1 * _2xy + p2 * (r2 + 2.0f * x2);
  const float yd = y * kr + p1 * (r2 + 2.0f * y2) + p2 * _2xy;
  const float u = fx * xd + cx;
  const float v = fy * yd + cy;
  map_x[map_idx] = u;
  map_y[map_idx] = v;
}

__global__ void build_fisheye_map_kernel(int out_image_w,
                                         int out_image_h,
                                         const float* cam_k,
                                         const float* distort,
                                         const float* new_cam_inv,
                                         float* map_x,
                                         float* map_y) {
  const int tidx = blockDim.x * blockIdx.x + threadIdx.x;
  const int tidy = blockDim.y * blockIdx.y + threadIdx.y;
  if (tidx >= out_image_w || tidy >= out_image_h) {
    return;
  }
  const float k1 = distort[0];
  const float k2 = distort[1];
  const float k3 = distort[2];
  const float k4 = distort[3];
  const float fx = cam_k[0];
  const float fy = cam_k[4];
  const float cx = cam_k[2];
  const float cy = cam_k[5];
  const float x_cam = tidx * new_cam_inv[0] + tidy * new_cam_inv[1] + new_cam_inv[2];
  const float y_cam = tidx * new_cam_inv[3] + tidy * new_cam_inv[4] + new_cam_inv[5];
  const float w_cam = tidx * new_cam_inv[6] + tidy * new_cam_inv[7] + new_cam_inv[8];
  const float w = 1.0f / w_cam;
  const float x = x_cam * w;
  const float y = y_cam * w;
  const float r = sqrt(x * x + y * y);
  const float theta = atan(r);
  const float theta2 = theta * theta;
  const float theta4 = theta2 * theta2;
  const float theta6 = theta4 * theta2;
  const float theta8 = theta4 * theta4;
  const float rd = theta * (1.0f + k1 * theta2 + k2 * theta4 + k3 * theta6 + k4 * theta8) / r;
  const float xd = rd * x;
  const float yd = rd * y;
  const float u = fx * xd + cx;
  const float v = fy * yd + cy;
  const int map_idx = tidy * out_image_w + tidx;
  map_x[map_idx] = u;
  map_y[map_idx] = v;
}

template <typename T>
__global__ void undistort_remap_cuda(const T* src_img,
                                     const float* map_x,
                                     const float* map_y,
                                     int image_w,
                                     int image_h,
                                     int out_image_w,
                                     int out_image_h,
                                     int channels,
                                     T* out_img) {
  const int tidx = blockDim.x * blockIdx.x + threadIdx.x;
  const int tidy = blockDim.y * blockIdx.y + threadIdx.y;
  if (tidx >= out_image_w || tidy >= out_image_h) {
    return;
  }
  const int map_idx = tidy * out_image_w + tidx;
  const int result_idx = map_idx * channels;
  const float u = map_x[map_idx];
  const float v = map_y[map_idx];
  const int u1 = floor(u);
  const int v1 = floor(v);
  const int u2 = u1 + 1;
  const int v2 = v1 + 1;
  if (u1 >= 0 && v1 >= 0 && u2 < image_w && v2 < image_h) {
    const float dx = u - u1;
    const float dy = v - v1;
    const float weight1 = (1.0f - dx) * (1.0f - dy);
    const float weight2 = dx * (1.0f - dy);
    const float weight3 = (1.0f - dx) * dy;
    const float weight4 = dx * dy;
    for (int c = 0; c < channels; c++) {
      out_img[result_idx + c] =
          static_cast<T>(weight1 * static_cast<float>(src_img[(v1 * image_w + u1) * channels + c]) +
                         weight2 * static_cast<float>(src_img[(v1 * image_w + u2) * channels + c]) +
                         weight3 * static_cast<float>(src_img[(v2 * image_w + u1) * channels + c]) +
                         weight4 * static_cast<float>(src_img[(v2 * image_w + u2) * channels + c]));
    }
  } else {
    for (int c = 0; c < channels; c++) {
      out_img[result_idx + c] = static_cast<T>(kDefaultPixelValue);
    }
  }
}

template <typename T0, typename T1>
void UndistortImageWrapper(cudaStream_t stream,
                           const std::string& cam_type,
                           const ConstDeviceTensorView<T0, 3>& src_img,
                           const ConstDeviceTensorView<T1, 2>& cam_k,
                           const ConstDeviceTensorView<T1, 1>& distort,
                           const ConstDeviceTensorView<T1, 2>& new_cam_k_inv,
                           DeviceTensor<T0, 3>* out_img) {
  const int out_img_height = out_img->Dimension(0);
  const int out_img_width = out_img->Dimension(1);
  DeviceTensor<T1, 2> map_x(TensorShape({out_img_height, out_img_width}));
  DeviceTensor<T1, 2> map_y(TensorShape({out_img_height, out_img_width}));
  if (cam_type == "pinhole") {
    BuildPinholeUndistortMap(stream, cam_k, distort, new_cam_k_inv, &map_x, &map_y);
  } else if (cam_type == "fisheye") {
    BuildFisheyeUndistortMap(stream, cam_k, distort, new_cam_k_inv, &map_x, &map_y);
  } else {
    CHECK(false) << "Wrong cam_type: " << cam_type;
  }
  UndistortRemap(stream, src_img, map_x.ConstView(), map_y.ConstView(), out_img);
}

template void UndistortImageWrapper(cudaStream_t stream,
                                    const std::string& cam_type,
                                    const ConstDeviceTensorView<uint8_t, 3>& src_img,
                                    const ConstDeviceTensorView<float, 2>& cam_k,
                                    const ConstDeviceTensorView<float, 1>& distort,
                                    const ConstDeviceTensorView<float, 2>& new_cam_k_inv,
                                    DeviceTensor<uint8_t, 3>* out_img);

template <typename T>
void BuildPinholeUndistortMap(cudaStream_t stream,
                              const ConstDeviceTensorView<T, 2>& cam_k,
                              const ConstDeviceTensorView<T, 1>& distort,
                              const ConstDeviceTensorView<T, 2>& new_cam_k_inv,
                              DeviceTensor<T, 2>* map_x,
                              DeviceTensor<T, 2>* map_y) {
  CHECK_EQ(cam_k.Dimension(0), 3);
  CHECK_EQ(cam_k.Dimension(1), 3);
  CHECK_EQ(distort.Dimension(0), 5);
  CHECK_EQ(new_cam_k_inv.Dimension(0), 3);
  CHECK_EQ(new_cam_k_inv.Dimension(1), 3);
  const int input_height = map_x->Dimension(0);
  const int input_width = map_x->Dimension(1);
  CHECK_EQ(map_y->Dimension(0), input_height);
  CHECK_EQ(map_y->Dimension(1), input_width);
  dim3 block(kBlockNum, kBlockNum);
  dim3 grid((input_width + block.x - 1) / block.x, (input_height + block.y - 1) / block.y);
  build_pinhole_map_kernel<<<grid, block, 0, stream>>>(input_width,
                                                       input_height,
                                                       cam_k.raw_data(),
                                                       distort.raw_data(),
                                                       new_cam_k_inv.raw_data(),
                                                       map_x->mutable_raw_data(),
                                                       map_y->mutable_raw_data());
  CUDA_KERNEL_LAUNCH_CHECK();
}

template void BuildPinholeUndistortMap(cudaStream_t stream,
                                       const ConstDeviceTensorView<float, 2>& cam_k,
                                       const ConstDeviceTensorView<float, 1>& distort,
                                       const ConstDeviceTensorView<float, 2>& new_cam_k_inv,
                                       DeviceTensor<float, 2>* map_x,
                                       DeviceTensor<float, 2>* map_y);

template <typename T>
void BuildFisheyeUndistortMap(cudaStream_t stream,
                              const ConstDeviceTensorView<T, 2>& cam_k,
                              const ConstDeviceTensorView<T, 1>& distort,
                              const ConstDeviceTensorView<T, 2>& new_cam_k_inv,
                              DeviceTensor<T, 2>* map_x,
                              DeviceTensor<T, 2>* map_y) {
  CHECK_EQ(cam_k.Dimension(0), 3);
  CHECK_EQ(cam_k.Dimension(1), 3);
  CHECK_EQ(distort.Dimension(0), 4);
  CHECK_EQ(new_cam_k_inv.Dimension(0), 3);
  CHECK_EQ(new_cam_k_inv.Dimension(1), 3);
  const int input_height = map_x->Dimension(0);
  const int input_width = map_x->Dimension(1);
  CHECK_EQ(map_y->Dimension(0), input_height);
  CHECK_EQ(map_y->Dimension(1), input_width);
  dim3 block(kBlockNum, kBlockNum);
  dim3 grid((input_width + block.x - 1) / block.x, (input_height + block.y - 1) / block.y);
  build_fisheye_map_kernel<<<grid, block, 0, stream>>>(input_width,
                                                       input_height,
                                                       cam_k.raw_data(),
                                                       distort.raw_data(),
                                                       new_cam_k_inv.raw_data(),
                                                       map_x->mutable_raw_data(),
                                                       map_y->mutable_raw_data());
  CUDA_KERNEL_LAUNCH_CHECK();
}

template void BuildFisheyeUndistortMap(cudaStream_t stream,
                                       const ConstDeviceTensorView<float, 2>& cam_k,
                                       const ConstDeviceTensorView<float, 1>& distort,
                                       const ConstDeviceTensorView<float, 2>& new_cam_k_inv,
                                       DeviceTensor<float, 2>* map_x,
                                       DeviceTensor<float, 2>* map_y);

template <typename T0, typename T1>
void UndistortRemap(cudaStream_t stream,
                    const ConstDeviceTensorView<T0, 3>& src_img,
                    const ConstDeviceTensorView<T1, 2>& map_x,
                    const ConstDeviceTensorView<T1, 2>& map_y,
                    DeviceBuffer* out_img) {
  const int img_height = src_img.Dimension(0);
  const int img_width = src_img.Dimension(1);
  const int img_channels = src_img.Dimension(2);
  CHECK_EQ(map_x.Dimension(0), map_y.Dimension(0));
  CHECK_EQ(map_x.Dimension(1), map_y.Dimension(1));
  const int out_img_height = map_x.Dimension(0);
  const int out_img_width = map_x.Dimension(1);
  dim3 block(kBlockNum, kBlockNum);
  dim3 grid((out_img_width + block.x - 1) / block.x, (out_img_height + block.y - 1) / block.y);
  undistort_remap_cuda<<<grid, block, 0, stream>>>(src_img.raw_data(),
                                                   map_x.raw_data(),
                                                   map_y.raw_data(),
                                                   img_width,
                                                   img_height,
                                                   out_img_width,
                                                   out_img_height,
                                                   img_channels,
                                                   reinterpret_cast<T0*>(out_img->mutable_data()));
  CUDA_KERNEL_LAUNCH_CHECK();
}

template void UndistortRemap(cudaStream_t stream,
                             const ConstDeviceTensorView<uint8_t, 3>& src_img,
                             const ConstDeviceTensorView<float, 2>& map_x,
                             const ConstDeviceTensorView<float, 2>& map_y,
                             DeviceBuffer* out_img);
template void UndistortRemap(cudaStream_t stream,
                             const ConstDeviceTensorView<float, 3>& src_img,
                             const ConstDeviceTensorView<float, 2>& map_x,
                             const ConstDeviceTensorView<float, 2>& map_y,
                             DeviceBuffer* out_img);

template <typename T0, typename T1>
void UndistortRemap(cudaStream_t stream,
                    const ConstDeviceTensorView<T0, 3>& src_img,
                    const ConstDeviceTensorView<T1, 2>& map_x,
                    const ConstDeviceTensorView<T1, 2>& map_y,
                    DeviceTensor<T0, 3>* out_img) {
  UndistortRemap(stream, src_img, map_x, map_y, out_img->mutable_buffer());
}

template void UndistortRemap(cudaStream_t stream,
                             const ConstDeviceTensorView<uint8_t, 3>& src_img,
                             const ConstDeviceTensorView<float, 2>& map_x,
                             const ConstDeviceTensorView<float, 2>& map_y,
                             DeviceTensor<uint8_t, 3>* out_img);
template void UndistortRemap(cudaStream_t stream,
                             const ConstDeviceTensorView<float, 3>& src_img,
                             const ConstDeviceTensorView<float, 2>& map_x,
                             const ConstDeviceTensorView<float, 2>& map_y,
                             DeviceTensor<float, 3>* out_img);

}  // namespace cuda
