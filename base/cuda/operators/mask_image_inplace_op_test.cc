// Copyright @2022 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/cuda/operators/mask_image_inplace_op.h"

#include <numeric>
#include <vector>

#include "gtest/gtest.h"

#include "base/cuda/device_buffer.h"
#include "base/cuda/device_utils.h"
#include "base/cuda/tensor.h"

namespace cuda {
namespace {

constexpr int kImageWidth = 1920;
constexpr int kImageHeight = 1080;
constexpr int kImageChannels = 3;
constexpr int kNumImages = 1;
constexpr int kNumBoxDims = 4;
using Uint8ImagesTensor = cuda::DeviceTensor<uint8_t, 4>;

// [num_images, kImageHeight, kImageWidth, kImageChannels] / [num_boxes, 4] -> [num_images,
// kImageHeight, kImageWidth, kImageChannels]
template <typename T>
void MaskImageInplaceCpu(int num_mask_box,
                  int mask_value,
                  const std::vector<float>& mask_boxes,
                  Tensor4D<T>* image) {
  CHECK(image != nullptr);
  for (int y = 0; y < kImageHeight; ++y) {
    for (int x = 0; x < kImageWidth; ++x) {
      for (int box_idx = 0; box_idx < num_mask_box; ++box_idx) {
        const float x0 = mask_boxes[box_idx * 4 + 0];
        const float y0 = mask_boxes[box_idx * 4 + 1];
        const float x1 = mask_boxes[box_idx * 4 + 2];
        const float y1 = mask_boxes[box_idx * 4 + 3];
        if (x >= x0 && x < x1 && y >= y0 && y < y1) {
          (*image)(0, y, x, 0) = static_cast<T>(0);
          (*image)(0, y, x, 1) = static_cast<T>(0);
          (*image)(0, y, x, 2) = static_cast<T>(0);
        }
      }
    }
  }
}

void MaskImageInplaceOpTest() {
  // test valid & invalid boxes
  const std::vector<float> mask_boxes = {123.4, 567.8, 432.1, 876.5, 111.1, 111.1, 222.2, 222.2,
                                         0.1,   2,     100,   2,     10,    100,   11,    101,
                                         -1,    99,    -100,  200,   1e9,   1e9,   2e9,   2e9};
  const int num_mask_boxes = mask_boxes.size() / kNumBoxDims;
  std::vector<uint8_t> in_buf(kNumImages * kImageHeight * kImageWidth * kImageChannels, 0);
  std::iota(in_buf.begin(), in_buf.end(), 0);
  Tensor4D<uint8_t> in_tensor(in_buf.data(), kNumImages, kImageHeight, kImageWidth, kImageChannels);
  MaskImageInplaceCpu(num_mask_boxes, 0, mask_boxes, &in_tensor);
  std::vector<float> out_buf;
  // gpu routine
  DeviceContext ctx;
  // inputs
  DeviceTensor<uint8_t, 3> image_tensor(TensorShape({kImageHeight, kImageWidth, kImageChannels}));
  DeviceTensor<float, 2> mask_boxes_tensor(TensorShape({num_mask_boxes, kNumBoxDims}));
  std::vector<uint8_t> results(image_tensor.NumElements());
  // feed data
  std::vector<uint8_t> device_in_buf(kNumImages * kImageHeight * kImageWidth * kImageChannels, 0);
  std::iota(device_in_buf.begin(), device_in_buf.end(), 0);
  image_tensor.mutable_buffer()->FromCpu(device_in_buf.data(), device_in_buf.size(), &ctx);

  mask_boxes_tensor.mutable_buffer()->FromCpu(
      mask_boxes.data(), mask_boxes.size() * sizeof(float), &ctx);
  cuda::DeviceTensorView<uint8_t, 3> image_tensor_view = image_tensor.View();
  MaskImageInplace(ctx.stream(), num_mask_boxes, 0, mask_boxes_tensor.ConstView(), &image_tensor_view);
  image_tensor.ConstView().buffer().ToCpu(results.data(), &ctx);
  ctx.Sync();
  for (int i = 0; i < in_tensor.size(); i++) {
    EXPECT_NEAR(in_tensor.mutable_data()[i], results[i], 1);
  }
}

TEST(MaskImageInplaceOpTest, BasicTest) { MaskImageInplaceOpTest(); }

}  // namespace
}  // namespace cuda
