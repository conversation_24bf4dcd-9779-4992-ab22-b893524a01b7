// Copyright @2022 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/cuda/operators/inplace_add_op.h"

#include <vector>

#include "gtest/gtest.h"

namespace cuda {

TEST(InplaceAddOpTest, BasicTest) {
  const std::vector<int> a = {1, 2, 3, 4, 5, 6};
  const std::vector<int> b = {7, 10, 13, 15, 16, 20};
  DeviceBuffer device_buffer_a(a.size() * sizeof(int));
  DeviceBuffer device_buffer_b(b.size() * sizeof(int));
  DeviceBufferView<int> view_a = device_buffer_a.View<int>();
  DeviceBufferView<int> view_b = device_buffer_b.View<int>();
  cuda::DeviceContext device_context;
  view_a.FromCpu(a.data(), &device_context);
  view_b.FromCpu(b.data(), &device_context);
  InplaceAdd(view_b.ConstView(0, b.size()), &view_a, &device_context);
  std::vector<int> result_a(a.size());
  view_a.To<PERSON>pu(result_a.data(), 0, view_a.size(), &device_context);
  device_context.Sync();
  for (size_t i = 0; i < a.size(); i++) {
    EXPECT_EQ(result_a[i], a[i] + b[i]);
  }
}

}  // namespace cuda
