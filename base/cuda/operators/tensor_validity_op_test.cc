// Copyright @2024 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/cuda/operators/tensor_validity_op.h"

#include <limits>
#include <memory>
#include <random>
#include <utility>
#include <vector>

#include <cuda_fp16.h>
#include "gtest/gtest.h"

#include "base/cuda/mirror_buffer.h"

namespace cuda {
namespace {

const int kBasicSizes[] = {1, 1, 1, 16, 64, 512, 1024, 4096, 16000, 64000, 256000};

}  // namespace

template <typename Data>
void test() {
  const nvinfer1::DataType kDataType =
      sizeof(Data) == sizeof(float) ? nvinfer1::DataType::kFLOAT : nvinfer1::DataType::kHALF;
  const Data kInf = static_cast<Data>(std::numeric_limits<float>::infinity());
  const Data kNaN = static_cast<Data>(std::numeric_limits<float>::quiet_NaN());

  std::vector<int> test_sizes;
  for (const int basic_size : kBasicSizes) {
    for (int i = 0; i < 16; i++) {
      test_sizes.push_back(basic_size + i);
      test_sizes.push_back(static_cast<int>(basic_size * 1.1) + i);
    }
  }

  std::random_device rd;
  std::mt19937 mt(rd());
  std::uniform_real_distribution<double> dist(0.0, 1.0);
  std::vector<std::unique_ptr<cuda::MirrorBuffer>> buffers;
  cuda::MirrorBuffer result_buffer(test_sizes.size() * sizeof(int) * 2);
  std::vector<std::pair<int, int>> gt_numbers;
  cuda::DeviceContext context;
  int test_index = 0;
  for (const int test_size : test_sizes) {
    buffers.emplace_back(std::make_unique<cuda::MirrorBuffer>(test_size * sizeof(Data)));
    cuda::MirrorBuffer& buffer = *buffers.back();
    Data* host_data = reinterpret_cast<Data*>(buffer.mutable_host_data());
    const double threshold1 = 0.05 + dist(mt) * 0.25;
    const double threshold2 = 0.7 + dist(mt) * 0.25;
    int inf_num = 0;
    int nan_num = 0;
    for (int i = 0; i < test_size; i++) {
      const double score = dist(mt);
      if (score < threshold1) {
        inf_num++;
        host_data[i] = kInf;
      } else if (score < threshold2) {
        host_data[i] = static_cast<Data>(dist(mt) * 2 - 1);
      } else {
        nan_num++;
        host_data[i] = kNaN;
      }
    }
    gt_numbers.push_back(std::make_pair(inf_num, nan_num));
    buffer.HostToDevice(&context);
    CheckTensorValidity(
        buffer.device_data(),
        kDataType,
        buffer.size(),
        context.stream(),
        reinterpret_cast<int*>(result_buffer.mutable_device_data()) + test_index * 2);
    test_index++;
  }
  result_buffer.DeviceToHost(&context);
  context.Sync();

  const int* all_result_data = reinterpret_cast<const int*>(result_buffer.host_data());
  for (int i = 0; i < static_cast<int>(test_sizes.size()); i++) {
    const std::pair<int, int> gt_pair = gt_numbers[i];
    const int* test_result = all_result_data + i * 2;
    EXPECT_EQ(test_result[0], gt_pair.first) << " at size = " << test_sizes[i];
    EXPECT_EQ(test_result[1], gt_pair.second) << " at size = " << test_sizes[i];
  }
}

TEST(TensorValidityCheckTest, BasicFloatTest) { test<float>(); }

TEST(TensorValidityCheckTest, BasicHalfTest) { test<half>(); }

}  // namespace cuda
