// Copyright @2025 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/cuda/operators/lidar_rse1_unpack_op.h"

#include <cuda_runtime.h>
#include <memory>
#include <vector>

#include "gtest/gtest.h"

#include "base/cuda/device_buffer.h"
#include "base/cuda/device_utils.h"
#include "base/cuda/macros.h"
#include "sensor/lidar/data_types_device.h"
#include "sensor/lidar/robosense/robosense_data_types.h"

namespace walle {
namespace sensor {
namespace lidar {
namespace robosense {

class LidarRse1UnpackTest : public ::testing::Test {
 protected:
  void SetUp() override {
    device_context_ = std::make_unique<cuda::DeviceContext>();
    device_buffer_ = std::make_unique<cuda::DeviceBuffer>(total_points_ * sizeof(Pixel));
    h_channels_.resize(total_points_);
    h_pixels_.resize(total_points_);

    const uint16_t distance_mm = 5000 / 5;  // distance:5m
    const int16_t vector_x = 16384;         // 0.5 * kVectorBase
    const int16_t vector_y = 0;
    const int16_t vector_z = 0;
    for (int i = 0; i < kRSE1PacketSize; ++i) {
      for (int j = 0; j < kRSE1LaserCount; ++j) {
        const int index = j + i * kRSE1LaserCount;
        h_channels_[index].distance = ((distance_mm & 0xFF) << 8) | ((distance_mm >> 8) & 0xFF);
        h_channels_[index].x = ((vector_x & 0xFF) << 8) | ((vector_x >> 8) & 0xFF);
        h_channels_[index].y = ((vector_y & 0xFF) << 8) | ((vector_y >> 8) & 0xFF);
        h_channels_[index].z = ((vector_z & 0xFF) << 8) | ((vector_z >> 8) & 0xFF);
        h_channels_[index].intensity = 100;
      }
    }
    CUDA_CHECK(cudaMalloc(&d_channels_, total_points_ * sizeof(RSE1Channel)));
    CUDA_CHECK(cudaMemcpy(d_channels_,
                          h_channels_.data(),
                          total_points_ * sizeof(RSE1Channel),
                          cudaMemcpyHostToDevice));
  }

  void TearDown() override {
    if (d_channels_) {
      CUDA_CHECK(cudaFree(d_channels_));
      d_channels_ = nullptr;
    }
  }

  std::vector<RSE1Channel> h_channels_;
  std::vector<Pixel> h_pixels_;
  RSE1Channel* d_channels_ = nullptr;
  std::unique_ptr<cuda::DeviceContext> device_context_;
  std::unique_ptr<cuda::DeviceBuffer> device_buffer_;

  float min_range_ = 0.5f;
  float max_range_ = 200.0f;
  size_t total_points_ = kRSE1PacketSize * kRSE1LaserCount;
};

TEST_F(LidarRse1UnpackTest, TestUnpackLidarPacketsOnGpu) {
  UnpackOnGpu(d_channels_, min_range_, max_range_, device_buffer_.get(), device_context_.get());
  CUDA_CHECK(cudaDeviceSynchronize());
  Pixel* device_pixels = static_cast<Pixel*>(device_buffer_->mutable_data());
  CUDA_CHECK(cudaMemcpy(
      h_pixels_.data(), device_pixels, total_points_ * sizeof(Pixel), cudaMemcpyDeviceToHost));
  for (int i = 0; i < kRSE1PacketSize; ++i) {
    for (int j = 0; j < kRSE1LaserCount; ++j) {
      const int index = j + i * kRSE1LaserCount;
      EXPECT_NEAR(h_pixels_[index].position[0], 2.5f, 1e-3f);
      EXPECT_NEAR(h_pixels_[index].position[1], 0.0f, 1e-3f);
      EXPECT_NEAR(h_pixels_[index].position[2], 0.0f, 1e-3f);
      EXPECT_EQ(h_pixels_[index].intensity, 100);
      EXPECT_EQ(h_pixels_[index].packet_id, 0);
      EXPECT_FLOAT_EQ(h_pixels_[index].angle, 0.0f);
    }
  }
}

TEST_F(LidarRse1UnpackTest, TestRangeFiltering) {
  uint16_t distance_mm = 300 / 5;  // 0.3m
  for (int i = 0; i < kRSE1PacketSize / 2; ++i) {
    for (int j = 0; j < kRSE1LaserCount; ++j) {
      const int index = j + i * kRSE1LaserCount;
      h_channels_[index].distance = ((distance_mm & 0xFF) << 8) | ((distance_mm >> 8) & 0xFF);
    }
  }
  distance_mm = 250000 / 5;  // 250m
  for (int i = kRSE1PacketSize / 2; i < kRSE1PacketSize; ++i) {
    for (int j = 0; j < kRSE1LaserCount; ++j) {
      int index = j + i * kRSE1LaserCount;
      h_channels_[index].distance = ((distance_mm & 0xFF) << 8) | ((distance_mm >> 8) & 0xFF);
    }
  }
  CUDA_CHECK(cudaMemcpy(d_channels_,
                        h_channels_.data(),
                        total_points_ * sizeof(RSE1Channel),
                        cudaMemcpyHostToDevice));
  UnpackOnGpu(d_channels_, min_range_, max_range_, device_buffer_.get(), device_context_.get());
  CUDA_CHECK(cudaDeviceSynchronize());

  Pixel* device_pixels = static_cast<Pixel*>(device_buffer_->mutable_data());
  CUDA_CHECK(cudaMemcpy(
      h_pixels_.data(), device_pixels, total_points_ * sizeof(Pixel), cudaMemcpyDeviceToHost));
  for (int i = 0; i < kRSE1PacketSize; ++i) {
    for (int j = 0; j < kRSE1LaserCount; ++j) {
      int index = j + i * kRSE1LaserCount;
      EXPECT_FLOAT_EQ(h_pixels_[index].position[0], 0.0f);
      EXPECT_FLOAT_EQ(h_pixels_[index].position[1], 0.0f);
      EXPECT_FLOAT_EQ(h_pixels_[index].position[2], 0.0f);
    }
  }
}

TEST_F(LidarRse1UnpackTest, BenchmarkPerformance) {
  cudaEvent_t start, stop;
  CUDA_CHECK(cudaEventCreate(&start));
  CUDA_CHECK(cudaEventCreate(&stop));
  CUDA_CHECK(cudaEventRecord(start));

  const int num_iterations = 100;
  for (int i = 0; i < num_iterations; ++i) {
    UnpackOnGpu(d_channels_, min_range_, max_range_, device_buffer_.get(), device_context_.get());
  }
  CUDA_CHECK(cudaEventRecord(stop));
  CUDA_CHECK(cudaEventSynchronize(stop));

  float milliseconds = 0;
  CUDA_CHECK(cudaEventElapsedTime(&milliseconds, start, stop));

  const float avg_time_per_iteration = milliseconds / num_iterations;
  EXPECT_LE(avg_time_per_iteration, 0.1f);
  CUDA_CHECK(cudaEventDestroy(start));
  CUDA_CHECK(cudaEventDestroy(stop));
}

}  // namespace robosense
}  // namespace lidar
}  // namespace sensor
}  // namespace walle
