// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/cuda/operators/utils/op_params.h"

#include "gtest/gtest.h"

namespace cuda {

TEST(OpParamsTest, BasicTest) {
  OpParamsMeta op_params_meta;
  (*op_params_meta.mutable_attrs())["param_bool"].set_int_value(1);
  (*op_params_meta.mutable_attrs())["param_int"].set_int_value(100);
  (*op_params_meta.mutable_attrs())["param_int64"].set_int_value(101);
  (*op_params_meta.mutable_attrs())["param_float"].set_double_value(1.0);
  (*op_params_meta.mutable_attrs())["param_double"].set_double_value(1.0);

  (*op_params_meta.mutable_attrs())["param_int_array"].mutable_int_array()->add_data(1);
  (*op_params_meta.mutable_attrs())["param_int_array"].mutable_int_array()->add_data(2);

  (*op_params_meta.mutable_attrs())["param_double_array"].mutable_double_array()->add_data(3.0);
  (*op_params_meta.mutable_attrs())["param_double_array"].mutable_double_array()->add_data(4.0);
  (*op_params_meta.mutable_attrs())["param_double_array"].mutable_double_array()->add_data(5.0);

  *(*op_params_meta.mutable_attrs())["param_string"].mutable_bytes_value() = "test bytes";

  (*op_params_meta.mutable_attrs())["param_string_array"].mutable_bytes_array()->add_data("str1");
  (*op_params_meta.mutable_attrs())["param_string_array"].mutable_bytes_array()->add_data("str2");

  OpParams op_params(op_params_meta);
  bool param_bool = false;
  int param_int = 0;
  int64_t param_int64 = 0;
  float param_float = 0.f;
  double param_double = 0.f;
  std::vector<int> int_array;
  std::vector<float> float_array;
  std::vector<double> double_array;
  std::string param_str;
  std::vector<std::string> str_array;

  op_params.PopParam<bool>("param_bool", false, &param_bool)
      .PopParam<int>("param_int", 0, &param_int)
      .PopParam<int64_t>("param_int64", 0, &param_int64)
      .PopParam<float>("param_float", 0.f, &param_float)
      .PopParam<double>("param_double", 0.0, &param_double)
      .PopParam<std::vector<int>>("param_int_array", {}, &int_array)
      .PopParam<std::vector<double>>("param_double_array", {}, &double_array)
      .PopParam<std::vector<float>>("param_float_array", {}, &float_array)
      .PopParam<std::string>("param_string", "", &param_str)
      .PopParam<std::vector<std::string>>("param_string_array", {}, &str_array)
      .CheckEmpty();

  EXPECT_TRUE(param_bool);
  EXPECT_EQ(param_int, 100);
  EXPECT_EQ(param_int64, 101);
  EXPECT_NEAR(param_float, 1.0f, 1e-3);
  EXPECT_NEAR(param_double, 1.0f, 1e-6);

  EXPECT_EQ(int_array.size(), 2);
  EXPECT_EQ(int_array[0], 1);
  EXPECT_EQ(int_array[1], 2);

  EXPECT_EQ(float_array.size(), 0);
  EXPECT_EQ(double_array.size(), 3);
  EXPECT_NEAR(double_array[0], 3.0, 1e-6);
  EXPECT_NEAR(double_array[1], 4.0, 1e-6);
  EXPECT_NEAR(double_array[2], 5.0, 1e-6);

  EXPECT_EQ(param_str, "test bytes");
  EXPECT_EQ(str_array.size(), 2);
  EXPECT_EQ(str_array[0], "str1");
  EXPECT_EQ(str_array[1], "str2");
}

}  // namespace cuda
