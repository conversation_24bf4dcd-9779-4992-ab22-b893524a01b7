package(default_visibility = ["//visibility:public"])

cc_library(
    name = "op_params",
    srcs = ["op_params.cc"],
    hdrs = ["op_params.h"],
    deps = [
        "//base/common:macros",
        "//base/container:utils",
        "//base/strings:format",
        "//walle/common/proto/cuda:cc_operator_params_proto",
        "@com_google_absl//absl/strings",
        "@com_google_protobuf//:protobuf",
    ],
)

cc_test(
    name = "op_params_test",
    srcs = ["op_params_test.cc"],
    deps = [
        ":op_params",
        "//base/testing:test_main",
    ],
)
