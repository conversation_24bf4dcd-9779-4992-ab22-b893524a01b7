// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <string>
#include <unordered_set>
#include <vector>

#include "base/common/macros.h"
#include "base/container/utils.h"
#include "walle/common/proto/cuda/operator_params.pb.h"

namespace cuda {

class OpParams {
 public:
  explicit OpParams(const OpParamsMeta& op_params_meta);
  explicit OpParams(const std::string& serial_str);

  template <typename T>
  OpParams& PopParam(const std::string& name, const T& default_value, T* value);
  template <typename T>
  OpParams& GetParam(const std::string& name, T* value);
  OpParams& SetParam(const std::string& name, const std::vector<float>& value);

  void CheckEmpty() const;
  bool IsEmpty() const;
  const OpParamsMeta& op_params_meta() const { return op_params_meta_; }

 private:
  template <typename T>
  void DecodeOpAttribute(const OpAttribute& op_attr, T* value);

  OpParamsMeta op_params_meta_;
  std::unordered_set<std::string> attrs_name_;

  DISALLOW_COPY_AND_ASSIGN(OpParams);
};

template <typename T>
OpParams& OpParams::PopParam(const std::string& name, const T& default_value, T* value) {
  const OpAttribute* op_attr = base::FindOrNull(op_params_meta_.attrs(), name);
  if (op_attr) {
    CHECK(attrs_name_.count(name)) << "Attribute " << name << " has been popped!";
    attrs_name_.erase(name);
    DecodeOpAttribute(*op_attr, value);
  } else {
    *value = default_value;
  }
  return *this;
}

template <typename T>
OpParams& OpParams::GetParam(const std::string& name, T* value) {
  const OpAttribute* op_attr = base::FindOrNull(op_params_meta_.attrs(), name);
  if (op_attr) {
    DecodeOpAttribute(*op_attr, value);
  }
  return *this;
}

}  // namespace cuda
