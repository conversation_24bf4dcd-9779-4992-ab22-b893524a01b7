// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/cuda/operators/utils/op_params.h"

#include <google/protobuf/text_format.h>
#include <limits>

#include "absl/strings/str_join.h"

namespace cuda {

namespace {

#define SPECIALIZATION_INTEGER_DECODER(IntegerType)                                               \
  template <>                                                                                     \
  void OpParams::DecodeOpAttribute(const OpAttribute& op_attr, IntegerType* value) {              \
    CHECK_EQ(op_attr.attr_item_case(), OpAttribute::AttrItemCase::kIntValue);                     \
    CHECK_GE(op_attr.int_value(), std::numeric_limits<IntegerType>::min());                       \
    CHECK_LE(op_attr.int_value(), std::numeric_limits<IntegerType>::max());                       \
    *value = op_attr.int_value();                                                                 \
  }                                                                                               \
  template <>                                                                                     \
  void OpParams::DecodeOpAttribute(const OpAttribute& op_attr, std::vector<IntegerType>* value) { \
    CHECK_EQ(op_attr.attr_item_case(), OpAttribute::AttrItemCase::kIntArray);                     \
    value->resize(op_attr.int_array().data_size());                                               \
    for (int i = 0; i < op_attr.int_array().data_size(); ++i) {                                   \
      const auto& v = op_attr.int_array().data(i);                                                \
      CHECK_GE(v, std::numeric_limits<IntegerType>::min());                                       \
      CHECK_LE(v, std::numeric_limits<IntegerType>::max());                                       \
      value->at(i) = v;                                                                           \
    }                                                                                             \
  }

#define SPECIALIZATION_FLOAT_DECODER(FloatType)                                                 \
  template <>                                                                                   \
  void OpParams::DecodeOpAttribute(const OpAttribute& op_attr, FloatType* value) {              \
    CHECK_EQ(op_attr.attr_item_case(), OpAttribute::AttrItemCase::kDoubleValue);                \
    CHECK_GE(op_attr.int_value(), std::numeric_limits<FloatType>::lowest());                    \
    CHECK_LE(op_attr.int_value(), std::numeric_limits<FloatType>::max());                       \
    *value = op_attr.double_value();                                                            \
  }                                                                                             \
  template <>                                                                                   \
  void OpParams::DecodeOpAttribute(const OpAttribute& op_attr, std::vector<FloatType>* value) { \
    CHECK_EQ(op_attr.attr_item_case(), OpAttribute::AttrItemCase::kDoubleArray);                \
    value->resize(op_attr.double_array().data_size());                                          \
    for (int i = 0; i < op_attr.double_array().data_size(); ++i) {                              \
      const auto& v = op_attr.double_array().data(i);                                           \
      CHECK_GE(v, std::numeric_limits<FloatType>::lowest());                                    \
      CHECK_LE(v, std::numeric_limits<FloatType>::max());                                       \
      value->at(i) = v;                                                                         \
    }                                                                                           \
  }

}  // namespace

SPECIALIZATION_INTEGER_DECODER(bool);
SPECIALIZATION_INTEGER_DECODER(int);
SPECIALIZATION_INTEGER_DECODER(int64_t);
SPECIALIZATION_FLOAT_DECODER(float);
SPECIALIZATION_FLOAT_DECODER(double);

template <>
void OpParams::DecodeOpAttribute(const OpAttribute& op_attr, std::string* value) {
  CHECK_EQ(op_attr.attr_item_case(), OpAttribute::AttrItemCase::kBytesValue);
  *value = op_attr.bytes_value();
}

template <>
void OpParams::DecodeOpAttribute(const OpAttribute& op_attr, std::vector<std::string>* value) {
  CHECK_EQ(op_attr.attr_item_case(), OpAttribute::AttrItemCase::kBytesArray);
  value->resize(op_attr.bytes_array().data_size());
  for (int i = 0; i < op_attr.bytes_array().data_size(); ++i) {
    value->at(i) = op_attr.bytes_array().data(i);
  }
}

OpParams::OpParams(const OpParamsMeta& op_params_meta) : op_params_meta_(op_params_meta) {
  for (const auto& iter : op_params_meta_.attrs()) {
    attrs_name_.emplace(iter.first);
  }
}

OpParams::OpParams(const std::string& serial_str) {
  google::protobuf::TextFormat::ParseFromString(serial_str, &op_params_meta_);
  for (const auto& iter : op_params_meta_.attrs()) {
    attrs_name_.emplace(iter.first);
  }
}

void OpParams::CheckEmpty() const {
  if (!attrs_name_.empty()) {
    std::string log_str;
    CHECK(op_params_meta_.attrs().empty())
        << "Attributes not be used: " << absl::StrJoin(attrs_name_, ", ");
  }
}

bool OpParams::IsEmpty() const {
  return attrs_name_.empty();
}

OpParams& OpParams::SetParam(const std::string& name, const std::vector<float>& values) {
  (*op_params_meta_.mutable_attrs())[name].clear_double_array();
  for (const float& value : values) {
    (*op_params_meta_.mutable_attrs())[name].mutable_double_array()->add_data(value);
  }
  return *this;
}

}  // namespace cuda
