// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include <chrono>
#include <cmath>
#include <functional>
#include <numeric>
#include <vector>

#include <cuda_fp16.h>
#include "gtest/gtest.h"

#include "base/cuda/device_buffer.h"
#include "base/cuda/device_utils.h"
#include "base/cuda/operators/crop_and_resize_op.h"
#include "base/cuda/tensor.h"

namespace cuda {
namespace {

// [num_images, ih, iw, c] / [num_boxes, 4] / [num_boxes, 1] -> [num_boxes, oh, ow, c]
template <typename T0, typename T1>
void CropAndResizeCpu(
  bool enable_half_pixel_centers,
  const Tensor4D<T0>& input,
  const int num_boxes,
  const std::vector<float>& boxes,
  const std::vector<int>& boxes_idx,
  Tensor4D<T1>* output) {
  CHECK(output != nullptr);
  CHECK(num_boxes == output->Batch());

  for (int b = 0; b < output->Batch(); ++b) {
    const int image_id = boxes_idx[b];
    if (image_id < 0 || image_id >= input.Batch()) continue;
    const float x0 = boxes[b * 4 + 0];
    const float y0 = boxes[b * 4 + 1];
    const float x1 = boxes[b * 4 + 2];
    const float y1 = boxes[b * 4 + 3];
    const float height_scale = (y1 - y0) / output->Height();
    const float width_scale = (x1 - x0) / output->Width();

    for (int y = 0; y < output->Height(); ++y) {
      const float in_y =
        enable_half_pixel_centers ? y0 + (static_cast<float>(y) + 0.5f) * height_scale - 0.5f
        : (y0 + y) * height_scale;
      if (in_y < 0 || in_y > input.Height() - 1) continue;
      const int top_y_index = floorf(in_y);
      const int bottom_y_index = ceilf(in_y);
      const float y_lerp = in_y - std::floor(in_y);

      for (int x = 0; x < output->Width(); ++x) {
        const float in_x =
          enable_half_pixel_centers ? x0 + (static_cast<float>(x) + 0.5f) * width_scale - 0.5f
          : (x0 + x) * width_scale;
        if (in_x < 0 || in_x > input.Width() - 1) continue;
        const int left_x_index = floorf(in_x);
        const int right_x_index = ceilf(in_x);
        const float x_lerp = in_x - std::floor(in_x);

        for (int c = 0; c < output->Channels(); ++c) {
          const float top_left = input(image_id, top_y_index, left_x_index, c);
          const float top_right = input(image_id, top_y_index, right_x_index, c);
          const float bottom_left = input(image_id, bottom_y_index, left_x_index, c);
          const float bottom_right = input(image_id, bottom_y_index, right_x_index, c);
          const float top = top_left + (top_right - top_left) * x_lerp;
          const float bottom = bottom_left + (bottom_right - bottom_left) * x_lerp;
          (*output)(b, y, x, c) = static_cast<T1>(top + (bottom - top) * y_lerp);
        }
      }
    }
  }
}

TEST(CropAndResizeOpTest, CpuAgainstGpuFloat) {
  const int n = 2;
  const int ih = 1080;
  const int iw = 1920;
  const int c = 3;
  const int oh = 640;
  const int ow = 960;
  // test valid & invalid boxes
  const std::vector<float> boxes =
    {123.4, 567.8, 432.1, 876.5,
     111.1, 111.1, 222.2, 222.2,
     0.1, 2, 100, 2,
     10, 100, 11, 101,
     -1, 99, -100, 200,
     1e9, 1e9, 2e9, 2e9};
  const std::vector<int> boxes_idx = {0, 1, 1, 0, 1, 0};
  const int num_boxes = boxes.size() / 4;
  CHECK_EQ(num_boxes, static_cast<int>(boxes_idx.size()));
  std::vector<uint8_t> in_buf(n * ih * iw * c, 0);
  std::vector<float> out_buf(num_boxes * oh * ow * c, 0);
  std::iota(in_buf.begin(), in_buf.end(), 0);
  Tensor4D<uint8_t> in_tensor(in_buf.data(), n, ih, iw, c);
  Tensor4D<float> out_tensor(out_buf.data(), num_boxes, oh, ow, c);

  CropAndResizeCpu(true, in_tensor, num_boxes, boxes, boxes_idx, &out_tensor);

  // gpu routine
  DeviceContext ctx;
  // inputs
  DeviceTensor<uint8_t, 4> image_tensor(TensorShape({n, ih, iw, c}));
  DeviceTensor<float, 2> boxes_tensor(TensorShape({num_boxes, 4}));
  DeviceTensor<int, 1> boxes_idx_tensor(TensorShape({num_boxes}));
  // outputs
  DeviceTensor<float, 4> crops_tensor(TensorShape({num_boxes, oh, ow, c}));
  std::vector<float> results(crops_tensor.NumElements());
  // feed data
  image_tensor.mutable_buffer()->FromCpu(in_buf.data(), in_buf.size(), &ctx);
  boxes_tensor.mutable_buffer()->FromCpu(boxes.data(), boxes.size() * sizeof(float), &ctx);
  boxes_idx_tensor.mutable_buffer()->FromCpu(boxes_idx.data(), boxes_idx.size() * sizeof(int), &ctx);

  CropAndResize(
    ctx.stream(), n, num_boxes,
    image_tensor, boxes_tensor, boxes_idx_tensor, &crops_tensor);

  crops_tensor.buffer().ToCpu(results.data(), crops_tensor.NumBytes(), &ctx);
  ctx.Sync();

  for (size_t i = 0; i < out_buf.size(); i++) {
    EXPECT_NEAR(out_buf[i], results[i], 1);
  }
}

TEST(CropAndResizeOpTest, CpuAgainstGpuHalf) {
  const int n = 2;
  const int ih = 1080;
  const int iw = 1920;
  const int c = 3;
  const int oh = 640;
  const int ow = 960;
  // test valid & invalid boxes
  const std::vector<float> boxes =
    {123.4, 567.8, 432.1, 876.5,
     111.1, 111.1, 222.2, 222.2,
     0.1, 2, 100, 2,
     10, 100, 11, 101,
     -1, 99, -100, 200,
     1e9, 1e9, 2e9, 2e9};
  const std::vector<int> boxes_idx = {0, 1, 1, 0, 1, 0};
  const int num_boxes = boxes.size() / 4;
  CHECK_EQ(num_boxes, static_cast<int>(boxes_idx.size()));
  std::vector<uint8_t> in_buf(n * ih * iw * c, 0);
  std::vector<half> out_buf(num_boxes * oh * ow * c, 0.);
  std::iota(in_buf.begin(), in_buf.end(), 0);
  Tensor4D<uint8_t> in_tensor(in_buf.data(), n, ih, iw, c);
  Tensor4D<half> out_tensor(out_buf.data(), num_boxes, oh, ow, c);

  CropAndResizeCpu(true, in_tensor, num_boxes, boxes, boxes_idx, &out_tensor);

  // gpu routine
  DeviceContext ctx;
  // inputs
  DeviceTensor<uint8_t, 4> image_tensor(TensorShape({n, ih, iw, c}));
  DeviceTensor<float, 2> boxes_tensor(TensorShape({num_boxes, 4}));
  DeviceTensor<int, 1> boxes_idx_tensor(TensorShape({num_boxes}));
  // outputs
  DeviceTensor<half, 4> crops_tensor(TensorShape({num_boxes, oh, ow, c}));
  std::vector<half> results(crops_tensor.NumElements());
  // feed data
  image_tensor.mutable_buffer()->FromCpu(in_buf.data(), in_buf.size(), &ctx);
  boxes_tensor.mutable_buffer()->FromCpu(boxes.data(), boxes.size() * sizeof(float), &ctx);
  boxes_idx_tensor.mutable_buffer()->FromCpu(
      boxes_idx.data(), boxes_idx.size() * sizeof(int), &ctx);

  CropAndResize(
      ctx.stream(), n, num_boxes, image_tensor, boxes_tensor, boxes_idx_tensor, &crops_tensor);

  crops_tensor.buffer().ToCpu(results.data(), crops_tensor.NumBytes(), &ctx);
  ctx.Sync();

  for (size_t i = 0; i < out_buf.size(); i++) {
    EXPECT_NEAR(out_buf[i], results[i], 1);
  }
}

}  // namespace
}  // namespace cuda
