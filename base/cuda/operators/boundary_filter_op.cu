// Copyright @2023 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/cuda/operators/boundary_filter_op.h"

namespace cuda {

template <typename T>
__global__ void boundary_filter_kernel(int num_pixel, int image_height, int image_width, int channels,
                                       int pool_box_size, T value, const T* idata, T* odata) {
  const int pixel_idx = threadIdx.x + blockIdx.x * blockDim.x;
  if (pixel_idx >= num_pixel) {
    return;
  }
  int idx = pixel_idx;
  const int ic = idx % channels;
  idx /= channels;
  const int x = idx % image_width;
  idx /= image_width;
  const int y = idx % image_height;
  if (x < 0 || x >= image_width || y < 0 || y >= image_height) {
    return;
  }
  T min_center_val = idata[pixel_idx];
  T max_center_val = idata[pixel_idx];
  const int range = static_cast<int>(pool_box_size/2);
  for (int pool_offset_x = -range; pool_offset_x <= range; pool_offset_x++) {
    for (int pool_offset_y = -range; pool_offset_y <= range; pool_offset_y++) {
      const int coord_x = x + pool_offset_x;
      const int coord_y = y + pool_offset_y;
      if (coord_x < 0 || coord_x >= image_width || coord_y < 0 || coord_y >= image_height) {
        continue;
      }
      const int pool_pixel_id = (coord_x + coord_y * image_width) * channels + ic;
      if (min_center_val > idata[pool_pixel_id]) {
        min_center_val = idata[pool_pixel_id];
      }
      if (max_center_val < idata[pool_pixel_id]) {
        max_center_val = idata[pool_pixel_id];
      }
    }
  }
  odata[pixel_idx] = max_center_val == min_center_val ? idata[pixel_idx] : value;
}

template <typename T>
void BoundaryFilter(cudaStream_t stream,
                    const DeviceTensor<T, 3>& input_image,
                    DeviceTensor<T, 3>* mask_image) {
  // inputs
  const int image_height = input_image.Dimension(0);
  const int image_width = input_image.Dimension(1);
  const int channels = input_image.Dimension(2);
  // outputs
  CHECK(mask_image != nullptr);
  CHECK_EQ(mask_image->Dimension(0), input_image.Dimension(0));
  CHECK_EQ(mask_image->Dimension(1), input_image.Dimension(1));
  CHECK_EQ(mask_image->Dimension(2), input_image.Dimension(2));
  const int output_volume = image_height * image_width * channels;
  constexpr int kNumThreads = 512;
  const int num_blocks = DivUp(output_volume, kNumThreads);
  constexpr int kPoolBoxSize = 3;
  constexpr int kValue = 255;
  boundary_filter_kernel<T><<<num_blocks, kNumThreads, 0, stream>>>(output_volume,
                                                                    image_height,
                                                                    image_width,
                                                                    channels,
                                                                    kPoolBoxSize,
                                                                    kValue,
                                                                    input_image.raw_data(),
                                                                    mask_image->mutable_raw_data());
  CUDA_KERNEL_LAUNCH_CHECK();
}

template void BoundaryFilter(
    cudaStream_t stream,
    const DeviceTensor<int, 3>& input_image,
    DeviceTensor<int, 3>* mask_image);

template void BoundaryFilter(
    cudaStream_t stream,
    const DeviceTensor<float, 3>& input_image,
    DeviceTensor<float, 3>* mask_image);

template void BoundaryFilter(
    cudaStream_t stream,
    const DeviceTensor<uint8_t, 3>& input_image,
    DeviceTensor<uint8_t, 3>* mask_image);

}  // namespace cuda
