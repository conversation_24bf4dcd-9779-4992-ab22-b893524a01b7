// Copyright @2024 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/cuda/operators/tensor_validity_op.h"

#include <cuda_fp16.h>
#include "glog/logging.h"

#include "base/cuda/macros.h"

namespace cuda {
namespace {

constexpr int kNumThreads = 512;
constexpr int kChecksPerThread = 128;
constexpr int kChecksPerBlock = kNumThreads * kChecksPerThread;

__device__ __forceinline__ int IsInf(float val) { return isinf(val) ? 1 : 0; }

__device__ __forceinline__ int IsNaN(float val) { return isnan(val) ? 1 : 0; }

__device__ __forceinline__ int IsInf(half val) { return __hisinf(val) & 1; }

__device__ __forceinline__ int IsNaN(half val) { return __hisnan(val) ? 1 : 0; }

__device__ __forceinline__ int IsInf(short val) { return IsInf(__short_as_half(val)); }

__device__ __forceinline__ int IsNaN(short val) { return IsNaN(__short_as_half(val)); }

__device__ __forceinline__ half2 IsNaN(half2 val) { return __hisnan2(val); }

__device__ __forceinline__ int IsInf(int val) {  // use int4 to load 4 half2s in one op
  return IsInf(__short_as_half(val & 0xffff)) + IsInf(__short_as_half(val >> 16));
}

__device__ __forceinline__ half2 IsNaN(int val) {  // use int4 to load 4 half2s in one op
  return IsNaN(__halves2half2(__short_as_half(val & 0xffff), __short_as_half(val >> 16)));
}

__device__ __forceinline__ int ToInt(half2 val) { return __half2int_rz(val.x + val.y); }

__device__ __forceinline__ int ToInt(int val) { return val; }

#define IMPL_4(Check, Packed)                                         \
  __device__ __forceinline__ auto Check(Packed val) {                 \
    return Check(val.x) + Check(val.y) + Check(val.z) + Check(val.w); \
  }
#define IMPL_2(Check, Packed) \
  __device__ __forceinline__ auto Check(Packed val) { return Check(val.x) + Check(val.y); }

IMPL_4(IsInf, float4)
IMPL_2(IsInf, float2)
IMPL_4(IsInf, int4)
IMPL_4(IsInf, short4)
IMPL_2(IsInf, half2)

IMPL_4(IsNaN, float4)
IMPL_2(IsNaN, float2)
IMPL_4(IsNaN, int4)
IMPL_4(IsNaN, short4)

#undef IMPL_4
#undef IMPL_2

template <typename Element, typename Packed, typename NaNCounter>
__global__ void CheckTensorValidityKernel(const Element* data, int max_index, int* result_buffer) {
  __shared__ int shared_results[2];
  if (threadIdx.x == 0) {
    shared_results[0] = 0;
    shared_results[1] = 0;
  }
  __syncthreads();

  constexpr int kLoopPerThread = kChecksPerThread / (sizeof(Packed) / sizeof(Element));
  const int block_start = blockIdx.x * kNumThreads * kLoopPerThread;
  const int block_end = std::min(block_start + kNumThreads * kLoopPerThread, max_index);
  int inf_counter = 0;
  NaNCounter nan_counter{};
  for (int i = block_start + threadIdx.x; i < block_end; i += kNumThreads) {
    const Packed packed = reinterpret_cast<const Packed*>(data)[i];
    inf_counter += IsInf(packed);
    nan_counter += IsNaN(packed);
  }

#pragma unroll
  for (int i = 0; i < 2; i++) {
    const int thread_num = i == 0 ? inf_counter : ToInt(nan_counter);
    if (thread_num >= 0) {
      atomicAdd(&shared_results[i], thread_num);
    }
  }
  __syncthreads();
  if (threadIdx.x == 0) {
#pragma unroll
    for (int i = 0; i < 2; i++) {
      if (shared_results[i] >= 0) {
        atomicAdd(&result_buffer[i], shared_results[i]);
      }
    }
  }
}

}  // namespace

void CheckTensorValidity(const void* tensor_data,
                         nvinfer1::DataType data_type,
                         int byte_size,
                         cudaStream_t stream,
                         int* result_buffer) {
#define Kernel(Element, Packed, NaNCounter)                                                     \
  do {                                                                                          \
    CheckTensorValidityKernel<Element, Packed, NaNCounter><<<blocks, kNumThreads, 0, stream>>>( \
        data, tensor_size / (sizeof(Packed) / sizeof(Element)), result_buffer);                 \
    CUDA_KERNEL_LAUNCH_CHECK();                                                                 \
  } while (0)

  const int tensor_size = byte_size / (data_type == nvinfer1::DataType::kFLOAT ? 4 : 2);
  const int blocks = (tensor_size + kChecksPerBlock - 1) / kChecksPerBlock;
  if (data_type == nvinfer1::DataType::kFLOAT) {
    const float* data = reinterpret_cast<const float*>(tensor_data);
    if (tensor_size % 4 == 0) {
      Kernel(float, float4, int);
    } else if (tensor_size % 2 == 0) {
      Kernel(float, float2, int);
    } else {
      Kernel(float, float, int);
    }
  } else if (data_type == nvinfer1::DataType::kHALF) {
    const half* data = reinterpret_cast<const half*>(tensor_data);
    if (tensor_size % 8 == 0) {
      Kernel(half, int4, half2);
    } else if (tensor_size % 4 == 0) {
      Kernel(half, short4, int);
    } else if (tensor_size % 2 == 0) {
      Kernel(half, half2, half2);
    } else {
      Kernel(half, half, int);
    }
  } else {
    LOG(FATAL) << "Unsupported data_type = " << static_cast<int>(data_type);
  }
#undef Kernel
}

}  // namespace cuda
