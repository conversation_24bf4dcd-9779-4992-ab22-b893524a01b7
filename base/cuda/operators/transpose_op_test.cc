// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include <chrono>
#include <functional>
#include <numeric>
#include <vector>

#include <cuda_fp16.h>
#include "gtest/gtest.h"

#include "base/cuda/device_buffer.h"
#include "base/cuda/device_utils.h"
#include "base/cuda/operators/transpose_op.h"

namespace cuda {
namespace {

// [n, x, y] -> [n, y, x]
template <typename T>
void TransposeCpu(int n, int x, int y, const T* src, T* dst) {
  for (int in = 0; in < n; in++) {
    for (int ix = 0; ix < x; ix++) {
      for (int iy = 0; iy < y; iy++) {
        dst[ix + x * (iy + y * in)] = src[iy + y * (ix + x * in)];
      }
    }
  }
}

template <typename T>
void Transpose(int batch,
               int dim_x,
               int dim_y,
               int iterations,
               std::function<void(cudaStream_t, int, int, int, const T*, T*)> transpose) {
  const int kNumElements = batch * dim_x * dim_y;
  std::vector<T> src(kNumElements);
  std::vector<T> dst(kNumElements);
  std::iota(src.begin(), src.end(), 0.0f);

  DeviceContext ctx;
  DeviceTimer timer(ctx);
  DeviceBuffer src_dev_buf(kNumElements * sizeof(T));
  DeviceBuffer dst_dev_buf(kNumElements * sizeof(T));
  src_dev_buf.FromCpu(src.data(), kNumElements * sizeof(T), &ctx);
  std::vector<T> results(kNumElements);

  float t_cpu, t_gpu;
  for (int i = 0; i < iterations; i++) {
    // cpu
    auto t1 = std::chrono::high_resolution_clock::now();
    TransposeCpu(batch, dim_x, dim_y, src.data(), dst.data());
    auto t2 = std::chrono::high_resolution_clock::now();
    t_cpu = std::chrono::duration<float, std::milli>(t2 - t1).count();

    // gpu
    timer.Start();
    transpose(ctx.stream(),
              batch,
              dim_x,
              dim_y,
              reinterpret_cast<const T*>(src_dev_buf.data()),
              reinterpret_cast<T*>(dst_dev_buf.mutable_data()));
    timer.Stop();
    t_gpu = timer.ms();

    dst_dev_buf.ToCpu(results.data(), kNumElements * sizeof(T), &ctx);
    ctx.Sync();

    for (int i = 0; i < kNumElements; i++) {
      EXPECT_EQ(dst[i], results[i]);
    }

    printf("Transpose %dx%dx%d (CPU vs GPU) = %.2f ms / %.2f ms\n", batch, dim_x, dim_y, t_cpu, t_gpu);
  }
}

TEST(TransposeTest, BasicTest) {
  Transpose<float>(1, 100, 100, 1, Transpose2DNaiveWrapper<float>);
  Transpose<float>(1, 1000, 3, 1, Transpose2DNaiveWrapper<float>);
  Transpose<float>(5, 1000, 3, 1, Transpose2DNaiveWrapper<float>);
  Transpose<float>(5, 513, 99, 1, Transpose2DNaiveWrapper<float>);
  Transpose<float>(12, 345, 678, 1, Transpose2DNaiveWrapper<float>);

  Transpose<uint8_t>(1, 100, 100, 1, Transpose2DTiledWrapper<uint8_t>);
  Transpose<uint8_t>(1, 1000, 3, 1, Transpose2DTiledWrapper<uint8_t>);
  Transpose<uint8_t>(5, 1000, 3, 1, Transpose2DTiledWrapper<uint8_t>);
  Transpose<uint8_t>(5, 513, 99, 1, Transpose2DTiledWrapper<uint8_t>);
  Transpose<uint8_t>(12, 345, 678, 1, Transpose2DTiledWrapper<uint8_t>);

  Transpose<half>(1, 100, 100, 1, Transpose2DWrapper<half>);
  Transpose<half>(1, 1000, 3, 1, Transpose2DWrapper<half>);
  Transpose<half>(5, 1000, 3, 1, Transpose2DWrapper<half>);
  Transpose<half>(5, 513, 99, 1, Transpose2DWrapper<half>);
  Transpose<half>(12, 345, 678, 1, Transpose2DWrapper<half>);
}

}  // namespace
}  // namespace cuda
