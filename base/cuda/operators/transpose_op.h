// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <algorithm>
#include <memory>
#include <string>
#include <utility>

#include <cuda_runtime.h>
#include "glog/logging.h"

#include "base/cuda/device_utils.h"

namespace cuda {

template <typename T>
void Transpose2DNaiveWrapper(
  cudaStream_t stream, int batch, int dim_x, int dim_y, const T* idata, T* odata);

template <typename T>
void Transpose2DTiledWrapper(
  cudaStream_t stream, int batch, int dim_x, int dim_y, const T* idata, T* odata);

template <typename T>
void Transpose2DWrapper(
  cudaStream_t stream, int batch, int dim_x, int dim_y, const T* idata, T* odata);

}  // namespace cuda
