// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/cuda/operators/image_type_cast_op.h"

#include "base/cuda/macros.h"

namespace cuda {

template <typename T0, typename T1>
__global__ void type_cast_kernel(
  int n, int batch, int in_height, int in_width, int channels,
  float convert_factor, const T0* idata, T1* odata) {
  int tid = threadIdx.x + blockIdx.x * blockDim.x;
  if (tid >= n) return;
  int idx = tid;
  const int c = idx % channels;
  idx /= channels;
  const int x = idx % in_width;
  idx /= in_width;
  const int y = idx % in_height;
  const int b = idx / in_height;

  const int index = ((b * in_height + y) * in_width + x) * channels + c;
  odata[index] = static_cast<T1>(static_cast<float>(idata[index]) * convert_factor);
}

template <typename T0, typename T1>
void ImageTypeCastWrapper(
  cudaStream_t stream, int batch_size, int image_height, int image_width,
  int num_channels, float convert_factor, const T0* idata, T1* odata) {
  const int output_volume = batch_size * image_height * image_width * num_channels;
  constexpr int kNumThreads = 512;
  int num_blocks = (output_volume + kNumThreads - 1) / kNumThreads;
  type_cast_kernel<<<num_blocks, kNumThreads, 0, stream>>>(
      output_volume, batch_size, image_height, image_width, num_channels, convert_factor,
      idata, odata);
  CUDA_KERNEL_LAUNCH_CHECK();
}

template void ImageTypeCastWrapper(
  cudaStream_t stream, int batch_size, int image_height, int image_width,
  int num_channels, float converter_factor, const float* idata, uint8_t* odata);

template void ImageTypeCastWrapper(
  cudaStream_t stream, int batch_size, int image_height, int image_width,
  int num_channels, float convert_factor, const uint8_t* idata, float* odata);

}  // namespace cuda
