// Copyright @2023 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <algorithm>
#include <memory>
#include <string>
#include <utility>

#include <cuda_runtime.h>
#include "glog/logging.h"

#include "base/cuda/device_utils.h"
#include "base/cuda/tensor.h"

namespace cuda {

template <typename T0, typename T1>
void UndistortImageWrapper(cudaStream_t stream,
                           const std::string& cam_type,
                           const ConstDeviceTensorView<T0, 3>& src_img,
                           const ConstDeviceTensorView<T1, 2>& cam_k,
                           const ConstDeviceTensorView<T1, 1>& distort,
                           const ConstDeviceTensorView<T1, 2>& new_cam_k_inv,
                           DeviceTensor<T0, 3>* out_img);

template <typename T>
void BuildPinholeUndistortMap(cudaStream_t stream,
                              const ConstDeviceTensorView<T, 2>& cam_k,
                              const ConstDeviceTensorView<T, 1>& distort,
                              const ConstDeviceTensorView<T, 2>& new_cam_k_inv,
                              DeviceTensor<T, 2>* map_x,
                              DeviceTensor<T, 2>* map_y);

template <typename T>
void BuildFisheyeUndistortMap(cudaStream_t stream,
                              const ConstDeviceTensorView<T, 2>& cam_k,
                              const ConstDeviceTensorView<T, 1>& distort,
                              const ConstDeviceTensorView<T, 2>& new_cam_k_inv,
                              DeviceTensor<T, 2>* map_x,
                              DeviceTensor<T, 2>* map_y);

template <typename T0, typename T1>
void UndistortRemap(cudaStream_t stream,
                    const ConstDeviceTensorView<T0, 3>& src_img,
                    const ConstDeviceTensorView<T1, 2>& map_x,
                    const ConstDeviceTensorView<T1, 2>& map_y,
                    DeviceTensor<T0, 3>* out_img);

template <typename T0, typename T1>
void UndistortRemap(cudaStream_t stream,
                    const ConstDeviceTensorView<T0, 3>& src_img,
                    const ConstDeviceTensorView<T1, 2>& map_x,
                    const ConstDeviceTensorView<T1, 2>& map_y,
                    DeviceBuffer* out_img);
                    
}  // namespace cuda
