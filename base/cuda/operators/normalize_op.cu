// Copyright @2024 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/cuda/operators/normalize_op.h"

namespace cuda {
namespace {

__constant__ float kMeanVec[3] = {0.485, 0.456, 0.406};
__constant__ float kStdVec[3] = {0.229, 0.224, 0.225};
__constant__ float kRatio = 255.0;

}  // namespace

template <typename T>
__device__ float z_score_normalize(T idata, float mean, float std) {
  return (static_cast<float>(idata) / kRatio - mean) / std;
}

template <typename T>
__device__ float min_max_normalize(T idata) {
  return static_cast<float>(idata) / kRatio;
}

template <typename T0, typename T1>
__global__ void normalize_kernel(int n,
                                 int in_height,
                                 int in_width,
                                 int in_channel,
                                 NormalizeType type,
                                 bool is_channel_first,
                                 const T0* idata,
                                 T1* odata) {
  int tid = threadIdx.x + blockIdx.x * blockDim.x;
  if (tid >= n) return;
  int idx = tid;
  const int c = idx % in_channel;
  idx /= in_channel;
  const int w = idx % in_width;
  idx /= in_width;
  const int h = idx % in_height;
  const int b = idx / in_height;
  int o_idx = tid;
  if (is_channel_first) {
    o_idx = w + (h + (c + b * in_channel) * in_height) * in_width;
  }
  if (NormalizeType::kZScore == type) {
    odata[o_idx] = static_cast<T1>(z_score_normalize(idata[tid], kMeanVec[c], kStdVec[c]));
  } else if (NormalizeType::kMinMax == type) {
    odata[o_idx] = static_cast<T1>(min_max_normalize(idata[tid]));
  }
}

template <typename T0, typename T1>
void Normalize(const ConstDeviceTensorView<T0, 4>& input,
               cudaStream_t stream,
               NormalizeType type,
               bool is_channel_first,
               DeviceTensorView<T1, 4>* output) {
  CHECK(output != nullptr);
  CHECK_EQ(input.Dimension(0), output->Dimension(0));
  const int in_height = input.Dimension(1);
  const int in_width = input.Dimension(2);
  const int in_channels = input.Dimension(3);
  if (is_channel_first) {
    CHECK_EQ(output->Dimension(1), in_channels);
    CHECK_EQ(output->Dimension(2), in_height);
    CHECK_EQ(output->Dimension(3), in_width);
  } else {
    CHECK_EQ(output->Dimension(1), in_height);
    CHECK_EQ(output->Dimension(2), in_width);
    CHECK_EQ(output->Dimension(3), in_channels);
  }
  const int output_volume = output->NumElements();
  constexpr int kNumThreads = 512;
  const int num_blocks = DivUp(output_volume, kNumThreads);
  normalize_kernel<<<num_blocks, kNumThreads, 0, stream>>>(output_volume,
                                                           in_height,
                                                           in_width,
                                                           in_channels,
                                                           type,
                                                           is_channel_first,
                                                           input.raw_data(),
                                                           output->mutable_raw_data());
  CUDA_KERNEL_LAUNCH_CHECK();
}

template void Normalize(const ConstDeviceTensorView<uint8_t, 4>& input,
                        cudaStream_t stream,
                        NormalizeType type,
                        bool is_channel_first,
                        DeviceTensorView<float, 4>* output);
template void Normalize(const ConstDeviceTensorView<float, 4>& input,
                        cudaStream_t stream,
                        NormalizeType type,
                        bool is_channel_first,
                        DeviceTensorView<float, 4>* output);

}  // namespace cuda
