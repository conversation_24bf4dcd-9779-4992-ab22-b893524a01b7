// Copyright @2023 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>
//          <PERSON> (<EMAIL>)

#pragma once

#include <cuda_runtime.h>

namespace cuda {

template <typename Element>
size_t PillarScatterWrapper(cudaStream_t stream,
                            const Element* pillar_features_data,
                            const int* coords_data,
                            const int* pillar_num_data,
                            int batch_size,
                            int max_pillar_num,
                            int num_features,
                            int feature_x,
                            int feature_y,
                            bool output_nhwc,
                            Element* spatial_feature_data);

}  // namespace cuda
