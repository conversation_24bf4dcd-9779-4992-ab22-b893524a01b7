// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/cuda/operators/resize_bilinear_op.h"

#include <cuda_fp16.h>

#include "base/cuda/macros.h"

namespace cuda {

/**
 * task ~ output volume
 * tensor format: NHWC
 * center at half pixel
 * results aligned with cv::INTER_LINEAR
 */
template <typename T0, typename T1>
__global__ void resize_bilinear_kernel(
  int n, int batch, float height_scale, float width_scale,
  int in_height, int in_width, int channels,
  int out_height, int out_width, const T0* idata, T1* odata) {
  int tid = threadIdx.x + blockIdx.x * blockDim.x;
  if (tid >= n) return;
  int idx = tid;
  const int c = idx % channels;
  idx /= channels;
  const int x = idx % out_width;
  idx /= out_width;
  const int y = idx % out_height;
  const int b = idx / out_height;

  const float in_y = (static_cast<float>(y) + 0.5f) * height_scale - 0.5f;
  const int top_y_index = in_y > 0.0f ? floorf(in_y) : 0;
  const int bottom_y_index = (in_y < in_height - 1) ? ceilf(in_y) : in_height - 1;
  const float y_lerp = in_y - floorf(in_y);

  const float in_x = (static_cast<float>(x) + 0.5f) * width_scale - 0.5f;
  const int left_x_index = in_x > 0.0f ? floorf(in_x) : 0;
  const int right_x_index = (in_x < in_width - 1) ? ceilf(in_x) : in_width - 1;
  const float x_lerp = in_x - left_x_index;

  const float top_left = idata[((b * in_height + top_y_index) * in_width + left_x_index) * channels + c];
  const float top_right = idata[((b * in_height + top_y_index) * in_width + right_x_index) * channels + c];
  const float bottom_left = idata[((b * in_height + bottom_y_index) * in_width + left_x_index) * channels + c];
  const float bottom_right = idata[((b * in_height + bottom_y_index) * in_width + right_x_index) * channels + c];

  const float top = top_left + (top_right - top_left) * x_lerp;
  const float bottom = bottom_left + (bottom_right - bottom_left) * x_lerp;
  odata[tid] = static_cast<T1>(top + (bottom - top) * y_lerp);
}

template <typename T0, typename T1>
void ResizeBilinearWrapper(
  cudaStream_t stream, int batch_size, int input_height, int input_width,
  int output_height, int output_width, int num_channels,
  const T0* idata, T1* odata) {
  float h_scale = static_cast<float>(input_height) / static_cast<float>(output_height);
  float w_scale = static_cast<float>(input_width) / static_cast<float>(output_width);
  int output_volume = batch_size * output_height * output_width * num_channels;
  constexpr int kNumThreads = 512;
  int num_blocks = (output_volume + kNumThreads - 1) / kNumThreads;
  resize_bilinear_kernel<T0, T1><<<num_blocks, kNumThreads, 0, stream>>>(output_volume,
    batch_size, h_scale, w_scale, input_height, input_width, num_channels,
    output_height, output_width, idata, odata);
  CUDA_KERNEL_LAUNCH_CHECK();
}

#define RESIZE_BILINERA_WRAPPER(T0, T1)        \
  template void ResizeBilinearWrapper<T0, T1>( \
      cudaStream_t, int, int, int, int, int, int, const T0*, T1*);
RESIZE_BILINERA_WRAPPER(uint8_t, half)
RESIZE_BILINERA_WRAPPER(uint8_t, float)
RESIZE_BILINERA_WRAPPER(uint8_t, uint8_t)
RESIZE_BILINERA_WRAPPER(float, half)
RESIZE_BILINERA_WRAPPER(float, float)
RESIZE_BILINERA_WRAPPER(half, half)
#undef RESIZE_BILINERA_WRAPPER

}  // namespace cuda
