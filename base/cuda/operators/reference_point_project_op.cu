// Copyright @2023 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/cuda/operators/reference_point_project_op.h"

#include "base/cuda/macros.h"

namespace cuda {
namespace {

constexpr float kEpsilon = 1e-5f;
constexpr int kIntrinsicMatrixSize = 9;
constexpr int kTransformMatrixSize = 16;

}  // namespace

/**
 * task ~ output volume
 * tensor format: N*num_camera*grid_width*grid_length*Z
 */

template <typename T0, typename T1>
__global__ void reference_point_project_kernel(int n,
                                               int grid_width,
                                               int grid_length,
                                               float grid_center_x,
                                               float grid_center_y,
                                               float resolution_x,
                                               float resolution_y,
                                               int num_sampled_points_in_pillar,
                                               int num_camera,
                                               int output_offset_per_camera,
                                               const bool* is_camera_valid,
                                               const int* image_width,
                                               const int* image_height,
                                               const bool* is_fisheye,
                                               const float* z_value,
                                               const float* intrinsic_matrix,
                                               const float* transform_matrix,
                                               T0* projection_result,
                                               T1* mask) {
  int index = blockDim.x * blockIdx.x + threadIdx.x;
  if (index >= n) {
    return;
  }
  const int camera_index = index % num_camera;
  index /= num_camera;
  const int result_index = camera_index * output_offset_per_camera + index;
  if (!is_camera_valid[camera_index]) {
    projection_result[result_index * 2] = 0.0f;
    projection_result[result_index * 2 + 1] = 0.0f;
    mask[result_index] = false;
    return;
  }
  const int grid_x = (index / (grid_length * num_sampled_points_in_pillar)) % grid_width;
  const int grid_y = (index / num_sampled_points_in_pillar) % grid_length;
  const int grid_z = index % num_sampled_points_in_pillar;
  const float reference_point_x = (grid_x - grid_center_x + 0.5) * resolution_x;
  const float reference_point_y = (grid_y - grid_center_y + 0.5) * resolution_y;
  const float reference_point_z = z_value[grid_z];
  // transform_matrix is lidar_coord to camera_coord
  // transform_matrix[0 … (kTransformMatrixSize -1)] is the transform_matrix of first camera
  const int transform_matrix_offset = camera_index * kTransformMatrixSize;
  const float transformed_x = transform_matrix[transform_matrix_offset + 0] * reference_point_x +
                              transform_matrix[transform_matrix_offset + 1] * reference_point_y +
                              transform_matrix[transform_matrix_offset + 2] * reference_point_z +
                              transform_matrix[transform_matrix_offset + 3];
  const float transformed_y = transform_matrix[transform_matrix_offset + 4] * reference_point_x +
                              transform_matrix[transform_matrix_offset + 5] * reference_point_y +
                              transform_matrix[transform_matrix_offset + 6] * reference_point_z +
                              transform_matrix[transform_matrix_offset + 7];
  const float transformed_z = transform_matrix[transform_matrix_offset + 8] * reference_point_x +
                              transform_matrix[transform_matrix_offset + 9] * reference_point_y +
                              transform_matrix[transform_matrix_offset + 10] * reference_point_z +
                              transform_matrix[transform_matrix_offset + 11];
  // intrinsic_matrix is camera_coord to image
  // intrinsic_matrix[0 … (kIntrinsicMatrixSize -1)] is focal_x center_x focal_y center_y  and
  // distort_params of first camera
  const int intrinsic_matrix_offset = camera_index * kIntrinsicMatrixSize;
  const float focal_x = intrinsic_matrix[intrinsic_matrix_offset + 0];
  const float center_x = intrinsic_matrix[intrinsic_matrix_offset + 1];
  const float focal_y = intrinsic_matrix[intrinsic_matrix_offset + 2];
  const float center_y = intrinsic_matrix[intrinsic_matrix_offset + 3];
  const float distort_params_0 = intrinsic_matrix[intrinsic_matrix_offset + 4];
  const float distort_params_1 = intrinsic_matrix[intrinsic_matrix_offset + 5];
  const float distort_params_2 = intrinsic_matrix[intrinsic_matrix_offset + 6];
  const float distort_params_3 = intrinsic_matrix[intrinsic_matrix_offset + 7];
  const float distort_params_4 = intrinsic_matrix[intrinsic_matrix_offset + 8];
  const float pt2d_x = transformed_x / transformed_z;
  const float pt2d_y = transformed_y / transformed_z;
  float pt2d_distort_x = 0.0f;
  float pt2d_distort_y = 0.0f;
  if (is_fisheye[camera_index]) {
    // fisheye pixel_denormalize
    float r = std::sqrt(pt2d_x * pt2d_x + pt2d_y * pt2d_y);
    if (std::fabs(r) < 1e-8) {
      r = 1e-8;
    }
    const float theta = std::atan(r);
    const float theta_2 = theta * theta;
    const float theta_4 = theta_2 * theta_2;
    const float theta_6 = theta_4 * theta_2;
    const float theta_8 = theta_4 * theta_4;
    const float k_radial = theta * (1.0 + distort_params_0 * theta_2 + distort_params_1 * theta_4 +
                                    distort_params_2 * theta_6 + distort_params_3 * theta_8);
    pt2d_distort_x = k_radial / r * pt2d_x;
    pt2d_distort_y = k_radial / r * pt2d_y;
  } else {
    // pin-hole pixel_denormalize
    const float r_sq = pt2d_x * pt2d_x + pt2d_y * pt2d_y;
    const float pt2d_radial = 1 + distort_params_0 * r_sq + distort_params_1 * r_sq * r_sq +
                              distort_params_4 * r_sq * r_sq * r_sq;
    const float pt2d_radial_x = pt2d_x * pt2d_radial;
    const float pt2d_radial_y = pt2d_y * pt2d_radial;
    const float dpt2d_x =
        2 * distort_params_2 * pt2d_x * pt2d_y + distort_params_3 * (r_sq + 2 * pt2d_x * pt2d_x);
    const float dpt2d_y =
        distort_params_2 * (r_sq + 2 * pt2d_y * pt2d_y) + 2 * distort_params_3 * pt2d_x * pt2d_y;
    pt2d_distort_x = pt2d_radial_x + dpt2d_x;
    pt2d_distort_y = pt2d_radial_y + dpt2d_y;
  }
  const float projection_result_x = focal_x * pt2d_distort_x + center_x;
  const float projection_result_y = focal_y * pt2d_distort_y + center_y;
  projection_result[result_index * 2] = projection_result_x;
  projection_result[result_index * 2 + 1] = projection_result_y;
  if (transformed_z > kEpsilon && projection_result_x >= 0 &&
      projection_result_x < image_width[camera_index] && projection_result_y >= 0 &&
      projection_result_y < image_height[camera_index]) {
    mask[result_index] = true;
  } else {
    mask[result_index] = false;
  }
}

template <typename T0, typename T1>
__global__ void reference_point_adaptive_resolution_project_kernel(int n,
                                                                   int grid_width,
                                                                   int grid_length,
                                                                   float grid_center_x,
                                                                   float grid_center_y,
                                                                   float resolution_x,
                                                                   float resolution_y,
                                                                   int num_sampled_points_in_pillar,
                                                                   int num_camera,
                                                                   int output_offset_per_camera,
                                                                   const bool* is_camera_valid,
                                                                   const int* image_width,
                                                                   const int* image_height,
                                                                   const bool* is_fisheye,
                                                                   const float* z_value,
                                                                   const float* intrinsic_matrix,
                                                                   const float* transform_matrix,
                                                                   const float* adaptive_coeffs,
                                                                   T0* projection_result,
                                                                   T1* mask) {
  int index = blockDim.x * blockIdx.x + threadIdx.x;
  if (index >= n) {
    return;
  }
  const int camera_index = index % num_camera;
  index /= num_camera;
  const int result_index = camera_index * output_offset_per_camera + index;
  if (!is_camera_valid[camera_index]) {
    projection_result[result_index * 2] = 0.0f;
    projection_result[result_index * 2 + 1] = 0.0f;
    mask[result_index] = false;
    return;
  }
  const int grid_x = (index / (grid_length * num_sampled_points_in_pillar)) % grid_width;
  const int grid_y = (index / num_sampled_points_in_pillar) % grid_length;
  const int grid_z = index % num_sampled_points_in_pillar;
  const float grid_x_to_center = (grid_x - grid_center_x);
  const float grid_y_to_center = (grid_y - grid_center_y);
  float x_sign = (grid_x_to_center > 0.0f) ? 1.0f : -1.0f;
  float y_sign = (grid_y_to_center > 0.0f) ? 1.0f : -1.0f;
  const float grid_x_to_center_norm = fabsf(grid_x_to_center);
  const float grid_y_to_center_norm = fabsf(grid_y_to_center);
  // wiki: https://km.sankuai.com/collabpage/2062300395#b-e2a8508974834acda9bfcff7d0e2b3ef
  const float reference_point_x =
      x_sign *
      (adaptive_coeffs[2] +
       grid_x_to_center_norm * (adaptive_coeffs[1] + grid_x_to_center_norm * adaptive_coeffs[0]));
  const float reference_point_y =
      y_sign *
      (adaptive_coeffs[2] +
       grid_y_to_center_norm * (adaptive_coeffs[1] + grid_y_to_center_norm * adaptive_coeffs[0]));
  const float reference_point_z = z_value[grid_z];
  // transform_matrix is lidar_coord to camera_coord
  // transform_matrix[0 … (kTransformMatrixSize -1)] is the transform_matrix of first camera
  const int transform_matrix_offset = camera_index * kTransformMatrixSize;
  const float transformed_x = transform_matrix[transform_matrix_offset + 0] * reference_point_x +
                              transform_matrix[transform_matrix_offset + 1] * reference_point_y +
                              transform_matrix[transform_matrix_offset + 2] * reference_point_z +
                              transform_matrix[transform_matrix_offset + 3];
  const float transformed_y = transform_matrix[transform_matrix_offset + 4] * reference_point_x +
                              transform_matrix[transform_matrix_offset + 5] * reference_point_y +
                              transform_matrix[transform_matrix_offset + 6] * reference_point_z +
                              transform_matrix[transform_matrix_offset + 7];
  const float transformed_z = transform_matrix[transform_matrix_offset + 8] * reference_point_x +
                              transform_matrix[transform_matrix_offset + 9] * reference_point_y +
                              transform_matrix[transform_matrix_offset + 10] * reference_point_z +
                              transform_matrix[transform_matrix_offset + 11];
  // intrinsic_matrix is camera_coord to image
  // intrinsic_matrix[0 … (kIntrinsicMatrixSize -1)] is focal_x center_x focal_y center_y  and
  // distort_params of first camera
  const int intrinsic_matrix_offset = camera_index * kIntrinsicMatrixSize;
  const float focal_x = intrinsic_matrix[intrinsic_matrix_offset + 0];
  const float center_x = intrinsic_matrix[intrinsic_matrix_offset + 1];
  const float focal_y = intrinsic_matrix[intrinsic_matrix_offset + 2];
  const float center_y = intrinsic_matrix[intrinsic_matrix_offset + 3];
  const float distort_params_0 = intrinsic_matrix[intrinsic_matrix_offset + 4];
  const float distort_params_1 = intrinsic_matrix[intrinsic_matrix_offset + 5];
  const float distort_params_2 = intrinsic_matrix[intrinsic_matrix_offset + 6];
  const float distort_params_3 = intrinsic_matrix[intrinsic_matrix_offset + 7];
  const float distort_params_4 = intrinsic_matrix[intrinsic_matrix_offset + 8];
  const float pt2d_x = transformed_x / transformed_z;
  const float pt2d_y = transformed_y / transformed_z;
  float pt2d_distort_x = 0.0f;
  float pt2d_distort_y = 0.0f;
  if (is_fisheye[camera_index]) {
    // fisheye pixel_denormalize
    float r = std::sqrt(pt2d_x * pt2d_x + pt2d_y * pt2d_y);
    if (std::fabs(r) < 1e-8) {
      r = 1e-8;
    }
    const float theta = std::atan(r);
    const float theta_2 = theta * theta;
    const float theta_4 = theta_2 * theta_2;
    const float theta_6 = theta_4 * theta_2;
    const float theta_8 = theta_4 * theta_4;
    const float k_radial = theta * (1.0 + distort_params_0 * theta_2 + distort_params_1 * theta_4 +
                                    distort_params_2 * theta_6 + distort_params_3 * theta_8);
    pt2d_distort_x = k_radial / r * pt2d_x;
    pt2d_distort_y = k_radial / r * pt2d_y;
  } else {
    // pin-hole pixel_denormalize
    const float r_sq = pt2d_x * pt2d_x + pt2d_y * pt2d_y;
    const float pt2d_radial = 1 + distort_params_0 * r_sq + distort_params_1 * r_sq * r_sq +
                              distort_params_4 * r_sq * r_sq * r_sq;
    const float pt2d_radial_x = pt2d_x * pt2d_radial;
    const float pt2d_radial_y = pt2d_y * pt2d_radial;
    const float dpt2d_x =
        2 * distort_params_2 * pt2d_x * pt2d_y + distort_params_3 * (r_sq + 2 * pt2d_x * pt2d_x);
    const float dpt2d_y =
        distort_params_2 * (r_sq + 2 * pt2d_y * pt2d_y) + 2 * distort_params_3 * pt2d_x * pt2d_y;
    pt2d_distort_x = pt2d_radial_x + dpt2d_x;
    pt2d_distort_y = pt2d_radial_y + dpt2d_y;
  }
  const float projection_result_x = focal_x * pt2d_distort_x + center_x;
  const float projection_result_y = focal_y * pt2d_distort_y + center_y;
  projection_result[result_index * 2] = projection_result_x;
  projection_result[result_index * 2 + 1] = projection_result_y;
  if (transformed_z > kEpsilon && projection_result_x >= 0 &&
      projection_result_x < image_width[camera_index] && projection_result_y >= 0 &&
      projection_result_y < image_height[camera_index]) {
    mask[result_index] = true;
  } else {
    mask[result_index] = false;
  }
}

template <typename T0, typename T1>
void ReferencePointProjectWrapper(cudaStream_t stream,
                                  int batch_size,
                                  int grid_width,
                                  int grid_length,
                                  float grid_center_x,
                                  float grid_center_y,
                                  float resolution_x,
                                  float resolution_y,
                                  int num_sampled_points_in_pillar,
                                  int num_camera,
                                  const bool* is_camera_valid,
                                  const int* image_width,
                                  const int* image_height,
                                  const bool* is_fisheye,
                                  const float* z_value,
                                  const float* intrinsic_matrix,
                                  const float* transform_matrix,
                                  T0* projection_result,
                                  T1* mask) {
  const int output_offset_per_camera = grid_width * grid_length * num_sampled_points_in_pillar;
  const int output_volume = batch_size * num_camera * output_offset_per_camera;
  constexpr int kNumThreads = 512;
  const int num_blocks = (output_volume + kNumThreads - 1) / kNumThreads;
  reference_point_project_kernel<T0, T1>
      <<<num_blocks, kNumThreads, 0, stream>>>(output_volume,
                                               grid_width,
                                               grid_length,
                                               grid_center_x,
                                               grid_center_y,
                                               resolution_x,
                                               resolution_y,
                                               num_sampled_points_in_pillar,
                                               num_camera,
                                               output_offset_per_camera,
                                               is_camera_valid,
                                               image_width,
                                               image_height,
                                               is_fisheye,
                                               z_value,
                                               intrinsic_matrix,
                                               transform_matrix,
                                               projection_result,
                                               mask);
  CUDA_KERNEL_LAUNCH_CHECK();
}

template <typename T0, typename T1>
void ReferencePointProjectAdaptiveResolutionWrapper(cudaStream_t stream,
                                                    int batch_size,
                                                    int grid_width,
                                                    int grid_length,
                                                    float grid_center_x,
                                                    float grid_center_y,
                                                    float resolution_x,
                                                    float resolution_y,
                                                    int num_sampled_points_in_pillar,
                                                    int num_camera,
                                                    const bool* is_camera_valid,
                                                    const int* image_width,
                                                    const int* image_height,
                                                    const bool* is_fisheye,
                                                    const float* z_value,
                                                    const float* intrinsic_matrix,
                                                    const float* transform_matrix,
                                                    const float* adaptive_coeffs,
                                                    T0* projection_result,
                                                    T1* mask) {
  const int output_offset_per_camera = grid_width * grid_length * num_sampled_points_in_pillar;
  const int output_volume = batch_size * num_camera * output_offset_per_camera;
  constexpr int kNumThreads = 512;
  const int num_blocks = (output_volume + kNumThreads - 1) / kNumThreads;
  reference_point_adaptive_resolution_project_kernel<T0, T1>
      <<<num_blocks, kNumThreads, 0, stream>>>(output_volume,
                                               grid_width,
                                               grid_length,
                                               grid_center_x,
                                               grid_center_y,
                                               resolution_x,
                                               resolution_y,
                                               num_sampled_points_in_pillar,
                                               num_camera,
                                               output_offset_per_camera,
                                               is_camera_valid,
                                               image_width,
                                               image_height,
                                               is_fisheye,
                                               z_value,
                                               intrinsic_matrix,
                                               transform_matrix,
                                               adaptive_coeffs,
                                               projection_result,
                                               mask);
  CUDA_KERNEL_LAUNCH_CHECK();
}

template void ReferencePointProjectWrapper(cudaStream_t stream,
                                           int batch_size,
                                           int grid_width,
                                           int grid_length,
                                           float grid_center_x,
                                           float grid_center_y,
                                           float resolution_x,
                                           float resolution_y,
                                           int num_sampled_points_in_pillar,
                                           int num_camera,
                                           const bool* is_camera_valid,
                                           const int* image_width,
                                           const int* image_height,
                                           const bool* is_fisheye,
                                           const float* z_value,
                                           const float* intrinsic_matrix,
                                           const float* transform_matrix,
                                           float* projection_result,
                                           bool* mask);

template void ReferencePointProjectAdaptiveResolutionWrapper(cudaStream_t stream,
                                                             int batch_size,
                                                             int grid_width,
                                                             int grid_length,
                                                             float grid_center_x,
                                                             float grid_center_y,
                                                             float resolution_x,
                                                             float resolution_y,
                                                             int num_sampled_points_in_pillar,
                                                             int num_camera,
                                                             const bool* is_camera_valid,
                                                             const int* image_width,
                                                             const int* image_height,
                                                             const bool* is_fisheye,
                                                             const float* z_value,
                                                             const float* intrinsic_matrix,
                                                             const float* transform_matrix,
                                                             const float* adaptive_coeffs,
                                                             float* projection_result,
                                                             bool* mask);

}  // namespace cuda
