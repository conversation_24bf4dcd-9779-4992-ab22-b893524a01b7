// Copyright @2022 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/cuda/operators/inplace_add_op.h"

namespace cuda {
namespace {

constexpr int kNumThreads = 512;

}  // namespace

template <typename T>
__global__ void InplaceAddKernel(const T* number_added, int size, T* result) {
  const int idx = threadIdx.x + blockIdx.x * blockDim.x;
  if (idx >= size) {
    return;
  }
  result[idx] += number_added[idx];
}

template <typename T>
void InplaceAdd(const ConstDeviceBufferView<T>& number_added,
                DeviceBufferView<T>* result,
                DeviceContext* device_context) {
  CHECK_EQ(result->size(), number_added.size());
  const int size = result->size();
  const int num_blocks = cuda::DivUp(size, kNumThreads);
  InplaceAddKernel<<<num_blocks, kNumThreads, 0, device_context->stream()>>>(
      number_added.data(), size, result->mutable_data());
  CUDA_KERNEL_LAUNCH_CHECK();
}
template void InplaceAdd(const ConstDeviceBufferView<int>& number_added,
                         DeviceBufferView<int>* result,
                         DeviceContext* device_context);

}  // namespace cuda
