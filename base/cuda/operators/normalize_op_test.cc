// Copyright @2023 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/cuda/operators/normalize_op.h"

#include <memory>
#include <numeric>
#include <vector>

#include "gtest/gtest.h"

#include "base/cuda/device_utils.h"
#include "base/cuda/tensor.h"

namespace cuda {
namespace {

constexpr int kImageWidth = 5;
constexpr int kImageHeight = 4;
constexpr int kImageChannels = 3;
constexpr int kNumImages = 2;

template <typename T0, typename T1>
void ZScoreNormalizeCpu(const Tensor4D<T0>& image, bool is_nchw, Tensor4D<T1>* out_image) {
  const std::vector<float> kMeanVec{0.485, 0.456, 0.406};
  const std::vector<float> kStdVec{0.229, 0.224, 0.225};
  CHECK(out_image != nullptr);
  for (int b = 0; b < kNumImages; ++b) {
    for (int c = 0; c < kImageChannels; ++c) {
      for (int x = 0; x < kImageWidth; ++x) {
        for (int y = 0; y < kImageHeight; ++y) {
          if (is_nchw) {
            (*out_image)(b, c, y, x) =
                static_cast<T1>((image(b, y, x, c) / 255.0 - kMeanVec[c]) / kStdVec[c]);
          } else {
            (*out_image)(b, y, x, c) =
                static_cast<T1>((image(b, y, x, c) / 255.0 - kMeanVec[c]) / kStdVec[c]);
          }
        }
      }
    }
  }
}

class NormalizeOpTest : public ::testing::Test {
 public:
  void SetUp() override { PrepareInput(); }

 protected:
  int buf_size_;
  std::vector<uint8_t> in_buf_;
  std::vector<float> cpu_out_buf_;
  std::vector<float> device_out_buf_;
  std::unique_ptr<Tensor4D<uint8_t>> cpu_in_tensor_;
  std::unique_ptr<Tensor4D<float>> cpu_out_tensor_;
  std::unique_ptr<cuda::DeviceTensor<uint8_t, 4>> device_in_tensor_;
  std::unique_ptr<cuda::DeviceTensor<float, 4>> device_out_tensor_;
  std::unique_ptr<cuda::DeviceTensorView<float, 4>> device_out_tensor_view_;
  DeviceContext ctx_;

 private:
  void PrepareInput() {
    buf_size_ = kNumImages * kImageHeight * kImageWidth * kImageChannels;
    in_buf_.resize(buf_size_, 0);
    std::iota(in_buf_.begin(), in_buf_.end(), 0);
    cpu_out_buf_.resize(buf_size_, 0);
    device_out_buf_.resize(buf_size_, 0);
    cpu_in_tensor_ = std::make_unique<Tensor4D<uint8_t>>(
        in_buf_.data(), kNumImages, kImageHeight, kImageWidth, kImageChannels);
    device_in_tensor_ = std::make_unique<cuda::DeviceTensor<uint8_t, 4>>(
        cuda::TensorShape({kNumImages, kImageHeight, kImageWidth, kImageChannels}));
  }
};

TEST_F(NormalizeOpTest, MinMaxNormalizeTest) {
  // gpu routine
  cuda::DeviceTensorView<uint8_t, 4> device_in_tensor_view(device_in_tensor_.get());
  device_in_tensor_view.mutable_buffer()->FromCpu(in_buf_.data(), 0, buf_size_, &ctx_);
  device_out_tensor_ = std::make_unique<cuda::DeviceTensor<float, 4>>(
      cuda::TensorShape({kNumImages, kImageHeight, kImageWidth, kImageChannels}));
  device_out_tensor_view_ = std::make_unique<cuda::DeviceTensorView<float, 4>>(
      device_out_tensor_->mutable_raw_data(),
      cuda::TensorShape({kNumImages, kImageHeight, kImageWidth, kImageChannels}));
  cuda::Normalize(device_in_tensor_->ConstView(),
                  ctx_.stream(),
                  NormalizeType::kMinMax,
                  false,
                  device_out_tensor_view_.get());
  device_out_tensor_view_->buffer().ToCpu(device_out_buf_.data(), &ctx_);
  ctx_.Sync();

  for (int i = 0; i < buf_size_; i++) {
    EXPECT_NEAR(static_cast<float>(in_buf_[i]) / 255.0, device_out_buf_[i], 1e-6) << "at:" << i;
  }
}

TEST_F(NormalizeOpTest, ZScoreNormalizeTest) {
  // cpu routine
  cpu_out_tensor_ = std::make_unique<Tensor4D<float>>(
      cpu_out_buf_.data(), kNumImages, kImageHeight, kImageWidth, kImageChannels);
  ZScoreNormalizeCpu(*cpu_in_tensor_, false, cpu_out_tensor_.get());

  // gpu routine
  cuda::DeviceTensorView<uint8_t, 4> device_in_tensor_view(device_in_tensor_.get());
  device_in_tensor_view.mutable_buffer()->FromCpu(in_buf_.data(), 0, buf_size_, &ctx_);
  device_out_tensor_ = std::make_unique<cuda::DeviceTensor<float, 4>>(
      cuda::TensorShape({kNumImages, kImageHeight, kImageWidth, kImageChannels}));
  device_out_tensor_view_ = std::make_unique<cuda::DeviceTensorView<float, 4>>(
      device_out_tensor_->mutable_raw_data(),
      cuda::TensorShape({kNumImages, kImageHeight, kImageWidth, kImageChannels}));
  cuda::Normalize(device_in_tensor_->ConstView(),
                  ctx_.stream(),
                  NormalizeType::kZScore,
                  false,
                  device_out_tensor_view_.get());
  device_out_tensor_view_->buffer().ToCpu(device_out_buf_.data(), &ctx_);
  ctx_.Sync();

  for (int i = 0; i < buf_size_; i++) {
    EXPECT_NEAR(cpu_out_tensor_->mutable_data()[i], device_out_buf_[i], 1e-6) << "at:" << i;
  }
}

TEST_F(NormalizeOpTest, ZScoreNCHWNormalizeTest) {
  // cpu routine
  cpu_out_tensor_ = std::make_unique<Tensor4D<float>>(
      cpu_out_buf_.data(), kNumImages, kImageChannels, kImageHeight, kImageWidth);
  ZScoreNormalizeCpu(*cpu_in_tensor_, true, cpu_out_tensor_.get());

  // gpu routine
  cuda::DeviceTensorView<uint8_t, 4> device_in_tensor_view(device_in_tensor_.get());
  device_in_tensor_view.mutable_buffer()->FromCpu(in_buf_.data(), 0, buf_size_, &ctx_);
  device_out_tensor_ = std::make_unique<cuda::DeviceTensor<float, 4>>(
      cuda::TensorShape({kNumImages, kImageChannels, kImageHeight, kImageWidth}));
  device_out_tensor_view_ = std::make_unique<cuda::DeviceTensorView<float, 4>>(
      device_out_tensor_->mutable_raw_data(),
      cuda::TensorShape({kNumImages, kImageChannels, kImageHeight, kImageWidth}));
  cuda::Normalize(device_in_tensor_->ConstView(),
                  ctx_.stream(),
                  NormalizeType::kZScore,
                  true,
                  device_out_tensor_view_.get());
  device_out_tensor_view_->buffer().ToCpu(device_out_buf_.data(), &ctx_);
  ctx_.Sync();

  for (int i = 0; i < buf_size_; i++) {
    EXPECT_NEAR(cpu_out_tensor_->mutable_data()[i], device_out_buf_[i], 1e-6) << "at:" << i;
  }
}

}  // namespace
}  // namespace cuda
