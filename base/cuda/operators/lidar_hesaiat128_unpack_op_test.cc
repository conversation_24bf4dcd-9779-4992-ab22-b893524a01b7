// Copyright @2025 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/cuda/operators/lidar_hesaiat128_unpack_op.h"

#include <cuda_runtime.h>

#include "gtest/gtest.h"

#include <algorithm>
#include <cmath>
#include <memory>
#include <vector>

#include "base/cuda/device_buffer.h"
#include "base/cuda/device_utils.h"
#include "base/cuda/macros.h"
#include "sensor/lidar/data_types_device.h"
#include "sensor/lidar/hesai/hesai_data_types.h"

namespace walle {
namespace sensor {
namespace lidar {
namespace hesai {

class HesaiAt128UnpackCudaTest : public ::testing::Test {
 protected:
  void SetUp() override {
    device_context_ = std::make_unique<cuda::DeviceContext>();
    output_buffer_ =
        std::make_unique<cuda::DeviceBuffer>(num_blocks_ * num_lasers_ * sizeof(lidar::Pixel));

    SetupBlocksData();
    SetupCalibrationData();
  }

  void SetupBlocksData() {
    h_blocks_.resize(num_blocks_);
    h_dis_units_.resize(num_blocks_);

    const double angle_step = 360.0 / num_blocks_;

    for (int i = 0; i < num_blocks_; ++i) {
      const double current_angle = i * angle_step;
      const int azimuth_raw = static_cast<int>(current_angle * 25600.0 / 360.0);
      h_blocks_[i].azimuth = (azimuth_raw >> 8) & 0xFFFF;
      h_blocks_[i].fine_azimuth = azimuth_raw & 0xFF;
      h_dis_units_[i] = 4;  // 4mm

      for (int j = 0; j < num_lasers_; ++j) {
        h_blocks_[i].unit[j].distance = 1000 + ((i * num_lasers_ + j) % 9000);
        h_blocks_[i].unit[j].reflectivity = 100 + (j % 155);
        h_blocks_[i].unit[j].confidence = 200;
      }
    }

    blocks_buffer_ = std::make_unique<cuda::DeviceBuffer>(num_blocks_ * sizeof(HesaiAT128RawBlock));
    blocks_buffer_->FromCpu(h_blocks_.data(), device_context_.get());

    dis_unit_buffer_ = std::make_unique<cuda::DeviceBuffer>(num_blocks_ * sizeof(uint8_t));
    dis_unit_buffer_->FromCpu(h_dis_units_.data(), device_context_.get());
    device_context_->Sync();
  }

  void SetupCalibrationData() {
    h_start_azimuth_per_frame_ = {0.0, 120.0, 240.0};
    h_end_azimuth_per_frame_ = {120.0, 240.0, 360.0};

    start_azimuth_per_frame_buffer_ = std::make_unique<cuda::DeviceBuffer>(3 * sizeof(double));
    start_azimuth_per_frame_buffer_->FromCpu(h_start_azimuth_per_frame_.data(),
                                             device_context_.get());

    end_azimuth_per_frame_buffer_ = std::make_unique<cuda::DeviceBuffer>(3 * sizeof(double));
    end_azimuth_per_frame_buffer_->FromCpu(h_end_azimuth_per_frame_.data(), device_context_.get());

    h_vertical_angle_offset_.resize(num_lasers_);
    const double v_angle_min = -12.5;
    const double v_angle_max = 12.9;
    const double v_angle_range = v_angle_max - v_angle_min;
    for (int i = 0; i < num_lasers_; ++i) {
      h_vertical_angle_offset_[i] = v_angle_min + v_angle_range * i / (num_lasers_ - 1);
    }

    vertical_angle_offset_buffer_ =
        std::make_unique<cuda::DeviceBuffer>(num_lasers_ * sizeof(double));
    vertical_angle_offset_buffer_->FromCpu(h_vertical_angle_offset_.data(), device_context_.get());

    h_horizontal_angle_offset_.resize(num_lasers_);
    for (int i = 0; i < num_lasers_; ++i) {
      h_horizontal_angle_offset_[i] = 0.0;
    }

    horizontal_angle_offset_buffer_ =
        std::make_unique<cuda::DeviceBuffer>(num_lasers_ * sizeof(double));
    horizontal_angle_offset_buffer_->FromCpu(h_horizontal_angle_offset_.data(),
                                             device_context_.get());

    const int adjust_table_size = num_lasers_ * 180;
    h_vertical_angle_adjust_.resize(adjust_table_size, 0.0);
    h_horizontal_angle_adjust_.resize(adjust_table_size, 0.0);

    vertical_angle_adjust_buffer_ =
        std::make_unique<cuda::DeviceBuffer>(adjust_table_size * sizeof(double));
    vertical_angle_adjust_buffer_->FromCpu(h_vertical_angle_adjust_.data(), device_context_.get());

    horizontal_angle_adjust_buffer_ =
        std::make_unique<cuda::DeviceBuffer>(adjust_table_size * sizeof(double));
    horizontal_angle_adjust_buffer_->FromCpu(h_horizontal_angle_adjust_.data(),
                                             device_context_.get());
    device_context_->Sync();
  }

 protected:
  std::unique_ptr<cuda::DeviceContext> device_context_;

  std::unique_ptr<cuda::DeviceBuffer> blocks_buffer_;
  std::unique_ptr<cuda::DeviceBuffer> dis_unit_buffer_;
  std::unique_ptr<cuda::DeviceBuffer> start_azimuth_per_frame_buffer_;
  std::unique_ptr<cuda::DeviceBuffer> end_azimuth_per_frame_buffer_;
  std::unique_ptr<cuda::DeviceBuffer> vertical_angle_offset_buffer_;
  std::unique_ptr<cuda::DeviceBuffer> horizontal_angle_offset_buffer_;
  std::unique_ptr<cuda::DeviceBuffer> vertical_angle_adjust_buffer_;
  std::unique_ptr<cuda::DeviceBuffer> horizontal_angle_adjust_buffer_;
  std::unique_ptr<cuda::DeviceBuffer> output_buffer_;

  std::vector<HesaiAT128RawBlock> h_blocks_;
  std::vector<uint8_t> h_dis_units_;
  std::vector<double> h_start_azimuth_per_frame_;
  std::vector<double> h_end_azimuth_per_frame_;
  std::vector<double> h_vertical_angle_offset_;
  std::vector<double> h_horizontal_angle_offset_;
  std::vector<double> h_vertical_angle_adjust_;
  std::vector<double> h_horizontal_angle_adjust_;

  int num_blocks_ = kHesaiAT128NumScanPerRotation;
  int num_lasers_ = kHesaiAT128LaserCount;
  float min_range_ = 0.5;
  float max_range_ = 200.0;
};

TEST_F(HesaiAt128UnpackCudaTest, BasicUnpackTest) {
  UnpackOnGpu(*blocks_buffer_,
              *dis_unit_buffer_,
              *start_azimuth_per_frame_buffer_,
              *end_azimuth_per_frame_buffer_,
              *vertical_angle_offset_buffer_,
              *horizontal_angle_offset_buffer_,
              *vertical_angle_adjust_buffer_,
              *horizontal_angle_adjust_buffer_,
              min_range_,
              max_range_,
              output_buffer_.get(),
              device_context_.get());
  device_context_->Sync();

  std::vector<lidar::Pixel> h_pixels(num_blocks_ * num_lasers_);
  output_buffer_->ToCpu(h_pixels.data(), output_buffer_->size(), device_context_.get());
  device_context_->Sync();

  int valid_points = 0;
  int total_points = 0;
  for (int block_idx = 0; block_idx < std::min(10, num_blocks_); ++block_idx) {
    const HesaiAT128RawBlock& block = h_blocks_[block_idx];
    const uint8_t dis_unit = h_dis_units_[block_idx];

    for (int laser_id = 0; laser_id < std::min(10, num_lasers_); ++laser_id) {
      const HesaiAT128RawUnit& unit = block.unit[laser_id];
      const double distance = unit.distance * dis_unit * 0.001;
      const int idx = laser_id + block_idx * num_lasers_;
      const lidar::Pixel& pixel = h_pixels[idx];
      total_points++;

      EXPECT_EQ(pixel.intensity, unit.reflectivity)
          << "Block: " << block_idx << ", Laser: " << laser_id;

      if (distance >= min_range_ && distance <= max_range_) {
        valid_points++;
        const float pos_magnitude =
            sqrt(pixel.position[0] * pixel.position[0] + pixel.position[1] * pixel.position[1] +
                 pixel.position[2] * pixel.position[2]);

        if (pos_magnitude > 1e-6) {
          EXPECT_NEAR(pos_magnitude, distance, 1e-3)
              << "Distance mismatch at Block: " << block_idx << ", Laser: " << laser_id
              << ", Expected: " << distance << ", Got: " << pos_magnitude;
        }
      }
    }
  }
  LOG(INFO) << "Valid points: " << valid_points << " / " << total_points;
  EXPECT_GT(valid_points, 0) << "Should have at least some valid points";
}

TEST_F(HesaiAt128UnpackCudaTest, DataIntegrityTest) {
  UnpackOnGpu(*blocks_buffer_,
              *dis_unit_buffer_,
              *start_azimuth_per_frame_buffer_,
              *end_azimuth_per_frame_buffer_,
              *vertical_angle_offset_buffer_,
              *horizontal_angle_offset_buffer_,
              *vertical_angle_adjust_buffer_,
              *horizontal_angle_adjust_buffer_,
              min_range_,
              max_range_,
              output_buffer_.get(),
              device_context_.get());
  device_context_->Sync();

  std::vector<lidar::Pixel> h_pixels(num_blocks_ * num_lasers_);
  output_buffer_->ToCpu(h_pixels.data(), output_buffer_->size(), device_context_.get());
  device_context_->Sync();

  const lidar::Pixel& first_pixel = h_pixels[0];
  EXPECT_NE(first_pixel.intensity, 0) << "First pixel should have non-zero intensity";

  bool found_different_z = false;
  float first_z = h_pixels[0].position[2];
  for (int i = 1; i < std::min(10, num_lasers_); ++i) {
    if (std::abs(h_pixels[i].position[2] - first_z) > 1e-3) {
      found_different_z = true;
      break;
    }
  }
  EXPECT_TRUE(found_different_z) << "Different lasers should have different Z coordinates";
}

TEST_F(HesaiAt128UnpackCudaTest, PerformanceTest) {
  cudaEvent_t start, stop;
  CUDA_CHECK(cudaEventCreate(&start));
  CUDA_CHECK(cudaEventCreate(&stop));

  for (int i = 0; i < 5; ++i) {
    UnpackOnGpu(*blocks_buffer_,
                *dis_unit_buffer_,
                *start_azimuth_per_frame_buffer_,
                *end_azimuth_per_frame_buffer_,
                *vertical_angle_offset_buffer_,
                *horizontal_angle_offset_buffer_,
                *vertical_angle_adjust_buffer_,
                *horizontal_angle_adjust_buffer_,
                min_range_,
                max_range_,
                output_buffer_.get(),
                device_context_.get());
  }
  device_context_->Sync();

  CUDA_CHECK(cudaEventRecord(start));
  const int iterations = 100;
  for (int i = 0; i < iterations; ++i) {
    UnpackOnGpu(*blocks_buffer_,
                *dis_unit_buffer_,
                *start_azimuth_per_frame_buffer_,
                *end_azimuth_per_frame_buffer_,
                *vertical_angle_offset_buffer_,
                *horizontal_angle_offset_buffer_,
                *vertical_angle_adjust_buffer_,
                *horizontal_angle_adjust_buffer_,
                min_range_,
                max_range_,
                output_buffer_.get(),
                device_context_.get());
  }

  CUDA_CHECK(cudaEventRecord(stop));
  CUDA_CHECK(cudaEventSynchronize(stop));

  float milliseconds = 0;
  CUDA_CHECK(cudaEventElapsedTime(&milliseconds, start, stop));
  const float average = milliseconds / iterations;

  LOG(INFO) << "Average processing time: " << average << " ms";
  EXPECT_LT(average, 5.0) << "Processing should be faster than 5ms per frame";

  CUDA_CHECK(cudaEventDestroy(start));
  CUDA_CHECK(cudaEventDestroy(stop));
}

}  // namespace hesai
}  // namespace lidar
}  // namespace sensor
}  // namespace walle
