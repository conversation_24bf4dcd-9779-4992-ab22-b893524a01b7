// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>
//          <PERSON> (<EMAIL>)
//          <PERSON><PERSON> (<EMAIL>)

#include "base/cuda/operators/crop_resize_padding_op.h"

#include <cuda_fp16.h>
#include <cuda_runtime.h>
#include <algorithm>

namespace cuda {
namespace {

constexpr int kNumLoop = 4;
constexpr int kNumThreads = 128;
constexpr int kMaxNumTensors = 50;
constexpr float kInt8ImageScale = 0.498039216f; // 127/255

}  // namespace

template <typename T>
struct TensorList {
  int num_tensors;
  const T* datas[kMaxNumTensors];
  int heights[kMaxNumTensors];
  int widths[kMaxNumTensors];
  int channels[kMaxNumTensors];
};

template <typename T0, typename T1>
__global__ void CropResizePaddingKernel(TensorList<T0> tensor_list,
                                        const float* boxes,
                                        const int* boxes_idx,
                                        int num_task,
                                        int output_height,
                                        int output_width,
                                        int channels,
                                        int num_boxes,
                                        bool is_padding,
                                        bool is_channel_first,
                                        T1* output_data) {
  const int img_size = output_height * output_width;
  const int img_pixels = img_size * channels;
  const int b = blockIdx.y;
  const int image_id = boxes_idx[b];                                        

  for (int tid = threadIdx.x + blockIdx.x * blockDim.x; tid < num_task; tid += blockDim.x * gridDim.x) {
    if (image_id < 0 || image_id >= tensor_list.num_tensors) {
#pragma unroll
      for (int c = 0; c < channels; c++) {
        if (is_channel_first) {
          output_data[b * img_pixels + c * img_size + tid] = static_cast<T1>(0);
        } else {
          output_data[b * img_pixels + channels * tid + c] = static_cast<T1>(0);
        }
      }
      continue;
    }
    const int x = tid % output_width;
    const int y = tid / output_width;

    const T0* image = tensor_list.datas[image_id];
    const int input_height = tensor_list.heights[image_id];
    const int input_width = tensor_list.widths[image_id];
    const float x0 = boxes[b * 4 + 0];
    const float y0 = boxes[b * 4 + 1];
    const float x1 = boxes[b * 4 + 2];
    const float y1 = boxes[b * 4 + 3];
    const float box_h = y1 - y0;
    const float box_w = x1 - x0;
    float height_scale = box_h / output_height;
    float width_scale = box_w / output_width;
    float scale = std::max(height_scale, width_scale);
    // padding
    if (is_padding) {
      if (y * scale >= box_h || x * scale >= box_w) {
#pragma unroll
        for (int c = 0; c < channels; c++) {
          if (is_channel_first) {
            output_data[b * img_pixels + c * img_size + tid] = static_cast<T1>(0);
          } else {
            output_data[b * img_pixels + channels * tid + c] = static_cast<T1>(0);
          }
        }
        continue;
      }
      height_scale = scale;
      width_scale = scale;
    }
    const float input_y = y0 + (y + 0.5f) * height_scale - 0.5f;
    const float input_x = x0 + (x + 0.5f) * width_scale - 0.5f;
    if (input_y < 0 || input_y > input_height - 1 || input_x < 0 || input_x > input_width - 1) {
#pragma unroll
      for (int c = 0; c < channels; c++) {
        if (is_channel_first) {
          output_data[b * img_pixels + c * img_size + tid] = static_cast<T1>(0);
        } else {
          output_data[b * img_pixels + channels * tid + c] = static_cast<T1>(0);
        }
      }
      continue;
    }
    const int top_y_index = floorf(input_y);
    const int bottom_y_index = ceilf(input_y);
    const float y_lerp = input_y - top_y_index;
    const int left_x_index = floorf(input_x);
    const int right_x_index = ceilf(input_x);
    const float x_lerp = input_x - left_x_index;
    const int top_left_index = (top_y_index * input_width + left_x_index) * channels;
    const int top_right_index = (top_y_index * input_width + right_x_index) * channels;
    const int bottom_left_index = (bottom_y_index * input_width + left_x_index) * channels;
    const int bottom_right_index = (bottom_y_index * input_width + right_x_index) * channels;
#pragma unroll
    for (int c = 0; c < channels; c++) {
      const float top_left = image[top_left_index + c];
      const float top_right = image[top_right_index + c];
      const float bottom_left = image[bottom_left_index + c];
      const float bottom_right = image[bottom_right_index + c];
      const float top = top_left + (top_right - top_left) * x_lerp;
      const float bottom = bottom_left + (bottom_right - bottom_left) * x_lerp;
      float output_val = top + (bottom - top) * y_lerp;
      if (std::is_same<T1, int8_t>::value){
        output_val = std::round(output_val * kInt8ImageScale);
      }
      if (is_channel_first) {
        output_data[b * img_pixels + c * img_size + tid] = static_cast<T1>(output_val);
      } else {
        output_data[b * img_pixels + channels * tid + c] = static_cast<T1>(output_val);
      }
    }
  }
}

template <typename T0, typename T1>
void CropResizePadding(const std::vector<ConstDeviceTensorView<T0, 3>>& tensors,
                       const ConstDeviceTensorView<float, 2>& boxes,
                       const ConstDeviceTensorView<int, 1>& boxes_idx,
                       cudaStream_t stream,
                       int num_boxes,
                       bool is_padding,
                       bool is_channel_first,
                       DeviceTensorView<T1, 4>* crops) {
  // inputs
  CHECK(crops != nullptr);
  CHECK_GT(tensors.size(), 0);
  CHECK_LE(tensors.size(), kMaxNumTensors);
  CHECK_GT(num_boxes, 0);
  CHECK_LE(num_boxes, boxes.Dimension(0));
  CHECK_LE(num_boxes, boxes_idx.Dimension(0));
  // outputs
  CHECK_LE(num_boxes, crops->Dimension(0));
  int output_height = -1;
  int output_width = -1;
  int channels = -1;
  if (is_channel_first) {
    channels = crops->Dimension(1);
    output_height = crops->Dimension(2);
    output_width = crops->Dimension(3);
  } else {
    output_height = crops->Dimension(1);
    output_width = crops->Dimension(2);
    channels = crops->Dimension(3);
  }
  const int output_volume = output_height * output_width;
  const dim3 multi_dim_blocks(DivUp(output_volume, kNumThreads * kNumLoop), num_boxes, 1);

  TensorList<T0> tensor_list;
  tensor_list.num_tensors = tensors.size();
  for (size_t i = 0; i < tensors.size(); i++) {
    tensor_list.heights[i] = tensors[i].Dimension(0);
    tensor_list.widths[i] = tensors[i].Dimension(1);
    CHECK_EQ(tensors[i].Dimension(2), channels);
    tensor_list.channels[i] = tensors[i].Dimension(2);
    tensor_list.datas[i] = tensors[i].raw_data();
  }
  CropResizePaddingKernel<T0, T1>
      <<<multi_dim_blocks, kNumThreads, 0, stream>>>(tensor_list,
                                                     boxes.raw_data(),
                                                     boxes_idx.raw_data(),
                                                     output_volume,
                                                     output_height,
                                                     output_width,
                                                     channels,
                                                     num_boxes,
                                                     is_padding,
                                                     is_channel_first,
                                                     crops->mutable_raw_data());
  CUDA_KERNEL_LAUNCH_CHECK();
}

#define CROP_RESIZE_PADDING(T0, T1)                                                         \
  template void CropResizePadding<T0, T1>(const std::vector<ConstDeviceTensorView<T0, 3>>&, \
                                          const ConstDeviceTensorView<float, 2>&,           \
                                          const ConstDeviceTensorView<int, 1>&,             \
                                          cudaStream_t,                                     \
                                          int,                                              \
                                          bool,                                             \
                                          bool,                                             \
                                          DeviceTensorView<T1, 4>*);
CROP_RESIZE_PADDING(uint8_t, half)
CROP_RESIZE_PADDING(uint8_t, float)
CROP_RESIZE_PADDING(uint8_t, uint8_t)
CROP_RESIZE_PADDING(uint8_t, int8_t)
CROP_RESIZE_PADDING(float, half)
CROP_RESIZE_PADDING(float, float)
CROP_RESIZE_PADDING(half, half)
#undef CROP_RESIZE_PADDING

}  // namespace cuda
