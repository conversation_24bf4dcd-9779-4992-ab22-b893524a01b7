// Copyright @2023 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include <chrono>
#include <cmath>
#include <functional>
#include <numeric>
#include <vector>

#include "gtest/gtest.h"

#include "base/cuda/device_buffer.h"
#include "base/cuda/device_utils.h"
#include "base/cuda/operators/remap_bilinear_op.h"
#include "base/cuda/tensor.h"

namespace cuda {
namespace {

// [n, ih, iw, c] -> [n, oh, ow, c]
template <typename T>
void RemapBilinearCpu(const Tensor4D<T>& input,
                      const std::vector<float>& map_x,
                      const std::vector<float>& map_y,
                      Tensor4D<T>* output) {
  CHECK(output != nullptr);
  CHECK(input.Batch() == output->Batch());
  for (int b = 0; b < output->Batch(); ++b) {
    for (int y = 0; y < output->Height(); ++y) {
      for (int x = 0; x < output->Width(); ++x) {
        const int map_idx = (b * output->Height() + y) * output->Width() + x;
        const float in_y = map_y[map_idx];
        const int top_y_index = std::max(static_cast<int>(floorf(in_y)), static_cast<int>(0));
        const int bottom_y_index = std::min(static_cast<int>(ceilf(in_y)), input.Height() - 1);
        const float y_lerp = in_y - std::floor(in_y);
        const float in_x = map_x[map_idx];
        const int left_x_index = std::max(static_cast<int>(floorf(in_x)), static_cast<int>(0));
        const int right_x_index = std::min(static_cast<int>(ceilf(in_x)), input.Width() - 1);
        const float x_lerp = in_x - std::floor(in_x);
        for (int c = 0; c < output->Channels(); ++c) {
          const float top_left = input(b, top_y_index, left_x_index, c);
          const float top_right = input(b, top_y_index, right_x_index, c);
          const float bottom_left = input(b, bottom_y_index, left_x_index, c);
          const float bottom_right = input(b, bottom_y_index, right_x_index, c);
          const float top = top_left + (top_right - top_left) * x_lerp;
          const float bottom = bottom_left + (bottom_right - bottom_left) * x_lerp;
          (*output)(b, y, x, c) = top + (bottom - top) * y_lerp;
        }
      }
    }
  }
}

TEST(ResizeBilinearOpTest, CpuAgainstGpu) {
  const int n = 2;
  const int ih = 1080;
  const int iw = 1920;
  const int c = 3;
  const int oh = 640;
  const int ow = 960;
  std::vector<uint8_t> in_buf(n * ih * iw * c, 0);
  std::vector<uint8_t> out_buf(n * oh * ow * c, 0);
  std::iota(in_buf.begin(), in_buf.end(), 0);
  Tensor4D<uint8_t> in_tensor(in_buf.data(), n, ih, iw, c);
  Tensor4D<uint8_t> out_tensor(out_buf.data(), n, oh, ow, c);
  std::vector<float> map_x(n * oh * ow, 0);
  std::vector<float> map_y(n * oh * ow, 0);
  const float h_scale = ih / oh;
  const float w_scale = iw / ow;
  for (int i = 0; i < n; i++) {
    for (int y = 0; y < oh; y++) {
      for (int x = 0; x < ow; x++) {
        int map_idx = (i * oh + y) * ow + x;
        map_x[map_idx] = x * w_scale;
        map_y[map_idx] = y * h_scale;
      }
    }
  }
  RemapBilinearCpu(in_tensor, map_x, map_y, &out_tensor);
  DeviceContext ctx;
  DeviceBuffer dev_in_buf(in_buf.size());
  DeviceBuffer dev_out_buf(out_buf.size());
  std::vector<uint8_t> results(out_buf.size());
  dev_in_buf.FromCpu(in_buf.data(), in_buf.size(), &ctx);
  const int map_size = n * oh * ow * sizeof(float);
  DeviceBuffer dev_map_x(map_size);
  DeviceBuffer dev_map_y(map_size);
  dev_map_x.FromCpu(map_x.data(), map_size, &ctx);
  dev_map_y.FromCpu(map_y.data(), map_size, &ctx);
  RemapBilinearWrapper(ctx.stream(),
                       n,
                       ih,
                       iw,
                       oh,
                       ow,
                       c,
                       reinterpret_cast<const float*>(dev_map_x.data()),
                       reinterpret_cast<const float*>(dev_map_y.data()),
                       reinterpret_cast<const uint8_t*>(dev_in_buf.data()),
                       reinterpret_cast<uint8_t*>(dev_out_buf.mutable_data()));

  dev_out_buf.ToCpu(results.data(), dev_out_buf.size(), &ctx);
  ctx.Sync();
  for (size_t i = 0; i < out_buf.size(); i++) {
    EXPECT_EQ(out_buf[i], results[i]);
  }
}

}  // namespace
}  // namespace cuda
