// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/cuda/operators/crop_resize_padding_op.h"

#include <algorithm>
#include <cmath>
#include <numeric>

#include <cuda_fp16.h>
#include "gtest/gtest.h"

#include "base/cuda/device_buffer.h"
#include "base/cuda/device_utils.h"
#include "base/cuda/tensor.h"

namespace cuda {
namespace {

// [num_images, ih, iw, c] / [num_boxes, 4] / [num_boxes, 1] -> [num_boxes, oh, ow, c]
template <typename T0, typename T1>
void CropResizePaddingCpu(const std::vector<ConstDeviceTensorView<T0, 3>>& buffers,
                          const std::vector<float>& boxes,
                          const std::vector<int>& boxes_idx,
                          int num_boxes,
                          bool is_padding,
                          Tensor4D<T1>* output) {
  CHECK(output != nullptr);
  CHECK(num_boxes == output->Batch());
  for (int b = 0; b < output->Batch(); ++b) {
    const int image_id = boxes_idx[b];
    if (image_id < 0 || image_id >= static_cast<int>(buffers.size())) continue;
    const float x0 = boxes[b * 4 + 0];
    const float y0 = boxes[b * 4 + 1];
    const float x1 = boxes[b * 4 + 2];
    const float y1 = boxes[b * 4 + 3];
    float height_scale = (y1 - y0) / output->Height();
    float width_scale = (x1 - x0) / output->Width();
    float scale = std::max(height_scale, width_scale);
    const int input_height = buffers[image_id].Dimension(0);
    const int input_width = buffers[image_id].Dimension(1);
    const int channels = buffers[image_id].Dimension(2);
    const T0* input_data = buffers[image_id].raw_data();
    if (is_padding) {
      height_scale = scale;
      width_scale = scale;
    }
    for (int y = 0; y < output->Height(); ++y) {
      if (is_padding && (y * scale + y0 >= y1)) {
        continue;
      }
      const float in_y = y0 + (static_cast<float>(y) + 0.5f) * height_scale - 0.5f;
      if (in_y < 0 || in_y > input_height - 1) continue;
      const int top_y_index = floorf(in_y);
      const int bottom_y_index = ceilf(in_y);
      const float y_lerp = in_y - std::floor(in_y);
      for (int x = 0; x < output->Width(); ++x) {
        if (is_padding && (x * scale + x0 >= x1)) {
          continue;
        }
        const float in_x = x0 + (static_cast<float>(x) + 0.5f) * width_scale - 0.5f;
        if (in_x < 0 || in_x > input_width - 1) {
          continue;
        }
        const int left_x_index = floorf(in_x);
        const int right_x_index = ceilf(in_x);
        const float x_lerp = in_x - std::floor(in_x);
        for (int c = 0; c < output->Channels(); ++c) {
          const int top_left_index = (top_y_index * input_width + left_x_index) * channels + c;
          const int top_right_index = (top_y_index * input_width + right_x_index) * channels + c;
          const int bottom_left_index =
              (bottom_y_index * input_width + left_x_index) * channels + c;
          const int bottom_right_index =
              (bottom_y_index * input_width + right_x_index) * channels + c;
          const float top_left = input_data[top_left_index];
          const float top_right = input_data[top_right_index];
          const float bottom_left = input_data[bottom_left_index];
          const float bottom_right = input_data[bottom_right_index];
          const float top = top_left + (top_right - top_left) * x_lerp;
          const float bottom = bottom_left + (bottom_right - bottom_left) * x_lerp;
          (*output)(b, y, x, c) = static_cast<T1>(top + (bottom - top) * y_lerp);
        }
      }
    }
  }
}

template <typename T0, typename T1>
void CropResizePaddingTest(bool is_padding, bool is_channel_first) {
  const int n = 2;
  const int ih = 1080;
  const int iw = 1920;
  const int c = 3;
  const int oh = 640;
  const int ow = 960;
  // test valid & invalid boxes
  const std::vector<float> boxes = {123.4, 567.8, 432.1, 876.5, 111.1, 111.1, 222.2, 222.2,
                                    0.1,   2,     100,   2,     10,    100,   11,    101,
                                    -1,    99,    -100,  200,   1e9,   1e9,   2e9,   2e9};
  const std::vector<int> boxes_idx = {0, 1, 1, 0, 1, 0};
  const int num_boxes = boxes.size() / 4;
  CHECK_EQ(num_boxes, static_cast<int>(boxes_idx.size()));
  std::vector<T0> in_buf(n * ih * iw * c, 0);
  std::vector<T1> out_buf(num_boxes * oh * ow * c, 0.);

  std::iota(in_buf.begin(), in_buf.end(), 0.);
  Tensor4D<T0> in_tensor(in_buf.data(), n, ih, iw, c);
  Tensor4D<T1> out_tensor(out_buf.data(), num_boxes, oh, ow, c, is_channel_first);
  std::vector<ConstDeviceTensorView<uint8_t, 3>> buffers;
  const int stride = in_tensor.size() / in_tensor.Batch();
  TensorShape tensor_shape({in_tensor.Height(), in_tensor.Width(), in_tensor.Channels()});
  for (int i = 0; i < in_tensor.Batch(); i++) {
    buffers.emplace_back(ConstDeviceTensorView<T0, 3>(
        static_cast<const T0*>(in_tensor.mutable_data() + i * stride), tensor_shape));
  }
  CropResizePaddingCpu(buffers, boxes, boxes_idx, num_boxes, is_padding, &out_tensor);

  // gpu routine
  DeviceContext ctx;
  // inputs
  DeviceTensor<T0, 4> image_tensor(TensorShape({n, ih, iw, c}));
  std::vector<ConstDeviceTensorView<T0, 3>> device_tensors_view;
  DeviceTensor<float, 2> boxes_tensor(TensorShape({num_boxes, 4}));
  DeviceTensor<int, 1> boxes_idx_tensor(TensorShape({num_boxes}));
  // outputs
  TensorShape output_shape;
  if (is_channel_first) {
    output_shape = TensorShape({num_boxes, c, oh, ow});
  } else {
    output_shape = TensorShape({num_boxes, oh, ow, c});
  }
  DeviceTensor<T1, 4> crops_tensor(std::move(output_shape));
  DeviceTensorView<T1, 4> crops_tensor_view(&crops_tensor);
  std::vector<T1> results(crops_tensor.NumElements());

  // feed data
  image_tensor.mutable_buffer()->FromCpu(in_buf.data(), in_buf.size(), &ctx);
  for (int i = 0; i < in_tensor.Batch(); i++) {
    device_tensors_view.emplace_back(ConstDeviceTensorView<uint8_t, 3>(
        reinterpret_cast<const T0*>(image_tensor[i].mutable_data()),
        TensorShape(
            {image_tensor.Dimension(1), image_tensor.Dimension(2), image_tensor.Dimension(3)})));
  }
  boxes_tensor.mutable_buffer()->FromCpu(boxes.data(), boxes.size() * sizeof(float), &ctx);
  boxes_idx_tensor.mutable_buffer()->FromCpu(
      boxes_idx.data(), boxes_idx.size() * sizeof(int), &ctx);

  CropResizePadding(device_tensors_view,
                    boxes_tensor.ConstView(),
                    boxes_idx_tensor.ConstView(),
                    ctx.stream(),
                    num_boxes,
                    is_padding,
                    is_channel_first,
                    &crops_tensor_view);
  crops_tensor.buffer().ToCpu(results.data(), crops_tensor.NumBytes(), &ctx);
  ctx.Sync();
  for (size_t i = 0; i < out_buf.size(); i++) {
    EXPECT_NEAR(out_buf[i], results[i], 1);
  }
}

TEST(CropResizePaddingOpTest, PaddingTest) {
  const bool is_padding = true;
  const bool is_channel_first = false;
  CropResizePaddingTest<uint8_t, float>(is_padding, is_channel_first);
}

TEST(CropResizePaddingOpTest, NoPaddingTest) {
  const bool is_padding = false;
  const bool is_channel_first = false;
  CropResizePaddingTest<uint8_t, float>(is_padding, is_channel_first);
}

TEST(CropResizePaddingOpTest, ChannelFirstTest) {
  const bool is_padding = false;
  const bool is_channel_first = true;
  CropResizePaddingTest<uint8_t, uint8_t>(is_padding, is_channel_first);
}

TEST(CropResizePaddingOpTest, HalfTest) {
  const bool is_padding = false;
  const bool is_channel_first = true;
  CropResizePaddingTest<uint8_t, half>(is_padding, is_channel_first);
}

}  // namespace
}  // namespace cuda
