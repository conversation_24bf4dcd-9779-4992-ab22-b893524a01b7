load("//build/bazel_rules:cc_rules.bzl", "cc_filegroup")
load("//build/bazel_rules:cuda_library.bzl", "cuda_library")

package(default_visibility = ["//visibility:public"])

cuda_library(
    name = "crop_and_resize_cuda",
    srcs = [
        "crop_and_resize_op.cu",
    ],
    hdrs = [
        "crop_and_resize_op.h",
    ],
    deps = [
        "//base/cuda:tensor",
        "@cuda",
        "@gflags",
        "@glog",
    ],
)

cuda_library(
    name = "crop_resize_padding_cuda",
    srcs = [
        "crop_resize_padding_op.cu",
    ],
    hdrs = [
        "crop_resize_padding_op.h",
    ],
    deps = [
        "//base/cuda:tensor",
        "@cuda",
        "@gflags",
        "@glog",
    ],
)

cuda_library(
    name = "mask_image_inplace_cuda",
    srcs = [
        "mask_image_inplace_op.cu",
    ],
    hdrs = [
        "mask_image_inplace_op.h",
    ],
    deps = [
        "//base/cuda:tensor",
        "@cuda",
        "@gflags",
        "@glog",
    ],
)

cuda_library(
    name = "image_type_cast_cuda",
    srcs = [
        "image_type_cast_op.cu",
    ],
    hdrs = [
        "image_type_cast_op.h",
    ],
    deps = [
        "//base/cuda:macros",
        "@cuda",
        "@gflags",
        "@glog",
    ],
)

cuda_library(
    name = "resize_bilinear_cuda",
    srcs = [
        "resize_bilinear_op.cu",
    ],
    hdrs = [
        "resize_bilinear_op.h",
    ],
    deps = [
        "//base/cuda:macros",
        "@cuda",
        "@gflags",
        "@glog",
    ],
)

cuda_library(
    name = "remap_bilinear_cuda",
    srcs = [
        "remap_bilinear_op.cu",
    ],
    hdrs = [
        "remap_bilinear_op.h",
    ],
    deps = [
        "//base/cuda:macros",
        "@cuda",
        "@gflags",
        "@glog",
    ],
)

cuda_library(
    name = "reference_point_project_cuda",
    srcs = [
        "reference_point_project_op.cu",
    ],
    hdrs = [
        "reference_point_project_op.h",
    ],
    deps = [
        "//base/cuda:device_utils",
        "@cuda",
        "@gflags",
        "@glog",
    ],
)

cuda_library(
    name = "transpose_cuda",
    srcs = [
        "transpose_op.cu",
    ],
    hdrs = [
        "transpose_op.h",
    ],
    deps = [
        "//base/cuda:device_utils",
        "@cuda",
        "@gflags",
        "@glog",
    ],
)

cuda_library(
    name = "inplace_add_cuda",
    srcs = [
        "inplace_add_op.cu",
    ],
    hdrs = [
        "inplace_add_op.h",
    ],
    deps = [
        "//base/cuda:device_buffer",
        "@cuda",
        "@gflags",
        "@glog",
    ],
)

cuda_library(
    name = "boundary_filter_cuda",
    srcs = [
        "boundary_filter_op.cu",
    ],
    hdrs = [
        "boundary_filter_op.h",
    ],
    deps = [
        "//base/cuda:tensor",
        "@cuda",
        "@gflags",
        "@glog",
    ],
)

cuda_library(
    name = "normalize_cuda",
    srcs = [
        "normalize_op.cu",
    ],
    hdrs = [
        "normalize_op.h",
    ],
    deps = [
        "//base/cuda:tensor",
        "@cuda",
    ],
)

cuda_library(
    name = "undistort_image_cuda",
    srcs = [
        "undistort_image_op.cu",
    ],
    hdrs = [
        "undistort_image_op.h",
    ],
    deps = [
        "//base/cuda:tensor",
        "@cuda",
        "@gflags",
        "@glog",
    ],
)

cuda_library(
    name = "pillar_scatter_cuda",
    srcs = [
        "pillar_scatter_op.cu",
    ],
    hdrs = [
        "pillar_scatter_op.h",
    ],
    implementation_deps = [
        "//base/cuda:macros",
    ],
    deps = [
        "@glog",
    ],
)

cuda_library(
    name = "tensor_validity_cuda",
    srcs = [
        "tensor_validity_op.cu",
    ],
    hdrs = [
        "tensor_validity_op.h",
    ],
    deps = [
        "//base/cuda:macros",
        "@glog",
        "@tensorrt",
    ],
)

cc_library(
    name = "cuda_ops_library",
    hdrs = glob([
        "*.h",
    ]),
    deps = [
        ":lib",
        "//base/cuda:tensor",
        "@cuda",
    ],
)

cc_filegroup(
    name = "lib",
    srcs = [
        ":boundary_filter_cuda",
        ":crop_and_resize_cuda",
        ":crop_resize_padding_cuda",
        ":lidar_hesaiat128_unpack_cuda",
        ":lidar_rse1_unpack_cuda",
        ":image_type_cast_cuda",
        ":inplace_add_cuda",
        ":mask_image_inplace_cuda",
        ":normalize_cuda",
        ":pillar_scatter_cuda",
        ":reference_point_project_cuda",
        ":remap_bilinear_cuda",
        ":resize_bilinear_cuda",
        ":tensor_validity_cuda",
        ":transpose_cuda",
        ":undistort_image_cuda",
    ],
)

cc_test(
    name = "crop_and_resize_op_test",
    srcs = ["crop_and_resize_op_test.cc"],
    tags = [
        "ci",
        "ci_gpu",
    ],
    deps = [
        ":cuda_ops_library",
        "//base/testing:test_main",
    ],
)

cc_test(
    name = "crop_resize_padding_op_test",
    srcs = ["crop_resize_padding_op_test.cc"],
    tags = [
        "ci",
        "ci_gpu",
    ],
    deps = [
        ":cuda_ops_library",
        "//base/testing:test_main",
    ],
)

cc_test(
    name = "mask_image_inplace_op_test",
    srcs = ["mask_image_inplace_op_test.cc"],
    tags = [
        "ci",
        "ci_gpu",
    ],
    deps = [
        ":cuda_ops_library",
        "//base/testing:test_main",
    ],
)

cc_test(
    name = "resize_bilinear_op_test",
    srcs = ["resize_bilinear_op_test.cc"],
    tags = [
        "ci",
        "ci_gpu",
    ],
    deps = [
        ":cuda_ops_library",
        "//base/testing:test_main",
    ],
)

cc_test(
    name = "remap_bilinear_op_test",
    srcs = ["remap_bilinear_op_test.cc"],
    tags = [
        "ci",
        "ci_gpu",
    ],
    deps = [
        ":cuda_ops_library",
        "//base/testing:test_main",
    ],
)

cc_test(
    name = "reference_point_project_op_test",
    srcs = ["reference_point_project_op_test.cc"],
    tags = [
        "ci",
        "ci_gpu",
    ],
    deps = [
        ":cuda_ops_library",
        "//base/cuda:device_buffer",
        "//base/cuda:device_utils",
        "//base/testing:test_main",
    ],
)

cc_test(
    name = "transpose_op_test",
    srcs = ["transpose_op_test.cc"],
    tags = [
        "ci",
        "ci_gpu",
    ],
    deps = [
        ":cuda_ops_library",
        "//base/testing:test_main",
    ],
)

cc_binary(
    name = "transpose_op_benchmark",
    srcs = ["transpose_op_benchmark.cc"],
    deps = [
        ":cuda_ops_library",
        "@benchmark",
    ],
)

cc_test(
    name = "inplace_add_op_test",
    srcs = ["inplace_add_op_test.cc"],
    tags = [
        "ci",
        "ci_gpu",
    ],
    deps = [
        ":cuda_ops_library",
        "//base/testing:test_main",
    ],
)

cc_test(
    name = "boundary_filter_op_test",
    srcs = ["boundary_filter_op_test.cc"],
    tags = [
        "ci",
        "ci_gpu",
    ],
    deps = [
        ":cuda_ops_library",
        "//base/testing:test_main",
    ],
)

cc_test(
    name = "normalize_op_test",
    srcs = ["normalize_op_test.cc"],
    tags = [
        "ci",
        "ci_gpu",
    ],
    deps = [
        ":cuda_ops_library",
        "//base/testing:test_main",
    ],
)

cc_test(
    name = "undistort_image_op_test",
    srcs = ["undistort_image_op_test.cc"],
    data = ["//base/image/test_data"],
    tags = [
        "ci",
        "ci_gpu",
    ],
    deps = [
        ":cuda_ops_library",
        "//base/testing:test_main",
        "@eigen",
        "@opencv2//:calib3d",
        "@opencv2//:core",
        "@opencv2//:highgui",
        "@opencv2//:imgcodecs",
        "@opencv2//:imgproc",
    ],
)

cc_test(
    name = "pillar_scatter_op_test",
    srcs = [
        "pillar_scatter_op_test.cc",
    ],
    tags = [
        "ci",
        "ci_gpu",
    ],
    deps = [
        ":pillar_scatter_cuda",
        "//base/cuda:device_buffer",
        "//base/testing:test_main",
    ],
)

cc_test(
    name = "tensor_validity_op_test",
    srcs = [
        "tensor_validity_op_test.cc",
    ],
    tags = [
        "ci",
        "ci_gpu",
    ],
    deps = [
        ":tensor_validity_cuda",
        "//base/cuda:mirror_buffer",
        "//base/testing:test_main",
    ],
)

cuda_library(
    name="lidar_hesaiat128_unpack_cuda",
    srcs=["lidar_hesaiat128_unpack_op.cu"],
    hdrs=["lidar_hesaiat128_unpack_op.h"],
    deps=[
        "//base/cuda:device_buffer",
        "//base/cuda:macros",
        "//sensor/lidar:data_types_device",
        "//sensor/lidar/hesai:hesai_data_types",
        "@cuda",
    ],
)
cc_test(
    name="lidar_hesaiat128_unpack_op_test",
    srcs=[
        "lidar_hesaiat128_unpack_op_test.cc",
    ],
    tags=[
        "ci",
        "ci_gpu",
    ],
    deps=[
        ":lidar_hesaiat128_unpack_cuda",
        "//base/cuda:device_buffer",
        "//base/cuda:device_utils",
        "//base/cuda:macros",
        "//base/math:vector",
        "//base/testing:test_main",
        "//sensor/lidar:data_types_device",
    ],
)
cuda_library(
    name="lidar_rse1_unpack_cuda",
    srcs=["lidar_rse1_unpack_op.cu"],
    hdrs=["lidar_rse1_unpack_op.h"],
    deps=[
        "//base/cuda:macros",
        "//base/cuda:device_buffer",
        "//base/cuda:device_utils",
        "//sensor/lidar:data_types_device",
        "//sensor/lidar/robosense:robosense_data_types",
        "@cuda",
    ],
)
cc_test(
    name="lidar_rse1_unpack_op_test",
    srcs=[
        "lidar_rse1_unpack_op_test.cc",
    ],
    tags=[
        "ci",
        "ci_gpu",
    ],
    deps=[
        ":lidar_rse1_unpack_cuda",
        "//base/cuda:device_buffer",
        "//base/cuda:device_utils",
        "//base/cuda:macros",
        "//base/testing:test_main",
        "//sensor/lidar:data_types_device",
        "//sensor/lidar/robosense:robosense_data_types",
    ],
)
