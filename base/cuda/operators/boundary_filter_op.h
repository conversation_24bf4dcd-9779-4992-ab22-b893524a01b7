// Copyright @2023 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <algorithm>
#include <memory>
#include <string>
#include <utility>

#include <cuda_runtime.h>
#include "glog/logging.h"

#include "base/cuda/device_utils.h"
#include "base/cuda/tensor.h"

namespace cuda {

/*
 * BoundaryFilter
 * Given an image
 * set center pixel as 255 when max and min in 3*3 equals
 *
 * inputs
 *   - image: 3D tensor [in_height, in_width, channels],
 * outputs
 *   - image_tensor: 3D tensor [in_height, in_width, channels].
 *
 */
template <typename T>
void BoundaryFilter(cudaStream_t stream,
                    const DeviceTensor<T, 3>& input_image,
                    DeviceTensor<T, 3>* mask_image);

}  // namespace cuda
