// Copyright @2023 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/cuda/operators/undistort_image_op.h"

#include <chrono>
#include <cmath>
#include <functional>
#include <numeric>
#include <vector>

#include "Eigen/Dense"
#include "gtest/gtest.h"
#include <opencv2/core/core.hpp>
#include <opencv2/imgproc/imgproc.hpp>
#include <opencv2/opencv.hpp>

#include "base/cuda/device_buffer.h"
#include "base/cuda/device_utils.h"
#include "base/cuda/tensor.h"

namespace cuda {
namespace {

constexpr char kTestInputData[] = "base/image/test_data/input.png";
constexpr float kMapPointEpsilon = 0.06;
constexpr uint8_t kImagePixelEpsilon = 5;

class UndistortImageOpTest : public ::testing::Test {
 public:
  void SetUp() override { PrepareInput(); }

 protected:
  using Device1DTensor = cuda::DeviceTensor<float, 1>;
  using Device2DTensor = cuda::DeviceTensor<float, 2>;
  using Device1DTensorPtr = std::unique_ptr<Device1DTensor>;
  using Device2DTensorPtr = std::unique_ptr<Device2DTensor>;
  using DeviceImageTensor = cuda::DeviceTensor<uint8_t, 3>;
  using DeviceImageTensorPtr = std::unique_ptr<DeviceImageTensor>;

  int image_width_ = 0;
  int image_height_ = 0;
  int image_channel_ = 0;
  int image_element_num_ = 0;
  int resize_image_width_ = 0;
  int resize_image_height_ = 0;
  int resize_image_element_num_ = 0;
  cv::Mat image_cv_;
  DeviceContext ctx_;
  std::vector<float> pinhole_cam_k_ = GetFakePinholeCamK();
  Device2DTensorPtr pinhole_device_cam_k_;
  cv::Mat pinhole_cv_cam_k_;
  std::vector<float> pinhole_distort_ = GetFakePinholeDistort();
  Device1DTensorPtr pinhole_device_distort_;
  std::vector<float> fisheye_cam_k_ = GetFakeFisheyeCamK();
  Device2DTensorPtr fisheye_device_cam_k_;
  cv::Mat fisheye_cv_cam_k_;
  std::vector<float> fisheye_distort_ = GetFakeFisheyeDistort();
  Device1DTensorPtr fisheye_device_distort_;
  std::vector<float> new_cam_k_ = GetFakeNewCamK();
  cv::Mat cv_new_cam_k_;
  std::vector<float> new_cam_k_inv_ = GetCamKInv(new_cam_k_);
  Device2DTensorPtr device_new_cam_k_inv_;
  DeviceImageTensorPtr image_device_;

  void CheckUndistortMapResult(const DeviceImageTensor& undistorted_image_device,
                               const Device2DTensor& map_x,
                               const Device2DTensor& map_y) {
    const int out_height = undistorted_image_device.Dimension(0);
    const int out_width = undistorted_image_device.Dimension(1);
    const int out_channel = undistorted_image_device.Dimension(2);
    std::vector<uint8_t> undistorted_image_host(undistorted_image_device.NumElements(), 0);
    undistorted_image_device.buffer().ToCpu(
        undistorted_image_host.data(), undistorted_image_device.NumBytes(), &ctx_);
    cv::Mat cv_map_x(out_height, out_width, CV_32FC1);
    cv::Mat cv_map_y(out_height, out_width, CV_32FC1);
    map_x.buffer().ToCpu(cv_map_x.data, map_x.NumBytes(), &ctx_);
    map_y.buffer().ToCpu(cv_map_y.data, map_y.NumBytes(), &ctx_);
    ctx_.Sync();
    cv::Mat undistorted_image_cv;
    cv::remap(image_cv_, undistorted_image_cv, cv_map_x, cv_map_y, cv::INTER_LINEAR);
    for (int ri = 0; ri < out_height; ++ri) {
      for (int ci = 0; ci < out_width; ++ci) {
        for (int hi = 0; hi < out_channel; ++hi) {
          if (cv_map_x.at<float>(ri, ci) < 0 || cv_map_y.at<float>(ri, ci) < 0) {
            continue;
          }
          if (cv_map_x.at<float>(ri, ci) >= image_width_ - 1 ||
              cv_map_y.at<float>(ri, ci) >= image_height_ - 1) {
            continue;
          }
          const int val_cuda =
              undistorted_image_host.at((ri * out_width + ci) * image_channel_ + hi);
          const int val_cv = undistorted_image_cv.at<cv::Vec3b>(ri, ci)[hi];
          EXPECT_NEAR(val_cuda, val_cv, kImagePixelEpsilon);
        }
      }
    }
  }

 private:
  void PrepareInput() {
    image_cv_ = cv::imread(kTestInputData);
    CHECK(!image_cv_.empty());
    image_height_ = image_cv_.rows;
    image_width_ = image_cv_.cols;
    image_channel_ = image_cv_.channels();
    image_element_num_ = image_height_ * image_width_ * image_channel_;
    resize_image_height_ = std::floor(image_height_ / 2);
    resize_image_width_ = std::floor(image_width_ / 2);
    resize_image_element_num_ = resize_image_height_ * resize_image_width_ * image_channel_;
    pinhole_device_cam_k_ = std::make_unique<Device2DTensor>(TensorShape({3, 3}));
    pinhole_device_cam_k_->mutable_buffer()->FromCpu(pinhole_cam_k_.data(), &ctx_);
    pinhole_cv_cam_k_ = cv::Mat(3, 3, CV_32F, pinhole_cam_k_.data());
    fisheye_device_cam_k_ = std::make_unique<Device2DTensor>(TensorShape({3, 3}));
    fisheye_device_cam_k_->mutable_buffer()->FromCpu(fisheye_cam_k_.data(), &ctx_);
    fisheye_cv_cam_k_ = cv::Mat(3, 3, CV_32F, fisheye_cam_k_.data());
    device_new_cam_k_inv_ = std::make_unique<Device2DTensor>(TensorShape({3, 3}));
    device_new_cam_k_inv_->mutable_buffer()->FromCpu(new_cam_k_inv_.data(), &ctx_);
    cv_new_cam_k_ = cv::Mat(3, 3, CV_32F, new_cam_k_.data());
    pinhole_device_distort_ = std::make_unique<Device1DTensor>(TensorShape({5}));
    pinhole_device_distort_->mutable_buffer()->FromCpu(pinhole_distort_.data(), &ctx_);
    fisheye_device_distort_ = std::make_unique<Device1DTensor>(TensorShape({4}));
    fisheye_device_distort_->mutable_buffer()->FromCpu(fisheye_distort_.data(), &ctx_);
    image_device_ = std::make_unique<DeviceImageTensor>(
        TensorShape({image_height_, image_width_, image_channel_}));
    image_device_->mutable_buffer()->FromCpu(image_cv_.data, &ctx_);
    ctx_.Sync();
  }

  std::vector<float> GetFakePinholeCamK() {
    std::vector<float> in_buf(9, 0.0);
    in_buf.at(0) = 1939.35;
    in_buf.at(2) = 946.51;
    in_buf.at(4) = 1938.77;
    in_buf.at(5) = 514.82;
    in_buf.at(8) = 1.0;
    return in_buf;
  }

  std::vector<float> GetFakePinholeDistort() {
    std::vector<float> in_buf(5, 0.0);
    in_buf.at(0) = -0.5343808;
    in_buf.at(1) = 0.24949104;
    in_buf.at(2) = -0.0001652;
    in_buf.at(3) = 0.000010;
    in_buf.at(4) = 0.0;
    return in_buf;
  }

  std::vector<float> GetFakeFisheyeCamK() {
    std::vector<float> in_buf(9, 0.0);
    in_buf.at(0) = 1010.519;
    in_buf.at(2) = 959.202;
    in_buf.at(4) = 1009.976;
    in_buf.at(5) = 523.683;
    return in_buf;
  }

  std::vector<float> GetFakeNewCamK() {
    std::vector<float> in_buf(9, 0.0);
    in_buf.at(0) = 1500;
    in_buf.at(2) = 960;
    in_buf.at(4) = 1500;
    in_buf.at(5) = 540;
    in_buf.at(8) = 1.0;
    return in_buf;
  }

  std::vector<float> GetCamKInv(const std::vector<float>& in_buf) {
    Eigen::Matrix3f cam_k = Eigen::Matrix3f::Identity();
    cam_k(0, 0) = in_buf.at(0);
    cam_k(0, 2) = in_buf.at(2);
    cam_k(1, 1) = in_buf.at(4);
    cam_k(1, 2) = in_buf.at(5);
    const Eigen::Matrix3f cam_k_inv = cam_k.inverse();
    std::vector<float> out_buf(9, 0.0);
    out_buf.at(0) = cam_k_inv(0, 0);
    out_buf.at(2) = cam_k_inv(0, 2);
    out_buf.at(4) = cam_k_inv(1, 1);
    out_buf.at(5) = cam_k_inv(1, 2);
    out_buf.at(8) = 1.0;
    return out_buf;
  }

  std::vector<float> GetFakeFisheyeDistort() {
    std::vector<float> in_buf(4, 0.0);
    in_buf.at(0) = -0.0638489;
    in_buf.at(1) = -0.0041770;
    in_buf.at(2) = -0.0030149;
    in_buf.at(3) = 0.00045078;
    return in_buf;
  }
};

TEST_F(UndistortImageOpTest, PinholeUndistortMapTest) {
  Device2DTensor map_x(TensorShape({image_height_, image_width_}));
  Device2DTensor map_y(TensorShape({image_height_, image_width_}));
  BuildPinholeUndistortMap(ctx_.stream(),
                           pinhole_device_cam_k_->ConstView(),
                           pinhole_device_distort_->ConstView(),
                           device_new_cam_k_inv_->ConstView(),
                           &map_x,
                           &map_y);
  ctx_.Sync();
  cv::Mat cv_map_x;
  cv::Mat cv_map_y;
  cv::initUndistortRectifyMap(pinhole_cv_cam_k_,
                              pinhole_distort_,
                              cv::Mat(),
                              cv_new_cam_k_,
                              cv::Size(image_width_, image_height_),
                              CV_32F,
                              cv_map_x,
                              cv_map_y);
  EXPECT_TRUE(cv_map_x.rows == image_height_);
  EXPECT_TRUE(cv_map_x.cols == image_width_);
  std::vector<float> host_map_x(image_width_ * image_height_);
  std::vector<float> host_map_y(image_width_ * image_height_);
  map_x.buffer().ToCpu(host_map_x.data(), image_width_ * image_height_ * sizeof(float), &ctx_);
  map_y.buffer().ToCpu(host_map_y.data(), image_width_ * image_height_ * sizeof(float), &ctx_);
  ctx_.Sync();
  for (int ri = 0; ri < cv_map_x.rows; ++ri) {
    for (int ci = 0; ci < cv_map_y.cols; ++ci) {
      const float val_cv_x = cv_map_x.at<float>(ri, ci);
      const float val_cuda_x = host_map_x.at(ri * image_width_ + ci);
      EXPECT_NEAR(val_cv_x, val_cuda_x, kMapPointEpsilon);
      const float val_cv_y = cv_map_y.at<float>(ri, ci);
      const float val_cuda_y = host_map_y.at(ri * image_width_ + ci);
      EXPECT_NEAR(val_cv_y, val_cuda_y, kMapPointEpsilon);
    }
  }
}

TEST_F(UndistortImageOpTest, FisheyeUndistortMapTest) {
  Device2DTensor map_x(TensorShape({image_height_, image_width_}));
  Device2DTensor map_y(TensorShape({image_height_, image_width_}));
  BuildFisheyeUndistortMap(ctx_.stream(),
                           fisheye_device_cam_k_->ConstView(),
                           fisheye_device_distort_->ConstView(),
                           device_new_cam_k_inv_->ConstView(),
                           &map_x,
                           &map_y);
  ctx_.Sync();
  cv::Mat cv_map_x;
  cv::Mat cv_map_y;
  cv::fisheye::initUndistortRectifyMap(fisheye_cv_cam_k_,
                                       fisheye_distort_,
                                       cv::Mat(),
                                       cv_new_cam_k_,
                                       cv::Size(image_width_, image_height_),
                                       CV_32F,
                                       cv_map_x,
                                       cv_map_y);
  EXPECT_TRUE(cv_map_x.rows == image_height_);
  EXPECT_TRUE(cv_map_x.cols == image_width_);
  std::vector<float> host_map_x(image_width_ * image_height_);
  std::vector<float> host_map_y(image_width_ * image_height_);
  map_x.buffer().ToCpu(host_map_x.data(), image_width_ * image_height_ * sizeof(float), &ctx_);
  map_y.buffer().ToCpu(host_map_y.data(), image_width_ * image_height_ * sizeof(float), &ctx_);
  ctx_.Sync();
  for (int ri = 0; ri < cv_map_x.rows; ++ri) {
    for (int ci = 0; ci < cv_map_y.cols; ++ci) {
      const float val_cv_x = cv_map_x.at<float>(ri, ci);
      const float val_cuda_x = host_map_x.at(ri * image_width_ + ci);
      EXPECT_NEAR(val_cv_x, val_cuda_x, kMapPointEpsilon);
      const float val_cv_y = cv_map_y.at<float>(ri, ci);
      const float val_cuda_y = host_map_y.at(ri * image_width_ + ci);
      EXPECT_NEAR(val_cv_y, val_cuda_y, kMapPointEpsilon);
    }
  }
}

TEST_F(UndistortImageOpTest, PinholeUndistortImageTest) {
  Device2DTensor map_x(TensorShape({image_height_, image_width_}));
  Device2DTensor map_y(TensorShape({image_height_, image_width_}));
  BuildPinholeUndistortMap(ctx_.stream(),
                           pinhole_device_cam_k_->ConstView(),
                           pinhole_device_distort_->ConstView(),
                           device_new_cam_k_inv_->ConstView(),
                           &map_x,
                           &map_y);
  DeviceImageTensor undistorted_image_device(
      TensorShape({image_height_, image_width_, image_channel_}));
  UndistortImageWrapper(ctx_.stream(),
                        "pinhole",
                        image_device_->ConstView(),
                        pinhole_device_cam_k_->ConstView(),
                        pinhole_device_distort_->ConstView(),
                        device_new_cam_k_inv_->ConstView(),
                        &undistorted_image_device);
  CheckUndistortMapResult(undistorted_image_device, map_x, map_y);
}

TEST_F(UndistortImageOpTest, FisheyeUndistortImageTest) {
  Device2DTensor map_x(TensorShape({image_height_, image_width_}));
  Device2DTensor map_y(TensorShape({image_height_, image_width_}));
  BuildFisheyeUndistortMap(ctx_.stream(),
                           fisheye_device_cam_k_->ConstView(),
                           fisheye_device_distort_->ConstView(),
                           device_new_cam_k_inv_->ConstView(),
                           &map_x,
                           &map_y);
  DeviceImageTensor undistorted_image_device(
      TensorShape({image_height_, image_width_, image_channel_}));
  UndistortImageWrapper(ctx_.stream(),
                        "fisheye",
                        image_device_->ConstView(),
                        fisheye_device_cam_k_->ConstView(),
                        fisheye_device_distort_->ConstView(),
                        device_new_cam_k_inv_->ConstView(),
                        &undistorted_image_device);
  CheckUndistortMapResult(undistorted_image_device, map_x, map_y);
}

TEST_F(UndistortImageOpTest, PinholResizeUndistortImageTest) {
  Device2DTensor map_x(TensorShape({resize_image_height_, resize_image_width_}));
  Device2DTensor map_y(TensorShape({resize_image_height_, resize_image_width_}));
  BuildPinholeUndistortMap(ctx_.stream(),
                           pinhole_device_cam_k_->ConstView(),
                           pinhole_device_distort_->ConstView(),
                           device_new_cam_k_inv_->ConstView(),
                           &map_x,
                           &map_y);
  DeviceImageTensor undistorted_image_device(
      TensorShape({resize_image_height_, resize_image_width_, image_channel_}));
  UndistortImageWrapper(ctx_.stream(),
                        "pinhole",
                        image_device_->ConstView(),
                        pinhole_device_cam_k_->ConstView(),
                        pinhole_device_distort_->ConstView(),
                        device_new_cam_k_inv_->ConstView(),
                        &undistorted_image_device);
  CheckUndistortMapResult(undistorted_image_device, map_x, map_y);
}

TEST_F(UndistortImageOpTest, FisheyeResizeUndistortImageTest) {
  Device2DTensor map_x(TensorShape({resize_image_height_, resize_image_width_}));
  Device2DTensor map_y(TensorShape({resize_image_height_, resize_image_width_}));
  BuildFisheyeUndistortMap(ctx_.stream(),
                           fisheye_device_cam_k_->ConstView(),
                           fisheye_device_distort_->ConstView(),
                           device_new_cam_k_inv_->ConstView(),
                           &map_x,
                           &map_y);
  DeviceImageTensor undistorted_image_device(
      TensorShape({resize_image_height_, resize_image_width_, image_channel_}));
  UndistortImageWrapper(ctx_.stream(),
                        "fisheye",
                        image_device_->ConstView(),
                        fisheye_device_cam_k_->ConstView(),
                        fisheye_device_distort_->ConstView(),
                        device_new_cam_k_inv_->ConstView(),
                        &undistorted_image_device);
  CheckUndistortMapResult(undistorted_image_device, map_x, map_y);
}

}  // namespace
}  // namespace cuda
