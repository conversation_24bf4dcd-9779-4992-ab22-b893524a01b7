// Copyright @2023 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <cuda_runtime.h>

namespace cuda {

template <typename T0, typename T1>
void ReferencePointProjectWrapper(cudaStream_t stream,
                                  int batch_size,
                                  int grid_width,
                                  int grid_length,
                                  float grid_center_x,
                                  float grid_center_y,
                                  float resolution_x,
                                  float resolution_y,
                                  int num_sampled_points_in_pillar,
                                  int num_camera,
                                  const bool* is_camera_valid,
                                  const int* image_width,
                                  const int* image_height,
                                  const bool* is_fisheye,
                                  const float* z_value,
                                  const float* intrinsic_matrix,
                                  const float* transform_matrix,
                                  T0* projection_result,
                                  T1* mask);

template <typename T0, typename T1>
void ReferencePointProjectAdaptiveResolutionWrapper(cudaStream_t stream,
                                                    int batch_size,
                                                    int grid_width,
                                                    int grid_length,
                                                    float grid_center_x,
                                                    float grid_center_y,
                                                    float resolution_x,
                                                    float resolution_y,
                                                    int num_sampled_points_in_pillar,
                                                    int num_camera,
                                                    const bool* is_camera_valid,
                                                    const int* image_width,
                                                    const int* image_height,
                                                    const bool* is_fisheye,
                                                    const float* z_value,
                                                    const float* intrinsic_matrix,
                                                    const float* transform_matrix,
                                                    const float* adaptive_coeffs,
                                                    T0* projection_result,
                                                    T1* mask);

}  // namespace cuda
