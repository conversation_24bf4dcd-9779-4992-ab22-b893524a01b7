// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include <chrono>
#include <cmath>
#include <functional>
#include <numeric>
#include <vector>

#include <cuda_fp16.h>
#include "gtest/gtest.h"

#include "base/cuda/device_buffer.h"
#include "base/cuda/device_utils.h"
#include "base/cuda/operators/resize_bilinear_op.h"
#include "base/cuda/tensor.h"

namespace cuda {
namespace {

// [n, ih, iw, c] -> [n, oh, ow, c]
template <typename T>
void ResizeBilinearCpu(bool enable_half_pixel_centers, const Tensor4D<T>& input, Tensor4D<T>* output) {
  CHECK(output != nullptr);
  CHECK(input.Batch() == output->Batch());

  const float height_scale = input.Height() / static_cast<float>(output->Height());
  const float width_scale = input.Width() / static_cast<float>(output->Width());

  for (int b = 0; b < output->Batch(); ++b) {
    for (int y = 0; y < output->Height(); ++y) {
      const float in_y =
        enable_half_pixel_centers ? (static_cast<float>(y) + 0.5f) * height_scale - 0.5f : y * height_scale;
      const int top_y_index = std::max(static_cast<int>(floorf(in_y)), static_cast<int>(0));
      const int bottom_y_index = std::min(static_cast<int>(ceilf(in_y)), input.Height() - 1);
      const float y_lerp = in_y - std::floor(in_y);
      for (int x = 0; x < output->Width(); ++x) {
        const float in_x =
          enable_half_pixel_centers ? (static_cast<float>(x) + 0.5f) * width_scale - 0.5f : x * width_scale;
        const int left_x_index = std::max(static_cast<int>(floorf(in_x)), static_cast<int>(0));
        const int right_x_index = std::min(static_cast<int>(ceilf(in_x)), input.Width() - 1);
        const float x_lerp = in_x - std::floor(in_x);
        for (int c = 0; c < output->Channels(); ++c) {
          const float top_left = input(b, top_y_index, left_x_index, c);
          const float top_right = input(b, top_y_index, right_x_index, c);
          const float bottom_left = input(b, bottom_y_index, left_x_index, c);
          const float bottom_right = input(b, bottom_y_index, right_x_index, c);
          const float top = top_left + (top_right - top_left) * x_lerp;
          const float bottom = bottom_left + (bottom_right - bottom_left) * x_lerp;
          (*output)(b, y, x, c) = top + (bottom - top) * y_lerp;
        }
      }
    }
  }
}

TEST(ResizeBilinearOpTest, CpuAgainstGpuUint8) {
  const int n = 2;
  const int ih = 1080;
  const int iw = 1920;
  const int c = 3;
  const int oh = 640;
  const int ow = 960;
  std::vector<uint8_t> in_buf(n * ih * iw * c, 0);
  std::vector<uint8_t> out_buf(n * oh * ow * c, 0);
  std::iota(in_buf.begin(), in_buf.end(), 0);
  Tensor4D<uint8_t> in_tensor(in_buf.data(), n, ih, iw, c);
  Tensor4D<uint8_t> out_tensor(out_buf.data(), n, oh, ow, c);
  ResizeBilinearCpu(true, in_tensor, &out_tensor);

  DeviceContext ctx;
  DeviceBuffer dev_in_buf(in_buf.size());
  DeviceBuffer dev_out_buf(out_buf.size());
  std::vector<uint8_t> results(out_buf.size());

  dev_in_buf.FromCpu(in_buf.data(), in_buf.size(), &ctx);
  ResizeBilinearWrapper(
    ctx.stream(), n, ih, iw, oh, ow, c,
    reinterpret_cast<const uint8_t*>(dev_in_buf.data()),
    reinterpret_cast<uint8_t*>(dev_out_buf.mutable_data()));
  dev_out_buf.ToCpu(results.data(), dev_out_buf.size(), &ctx);
  ctx.Sync();

  for (size_t i = 0; i < out_buf.size(); i++) {
    EXPECT_EQ(out_buf[i], results[i]);
  }
}

TEST(ResizeBilinearOpTest, CpuAgainstGpuHalf) {
  const int n = 2;
  const int ih = 1080;
  const int iw = 1920;
  const int c = 3;
  const int oh = 640;
  const int ow = 960;
  std::vector<half> in_buf(n * ih * iw * c, 0.);
  std::vector<half> out_buf(n * oh * ow * c, 0.);
  Tensor4D<half> in_tensor(in_buf.data(), n, ih, iw, c);
  Tensor4D<half> out_tensor(out_buf.data(), n, oh, ow, c);
  ResizeBilinearCpu(true, in_tensor, &out_tensor);

  DeviceContext ctx;
  DeviceBuffer dev_in_buf(in_buf.size() * sizeof(half));
  DeviceBuffer dev_out_buf(out_buf.size() * sizeof(half));
  std::vector<half> results(out_buf.size());

  dev_in_buf.FromCpu(in_buf.data(), in_buf.size() * sizeof(half), &ctx);
  ResizeBilinearWrapper(ctx.stream(),
                        n,
                        ih,
                        iw,
                        oh,
                        ow,
                        c,
                        reinterpret_cast<const half*>(dev_in_buf.data()),
                        reinterpret_cast<half*>(dev_out_buf.mutable_data()));
  dev_out_buf.ToCpu(results.data(), dev_out_buf.size(), &ctx);
  ctx.Sync();

  for (size_t i = 0; i < out_buf.size(); i++) {
    EXPECT_EQ(out_buf[i], results[i]);
  }
}

}  // namespace
}  // namespace cuda
