// Copyright @2023 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <cuda_runtime.h>

#include "base/cuda/device_utils.h"
#include "base/cuda/tensor.h"

namespace cuda {

enum class NormalizeType {
  kZScore = 0,
  kMinMax = 1,
};

/*
 * NormalizeWrapper
 * Given an image
 * set image pixel multiple and bias
 *
 * inputs
 *   - idata: vector of input image
 *   - type: type of normalize
 *   - is_channel_first: output NCHW data if true, else NHWC
 * outputs
 *   - odata: normalized vector of image
 */

template <typename T0, typename T1>
void Normalize(const ConstDeviceTensorView<T0, 4>& input,
               cudaStream_t stream,
               NormalizeType type,
               bool is_channel_first,
               DeviceTensorView<T1, 4>* output);

}  // namespace cuda
