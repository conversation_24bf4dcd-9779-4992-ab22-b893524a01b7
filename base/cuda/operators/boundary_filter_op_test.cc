// Copyright @2023 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/cuda/operators/boundary_filter_op.h"

#include <numeric>
#include <vector>

#include "gtest/gtest.h"

#include "base/cuda/device_buffer.h"
#include "base/cuda/device_utils.h"
#include "base/cuda/tensor.h"

namespace cuda {
namespace {

constexpr int kImageWidth = 4;
constexpr int kImageHeight = 4;
constexpr int kImageChannels = 1;
constexpr int kNumImages = 1;
constexpr int kPoolBoxSize = 3;

template <typename T>
void BoundaryFilterCpu(const Tensor4D<T>& image, Tensor4D<T>* out_image) {
  CHECK(out_image != nullptr);
  out_image->Fill(0);

  for (int x = 0; x < kImageWidth; ++x) {
    for (int y = 0; y < kImageHeight; ++y) {
      for (int channel = 0; channel < kImageChannels; channel++) {
        T max_center_val = image(0, y, x, channel);
        T min_center_val = image(0, y, x, channel);

        const int range = static_cast<int>(kPoolBoxSize / 2);
        for (int offset_x = -range; offset_x <= range; offset_x++) {    // x
          for (int offset_y = -range; offset_y <= range; offset_y++) {  // y
            // check whether in image
            const int coord_x = x + offset_x;
            const int coord_y = y + offset_y;
            if (coord_x < 0 || coord_x >= kImageWidth || coord_y < 0 || coord_y >= kImageHeight) {
              continue;
            }
            // LOG(ERROR) <<"coord:" << coord_x <<","<<coord_y;
            if (max_center_val < image(0, coord_y, coord_x, channel)) {
              max_center_val = image(0, coord_y, coord_x, channel);
            }
            if (image(0, coord_y, coord_x, channel) < min_center_val) {
              min_center_val = image(0, coord_y, coord_x, channel);
            }
          }
        }
        (*out_image)(0, y, x, channel) =
            max_center_val == min_center_val ? (*out_image)(0, y, x, channel) : static_cast<T>(255);
      }
    }
  }
}

void BoundaryFilterOpTest() {
  std::vector<int> in_buf(kNumImages * kImageHeight * kImageWidth * kImageChannels, 0);
  std::iota(in_buf.begin(), in_buf.end(), 0);
  Tensor4D<int> in_tensor(in_buf.data(), kNumImages, kImageHeight, kImageWidth, kImageChannels);
  std::vector<int> out_buf(kNumImages * kImageHeight * kImageWidth * kImageChannels, 0);
  Tensor4D<int> out_tensor(out_buf.data(), kNumImages, kImageHeight, kImageWidth, kImageChannels);
  BoundaryFilterCpu(in_tensor, &out_tensor);

  // gpu routine
  DeviceContext ctx;
  // inputs
  DeviceTensor<int, 3> image_tensor(TensorShape({kImageHeight, kImageWidth, kImageChannels}));
  std::vector<int> device_in_buf(kNumImages * kImageHeight * kImageWidth * kImageChannels, 0);
  std::iota(device_in_buf.begin(), device_in_buf.end(), 0);
  DeviceTensorView<int, 3> tensor_view(&image_tensor);
  tensor_view.mutable_buffer()->FromCpu(device_in_buf.data(), 0, device_in_buf.size(), &ctx);
  std::vector<int> check_val_buf(kNumImages * kImageHeight * kImageWidth * kImageChannels, 0);
  image_tensor.ConstView().buffer().ToCpu(check_val_buf.data(), &ctx);
  //  outputs
  DeviceTensor<int, 3> output_tensor(TensorShape({kImageHeight, kImageWidth, kImageChannels}));
  std::vector<int> results(output_tensor.NumElements(), 10);
  BoundaryFilter(ctx.stream(), image_tensor, &output_tensor);
  output_tensor.ConstView().buffer().ToCpu(results.data(), &ctx);
  ctx.Sync();

  for (int i = 0; i < in_tensor.size(); i++) {
    EXPECT_NEAR(out_tensor.mutable_data()[i], results[i], 1e-6) << "fail at:" << i;
  }
}

TEST(BoundaryFilterOpTest, BasicTest) { BoundaryFilterOpTest(); }

}  // namespace
}  // namespace cuda
