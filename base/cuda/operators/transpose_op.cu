// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/cuda/operators/transpose_op.h"

#include <cuda_fp16.h>

namespace cuda {

/*
 * transpose dimension x and y, suit for thin matrix
 *
 * task ~ n: number of elements, i.e. batch * dim_x * dim_y
 * input tensor shape: [batch, dim_x, dim_y], contiguous on dim_y
 * output tensor shape: [batch, dim_y, dim_x], contiguous on dim_x
 */
template <typename T>
__global__ void transpose_2d_naive_kernel(
  int n, int dim_x, int dim_y, const T* idata, T* odata) {
  const int tid = threadIdx.x + blockIdx.x * blockDim.x;
  if (tid >= n) return;
  int idx = tid;
  const int iy = idx % dim_y;
  idx /= dim_y;
  const int ix = idx % dim_x;
  idx /= dim_x;
  const int ib = idx;
  odata[ix + dim_x * (iy + dim_y * ib)] = idata[iy + dim_y * (ix + dim_x * ib)];
}

template <typename T>
void Transpose2DNaiveWrapper(
  cudaStream_t stream, int batch, int dim_x, int dim_y, const T* idata, T* odata) {
  int n = batch * dim_x * dim_y;
  constexpr int kNumThreads = 512;
  int num_blocks = DivUp(n, kNumThreads);
  transpose_2d_naive_kernel<T><<<num_blocks, kNumThreads, 0, stream>>>(n, dim_x, dim_y, idata, odata);
  CUDA_KERNEL_LAUNCH_CHECK();
}

template void Transpose2DNaiveWrapper(
  cudaStream_t stream, int batch, int dim_x, int dim_y, const float* idata, float* odata);

template void Transpose2DNaiveWrapper(
  cudaStream_t stream, int batch, int dim_x, int dim_y, const half* idata, half* odata);

template void Transpose2DNaiveWrapper(
  cudaStream_t stream, int batch, int dim_x, int dim_y, const uint8_t* idata, uint8_t* odata);


/*
 * transpose dimension x and y with shared mem
 *
 * task ~ number of elements, i.e. batch * dim_size_y * dim_size_x
 * each thread take care of one element, each block take care of one tile.
 * input tensor shape: [batch, dim_size_y, dim_size_x], contiguous on dim_size_x
 * output tensor shape: [batch, dim_size_x, dim_size_y], contiguous on dim_size_y
 */
constexpr int kTileSize = 16;
constexpr int kBlockRows = 16;

template <typename T>
__global__ void transpose_2d_tiled_kernel(
  int dim_size_y, int dim_size_x,
  int num_tiles_y, int num_tiles_x,
  const T* __restrict__ idata, T* __restrict__ odata) {
  __shared__ T tile[kTileSize][kTileSize + 1];
  const int n = blockIdx.x / (num_tiles_y * num_tiles_x);
  const int k = blockIdx.x % (num_tiles_y * num_tiles_x);
  const int r = k / num_tiles_x;
  const int c = k % num_tiles_x;
  const int tx = threadIdx.x;
  const int ty = threadIdx.y;
  const int ix = c * kTileSize + tx;
  const int iy = r * kTileSize + ty;
  const int ox = r * kTileSize + tx;
  const int oy = c * kTileSize + ty;

  if (ix < dim_size_x) {
    for (int i = 0; ty + i < kTileSize && iy + i < dim_size_y; i += kBlockRows) {
      // tile[y][x] = idata[n, iy, ix], coalesced read from global mem
      tile[ty + i][tx] = __ldg(idata + ix + dim_size_x * (iy + i + dim_size_y * n));
    }
  }
  __syncthreads();

  if (ox < dim_size_y) {
    for (int i = 0; ty + i < kTileSize && oy + i < dim_size_x; i += kBlockRows) {
      // odata[n, oy, ox] = tile[x][y], coalesced write to global mem
      odata[ox + dim_size_y * (oy + i + dim_size_x * n)] = tile[tx][ty + i];
    }
  }
}

template <typename T>
void Transpose2DTiledWrapper(
  cudaStream_t stream, int batch, int dim_x, int dim_y, const T* idata, T* odata) {
  const int num_tiles_x = DivUp(dim_x, kTileSize);
  const int num_tiles_y = DivUp(dim_y, kTileSize);
  dim3 num_threads(kTileSize, kBlockRows);
  int num_blocks = batch * num_tiles_x * num_tiles_y;
  transpose_2d_tiled_kernel<T><<<num_blocks, num_threads, 0, stream>>>(
    dim_x, dim_y, num_tiles_x, num_tiles_y, idata, odata);
  CUDA_KERNEL_LAUNCH_CHECK();
}

template void Transpose2DTiledWrapper(
  cudaStream_t stream, int batch, int dim_x, int dim_y, const float* idata, float* odata);

template void Transpose2DTiledWrapper(
  cudaStream_t stream, int batch, int dim_x, int dim_y, const uint8_t* idata, uint8_t* odata);

template void Transpose2DTiledWrapper(
  cudaStream_t stream, int batch, int dim_x, int dim_y, const int* idata, int* odata);

template <typename T>
void Transpose2DWrapper(
  cudaStream_t stream, int batch, int dim_x, int dim_y, const T* idata, T* odata) {
  if (dim_x < kTileSize || dim_y < kTileSize) {
    Transpose2DNaiveWrapper<T>(stream, batch, dim_x, dim_y, idata, odata);
  } else {
    Transpose2DTiledWrapper<T>(stream, batch, dim_x, dim_y, idata, odata);
  }
}

template void Transpose2DWrapper(
  cudaStream_t stream, int batch, int dim_x, int dim_y, const float* idata, float* odata);

template void Transpose2DWrapper(
  cudaStream_t stream, int batch, int dim_x, int dim_y, const half* idata, half* odata);

template void Transpose2DWrapper(
  cudaStream_t stream, int batch, int dim_x, int dim_y, const uint8_t* idata, uint8_t* odata);

template void Transpose2DWrapper(
  cudaStream_t stream, int batch, int dim_x, int dim_y, const int* idata, int* odata);

}  // namespace cuda
