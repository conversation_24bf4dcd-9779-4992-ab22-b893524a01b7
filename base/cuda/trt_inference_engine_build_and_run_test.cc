// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "gtest/gtest.h"

#include "base/cuda/trt_inference_engine.h"
#include "base/testing/scoped_temp_dir.h"

DECLARE_bool(use_trt_mock_engine);
DECLARE_bool(check_gpu_tensor_validity);

namespace base {
namespace {

constexpr char kModelConfigFile[] = "common/test/resources/model_configs/dummy.pb.txt";

}  // namespace

TEST(TrtInferenceEngineBuildAndRunTest, BasicTest) {
  FLAGS_infer_mode = static_cast<int>(cuda::InferMode::kNativeGPU);
  FLAGS_use_trt_mock_engine = false;
  FLAGS_check_gpu_tensor_validity = false;

  walle::ModelConfig model_config;
  CHECK(base::ReadTextProtoFile(kModelConfigFile, &model_config))
      << kModelConfigFile << " not found!";
  base::testing::ScopedTempDir scoped_dir("walle-%%%%");
  const std::string engine_file = file_path::Join(scoped_dir.temp_path(), "dummy.engine");
  model_config.set_engine_file(engine_file);

  cuda::DeviceContext ctx;
  TrtInferenceEngine trt_inference_engine(model_config, &ctx);
  cuda::DeviceBuffer* input_buffer =
      trt_inference_engine.GetMutableInputBufferOrDie(trt_inference_engine.input_names()[0]);
  EXPECT_EQ(4 * 3 * 224 * 224 * sizeof(float), input_buffer->size());
  cuda::DeviceBuffer* output_buffer =
      trt_inference_engine.GetMutableOutputBufferOrDie(trt_inference_engine.output_names()[0]);
  EXPECT_EQ(4 * 4 * sizeof(float), output_buffer->size());
  trt_inference_engine.ConfigureOutputBufferNamesForAsyncCopy(trt_inference_engine.output_names()[0]);

  const std::vector<float> input_data(1 * 3 * 224 * 224, 5.0f);
  input_buffer->FromCpu(
      reinterpret_cast<const void*>(input_data.data()), input_data.size() * sizeof(float), &ctx);
  trt_inference_engine.Run();
  trt_inference_engine.AsyncCopyOutputBuffersD2H(&ctx);
  ctx.Sync();

  EXPECT_TRUE(trt_inference_engine.CheckTensorValidity());
  FLAGS_check_gpu_tensor_validity = true;
  trt_inference_engine.Run();
  ctx.Sync();
}

}  // namespace base
