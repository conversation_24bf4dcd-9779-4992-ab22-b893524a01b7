package(default_visibility = ["//visibility:public"])

cc_library(
    name = "triton_runtime",
    srcs = ["triton_runtime.cc"],
    hdrs = [
        "triton_runtime.h",
    ],
    deps = [
        "//base/common:environment",
        "//base/container:utils",
        "//base/cuda:device_buffer",
        "//base/cuda:device_utils",
        "//base/cuda:model_runtime",
        "//base/cuda/proto:cc_model_server_grpc",
        "//base/file:file_path_util",
        "//base/file:file_util",
        "//base/strings:format",
        "//modules/common/util:http_client",
        "//third_party/triton_client:grpc_client",
        "//walle/common/proto/model:cc_model_config_proto",
        "@com_google_absl//absl/time",
    ],
)
