// Copyright @2022 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <memory>
#include <string>
#include <unordered_map>
#include <vector>

#include "NvInfer.h"
#include "NvInferPlugin.h"
#include "absl/time/clock.h"
#include "gflags/gflags.h"
#include "glog/logging.h"
#include "third_party/triton_client/grpc_client.h"

#include "base/cuda/device_utils.h"
#include "base/cuda/model_runtime.h"
#include "base/file/file_path_util.h"
#include "base/file/file_util.h"
#include "walle/common/proto/model/model_config.pb.h"

namespace base {
namespace triton {
const int kTimeoutInSecond = 2;
const int kMaxRetryCount = 7;

class TritonRuntime : public ModelRuntime {
 public:
  TritonRuntime(const walle::ModelConfig& model_config, cuda::DeviceContext* context);
  virtual ~TritonRuntime();

  void Enqueue(size_t batch_size, bool use_cuda_graph = false) override;
  void AssignBindings(const std::vector<void*>& bindings,
                      const std::vector<void*>& host_bindings) override;

 private:
  void InitializeBindingInfo();
  nvinfer1::DataType Convert2TrtType(const std::string& dtype);
  nvinfer1::Dims Convert2TrtDim(
      const google::protobuf::RepeatedField<::google::protobuf::int64>& dims);
  void LoadModelInfo();
  void SplitEngineFile();
  void UpdateModelServer();
  void PrepareModelServer();
  void UpdateCudaHandle();
  template <typename Func, typename RetryPred, typename ErrorHandler, typename... Args>
  auto Retry(Func func,
             RetryPred retry_pred,
             ErrorHandler error_handler,
             std::string interface_name,
             Args... args) -> decltype(func(args...));
  triton_client::Error Infer(const std::vector<triton_client::InferInput*>&,
                             const std::vector<const triton_client::InferRequestedOutput*>&,
                             triton_client::InferResult**);
  triton_client::Error CudaSharedMemoryStatus(inference::CudaSharedMemoryStatusResponse* status);
  triton_client::Error RegisterCudaSharedMemory(const std::string&,
                                                const cudaIpcMemHandle_t&,
                                                const size_t,
                                                const size_t);
  void LoadModel();
  void SetModelMetadata();
  std::string CheckTritonResult(const triton_client::Error&);
  void SleepErrorHandler(int);

  std::string model_config_basename_;
  std::string engine_file_;
  std::string repo_key_;
  std::string plugin_id_;
  std::string model_id_;

  std::string model_ser_addr_;
  std::string model_ser_worker_id_;
  std::string infer_ser_addr_;
  std::string uuid_;
  std::string ser_model_version_;
  std::string ser_model_name_;

  std::string modelserving_url_;

  uint32_t client_timeout_ = 0;
  uint32_t gpu_id_ = 0;
  bool use_triton_runtime_ = false;
  triton_client::Headers http_headers_;
  std::unique_ptr<triton_client::InferOptions> options_ = nullptr;
  std::unique_ptr<triton_client::InferenceServerGrpcClient> triton_client_ = nullptr;
  std::vector<cudaIpcMemHandle_t> cuda_shm_handles_;
  std::vector<std::string> cuda_shm_names_;
  inference::ModelMetadataResponse model_metadata_;
  grpc_compression_algorithm compression_algorithm = grpc_compression_algorithm::GRPC_COMPRESS_NONE;
  std::vector<std::string> output_names_;

  DISALLOW_COPY_AND_ASSIGN(TritonRuntime);
};

}  // namespace triton
}  // namespace base
