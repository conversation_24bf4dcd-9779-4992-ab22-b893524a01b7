// Copyright @2022 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/cuda/triton/triton_runtime.h"

#include <algorithm>
#include <boost/algorithm/string.hpp>
#include <chrono>
#include <fstream>

#include "base/common/environment.h"
#include "base/cuda/device_utils.h"
#include "base/cuda/proto/model_server.grpc.pb.h"
#include "base/cuda/proto/model_server.pb.h"
#include "grpcpp/grpcpp.h"
#include "modules/common/util/http_client.h"

namespace base {
namespace triton {
namespace {

using Json = nlohmann::json;
constexpr char kModelInfoFile[] = "config/simcluster/model_config.json";
constexpr char kModelInfoPluginKey[] = "plugin_md5";
constexpr char kModelEngineInfoListKey[] = "model_plugin_list";
constexpr char kUnknowInfo[] = "unknow";
constexpr int kKeepaliveTimeMs = 2000;
constexpr int kGrpcTimeoutSecond = 120;

const std::unordered_map<std::string, nvinfer1::DataType> kTrtDataType{
    {"FP32", nvinfer1::DataType::kFLOAT},
    {"FP16", nvinfer1::DataType::kHALF},
    {"INT8", nvinfer1::DataType::kINT8},
    {"INT32", nvinfer1::DataType::kINT32},
    {"BOOL", nvinfer1::DataType::kBOOL}};

}  // namespace

template <typename Func, typename CheckResultFunc, typename ErrorHandler, typename... Args>
auto TritonRuntime::Retry(Func func,
                          CheckResultFunc check_result_func,
                          ErrorHandler error_handler,
                          std::string interface_name,
                          Args... args) -> decltype(func(args...)) {
  int retry_count = 0;
  while (true) {
    absl::Time start_time = absl::Now();
    auto result = func(args...);
    absl::Duration duration = absl::Now() - start_time;
    std::string error_message = check_result_func(result);
    if (error_message.empty()) {
      LOG(INFO) << strings::Format(
          "ModelServing call success, interface_name:{}, cost_time:{}, "
          "infer_mode:{}, model_id:{}, "
          "model_ser_worker_id_:{}",
          interface_name,
          absl::ToDoubleSeconds(duration),
          static_cast<int>(cuda::GetInferMode(use_triton_runtime_)),
          model_id_,
          model_ser_worker_id_);
      return result;
    }
    LOG(ERROR) << strings::Format(
        "ModelServing call failed, err_msg:{}, interface_name:{}, cost_time:{}, infer_mode:{}, "
        "model_id:{}, model_ser_worker_id_:{}",
        error_message,
        interface_name,
        absl::ToDoubleSeconds(duration),
        static_cast<int>(cuda::GetInferMode(use_triton_runtime_)),
        model_id_,
        model_ser_worker_id_);

    if (++retry_count >= triton::kMaxRetryCount) {
      return result;
    }
    error_handler(retry_count);
  }
}

std::string TritonRuntime::CheckTritonResult(const triton_client::Error& err) {
  if (err.IsOk()) {
    return "";
  }
  return err.Message();
}

void TritonRuntime::SleepErrorHandler(int retry_count) {
  std::this_thread::sleep_for(std::chrono::seconds(static_cast<int>(std::pow(2, retry_count))));
}

triton_client::Error TritonRuntime::CudaSharedMemoryStatus(
    inference::CudaSharedMemoryStatusResponse* status) {
  return Retry([this, &status]() { return triton_client_->CudaSharedMemoryStatus(status); },
               [this](const triton_client::Error& err) { return CheckTritonResult(err); },
               [this](int retry_count) { SleepErrorHandler(retry_count); },
               "CudaSharedMemoryStatus");
}

triton_client::Error TritonRuntime::RegisterCudaSharedMemory(
    const std::string& name,
    const cudaIpcMemHandle_t& cuda_shm_handle,
    const size_t device_id,
    const size_t byte_size) {
  return Retry(
      [this, &name, &cuda_shm_handle, &device_id, &byte_size]() {
        return triton_client_->RegisterCudaSharedMemory(
            name, cuda_shm_handle, device_id, byte_size);
      },
      [this](const triton_client::Error& err) { return CheckTritonResult(err); },
      [this](int retry_count) { SleepErrorHandler(retry_count); },
      "RegisterCudaSharedMemory");
}

void TritonRuntime::PrepareModelServer() {
  LOG(INFO) << "Modelserving start preparing model server: " << engine_file_;

  LoadModel();
  SetModelMetadata();

  InitializeBindingInfo();

  LOG(INFO) << "ModelServing prepare model server success. "
            << "triton_server_addr=" << infer_ser_addr_ << ". "
            << "triton_model_name=" << ser_model_name_ << ". "
            << "triton_model_version=" << ser_model_version_ << ". "
            << "uuid=" << uuid_ << ". ";
}

TritonRuntime::TritonRuntime(const walle::ModelConfig& model_config, cuda::DeviceContext* context)
    : ModelRuntime(model_config.engine_file(), context) {
  model_config_basename_ = model_config.model_name();
  max_batch_size_ = model_config.max_batch_size();
  engine_file_ = model_config.engine_file();
  name_ = engine_file_;
  use_triton_runtime_ = context->use_triton_runtime();
  CHECK_GT(max_batch_size_, 0);
  for (const auto& node : model_config.output_node()) {
    output_names_.push_back(node.node_name());
  }
  base::Environment env;
  std::string gpu_id_str;
  bool succeed = env.GetVar("CUDA_VISIBLE_DEVICES", &gpu_id_str);
  if (!succeed) {
    gpu_id_ = 0;
  } else {
    gpu_id_ = strings::FromString<int>(gpu_id_str);
  }
  LOG(INFO) << strings::Format("gpu id is {} for model {}", gpu_id_, engine_file_);
  model_ser_addr_ = FLAGS_grpc_model_server;
  modelserving_url_ = strings::Format("{}?resource={}", FLAGS_modelserving_api, FLAGS_client_name);
  LoadModelInfo();
  PrepareModelServer();
}

void TritonRuntime::InitializeBindingInfo() {
  LOG(INFO) << "init binding info for " << engine_file_;
  size_t index = 0;
  // Init input bindings.
  for (const auto& item : model_metadata_.inputs()) {
    const std::string& name = item.name();
    nvinfer1::DataType trt_type = Convert2TrtType(item.datatype());
    nvinfer1::Dims trt_dim = Convert2TrtDim(item.shape());
    // -1: dynamic batch size
    if (trt_dim.d[0] == -1) {
      trt_dim.d[0] = max_batch_size_;
    }
    const size_t buffer_size = GetDimSize(trt_dim) * GetTypeSize(trt_type);
    input_binding_infos_[name] = {trt_type, trt_dim, buffer_size, index++, item.datatype()};
  }
  // Init output bindings.
  output_name_list_.clear();
  for (const auto& item : model_metadata_.outputs()) {
    const std::string name = item.name();
    auto it = std::find(output_names_.begin(), output_names_.end(), name);
    if (it == output_names_.end()) {
      // inner node
      continue;
    }
    nvinfer1::DataType trt_type = Convert2TrtType(item.datatype());
    nvinfer1::Dims trt_dim = Convert2TrtDim(item.shape());
    // -1: dynamic batch size
    if (trt_dim.d[0] == -1) {
      trt_dim.d[0] = max_batch_size_;
    }
    const size_t buffer_size = GetDimSize(trt_dim) * GetTypeSize(trt_type);
    output_binding_infos_[name] = {trt_type, trt_dim, buffer_size, index++, item.datatype()};
    output_name_list_.push_back(name);
  }
  UpdateCudaHandle();
}

void TritonRuntime::Enqueue(size_t batch_size, bool use_cuda_graph) {
  absl::Time start_time = absl::Now();
  CHECK_GT(batch_size, 0);
  CHECK_LE(batch_size, max_batch_size_);
  CHECK(!bindings_.empty());
  // Prepare input data.
  std::vector<triton_client::InferInput*> inputs;
  inputs.resize(input_binding_infos_.size());
  for (const auto& [name, item] : input_binding_infos_) {
    std::vector<int64_t> shape(item.dims.d, item.dims.d + item.dims.nbDims);
    triton_client::Error error = triton_client::InferInput::Create(
        &inputs[item.binding_index], name, shape, item.data_type_str);
    CHECK(error.IsOk()) << strings::Format("*** model runtime error: unable to get ", name);
    if (cuda::GetInferMode(use_triton_runtime_) == cuda::InferMode::kRemoteModelServing) {
      void* host_buffer = bindings_[item.binding_index];
      if (use_triton_runtime_) {
        host_buffer = host_bindings_[item.binding_index];
        cuda::DeviceToHost(bindings_[item.binding_index],
                           host_buffer,
                           item.buffer_size,
                           cuda::DeviceContext::kDefault);
      }
      inputs[item.binding_index]->AppendRaw(static_cast<const uint8_t*>(host_buffer),
                                            item.buffer_size / item.dims.d[0] * batch_size);
    } else {
      std::string shm_name = cuda_shm_names_[item.binding_index];
      inputs[item.binding_index]->SetSharedMemory(shm_name, item.buffer_size, 0 /* offset */);
    }
  }

  std::vector<const triton_client::InferRequestedOutput*> outputs;
  for (const auto& [name, item] : output_binding_infos_) {
    triton_client::InferRequestedOutput* output = nullptr;
    triton_client::InferRequestedOutput::Create(&output, name);
    if (cuda::GetInferMode(use_triton_runtime_) != cuda::InferMode::kRemoteModelServing) {
      std::string shm_name = cuda_shm_names_[item.binding_index];
      output->SetSharedMemory(shm_name, item.buffer_size, 0 /* offset */);
    }
    outputs.push_back(output);
  }

  // Start infer.
  triton_client::InferResult* results = nullptr;
  device_context_->Sync();
  triton_client::Error error = Infer(inputs, outputs, &results);
  CHECK(error.IsOk()) << strings::Format("infer error:{} {}", error.Message(), engine_file_);
  std::shared_ptr<triton_client::InferResult> result_ptr;
  result_ptr.reset(results);
  // Get output data.
  if (cuda::GetInferMode(use_triton_runtime_) == cuda::InferMode::kRemoteModelServing) {
    for (const auto& [name, item] : output_binding_infos_) {
      void* data = nullptr;
      size_t size = 0;
      triton_client::Error error = result_ptr->RawData(name, (const uint8_t**)&data, &size);
      CHECK(error.IsOk()) << strings::Format(
          "*** model runtime error: unable to get result data for {}", name);
      CHECK_EQ(size, item.buffer_size);
      if (use_triton_runtime_) {
        std::memcpy(host_bindings_[item.binding_index], data, item.buffer_size);
        cuda::HostToDevice(host_bindings_[item.binding_index],
                           bindings_[item.binding_index],
                           item.buffer_size,
                           cuda::DeviceContext::kDefault);
      } else {
        std::memcpy(bindings_[item.binding_index], data, item.buffer_size);
      }
    }
  }
  absl::Duration duration = absl::Now() - start_time;
  LOG_IF(WARNING, absl::ToDoubleSeconds(duration) > 1) << strings::Format(
      "infer time:{}, too long for {}", absl::ToDoubleSeconds(duration), engine_file_);
}

nvinfer1::DataType TritonRuntime::Convert2TrtType(const std::string& dtype) {
  return FindOrDie(kTrtDataType, dtype);
}

nvinfer1::Dims TritonRuntime::Convert2TrtDim(
    const google::protobuf::RepeatedField<::google::protobuf::int64>& dims) {
  nvinfer1::Dims trt_dim;
  CHECK_LE(dims.size(), nvinfer1::Dims::MAX_DIMS);
  trt_dim.nbDims = dims.size();
  for (int i = 0; i < dims.size(); i++) {
    trt_dim.d[i] = dims.Get(i);
  }
  return trt_dim;
}

void TritonRuntime::UpdateModelServer() {
  if (cuda::GetInferMode(use_triton_runtime_) != cuda::InferMode::kRemoteModelServing) return;
  int retry = 0;
  std::string last_model_ser = model_ser_addr_;
  for (; retry < triton::kMaxRetryCount; retry++) {
    nlohmann::json response;
    base::Status status = walle::common::util::HttpClient::GetJson(
        modelserving_url_, &response, triton::kTimeoutInSecond);
    if (!status.ok()) {
      LOG(ERROR) << strings::Format(
          "update model server error:{} for {} will retry", status.error_message(), engine_file_);
      absl::SleepFor(absl::Seconds(0.1));
      continue;
    }

    if (response["error_code"] != 0) {
      LOG(ERROR) << strings::Format("update model server error for {}, will retry", engine_file_);
      absl::SleepFor(absl::Seconds(0.1));
      continue;
    }

    if (response["msg"].find("model_server") == response["msg"].end()) {
      LOG(ERROR) << "get no model server, will retry";
      absl::SleepFor(absl::Seconds(0.1));
      continue;
    }
    model_ser_addr_ = response["msg"]["model_server"].get<std::string>();
    LOG(INFO) << strings::Format(
        "newest grpc model server: {} for {}", model_ser_addr_, engine_file_);
    break;
  }
  CHECK(retry < kMaxRetryCount) << "can't update model server url.";
  // if model ser addr not update, sleep some time to let it resume normal.
  if (last_model_ser != "" && last_model_ser == model_ser_addr_) {
    absl::SleepFor(absl::Seconds(5.0));
  }
}

void TritonRuntime::LoadModelInfo() {
  model_id_ = kUnknowInfo;
  plugin_id_ = kUnknowInfo;
  std::ifstream ifs(kModelInfoFile);
  CHECK(ifs && ifs.is_open()) << "load model info file error: " << kModelInfoFile;
  Json model_info;
  ifs >> model_info;
  plugin_id_ = model_info[kModelInfoPluginKey].get<std::string>();
  std::vector<Json> engine_infos = model_info[kModelEngineInfoListKey];
  for (auto& engine_info : engine_infos) {
    std::string engine_path = engine_info[0].get<std::string>();
    bool has_plugin = engine_info[1];
    std::string model_config_basename = file_path::BaseName(engine_info[2].get<std::string>());
    std::string model_id = engine_info[3].get<std::string>();
    if ((model_config_basename_ == model_config_basename || model_config_basename_ == "") &&
        engine_file_ == engine_path) {
      model_id_ = model_id;
      if (!has_plugin) {
        plugin_id_ = "";
      }
      break;
    }
  }
  CHECK_NE(model_id_, kUnknowInfo) << "load model_id error from " << kModelInfoFile;
  CHECK_NE(model_id_, "") << "load model_id error from " << kModelInfoFile;
  CHECK_NE(plugin_id_, kUnknowInfo) << "load plugin_id_ error from " << kModelInfoFile;
  LOG(INFO) << strings::Format(
      "LoadModelInfo {} model_id_ {} plugin_id_ {}", engine_file_, model_id_, plugin_id_);
}

void TritonRuntime::LoadModel() {
  LOG(INFO) << "Modelserving load model: " << engine_file_;
  if (model_ser_addr_ == "") {
    UpdateModelServer();
  }
  LoadModelRequest load_model_request;
  CHECK_NE(model_id_, kUnknowInfo) << "model_id is not set";
  CHECK_NE(model_id_, "") << "model_id is not set";
  LOG(INFO) << "model_id is: " << model_id_;
  load_model_request.set_model_name(model_id_);
  load_model_request.set_model_version("0");
  load_model_request.set_repo_key(repo_key_);
  load_model_request.set_uuid(uuid_);
  load_model_request.set_hop_count(0);
  load_model_request.set_infer_mode(InferMode(cuda::GetInferMode(use_triton_runtime_)));
  load_model_request.set_plugin_id(plugin_id_);

  LoadModelResponse load_model_response;

  auto LoadModelFunc = [this, &load_model_request, &load_model_response]() {
    grpc::ClientContext context;
    auto deadline = std::chrono::system_clock::now() + std::chrono::seconds(kGrpcTimeoutSecond);
    context.set_deadline(deadline);
    grpc::ChannelArguments chan_args;
    chan_args.SetInt(GRPC_ARG_KEEPALIVE_TIME_MS, kKeepaliveTimeMs);
    std::shared_ptr<grpc::Channel> chan =
        grpc::CreateCustomChannel(model_ser_addr_, grpc::InsecureChannelCredentials(), chan_args);
    std::unique_ptr<ModelServer::Stub> stub = ModelServer::NewStub(chan);
    return stub->load_model(&context, load_model_request, &load_model_response);
  };

  auto LoadModelCheckResult = [&load_model_response](const grpc::Status& status) {
    if (!status.ok()) {
      return status.error_message();
    }
    return load_model_response.err_msg();
  };

  auto UpdateServerErrorHandler = [this](int retry_count) {
    if (cuda::GetInferMode(use_triton_runtime_) == cuda::InferMode::kRemoteModelServing) {
      UpdateModelServer();
    }
    if (cuda::GetInferMode(use_triton_runtime_) == cuda::InferMode::kLocalModelServing) {
      SleepErrorHandler(retry_count);
    }
  };

  grpc::Status grpc_status =
      Retry(LoadModelFunc, LoadModelCheckResult, UpdateServerErrorHandler, "LoadModel");

  CHECK(grpc_status.ok() && load_model_response.err_msg() == "")
      << strings::Format("load model failed uuid {} for {}", uuid_, engine_file_);

  ser_model_name_ = load_model_response.ser_model_name();
  ser_model_version_ = load_model_response.ser_model_version();
  infer_ser_addr_ = load_model_response.infer_ser();
  model_ser_addr_ = load_model_response.model_ser();
  model_ser_worker_id_ = load_model_response.model_ser_worker_id();
  uuid_ = load_model_response.uuid();
  options_ = std::make_unique<triton_client::InferOptions>(ser_model_name_);
  options_->model_version_ = ser_model_version_;
  options_->client_timeout_ = client_timeout_;
}

void TritonRuntime::SetModelMetadata() {
  auto set_model_metadata_func = [this]() {
    triton_client::KeepAliveOptions keep_alive_options;
    keep_alive_options.keepalive_time_ms = kKeepaliveTimeMs;
    triton_client::InferenceServerGrpcClient::Create(&triton_client_,
                                                     infer_ser_addr_,
                                                     false,
                                                     false,
                                                     triton_client::SslOptions(),
                                                     keep_alive_options);
    return triton_client_->ModelMetadata(&model_metadata_, ser_model_name_, ser_model_version_);
  };

  auto update_model_error_handler = [this](int retry_count) {
    if (cuda::GetInferMode(use_triton_runtime_) == cuda::InferMode::kRemoteModelServing) {
      UpdateModelServer();
    }
    if (cuda::GetInferMode(use_triton_runtime_) == cuda::InferMode::kLocalModelServing) {
      SleepErrorHandler(retry_count);
    }
    LoadModel();
  };

  triton_client::Error err = Retry(
      set_model_metadata_func,
      [this](const triton_client::Error& err) { return CheckTritonResult(err); },
      update_model_error_handler,
      "ModelMetadata");

  CHECK(err.IsOk()) << strings::Format(
      "SetModelMetadata error uuid {} for {}", uuid_, engine_file_);
}

void TritonRuntime::UpdateCudaHandle() {
  if (cuda::GetInferMode(use_triton_runtime_) != cuda::InferMode::kLocalModelServing) {
    return;
  }
  if (cuda_shm_handles_.size() == 0) {
    return;
  }
  CHECK_EQ(cuda_shm_handles_.size(), cuda_shm_names_.size());
  CHECK_EQ(cuda_shm_handles_.size(), input_binding_infos_.size() + output_binding_infos_.size());
  inference::CudaSharedMemoryStatusResponse shm_status;
  triton_client::Error error = CudaSharedMemoryStatus(&shm_status);
  if (!error.IsOk()) {
    return;
  }
  const auto& shm_map = shm_status.regions();
  for (const auto& [name, item] : input_binding_infos_) {
    CHECK(!cuda::IsCpuMode(bindings_[item.binding_index]));
    const std::string& shm_name = cuda_shm_names_[item.binding_index];
    if (shm_map.find(shm_name) != shm_map.end()) {
      continue;
    }
    LOG(INFO) << "Reregist " << shm_name;
    triton_client::Error error = RegisterCudaSharedMemory(
        shm_name, cuda_shm_handles_[item.binding_index], gpu_id_, item.buffer_size);
  }
  for (const auto& [name, item] : output_binding_infos_) {
    CHECK(!cuda::IsCpuMode(bindings_[item.binding_index]));
    const std::string& shm_name = cuda_shm_names_[item.binding_index];
    if (shm_map.find(shm_name) != shm_map.end()) {
      continue;
    }
    LOG(INFO) << "Reregist " << shm_name;
    triton_client::Error error = RegisterCudaSharedMemory(
        shm_name, cuda_shm_handles_[item.binding_index], gpu_id_, item.buffer_size);
  }
}

void TritonRuntime::AssignBindings(const std::vector<void*>& bindings,
                                   const std::vector<void*>& host_bindings) {
  CHECK_EQ(bindings.size(), input_binding_infos_.size() + output_binding_infos_.size());
  bindings_ = bindings;
  host_bindings_ = host_bindings;
  cuda_shm_handles_.resize(bindings.size());
  cuda_shm_names_.resize(bindings.size());
  if (cuda::GetInferMode(use_triton_runtime_) == cuda::InferMode::kLocalModelServing) {
    std::string pid = strings::ToString(getpid());
    std::stringstream ss;
    ss << &uuid_;
    std::string uuid = ss.str();
    for (const auto& [name, item] : input_binding_infos_) {
      CHECK(!cuda::IsCpuMode(bindings_[item.binding_index]));
      CUDA_CHECK(cudaIpcGetMemHandle(&cuda_shm_handles_[item.binding_index],
                                     bindings_[item.binding_index]));
      std::string shm_name = strings::Format("in_{}_{}_{}_{}", model_id_, name, uuid, pid);
      triton_client::Error error = RegisterCudaSharedMemory(
          shm_name, cuda_shm_handles_[item.binding_index], gpu_id_, item.buffer_size);
      cuda_shm_names_[item.binding_index] = shm_name;
      CHECK(error.IsOk()) << strings::Format(
          "RegisterCudaSharedMemory error:{} {}", error.Message(), engine_file_);
      LOG(INFO) << strings::Format(
          "RegisterCudaSharedMemory succeed:{} {}", shm_name, engine_file_);
    }
    for (const auto& [name, item] : output_binding_infos_) {
      CHECK(!cuda::IsCpuMode(bindings_[item.binding_index]));
      CUDA_CHECK(cudaIpcGetMemHandle(&cuda_shm_handles_[item.binding_index],
                                     bindings_[item.binding_index]));
      std::string shm_name = strings::Format("out_{}_{}_{}_{}", model_id_, name, uuid, pid);
      triton_client::Error error = RegisterCudaSharedMemory(
          shm_name, cuda_shm_handles_[item.binding_index], gpu_id_, item.buffer_size);
      cuda_shm_names_[item.binding_index] = shm_name;
      CHECK(error.IsOk()) << strings::Format(
          "RegisterCudaSharedMemory error:{} {}", error.Message(), engine_file_);
      LOG(INFO) << strings::Format(
          "RegisterCudaSharedMemory succeed:{} {}", shm_name, engine_file_);
    }
  }
}

triton_client::Error TritonRuntime::Infer(
    const std::vector<triton_client::InferInput*>& inputs,
    const std::vector<const triton_client::InferRequestedOutput*>& outputs,
    triton_client::InferResult** results) {
  auto ReloadModelErrorHandler = [this](int retry_count) {
    if (cuda::GetInferMode(use_triton_runtime_) == cuda::InferMode::kRemoteModelServing) {
      UpdateModelServer();
    }
    if (cuda::GetInferMode(use_triton_runtime_) == cuda::InferMode::kLocalModelServing) {
      SleepErrorHandler(retry_count);
    }
    PrepareModelServer();
  };
  return Retry(
      [this, &inputs, &outputs, &results]() {
        return triton_client_->Infer(
            results, *options_, inputs, outputs, http_headers_, compression_algorithm);
      },
      [this](const triton_client::Error& err) { return CheckTritonResult(err); },
      ReloadModelErrorHandler,
      "Infer");
}

TritonRuntime::~TritonRuntime() {
  if (cuda::GetInferMode(use_triton_runtime_) == cuda::InferMode::kLocalModelServing) {
    for (const auto& [name, item] : input_binding_infos_) {
      triton_client::Error error =
          triton_client_->UnregisterCudaSharedMemory(cuda_shm_names_[item.binding_index]);
    }
    for (const auto& [name, item] : output_binding_infos_) {
      triton_client::Error error =
          triton_client_->UnregisterCudaSharedMemory(cuda_shm_names_[item.binding_index]);
    }
  }
}

}  // namespace triton
}  // namespace base
