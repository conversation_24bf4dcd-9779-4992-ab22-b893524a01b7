// Copyright @2025 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/cuda/vlm_inference_engine.h"

namespace base {

VlmInferenceEngine::VlmInferenceEngine(const walle::ModelConfig& model_config,
                                       bool use_model_serving,
                                       cuda::DeviceContext* context)
    : TrtInferenceEngine(model_config, context, use_model_serving) {
  CHECK(use_model_serving);  // currently can only be constructed while use_model_serving == true
  InitializeStringInputOutputMap(model_config);
}

void VlmInferenceEngine::InitializeStringInputOutputMap(const walle::ModelConfig& model_config) {
  for (const walle::StringModelNode& input_node : model_config.string_input_node()) {
    string_input_map_[input_node.node_name()] = std::vector<std::string>();
  }
  for (const walle::StringModelNode& output_node : model_config.string_output_node()) {
    string_output_map_[output_node.node_name()] = std::vector<std::string>();
  }
  ModelRuntime* model_runtime_ptr = CHECK_NOTNULL(GetMutableModelPtr());
  ModelServingRuntime* model_serving_runtime_ptr =
      CHECK_NOTNULL(dynamic_cast<ModelServingRuntime*>(model_runtime_ptr));
  model_serving_runtime_ptr->AssignStringInputOutputPtrs(&string_input_map_, &string_output_map_);
}

}  // namespace base
