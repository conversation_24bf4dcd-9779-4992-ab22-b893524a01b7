// Copyright @2023 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/cuda/flags.h"

DEFINE_string(grpc_model_server, "", "model serving url");
DEFINE_string(modelserving_api,
              "https://simcluster.sankuai.com/api/v1/get_model_server",
              "model serving api");
DEFINE_string(client_name, "client_prod", "cloud client name");
DEFINE_int32(infer_mode, 0, "0: native gpu; 1: local modelserving; 2: remote modelserving");

DEFINE_bool(enable_generate_trt_engine, true,
            "Whether enable generate trt engine online");
DEFINE_int32(trt_log_level,
             1,
             "0 = INTERNAL_ERROR, "
             "1 = ERROR, "
             "2 = WARNING: Synchronous on Engine level, "
             "3 = INFO, "
             "4 = VERBOSE: Synchronous on Layer level");
DEFINE_bool(trt_inference_by_max_batch_size, false, "Inference by max batch size.");
DEFINE_bool(use_trt_mock_engine, false, "use mocked data");
DEFINE_bool(check_gpu_tensor_validity, false, "Always check Infinity and NaN in GPU tensors.");
