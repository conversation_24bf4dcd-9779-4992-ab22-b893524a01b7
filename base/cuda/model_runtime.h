// Copyright @2022 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <iostream>
#include <memory>
#include <string>
#include <unordered_map>
#include <vector>

#include "NvInfer.h"
#include "glog/logging.h"

#include "base/common/macros.h"
#include "base/container/utils.h"
#include "base/cuda/device_buffer.h"
#include "base/strings/format.h"

namespace base {

size_t GetDimSize(const nvinfer1::Dims& dims);
size_t GetTypeSize(nvinfer1::DataType t);

class ModelRuntime {
 public:
  struct BindingInfo {
    nvinfer1::DataType data_type;
    nvinfer1::Dims dims;
    size_t buffer_size = 0;
    size_t binding_index = 0;
    std::string data_type_str;
    bool static_batch_size = false;
  };

  ModelRuntime(const std::string& engine_file, cuda::DeviceContext* context);
  virtual ~ModelRuntime() = default;

  void UpdateDeviceContext(cuda::DeviceContext* context);

  const std::string& name() const { return name_; }

  const std::vector<std::string>& outputs_name_list() const { return output_name_list_; }

  const std::unordered_map<std::string, BindingInfo>& input_binding_infos() const {
    return input_binding_infos_;
  }

  const std::unordered_map<std::string, BindingInfo>& output_binding_infos() const {
    return output_binding_infos_;
  }

  virtual void AssignBindings(const std::vector<void*>& bindings) {
    CHECK_EQ(bindings.size(), input_binding_infos_.size() + output_binding_infos_.size());
    bindings_ = bindings;
  }

  virtual void AssignBindings(const std::vector<void*>& bindings,
                              const std::vector<void*>& host_bindings) {
    CHECK_EQ(bindings.size(), input_binding_infos_.size() + output_binding_infos_.size());
    bindings_ = bindings;
    CHECK_EQ(host_bindings.size(), input_binding_infos_.size() + output_binding_infos_.size());
    host_bindings_ = host_bindings;
  }

  virtual void Enqueue(size_t batch_size, bool use_cuda_graph) = 0;
  virtual void InitCudaGraph() {}
  int max_batch_size() const { return max_batch_size_; }

 protected:
  cuda::DeviceContext* device_context_ = nullptr;  // Not owned.
  std::string engine_file_;
  std::vector<void*> bindings_;
  std::vector<void*> host_bindings_;
  int max_batch_size_ = 0;
  std::unordered_map<std::string, BindingInfo> input_binding_infos_;
  std::unordered_map<std::string, BindingInfo> output_binding_infos_;
  std::string name_;
  std::vector<std::string> output_name_list_;

 private:
  DISALLOW_COPY_AND_ASSIGN(ModelRuntime);
};

}  // namespace base
