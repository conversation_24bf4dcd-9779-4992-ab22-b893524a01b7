// Copyright @2023 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include <memory>
#include <string>
#include <unordered_map>

#include "base/container/utils.h"
#include "base/cuda/device_context_manager.h"

namespace cuda {
namespace {

constexpr int kDefaultPriority = 0;
constexpr int kMaxDeviceContextSize = 30;

const std::unordered_map<std::string, int> kModulePriorityMap = {
    {"Perception", -4},
    {"PerceptionCamera", -1},
    {"PerceptionLidar", -3},
    {"Prediction", -3},
    {"TrafficLight", -2},
    {"Planning", -4},
    {"MappingBev", -2},
    {"Maeb", -1},
};

const std::unordered_map<std::string, int> kUnifiedScheduleModulePriorityMap = {
    {"Perception", -4},
    {"TrafficLight", -3},
    {"PerceptionCamera", -1},
    {"PerceptionLidar", -1},
};

}  // namespace

DeviceContextManager::DeviceContextManager(const std::string& module_name,
                                           int gpu_id,
                                           walle::EuScheduleType eu_schedule_type) {
  priority_ = base::FindWithDefault(kModulePriorityMap, module_name, kDefaultPriority);
  if (eu_schedule_type == walle::EuScheduleType::UNIFIED_SCHEDULE_EXECUTE) {
    priority_ = base::FindWithDefault(kUnifiedScheduleModulePriorityMap, module_name, priority_);
  }
  gpu_id_ = gpu_id;
}

DeviceContextManager::DeviceContextManager(const std::string& module_name,
                                           int gpu_id,
                                           int num_context) {
  CHECK_GT(num_context, 0);
  CHECK_LE(num_context, kMaxDeviceContextSize);
  priority_ = base::FindWithDefault(kModulePriorityMap, module_name, kDefaultPriority);
  gpu_id_ = gpu_id;
  for (int index = 0; index < num_context; ++index) {
    device_context_group_.emplace_back(std::make_unique<cuda::DeviceContext>(gpu_id_, priority_));
  }
}

cuda::DeviceContext* DeviceContextManager::CreateDeviceContext(bool use_triton_runtime) {
  return CreateDeviceContextWithPriority(priority_, use_triton_runtime);
}

cuda::DeviceContext* DeviceContextManager::CreateDeviceContextWithPriority(int priority, bool use_triton_runtime) {
  base::MutexLock lock(&mutex_);
  CHECK_LE(device_context_group_.size(), kMaxDeviceContextSize);
  device_context_group_.emplace_back(std::make_unique<cuda::DeviceContext>(gpu_id_, priority_, use_triton_runtime));
  return device_context_group_.back().get();
}

cuda::DeviceContext* DeviceContextManager::GetDeviceContext() {
  base::MutexLock lock(&mutex_);
  const int group_size = static_cast<int>(device_context_group_.size());
  CHECK_LT(index_, group_size);
  cuda::DeviceContext* device_context = device_context_group_[index_].get();
  if (++index_ == group_size) {
    index_ = 0;
  }
  return device_context;
}

cuda::DeviceContext* DeviceContextManager::GetDeviceContextByIndex(int index) {
  CHECK_GE(index, 0);
  CHECK_LT(index, device_context_group_.size());
  return device_context_group_[index].get();
}

}  // namespace cuda
