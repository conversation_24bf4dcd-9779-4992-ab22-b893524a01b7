// Copyright @2023 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <vector>

#include "base/cuda/device_utils.h"
#include "base/synchronization/mutex.h"
#include "common/execution_unit_framework/common/eu_schedule_type.h"

namespace cuda {

class DeviceContextManager {
 public:
  DeviceContextManager(
      const std::string& module_name,
      int gpu_id,
      walle::EuScheduleType eu_schedule_type = walle::EuScheduleType::DEFAULT_SCHEDULE);
  DeviceContextManager(const std::string& module_name, int gpu_id, int num_context);
  virtual ~DeviceContextManager() = default;

  cuda::DeviceContext* CreateDeviceContext(bool use_triton_runtime = false);
  cuda::DeviceContext* CreateDeviceContextWithPriority(int priority, bool use_triton_runtime = false);

  cuda::DeviceContext* GetDeviceContext();

  cuda::DeviceContext* GetDeviceContextByIndex(int index);

  int GetPriority() const { return priority_; }

 private:
  int index_ = 0;
  int gpu_id_ = 0;
  int priority_ = 0;
  base::Mutex mutex_;
  std::vector<std::unique_ptr<cuda::DeviceContext>> device_context_group_;

  DISALLOW_COPY_AND_ASSIGN(DeviceContextManager);
};

}  // namespace cuda
