// Copyright @2023 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>
// Design Doc at: https://km.sankuai.com/collabpage/1612494744

#include "base/cuda/device_memory_recorder.h"

#include "glog/logging.h"

#include "base/strings/format.h"

namespace base {

#if defined(USE_CUDA) && defined(__x86_64__)
DeviceMemoryRecorder::DeviceMemoryRecorder() {
  nvmlReturn_t result = nvmlInit_v2();
  if (result != NVML_SUCCESS) {
    LOG(WARNING) << strings::Format("Failed to init nvml, error: {}.", nvmlErrorString(result));
    init_failure_ = true;
    return;
  }
  NVML_CHECK(nvmlDeviceGetHandleByIndex(0, &device_));
  // The actual device memory usage which contains CUDA overhead is more than the size of device
  // buffer, so we request a dummy device buf at first to avoid the mismatch. Refer to:
  // https://forums.developer.nvidia.com/t/cudamallocmanaged-allocating-more-memory-than-requested/63122/5
  dummy_dev_buf_ = std::make_unique<cuda::DeviceBuffer>(1);
  last_device_memory_used_mb_ = GetDeviceMemoryUsedMB();
}

DeviceMemoryRecorder::~DeviceMemoryRecorder() {
  if (init_failure_) {
    return;
  }
  NVML_CHECK(nvmlShutdown());
}

void DeviceMemoryRecorder::Update(const std::string& name, bool print_out) {
  if (init_failure_) {
    return;
  }
  const double device_memory_used_mb = GetDeviceMemoryUsedMB();
  const double unit_device_memory_usage_mb = device_memory_used_mb - last_device_memory_used_mb_;
  device_memory_usage_map_[name] = unit_device_memory_usage_mb;
  last_device_memory_used_mb_ = device_memory_used_mb;
  if (print_out) {
    LOG(INFO) << strings::Format(
        "Device memory usage diff of {} is {} MB, current used device memory is {} MB.",
        name,
        unit_device_memory_usage_mb,
        device_memory_used_mb);
  }
}

double DeviceMemoryRecorder::GetDeviceMemoryUsedMB() {
  if (init_failure_) {
    return 0.0;
  }
  nvmlMemory_t device_memory;
  NVML_CHECK(nvmlDeviceGetMemoryInfo(device_, &device_memory));
  return static_cast<double>(device_memory.used) / (1024 * 1024);
}
#else
DeviceMemoryRecorder::DeviceMemoryRecorder() {
  LOG(WARNING) << "No cuda, DeviceMemoryRecorder is not implemented.";
}

DeviceMemoryRecorder::~DeviceMemoryRecorder() {}

void DeviceMemoryRecorder::Update(const std::string& name, bool print_out) {}

double DeviceMemoryRecorder::GetDeviceMemoryUsedMB() { return 0.0; }
#endif
}  // namespace base
