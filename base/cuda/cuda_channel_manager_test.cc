// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "gtest/gtest.h"

#include "base/cuda/cuda_channel_manager.h"

namespace base {
namespace {
constexpr size_t kBufferSize = 100;
}  // namespace

TEST(CudaChannelManagerTest, BasicTest) {
  CudaChannelManager cuda_channel_manager;
  const std::string camera_driver_name = "CameraDriver";
  EXPECT_FALSE(cuda_channel_manager.HasCudaChannel(camera_driver_name));
  EXPECT_TRUE(cuda_channel_manager.AddCudaChannel(camera_driver_name, kBufferSize));
  EXPECT_TRUE(cuda_channel_manager.HasCudaChannel(camera_driver_name));
  EXPECT_FALSE(cuda_channel_manager.AddCudaChannel(camera_driver_name, kBufferSize));
  EXPECT_FALSE(cuda_channel_manager.HasCudaChannel("Lidar"));
  EXPECT_TRUE(cuda_channel_manager.FindMutableCudaChannel(camera_driver_name) != nullptr);
  CudaChannelManager::CudaChannel* cuda_channel =
      cuda_channel_manager.FindMutableCudaChannel(camera_driver_name);
  EXPECT_EQ(kBufferSize, cuda_channel->device_buffer->size());
}

}  // namespace base
