// Copyright @2023 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include <algorithm>
#include <memory>
#include <numeric>
#include <utility>
#include <vector>

#include "gtest/gtest.h"

#include "base/cuda/device_buffer.h"
#include "base/cuda/host_buffer.h"
#include "base/cuda/mirror_buffer.h"

namespace cuda {
namespace {

constexpr int kNumBytes = 100;
constexpr char kNumber = 0x5A; //0x01011010

TEST(MirrorBufferTest, DeviceMirrorTest) {
  DeviceBuffer dev_buf_src(kNumBytes);
  MirrorBuffer mirror_buf_dst(kNumBytes);
  DeviceContext ctx;

  dev_buf_src.Memset(kNumber, &ctx);
  cuda::DeviceBuffer* device_buf_ptr = mirror_buf_dst.GetDeviceBuffer();
  device_buf_ptr->FromGpu(dev_buf_src.data(), kNumBytes, &ctx);
  mirror_buf_dst.DeviceToHost(&ctx);
  ctx.Sync();

  const char* begin = reinterpret_cast<const char*>(mirror_buf_dst.host_data());
  for (int i = 0; i < kNumBytes; i++) {
    EXPECT_EQ(kNumber, begin[i]);
  }
}

TEST(MirrorBufferTest, MirrorDeviceHostTest) {
  MirrorBuffer mirror_buf_src(kNumBytes);
  DeviceBuffer dev_buf_dst(kNumBytes);
  HostBuffer host_buf_dst(kNumBytes);
  DeviceContext ctx;

  char* mirror_begin = reinterpret_cast<char*>(mirror_buf_src.mutable_host_data());
  for (int i = 0; i < kNumBytes; i++) {
    mirror_begin[i] = kNumber;
  }
  mirror_buf_src.HostToDevice(&ctx);
  dev_buf_dst.FromGpu(mirror_buf_src.device_data(), kNumBytes, &ctx);
  dev_buf_dst.ToCpu(host_buf_dst.mutable_data(), kNumBytes, &ctx);
  ctx.Sync();

  const char* begin = reinterpret_cast<const char*>(host_buf_dst.data());
  for (int i = 0; i < kNumBytes; i++) {
    EXPECT_EQ(kNumber, begin[i]);
  }
}

}  // namespace
}  // namespace cuda
