// Copyright @2023 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/cuda/device_memory_recorder.h"

#include <iostream>
#include <string>
#include <tuple>

#include "glog/logging.h"
#include "gtest/gtest.h"

#if defined(USE_CUDA) && defined(__x86_64__)
#include "base/cuda/device_buffer.h"
#endif
#include "base/strings/format.h"

namespace base {
namespace {
constexpr char kUnitNameSuffix[] = "MockUnit-{}";
constexpr char kPostUnitNameSuffix[] = "MockPostUnit-{}";

#if defined(USE_CUDA) && defined(__x86_64__)
constexpr char kScheduleName[] = "MockSchedule";
constexpr char kStageName[] = "MockStage";
constexpr char kStackName[] = "MockStack";
constexpr int kMegaBytes = 1024 * 1024;
#endif
}  // namespace

TEST(DeviceMemoryRecorderTest, BasicTest) {
  DeviceMemoryRecorder recorder;
  const std::string target_name = "target";
#if defined(USE_CUDA) && defined(__x86_64__)
  cuda::DeviceBuffer dev_buf(10 * kMegaBytes);
  recorder.Update(target_name, true);
  EXPECT_EQ(recorder.Get(target_name), 10);
  cuda::DeviceBuffer dev_buf2(20 * kMegaBytes);
  recorder.Update(target_name, true);
  EXPECT_EQ(recorder.Get(target_name), 20);
#else
  recorder.Update(target_name);
#endif
}

class DeviceMemoryRecorderMultiParametersTests
    : public ::testing::TestWithParam<std::tuple<double, int>> {
 protected:
  DeviceMemoryRecorder recorder;
};

TEST_P(DeviceMemoryRecorderMultiParametersTests, TestDeviceMemoryRecorder) {
  const int case_idx = std::get<1>(GetParam());
  const std::string unit_name = strings::Format(kUnitNameSuffix, case_idx);
  const std::string post_unit_name = strings::Format(kPostUnitNameSuffix, case_idx);

#if defined(USE_CUDA) && defined(__x86_64__)
  const double target_usage = std::get<0>(GetParam());
  cuda::DeviceBuffer dev_buf(target_usage * kMegaBytes);
  recorder.Update(unit_name);
  recorder.IncreaseParentMemoryUsage(kStackName, unit_name);
  cuda::DeviceBuffer dev_buf2(target_usage * kMegaBytes);
  recorder.Update(post_unit_name);
  recorder.IncreaseParentMemoryUsage(kStackName, post_unit_name);
  recorder.IncreaseParentMemoryUsage(kStageName, kStackName);
  recorder.IncreaseParentMemoryUsage(kScheduleName, kStageName);
  EXPECT_EQ(target_usage * 2, recorder.Get(kScheduleName));
  EXPECT_EQ(target_usage * 2, recorder.Get(kStageName));
  EXPECT_EQ(target_usage * 2, recorder.Get(kStackName));
  EXPECT_EQ(target_usage, recorder.Get(unit_name));
#else
  recorder.Update(unit_name);
  recorder.Update(post_unit_name);
#endif
}

INSTANTIATE_TEST_CASE_P(DeviceMemoryRecorderUnitTests,
                        DeviceMemoryRecorderMultiParametersTests,
                        ::testing::Values(std::make_tuple(10.0, 1),
                                          std::make_tuple(100.0, 2),
                                          std::make_tuple(200.0, 3),
                                          std::make_tuple(1024.0, 4)));

}  // namespace base
