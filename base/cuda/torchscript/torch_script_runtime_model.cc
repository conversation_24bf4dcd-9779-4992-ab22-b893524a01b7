// Copyright @2025 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/cuda/torchscript/torch_script_runtime_model.h"

#include <dlfcn.h>
#include <torch/cuda.h>
#include <torch/script.h>
#include <filesystem>

#include "base/common/environment.h"
#include "base/cuda/device_utils.h"
#include "base/cuda/tensorrt/trt_utils.h"
#include "base/file/file_path_util.h"
namespace base {

namespace {
constexpr char kBazelBinPath[] = "bazel-bin";
constexpr std::array<const char*, 2> kTorchScriptOpLibs = {
    "modules/perception/onboard/common/custom_ops/torch_ops/lidar_detection/"
    "liblidar_detection_ops.so",
    "modules/perception/onboard/common/custom_ops/torch_ops/sparseconv/libspconv_v2_ops.so",
};
}  // namespace

torch::ScalarType ConvertToTorchDtype(nvinfer1::DataType data_type) {
  switch (data_type) {
    case nvinfer1::DataType::kFLOAT:
      return torch::kFloat32;
    case nvinfer1::DataType::kHALF:
      return torch::kFloat16;
    case nvinfer1::DataType::kINT32:
      return torch::kInt32;
    case nvinfer1::DataType::kINT8:
      return torch::kInt8;
    case nvinfer1::DataType::kBOOL:
      return torch::kBool;
    default:
      LOG(WARNING) << "Unsupported data type, defaulting to float32";
      return torch::kFloat32;
  }
}

TorchScriptRuntimeModel::TorchScriptRuntimeModel(const walle::ModelConfig& model_config,
                                                 cuda::DeviceContext* context)
    : ModelRuntime(model_config.engine_file(), context), model_config_(model_config) {
  model_file_ = model_config.weight_file();
  LOG(ERROR) << "Loading TorchScript: " << model_file_;
  // Load op libs for torchscript model.
  const std::string base_dir = file_path::Exists(kBazelBinPath) ? kBazelBinPath : "";
  for (const auto& op_lib : kTorchScriptOpLibs) {
    const std::string lib_path = file_path::Join(base_dir, op_lib);
    void* handle = dlopen(lib_path.c_str(), RTLD_NOW | RTLD_LOCAL | RTLD_DEEPBIND);
    CHECK(handle) << "dlopen fail: " << lib_path << " dlerror: " << dlerror();
  }
  // Load torchscript model.
  module_ = std::make_unique<torch::jit::script::Module>(torch::jit::load(model_file_));
  LOG(ERROR) << "Successfully load TorchScript: " << model_file_;
  module_->eval();
  InitializeBindingInfo();
  GetInputTensorShape();
}

TorchScriptRuntimeModel::~TorchScriptRuntimeModel() { LOG(ERROR) << model_file_ << " destroyed."; }

void TorchScriptRuntimeModel::Enqueue(size_t batch_size, bool use_cuda_graph) {
  torch::NoGradGuard no_grad;
  c10::Dict<std::string, torch::Tensor> input_dict;
  for (const auto& entry : input_binding_infos_) {
    const std::string& tensor_name = entry.first;
    const BindingInfo& input_binding_info = entry.second;
    void* data_ptr = bindings_[input_binding_info.binding_index];
    CHECK(data_ptr) << "Input binding data pointer is null for " << entry.first;
    torch::Tensor input_tensor = torch::from_blob(
        data_ptr,
        input_tensor_shape_[tensor_name],
        torch::dtype(ConvertToTorchDtype(input_binding_info.data_type)).device(torch::kCUDA));
    input_dict.insert(tensor_name, input_tensor);
  }
  std::vector<torch::jit::IValue> inputs{input_dict};
  torch::jit::IValue outputs = module_->forward(inputs);
  if (outputs.isGenericDict()) {
    auto output_dict = outputs.toGenericDict();
    torch::cuda::synchronize();
    for (const auto& entry : output_binding_infos_) {
      const std::string& tensor_name = entry.first;
      const BindingInfo& binding_info = entry.second;
      if (output_dict.contains(tensor_name)) {
        auto output_tensor = output_dict.at(tensor_name).toTensor();
        void* output_ptr = bindings_[binding_info.binding_index];
        cuda::DeviceToDevice(output_tensor.data_ptr(),
                             output_ptr,
                             output_tensor.numel() * output_tensor.element_size(),
                             device_context_);
      } else {
        LOG(ERROR) << "Output tensor not found: " << tensor_name;
      }
    }
  } else {
    LOG(ERROR) << "Expected dictionary output but got different type.";
  }
}

void TorchScriptRuntimeModel::InitializeBindingInfo() {
  input_binding_infos_.clear();
  output_binding_infos_.clear();
  const nvinfer1::DataType model_data_type =
      trt::ConvertToTensorRTDataType(model_config_.data_type());
  size_t binding_idx = 0;
  for (const auto& input_node_config : model_config_.input_node()) {
    const std::string& node_name = input_node_config.node_name();
    const walle::IntArray& node_shape = input_node_config.node_shape();
    nvinfer1::Dims dims;
    dims.nbDims = node_shape.dim_size();
    for (int j = 0; j < node_shape.dim_size(); ++j) {
      dims.d[j] = static_cast<int32_t>(node_shape.dim(j));
    }
    const nvinfer1::DataType data_type =
        (input_node_config.has_precision()
             ? trt::ConvertToTensorRTDataType(input_node_config.precision())
             : model_data_type);
    const size_t buffer_size = GetDimSize(dims) * GetTypeSize(data_type);
    input_binding_infos_[node_name] = {data_type, dims, buffer_size, binding_idx};
    binding_idx++;
  }
  for (const auto& output_node_config : model_config_.output_node()) {
    const std::string& node_name = output_node_config.node_name();
    output_name_list_.push_back(node_name);
    const walle::IntArray& node_shape = output_node_config.node_shape();
    nvinfer1::Dims dims;
    dims.nbDims = node_shape.dim_size();
    for (int j = 0; j < node_shape.dim_size(); ++j) {
      dims.d[j] = static_cast<int32_t>(node_shape.dim(j));
    }
    const nvinfer1::DataType data_type =
        output_node_config.has_precision()
            ? trt::ConvertToTensorRTDataType(output_node_config.precision())
            : model_data_type;
    const size_t buffer_size = GetDimSize(dims) * GetTypeSize(data_type);
    output_binding_infos_[node_name] = {data_type, dims, buffer_size, binding_idx};
    binding_idx++;
  }
}

void TorchScriptRuntimeModel::GetInputTensorShape(int batch_size) {
  for (const auto& entry : input_binding_infos_) {
    const std::string& tensor_name = entry.first;
    const BindingInfo& input_binding_info = entry.second;
    std::vector<int64_t> tensor_shape;
    for (int i = 0; i < input_binding_info.dims.nbDims; ++i) {
      if (i == 0 && !input_binding_info.static_batch_size) {
        tensor_shape.push_back(batch_size);
      } else {
        tensor_shape.push_back(input_binding_info.dims.d[i]);
      }
    }
    input_tensor_shape_[tensor_name] = tensor_shape;
  }
}

}  // namespace base
