load("//build/bazel_rules:walle_platform_library.bzl", "walle_platform_library")

package(default_visibility = ["//visibility:public"])

walle_platform_library(
    name = "torch_script_runtime_model",
    platform = "x86_64_with_cuda",
    srcs = ["torch_script_runtime_model.cc"],
    hdrs = ["torch_script_runtime_model.h"],
    implementation_deps = [
        "//modules/perception/onboard/common/custom_ops/torch_ops/lidar_detection:lidar_detection_ops",
        "//modules/perception/onboard/common/custom_ops/torch_ops/sparseconv:spconv_v2_ops",
        "@pytorch//:torch_cpu",
    ],
    deps = [
        "//base/cuda:model_runtime",
        "//base/common:environment",
        "//base/cuda:device_utils",
        "//base/cuda/tensorrt:trt_utils",
        "//base/file:file_path_util",
        "//walle/common/proto/model:cc_model_config_proto",
    ],
)