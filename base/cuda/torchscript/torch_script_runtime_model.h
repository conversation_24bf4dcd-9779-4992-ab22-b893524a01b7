// Copyright @2025 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <memory>
#include <string>
#include <vector>
#include "base/container/utils.h"
#include "base/cuda/model_runtime.h"

#include "walle/common/proto/model/model_config.pb.h"
namespace torch {
namespace jit {
class Module;
}  // namespace jit
}  // namespace torch

namespace base {

class TorchScriptRuntimeModel : public ModelRuntime {
 public:
  TorchScriptRuntimeModel(const walle::ModelConfig& model_config,
                          cuda::DeviceContext* device_context);

  virtual ~TorchScriptRuntimeModel() override;

  void Enqueue(size_t batch_size, bool use_cuda_graph) override;

 private:
  void InitializeBindingInfo();
  void GetInputTensorShape(int batch_size = 1);
  std::string model_file_;

  std::unique_ptr<torch::jit::Module> module_;
  std::unordered_map<std::string, std::vector<int64_t>> input_tensor_shape_;

  size_t max_batch_size_;
  walle::ModelConfig model_config_;
};

}  // namespace base
