// Copyright @2022 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/cuda/mock_model_runtime.h"

#include "base/cuda/tensorrt/trt_utils.h"

namespace base {

MockModelRuntime::MockModelRuntime(const walle::ModelConfigManager& model_config_manager,
                                   cuda::DeviceContext* device_context)
    : ModelRuntime(model_config_manager.model_config().engine_file(), device_context) {
  const walle::ModelConfig model_config = model_config_manager.model_config();
  for (int i = 0; i < model_config.input_node_size(); ++i) {
    const std::string& node_name = model_config.input_node(i).node_name();
    const walle::IntArray& node_shape = model_config.input_node(i).node_shape();
    nvinfer1::Dims dims;
    dims.nbDims = node_shape.dim_size();
    for (int j = 0; j < node_shape.dim_size(); ++j) {
      dims.d[j] = node_shape.dim(j);
    }
    const nvinfer1::DataType data_type =
        trt::ConvertToTensorRTDataType(model_config.input_node(i).precision());
    const size_t buffer_size = GetDimSize(dims) * GetTypeSize(data_type);
    input_binding_infos_[node_name] = {data_type, dims, buffer_size, static_cast<size_t>(i)};
  }
  for (int i = 0; i < model_config.output_node_size(); ++i) {
    const std::string& node_name = model_config.output_node(i).node_name();
    output_name_list_.push_back(node_name);
    const walle::IntArray& node_shape = model_config.output_node(i).node_shape();
    nvinfer1::Dims dims;
    dims.nbDims = node_shape.dim_size();
    for (int j = 0; j < node_shape.dim_size(); ++j) {
      dims.d[j] = node_shape.dim(j);
    }
    const nvinfer1::DataType data_type = trt::ConvertToTensorRTDataType(model_config.data_type());
    const size_t buffer_size = GetDimSize(dims) * GetTypeSize(data_type);
    output_binding_infos_[node_name] = {data_type, dims, buffer_size, static_cast<size_t>(i)};
  }
}

MockModelRuntime::~MockModelRuntime() { LOG(INFO) << name_ << " destroyed."; }

}  // namespace base