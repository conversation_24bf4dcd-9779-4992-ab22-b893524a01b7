// Copyright @2022 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/cuda/base_model_manager.h"

#include "gtest/gtest.h"

#include "base/cuda/tensorrt/trt_onnx_parser.h"

namespace base {
namespace {
constexpr char kModelConfigFilePath[] =
    "modules/prediction/model/mock_model_configs/wayformer_multi_trajectory_model_config.pb.txt";
}  // namespace

TEST(BaseModelManagerTest, BasicTest) {
  FLAGS_use_trt_mock_engine = true;
  cuda::DeviceContext device_context;
  BaseModelManager base_model_manager("", kModelConfigFilePath, &device_context);
  const std::unordered_map<std::string, int>& value_num_per_batchs =
      base_model_manager.value_num_per_batchs();
  EXPECT_EQ(23040, value_num_per_batchs.at("target_obstacle_input"));
  EXPECT_EQ(1344, value_num_per_batchs.at("trajectory_classification_output"));
  base_model_manager.ClearBuffer(-300.0);
  const float* target_obstacle_input = base_model_manager.GetBuffer("target_obstacle_input");
  EXPECT_FLOAT_EQ(-300.0, target_obstacle_input[0]);
  EXPECT_EQ(64, base_model_manager.GetInputBufferDim("target_obstacle_input", 0));
}

}  // namespace base
