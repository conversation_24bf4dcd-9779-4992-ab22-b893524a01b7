// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <cuda_runtime.h>
#include <cstring>
#include <memory>
#include <string>
#include <unordered_map>
#include <utility>
#include <vector>

#include "base/common/macros.h"
#include "base/cuda/device_utils.h"

namespace cuda {

template <typename T>
class DeviceBufferView;

template <typename T>
class ConstDeviceBufferView;

class DeviceBuffer {
 public:
  explicit DeviceBuffer(size_t size) : size_(size) {
    if (GetInferMode() != InferMode::kRemoteModelServing) {
      CUDA_CHECK(cudaMalloc(&data_, size_)) << " malloc data size: " << size_;
    } else {
      data_ = malloc(size_);
    }
    own_memory_ = true;
  }

  DeviceBuffer(void* dev_ptr, size_t size) : data_(CHECK_NOTNULL(dev_ptr)), size_(size) {
    own_memory_ = false;
  }

  DeviceBuffer(DeviceBuffer&& other) {
    std::swap(data_, other.data_);
    std::swap(size_, other.size_);
    std::swap(own_memory_, other.own_memory_);
  }

  DeviceBuffer& operator=(DeviceBuffer&& other) {
    std::swap(data_, other.data_);
    std::swap(size_, other.size_);
    std::swap(own_memory_, other.own_memory_);
    return *this;
  }

  virtual ~DeviceBuffer() { this->Destroy(); }

  bool own_memory() const { return own_memory_; }

  template <typename T>
  DeviceBufferView<T> View() {
    return DeviceBufferView<T>(this);
  }

  template <typename T>
  ConstDeviceBufferView<T> ConstView() const {
    CHECK_EQ(size_ % sizeof(T), 0);
    return ConstDeviceBufferView<T>(static_cast<T*>(data_), size_ / sizeof(T));
  }

  template <typename T>
  DeviceBufferView<T> View(size_t offset_bytes, size_t num_bytes) {
    CHECK_LE(offset_bytes + num_bytes, size_);
    CHECK_EQ(num_bytes % sizeof(T), 0);
    uint8_t* tmp_ptr = static_cast<uint8_t*>(data_);
    T* offset_ptr = reinterpret_cast<T*>(tmp_ptr + offset_bytes);
    const size_t view_size = num_bytes / sizeof(T);
    return DeviceBufferView<T>(offset_ptr, view_size);
  }

  template <typename T>
  ConstDeviceBufferView<T> ConstView(size_t offset_bytes, size_t num_bytes) const {
    CHECK_LE(offset_bytes + num_bytes, size_);
    CHECK_EQ(num_bytes % sizeof(T), 0);
    uint8_t* tmp_ptr = static_cast<uint8_t*>(data_);
    T* offset_ptr = reinterpret_cast<T*>(tmp_ptr + offset_bytes);
    const size_t view_size = num_bytes / sizeof(T);
    return ConstDeviceBufferView<T>(offset_ptr, view_size);
  }

  void ToCpu(void* host_ptr, size_t num_bytes, DeviceContext* ctx) const {
    CHECK_GE(size_, num_bytes);
    DeviceToHost(data_, host_ptr, num_bytes, ctx);
  }

  void FromCpu(const void* host_ptr, size_t num_bytes, DeviceContext* ctx) {
    CHECK_GE(size_, num_bytes);
    HostToDevice(host_ptr, data_, num_bytes, ctx);
  }

  void FromCpu(const void* host_ptr, DeviceContext* ctx) {
    HostToDevice(host_ptr, data_, size_, ctx);
  }

  void FromGpu(const void* dev_ptr, size_t num_bytes, DeviceContext* ctx) {
    CHECK_GE(size_, num_bytes);
    DeviceToDevice(dev_ptr, data_, num_bytes, ctx);
  }

  void FromGpu(const void* dev_ptr, DeviceContext* ctx) {
    DeviceToDevice(dev_ptr, data_, size_, ctx);
  }

  void Memset(int value, DeviceContext* ctx) {
    if (GetInferMode() != InferMode::kRemoteModelServing) {
      if (ctx) {
        CUDA_CHECK(cudaMemsetAsync(data_, value, size_, ctx->stream()));
      } else {
        CUDA_CHECK(cudaMemset(data_, value, size_));
      }
    } else {
      std::memset(data_, value, size_);
    }
  }

  void Clean(DeviceContext* ctx) { this->Memset(0, ctx); }

  size_t size() const { return size_; }
  const void* data() const { return data_; }
  void* mutable_data() { return data_; }

 private:
  void Destroy() {
    if (own_memory_) {
      if (GetInferMode() != InferMode::kRemoteModelServing) {
        CUDA_CHECK(cudaFree(data_));
      } else {
        free(data_);
      }
      data_ = nullptr;
    }
  }

  void* data_ = nullptr;
  size_t size_ = 0;
  bool own_memory_ = false;

  DISALLOW_COPY_AND_ASSIGN(DeviceBuffer);
};

using DeviceBufferMap = std::unordered_map<std::string, DeviceBuffer>;

template <typename T>
class ConstDeviceBufferView {
 public:
  ConstDeviceBufferView(ConstDeviceBufferView&& const_view) {
    const_view_ptr_ = std::move(const_view.const_view_ptr_);
  }

  ConstDeviceBufferView(const void* data, size_t byte_size)
      : ConstDeviceBufferView(static_cast<T*>(const_cast<void*>(data)), byte_size / sizeof(T)) {
    CHECK_EQ(byte_size % sizeof(T), 0);
  }

  ~ConstDeviceBufferView() = default;

  const DeviceBufferView<T>& View() const { return *const_view_ptr_.get(); }

  ConstDeviceBufferView ConstView(size_t offset, size_t length) const {
    return const_view_ptr_->ConstView(offset, length);
  }
  const T* data() const { return const_view_ptr_->data(); }
  size_t size() const { return const_view_ptr_->size(); }
  void ToCpu(T* host_ptr, size_t offset, size_t length, DeviceContext* ctx) const {
    const_view_ptr_->ToCpu(host_ptr, offset, length, ctx);
  }
  void ToCpu(T* host_ptr, DeviceContext* ctx) const { const_view_ptr_->ToCpu(host_ptr, ctx); }

 private:
  ConstDeviceBufferView(T* data, size_t size) {
    const_view_ptr_ = std::make_unique<const DeviceBufferView<T>>(data, size);
  }

  std::unique_ptr<const DeviceBufferView<T>> const_view_ptr_ = nullptr;

  friend class DeviceBufferView<T>;
  friend class DeviceBuffer;

  ConstDeviceBufferView() = delete;
  DISALLOW_COPY_AND_ASSIGN(ConstDeviceBufferView);
};

template <typename T>
class DeviceBufferView {
 public:
  explicit DeviceBufferView(DeviceBuffer* device_buffer)
      : data_(static_cast<T*>(CHECK_NOTNULL(device_buffer)->mutable_data())),
        size_(device_buffer->size() / sizeof(T)) {
    CHECK_EQ(device_buffer->size() % sizeof(T), 0);
  }
  DeviceBufferView(T* dev_ptr, size_t size) : data_(dev_ptr), size_(size) {}

  DeviceBufferView(DeviceBufferView&& non_const_view)
      : data_(CHECK_NOTNULL(non_const_view.data_)), size_(non_const_view.size_) {}
  void operator=(DeviceBufferView&& non_const_view) {
    data_ = CHECK_NOTNULL(non_const_view.data_);
    size_ = non_const_view.size_;
  }

  virtual ~DeviceBufferView() = default;

  const T* data() const { return data_; }
  T* mutable_data() { return data_; }
  size_t size() const { return size_; }

  DeviceBufferView View(size_t offset, size_t length) {
    CHECK_LE(offset + length, size_);
    return DeviceBufferView(data_ + offset, length);
  }
  ConstDeviceBufferView<T> ConstView(size_t offset, size_t length) const {
    CHECK_LE(offset + length, size_);
    return ConstDeviceBufferView<T>(data_ + offset, length);
  }
  ConstDeviceBufferView<T> ConstView() const { return ConstDeviceBufferView<T>(data_, size_); }

  void ToCpu(T* host_ptr, size_t offset, size_t length, DeviceContext* ctx) const;
  void ToCpu(T* host_ptr, DeviceContext* ctx) const { ToCpu(host_ptr, 0, size_, ctx); }
  void FromCpu(const T* host_ptr, size_t offset, size_t length, DeviceContext* ctx);
  void FromCpu(const T* host_ptr, DeviceContext* ctx) { FromCpu(host_ptr, 0, size_, ctx); }

  void FromGpu(const T* dev_ptr, size_t offset, size_t length, DeviceContext* ctx);
  void FromGpu(const T* dev_ptr, DeviceContext* ctx) { FromGpu(dev_ptr, 0, size_, ctx); }

  void Fill(size_t offset, size_t length, uint8_t value, DeviceContext* ctx);
  void Fill(uint8_t value, DeviceContext* ctx) { Fill(0, size_, value, ctx); }

 private:
  T* data_ = nullptr;
  size_t size_ = 0;

  DISALLOW_COPY_AND_ASSIGN(DeviceBufferView);
};

template <typename T>
void DeviceBufferView<T>::ToCpu(T* host_ptr, size_t offset, size_t length,
                                DeviceContext* ctx) const {
  CHECK_LE(offset + length, size_);
  const size_t num_bytes = length * sizeof(T);
  DeviceToHost(static_cast<const void*>(data_ + offset), static_cast<void*>(host_ptr), num_bytes,
               ctx);
}

template <typename T>
void DeviceBufferView<T>::FromCpu(const T* host_ptr, size_t offset, size_t length,
                                  DeviceContext* ctx) {
  CHECK_LE(offset + length, size_);
  const size_t num_bytes = length * sizeof(T);
  HostToDevice(static_cast<const void*>(host_ptr), static_cast<void*>(data_ + offset), num_bytes,
               ctx);
}

template <typename T>
void DeviceBufferView<T>::FromGpu(const T* dev_ptr, size_t offset, size_t length,
                                  DeviceContext* ctx) {
  CHECK_LE(offset + length, size_);
  const size_t num_bytes = length * sizeof(T);
  DeviceToDevice(
      static_cast<const void*>(dev_ptr), static_cast<void*>(data_ + offset), num_bytes, ctx);
}

template <typename T>
void DeviceBufferView<T>::Fill(size_t offset, size_t length, uint8_t value, DeviceContext* ctx) {
  CHECK_LE(offset + length, size_);
  cuda::Fill(data_ + offset, value, length * sizeof(T), ctx);
}

}  // namespace cuda
