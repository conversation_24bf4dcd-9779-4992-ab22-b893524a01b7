// Copyright @2022 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include "gflags/gflags.h"

DECLARE_string(grpc_model_server);
DECLARE_string(modelserving_api);
DECLARE_string(client_name);
DECLARE_int32(infer_mode);

DECLARE_bool(enable_generate_trt_engine);
DECLARE_int32(trt_log_level);
DECLARE_bool(trt_inference_by_max_batch_size);
DECLARE_bool(use_trt_mock_engine);
DECLARE_bool(check_gpu_tensor_validity);
