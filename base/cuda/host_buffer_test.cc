// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "gtest/gtest.h"

#include "base/cuda/device_buffer.h"
#include "base/cuda/host_buffer.h"

namespace cuda {
namespace {

constexpr int kNumBytes = 100;
constexpr char kNumber = 0x5A; //0x01011010

TEST(HostBufferTest, HostDeviceHost) {
  HostBuffer host_buf_src(kNumBytes);
  HostBuffer host_buf_dst(kNumBytes);
  DeviceBuffer dev_buf(kNumBytes);

  host_buf_src.Memset(kNumber);  // 0x01011010
  dev_buf.FromCpu(host_buf_src.data(), kNumBytes, cuda::DeviceContext::kDefault);
  dev_buf.ToCpu(host_buf_dst.mutable_data(), kNumBytes, cuda::DeviceContext::kDefault);
  const char* begin = reinterpret_cast<const char*>(host_buf_dst.data());
  for (int i = 0; i < kNumBytes; i++) {
    EXPECT_EQ(kNumber, begin[i]);
  }
}

TEST(HostBufferTest, HostHostDeviceHost) {
  HostBuffer host_buf_src(kNumBytes);
  HostBuffer host_buf_dst(kNumBytes);
  DeviceBuffer dev_buf(kNumBytes);

  host_buf_src.Memset(kNumber);
  HostBuffer host_buf_tmp(host_buf_src.mutable_data(), kNumBytes);
  dev_buf.FromCpu(host_buf_tmp.data(), kNumBytes, cuda::DeviceContext::kDefault);
  dev_buf.ToCpu(host_buf_dst.mutable_data(), kNumBytes, cuda::DeviceContext::kDefault);
  const char* begin = reinterpret_cast<const char*>(host_buf_dst.data());
  for (int i = 0; i < kNumBytes; i++) {
    EXPECT_EQ(kNumber, begin[i]);
  }
}

TEST(HostBufferTest, MoveConstruct) {
  HostBuffer host_buf_src(kNumBytes);
  host_buf_src.Memset(kNumber);
  HostBuffer host_buf_dst(std::move(host_buf_src));
  EXPECT_EQ(host_buf_src.size(), 0);
  EXPECT_EQ(host_buf_src.data(), nullptr);
  const char* begin = reinterpret_cast<const char*>(host_buf_dst.data());
  for (int i = 0; i < kNumBytes; i++) {
    EXPECT_EQ(kNumber, begin[i]);
  }
}

TEST(HostBufferTest, HostBufferSpanTest) {
  HostBuffer buffer(100);
  auto buffer_span = buffer.Span<int>();
  EXPECT_TRUE(buffer_span.data() != nullptr);

  std::vector<int> x(25, 1);
  std::vector<int> y(25, 0);

  // Test partial copy
  std::memcpy(buffer_span.data() + 5, x.data() + 5, 10 * sizeof(int));
  std::memcpy(y.data() + 5, buffer_span.data() + 5, 10 * sizeof(int));
  for (int i = 0; i < 25; ++i) {
    const int buffer_value = buffer_span[i];
    EXPECT_EQ(buffer_value, i >= 5 && i < 15 ? 1 : 0);
    EXPECT_EQ(y[i], i >= 5 && i < 15 ? 1 : 0);
  }
  // Test full copy
  std::memcpy(buffer_span.data(), x.data(), 25 * sizeof(int));
  std::memcpy(y.data(), buffer_span.data(), 25 * sizeof(int));
  for (int i = 0; i < 25; ++i) {
    EXPECT_EQ(y[i], 1);
  }
  // Test const span
  auto const_span = buffer.ConstSpan<int>();
  std::vector<int> z(25, 0);
  std::memcpy(z.data(), const_span.data(), 25 * sizeof(int));
  for (int i = 0; i < 25; ++i) {
    EXPECT_EQ(z[i], 1);
  }
  // Test span size
  auto buffer_span_offset = buffer.Span<int>(10, 5);
  auto const_span_offset = buffer.ConstSpan<int>(10, 5);
  EXPECT_EQ(buffer_span_offset.size(), 5);
  EXPECT_EQ(const_span_offset.size(), 5);
}

}  // namespace
}  // namespace cuda
