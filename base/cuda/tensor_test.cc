// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/cuda/tensor.h"

#include <algorithm>
#include <memory>
#include <numeric>
#include <utility>
#include <vector>

#include "gtest/gtest.h"

namespace cuda {
namespace {

constexpr int kBatch = 2;
constexpr int kHeight = 200;
constexpr int kWidth = 300;
constexpr int kChannels = 3;
constexpr int kNumDims = 4;
constexpr int kNumElements = kBatch * kHeight * kWidth * kChannels;
constexpr char kShapeString[] = "[2, 200, 300, 3]";

TEST(TensorShapeTest, Tensor0D) {
  const std::string shape_string = "[]";
  const TensorShape shape;

  EXPECT_EQ(shape.Dimension(0), 0);
  EXPECT_EQ(shape.NumDims(), 0);
  EXPECT_EQ(shape.NumElements(), 1);
  EXPECT_EQ(shape.ToString(), shape_string);
}

TEST(TensorShapeTest, Tensor4D) {
  const TensorShape shape({kBatch, kHeight, kWidth, kChannels});

  EXPECT_EQ(shape.Dimension(0), kBatch);
  EXPECT_EQ(shape.Dimension(1), kHeight);
  EXPECT_EQ(shape.Dimension(2), kWidth);
  EXPECT_EQ(shape.Dimension(3), kChannels);
  EXPECT_EQ(shape.NumDims(), kNumDims);
  EXPECT_EQ(shape.NumElements(), kNumElements);
  EXPECT_EQ(shape.ToString(), kShapeString);
}

TEST(TensorShapeTest, MoveConstruct) {
  TensorShape shape({kBatch, kHeight, kWidth, kChannels});
  TensorShape shape2 = std::move(shape);

  EXPECT_EQ(shape2.Dimension(0), kBatch);
  EXPECT_EQ(shape2.Dimension(1), kHeight);
  EXPECT_EQ(shape2.Dimension(2), kWidth);
  EXPECT_EQ(shape2.Dimension(3), kChannels);
  EXPECT_EQ(shape2.NumDims(), kNumDims);
  EXPECT_EQ(shape2.NumElements(), kNumElements);
  EXPECT_EQ(shape2.ToString(), kShapeString);

  EXPECT_EQ(shape.NumDims(), 0);
  EXPECT_EQ(shape.NumElements(), 1);
  EXPECT_EQ(shape.ToString(), "[]");
}

TEST(TensorShapeTest, MoveAssignment) {
  const int new_dim0 = 2;
  const int new_dim1 = 3;
  const int new_dim2 = 4;
  const int new_num_dims = 3;
  const int new_num_elements = new_dim0 * new_dim1 * new_dim2;
  const std::string new_shape_string = "[2, 3, 4]";
  TensorShape shape({kBatch, kHeight, kWidth, kChannels});
  TensorShape shape2({new_dim0, new_dim1, new_dim2});
  shape2 = std::move(shape);

  EXPECT_EQ(shape.NumDims(), new_num_dims);
  EXPECT_EQ(shape.NumElements(), new_num_elements);
  EXPECT_EQ(shape.ToString(), new_shape_string);
  EXPECT_EQ(shape.Dimension(0), new_dim0);
  EXPECT_EQ(shape.Dimension(1), new_dim1);
  EXPECT_EQ(shape.Dimension(2), new_dim2);

  EXPECT_EQ(shape2.NumDims(), kNumDims);
  EXPECT_EQ(shape2.NumElements(), kNumElements);
  EXPECT_EQ(shape2.ToString(), kShapeString);
  EXPECT_EQ(shape2.Dimension(0), kBatch);
  EXPECT_EQ(shape2.Dimension(1), kHeight);
  EXPECT_EQ(shape2.Dimension(2), kWidth);
  EXPECT_EQ(shape2.Dimension(3), kChannels);
}

TEST(DeviceTensorTest, ShapeTest) {
  const DeviceTensor<float, kNumDims> tensor(TensorShape({kBatch, kHeight, kWidth, kChannels}));

  EXPECT_EQ(tensor.Dimension(0), kBatch);
  EXPECT_EQ(tensor.Dimension(1), kHeight);
  EXPECT_EQ(tensor.Dimension(2), kWidth);
  EXPECT_EQ(tensor.Dimension(3), kChannels);
  EXPECT_EQ(tensor.NumDims(), kNumDims);
  EXPECT_EQ(tensor.NumElements(), kNumElements);
  EXPECT_EQ(tensor.NumBytes(), kNumElements * sizeof(float));
  EXPECT_EQ(tensor.ShapeString(), kShapeString);
}

TEST(DeviceTensorTest, DataTest) {
  DeviceContext ctx;
  DeviceTensor<int, kNumDims> tensor(TensorShape({kBatch, kHeight, kWidth, kChannels}));
  std::vector<int> host(kNumElements);
  std::vector<int> ref(kNumElements);
  std::iota(host.begin(), host.end(), 0);
  tensor.mutable_buffer()->FromCpu(host.data(), host.size() * sizeof(int), &ctx);
  tensor.buffer().ToCpu(ref.data(), ref.size() * sizeof(int), &ctx);
  ctx.Sync();

  for (int i = 0; i < kNumElements; i++) {
    EXPECT_EQ(host[i], ref[i]);
  }
}

TEST(DeviceTensorTest, BatchAccessTest) {
  DeviceContext ctx;
  DeviceTensor<int, kNumDims> tensor(TensorShape({kBatch, kHeight, kWidth, kChannels}));
  const int stride = kNumElements / kBatch;
  std::vector<std::vector<int>> host;
  host.emplace_back(stride, 10);
  host.emplace_back(stride, 100);
  std::vector<int> ref(kNumElements, 0);

  tensor[0].FromCpu(host[0].data(), stride * sizeof(int), &ctx);
  tensor[1].FromCpu(host[1].data(), stride * sizeof(int), &ctx);
  tensor.buffer().ToCpu(ref.data(), ref.size() * sizeof(int), &ctx);
  ctx.Sync();

  for (int i = 0; i < kBatch; i++) {
    for (int j = 0; j < stride; j++) {
      EXPECT_EQ(host[i][j], ref[i * stride + j]);
    }
  }
}

TEST(DeviceTensorViewTest, ConstructorTest) {
  DeviceTensor<float, kNumDims> tensor(TensorShape({kBatch, kHeight, kWidth, kChannels}));
  DeviceTensorView<float, kNumDims> tensor_view1(&tensor);
  DeviceTensorView<float, kNumDims> tensor_view2(static_cast<float*>(tensor.mutable_raw_data()),
                                                 tensor.shape());
  EXPECT_EQ(tensor_view1.Dimension(0), kBatch);
  EXPECT_EQ(tensor_view1.Dimension(0), tensor_view2.Dimension(0));

  EXPECT_EQ(tensor_view1.Dimension(2), kWidth);
  EXPECT_EQ(tensor_view1.Dimension(2), tensor_view2.Dimension(2));

  DeviceTensorView<float, kNumDims> tensor_view3(std::move(tensor_view2));
  EXPECT_EQ(tensor_view1.Dimension(0), tensor_view3.Dimension(0));
  EXPECT_EQ(tensor_view1.Dimension(2), tensor_view3.Dimension(2));
}

TEST(DeviceTensorViewTest, DataTest) {
  DeviceContext ctx;
  DeviceTensor<int, kNumDims> tensor(TensorShape({kBatch, kHeight, kWidth, kChannels}));
  std::vector<int> host(kNumElements);
  std::vector<int> ref(kNumElements);
  std::iota(host.begin(), host.end(), 0);
  {
    DeviceTensorView<int, kNumDims> tensor_view(&tensor);
    tensor_view.mutable_buffer()->FromCpu(host.data(), 0, host.size(), &ctx);
  }
  tensor.buffer().ToCpu(ref.data(), ref.size() * sizeof(int), &ctx);
  ctx.Sync();

  for (int i = 0; i < kNumElements; i++) {
    EXPECT_EQ(host[i], ref[i]);
  }
}

TEST(DeviceTensorViewTest, ShapeTest) {
  DeviceTensor<float, kNumDims> tensor(TensorShape({kBatch, kHeight, kWidth, kChannels}));
  DeviceTensorView<float, kNumDims> tensor_view(&tensor);

  EXPECT_EQ(tensor_view.Dimension(0), kBatch);
  EXPECT_EQ(tensor_view.Dimension(1), kHeight);
  EXPECT_EQ(tensor_view.Dimension(2), kWidth);
  EXPECT_EQ(tensor_view.Dimension(3), kChannels);
  EXPECT_EQ(tensor_view.NumDims(), kNumDims);
  EXPECT_EQ(tensor_view.NumElements(), kNumElements);
  EXPECT_EQ(tensor_view.NumBytes(), kNumElements * sizeof(float));
  EXPECT_EQ(tensor_view.ShapeString(), kShapeString);
}

TEST(ConstDeviceTensorViewTest, ConstructorTest) {
  DeviceTensor<float, kNumDims> tensor(TensorShape({kBatch, kHeight, kWidth, kChannels}));
  ConstDeviceTensorView<float, kNumDims> tensor_view1(tensor.ConstView());
  ConstDeviceTensorView<float, kNumDims> tensor_view2(static_cast<const float*>(tensor.raw_data()),
                                                 tensor.shape());
  EXPECT_EQ(tensor_view1.Dimension(0), kBatch);
  EXPECT_EQ(tensor_view1.Dimension(0), tensor_view2.Dimension(0));

  EXPECT_EQ(tensor_view1.Dimension(2), kWidth);
  EXPECT_EQ(tensor_view1.Dimension(2), tensor_view2.Dimension(2));

  ConstDeviceTensorView<float, kNumDims> tensor_view3(std::move(tensor_view2));
  EXPECT_EQ(tensor_view1.Dimension(0), tensor_view3.Dimension(0));
  EXPECT_EQ(tensor_view1.Dimension(2), tensor_view3.Dimension(2));
}

TEST(ConstDeviceTensorViewTest, DataTest) {
  DeviceContext ctx;
  DeviceTensor<int, kNumDims> tensor(TensorShape({kBatch, kHeight, kWidth, kChannels}));
  std::vector<int> host(kNumElements);
  std::vector<int> ref(kNumElements);
  std::iota(host.begin(), host.end(), 0);
  {
    DeviceTensorView<int, kNumDims> tensor_view(&tensor);
    tensor_view.mutable_buffer()->FromCpu(host.data(), 0, host.size(), &ctx);
  }
  ConstDeviceTensorView<int, kNumDims> const_tensor_view(tensor.ConstView());
  const_tensor_view.buffer().ToCpu(ref.data(), &ctx);
  ctx.Sync();

  for (int i = 0; i < kNumElements; i++) {
    EXPECT_EQ(host[i], ref[i]);
  }
}

TEST(ConstDeviceTensorViewTest, ShapeTest) {
  DeviceTensor<float, kNumDims> tensor(TensorShape({kBatch, kHeight, kWidth, kChannels}));
  ConstDeviceTensorView<float, kNumDims> tensor_view(tensor.ConstView());

  EXPECT_EQ(tensor_view.Dimension(0), kBatch);
  EXPECT_EQ(tensor_view.Dimension(1), kHeight);
  EXPECT_EQ(tensor_view.Dimension(2), kWidth);
  EXPECT_EQ(tensor_view.Dimension(3), kChannels);
  EXPECT_EQ(tensor_view.NumDims(), kNumDims);
  EXPECT_EQ(tensor_view.NumElements(), kNumElements);
  EXPECT_EQ(tensor_view.NumBytes(), kNumElements * sizeof(float));
  EXPECT_EQ(tensor_view.ShapeString(), kShapeString);
}

TEST(HostTensorTest, BasicTest) {
  const int batch = 6;
  const int height = 5;
  const int width = 4;
  const int channels = 3;
  const int value = 123;
  std::vector<uint8_t> buffer(batch * height * width * channels, 0);
  Tensor4D<uint8_t> tensor(buffer.data(), batch, height, width, channels);
  EXPECT_EQ(tensor.Batch(), batch);
  EXPECT_EQ(tensor.Height(), height);
  EXPECT_EQ(tensor.Width(), width);
  EXPECT_EQ(tensor.Channels(), channels);

  tensor(5, 4, 3, 2) = value;
  EXPECT_EQ(tensor(5, 4, 3, 2), value);
}

TEST(HostTensorTest, OwnMemoryTest) {
  const int batch = 6;
  const int height = 5;
  const int width = 4;
  const int channels = 3;
  const uint8_t value = 123;
  Tensor4D<uint8_t> tensor(batch, height, width, channels);
  tensor.Fill(value);
  EXPECT_EQ(value, tensor(5, 4, 3, 2));
}

}  // namespace
}  // namespace cuda
