// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <cuda_runtime.h>
#include <memory>
#include <string>
#include <utility>
#include <vector>

#include "glog/logging.h"

#include "base/common/macros.h"
#include "base/cuda/device_buffer.h"
#include "base/cuda/device_utils.h"

namespace cuda {

/*
 * TensorShape
 * the shape descriptor of a DeviceTensor, support from 0-D up to 8-D.
 *
 * Examples:
 *   0-D: scalar
 *   1-D: vector
 *   2-D: matrix
 *   3-D: image
 *   4-D: batched images
 */
class TensorShape {
 public:
  // create 0-D (scalar) shape by default
  TensorShape() = default;

  explicit TensorShape(std::vector<int>&& shape) : shape_(std::move(shape)) {
    CHECK_LE(shape_.size(), kMaxNumDims);
    num_dims_ = shape_.size();
    for (auto x : shape_) {
      CHECK_GT(x, 0);
      num_elements_ *= x;
    }
  }

  TensorShape(TensorShape&& other) {
    std::swap(num_dims_, other.num_dims_);
    std::swap(num_elements_, other.num_elements_);
    std::swap(shape_, other.shape_);
  }

  TensorShape& operator=(TensorShape&& other) {
    std::swap(num_dims_, other.num_dims_);
    std::swap(num_elements_, other.num_elements_);
    std::swap(shape_, other.shape_);
    return *this;
  }

  TensorShape(const TensorShape& other) = default;

  virtual ~TensorShape() = default;

  int NumDims() const { return num_dims_; }

  int NumElements() const { return num_elements_; }

  int Dimension(int idx) const {
    CHECK_GE(idx, 0);
    if (num_dims_ == 0) {
      return 0;
    } else {
      CHECK_LT(idx, num_dims_);
      return shape_[idx];
    }
  }

  std::string ToString() const {
    std::string shape_string = "[";
    for (int i = 0; i < num_dims_; i++) {
      if (i > 0) {
        shape_string.append(", ");
      }
      shape_string.append(std::to_string(shape_[i]));
    }
    shape_string.append("]");
    return shape_string;
  }

 private:
  const int kMaxNumDims = 8;
  int num_dims_ = 0;
  int num_elements_ = 1;
  std::vector<int> shape_;
};

template <typename T, int NDIMS>
class DeviceTensorView;

template <typename T, int NDIMS>
class ConstDeviceTensorView;

/*
 * DeviceTensor
 * represent a high dimensional array (from 0-D to 8-D) on gpu,
 * aims to provide convenient tensor manipulations with various cuda operators.
 *
 * Example:
 *   DeviceTensor<uint8_t, 4> tensor4d(TensorShape({2, 512, 512, 3}))
 *   holds a batched image buffer on gpu, which batch size is 2,
 *   and the shape of each batch (frame) is 512 x 512 x 3.
 */
template <typename T, int NDIMS>
class DeviceTensor {
 public:
  explicit DeviceTensor(TensorShape&& shape) : shape_(std::move(shape)) {
    CHECK_EQ(shape_.NumDims(), NDIMS);
    buffer_ = std::make_unique<DeviceBuffer>(NumBytes());
  }

  virtual ~DeviceTensor() = default;

  const DeviceBuffer& buffer() const { return *(buffer_.get()); }
  DeviceBuffer* mutable_buffer() { return buffer_.get(); }

  const TensorShape& shape() const { return shape_; }

  const T* raw_data() const { return reinterpret_cast<const T*>(buffer_->data()); }
  T* mutable_raw_data() { return reinterpret_cast<T*>(buffer_->mutable_data()); }

  std::string ShapeString() const { return shape_.ToString(); }
  int NumDims() const { return shape_.NumDims(); }
  int NumElements() const { return shape_.NumElements(); }
  int NumBytes() const { return shape_.NumElements() * sizeof(T); }
  int Dimension(int idx) const { return shape_.Dimension(idx); }

  // Return a DeviceBuffer of the given batch slice for strided access.
  // not valid for 0-D scalar tensor.
  DeviceBuffer operator[](int batch_idx) {
    CHECK_GT(shape_.NumDims(), 0);
    const int batch_size = shape_.Dimension(0);
    CHECK_GE(batch_idx, 0);
    CHECK_LT(batch_idx, batch_size);
    const int stride = shape_.NumElements() / batch_size;
    T* dev_ptr = mutable_raw_data() + batch_idx * stride;
    return DeviceBuffer(dev_ptr, stride * sizeof(T));
  }

  DeviceTensorView<T, NDIMS> View() { return DeviceTensorView<T, NDIMS>(this); }
  ConstDeviceTensorView<T, NDIMS> ConstView() const {
    return ConstDeviceTensorView<T, NDIMS>(raw_data(), shape());
  }

 private:
  std::unique_ptr<DeviceBuffer> buffer_ = nullptr;
  TensorShape shape_;

  DISALLOW_COPY_AND_ASSIGN(DeviceTensor);
};

template <typename T, int NDIMS>
class DeviceTensorView {
 public:
  explicit DeviceTensorView(DeviceTensor<T, NDIMS>* device_tensor)
      : buffer_(CHECK_NOTNULL(device_tensor)->mutable_buffer()), shape_(device_tensor->shape()) {}
  DeviceTensorView(T* dev_ptr, const TensorShape& shape)
      : buffer_(CHECK_NOTNULL(dev_ptr), shape.NumElements()), shape_(shape) {}
  DeviceTensorView(DeviceTensorView&& other) = default;
  DeviceTensorView& operator=(DeviceTensorView&& other) = default;
  virtual ~DeviceTensorView() = default;

  const T* raw_data() const { return buffer_.data(); }
  T* mutable_raw_data() { return buffer_.mutable_data(); }
  const DeviceBufferView<T>& buffer() const { return buffer_; }
  DeviceBufferView<T>* mutable_buffer() { return &buffer_; }
  const TensorShape& shape() const { return shape_; }

  std::string ShapeString() const { return shape_.ToString(); }
  int NumDims() const { return shape_.NumDims(); }
  int NumElements() const { return shape_.NumElements(); }
  int NumBytes() const { return shape_.NumElements() * sizeof(T); }
  int Dimension(int idx) const { return shape_.Dimension(idx); }

  DeviceBufferView<T> operator[](int batch_idx) {
    CHECK_GT(shape_.NumDims(), 0);
    const int batch_size = shape_.Dimension(0);
    CHECK_GE(batch_idx, 0);
    CHECK_LT(batch_idx, batch_size);
    const int stride = shape_.NumElements() / batch_size;
    T* dev_ptr = mutable_raw_data() + batch_idx * stride;
    return DeviceBufferView<T>(dev_ptr, stride);
  }

 private:
  DeviceBufferView<T> buffer_;
  TensorShape shape_;
  DISALLOW_COPY_AND_ASSIGN(DeviceTensorView);
};

template <typename T, int NDIMS>
class ConstDeviceTensorView {
 public:
  ConstDeviceTensorView(ConstDeviceTensorView&& const_view) {
    const_view_ptr_ = std::move(const_view.const_view_ptr_);
  }
  ConstDeviceTensorView(const T* dev_ptr, const TensorShape& shape)
      : ConstDeviceTensorView(const_cast<T*>(dev_ptr), shape) {}
  virtual ~ConstDeviceTensorView() = default;

  const DeviceBufferView<T>& buffer() const { return const_view_ptr_->buffer(); }
  const TensorShape& shape() const { return const_view_ptr_->shape(); }
  const T* raw_data() const { return buffer().data(); }

  std::string ShapeString() const { return shape().ToString(); }
  int NumDims() const { return shape().NumDims(); }
  int NumElements() const { return shape().NumElements(); }
  int NumBytes() const { return shape().NumElements() * sizeof(T); }
  int Dimension(int idx) const { return shape().Dimension(idx); }

  ConstDeviceBufferView<T> operator[](int batch_idx) {
    const int batch_size = shape().Dimension(0);
    CHECK_GE(batch_idx, 0);
    CHECK_LT(batch_idx, batch_size);
    const int stride = shape().NumElements() / batch_size;
    const T* dev_ptr = raw_data() + batch_idx * stride;
    return ConstDeviceBufferView<T>(dev_ptr, stride);
  }

 private:
  ConstDeviceTensorView(T* dev_ptr, const TensorShape& shape) {
    const_view_ptr_ = std::make_unique<const DeviceTensorView<T, NDIMS>>(dev_ptr, shape);
  }

  std::unique_ptr<const DeviceTensorView<T, NDIMS>> const_view_ptr_;

  ConstDeviceTensorView() = delete;
  DISALLOW_COPY_AND_ASSIGN(ConstDeviceTensorView);
};

// Tensor4D represent a 4D host tensor.
template <typename T>
class Tensor4D {
 public:
  Tensor4D(T* data, int batch, int height, int width, int channels, bool channel_first = false)
      : data_(CHECK_NOTNULL(data)),
        batch_(batch),
        height_(height),
        width_(width),
        channels_(channels),
        channel_first_(channel_first),
        own_memory_(false) {
    Initialize();
  }
  Tensor4D(int batch, int height, int width, int channels, bool channel_first = false)
      : batch_(batch),
        height_(height),
        width_(width),
        channels_(channels),
        channel_first_(channel_first),
        own_memory_(true) {
    Initialize();
  }

  void Initialize() {
    CHECK_GT(batch_, 0);
    CHECK_GT(height_, 0);
    CHECK_GT(width_, 0);
    CHECK_GT(channels_, 0);
    data_size_ = batch_ * height_ * width_ * channels_;
    if (own_memory_) {
      data_ = new T[data_size_];
    }
  }

  ~Tensor4D() {
    if (own_memory_) {
      delete data_;
    }
  }

  const T* data() const { return data_; }
  T* mutable_data() { return data_; }

  int size() const { return data_size_; }

  void Fill(T value) { std::fill(data_, data_ + data_size_, value); }

  inline int GetValueIndex(int b, int h, int w, int c) const {
    if (channel_first_) {
      return w + width_ * (h + height_ * (c + channels_ * b));
    }
    return c + channels_ * (w + width_ * (h + height_ * b));
  }

  T operator()(int b, int h, int w, int c) const {
    DCHECK(b >= 0 && b < batch_);
    DCHECK(h >= 0 && h < height_);
    DCHECK(w >= 0 && w < width_);
    DCHECK(c >= 0 && c < channels_);
    return data_[GetValueIndex(b, h, w, c)];
  }

  T& operator()(int b, int h, int w, int c) {
    DCHECK(b >= 0 && b < batch_);
    DCHECK(h >= 0 && h < height_);
    DCHECK(w >= 0 && w < width_);
    DCHECK(c >= 0 && c < channels_);
    return data_[GetValueIndex(b, h, w, c)];
  }

  int Batch() const { return batch_; }
  int Height() const { return height_; }
  int Width() const { return width_; }
  int Channels() const { return channels_; }

  bool is_channel_first() const { return channel_first_; }

 private:
  T* data_ = nullptr;
  int batch_ = 0;
  int height_ = 0;
  int width_ = 0;
  int channels_ = 0;
  int data_size_ = 0;
  bool channel_first_ = false;
  bool own_memory_ = false;

  DISALLOW_COPY_AND_ASSIGN(Tensor4D);
};

}  // namespace cuda
