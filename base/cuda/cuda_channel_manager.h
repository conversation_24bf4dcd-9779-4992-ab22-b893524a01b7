// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <memory>
#include <string>
#include <unordered_map>

#include "base/cuda/device_buffer.h"

namespace base {

class CudaChannelManager final {
 public:
  struct CudaChannel {
    cuda::DeviceContext device_context;
    std::unique_ptr<cuda::<PERSON><PERSON><PERSON>uffer> device_buffer;
    explicit CudaChannel(size_t buffer_size) {
      device_buffer = std::make_unique<cuda::Devi<PERSON>Buffer>(buffer_size);
    }
    CudaChannel(size_t buffer_size, int priority) : device_context(0, priority) {
      device_buffer = std::make_unique<cuda::DeviceBuffer>(buffer_size);
    }
  };

  CudaChannelManager() = default;
  ~CudaChannelManager() = default;

  bool AddCudaChannel(const std::string& channel_name, size_t buffer_size, int priority = 0);
  bool HasCudaChannel(const std::string& channel_name) const {
    return channel_map_.count(channel_name);
  }
  CudaChannel* FindMutableCudaChannel(const std::string& channel_name);

 private:
  std::unordered_map<std::string, CudaChannel> channel_map_;

  DISALLOW_COPY_AND_ASSIGN(CudaChannelManager);
};

} // namespace base
