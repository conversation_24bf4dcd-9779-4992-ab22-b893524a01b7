// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>
//          <PERSON><PERSON> (<EMAIL>)

#include <utility>

#include "base/cuda/device_object_base.h"

#include "gtest/gtest.h"

namespace cuda {

namespace {
class ExampleObject : public DeviceObjectBase {
 public:
  explicit ExampleObject(void* dev_ptr, size_t size) : DeviceObjectBase(dev_ptr, size) {
    create_field1(10);
    create_field2(20);
  }

 private:
  DEVICE_OBJECT_FIELD(int, field1);
  DEVICE_OBJECT_FIELD(double, field2);
};
}  // namespace

TEST(DeviceObjectBase, BasicTest) {
  char dev_ptr[10 * sizeof(int) + 20 * sizeof(double)];
  ExampleObject example_object(dev_ptr, (10 * sizeof(int) + 20 * sizeof(double)));
  EXPECT_EQ(example_object.field1().size(), 10);
  EXPECT_EQ(example_object.field2().size(), 20);
}

}  // namespace cuda
