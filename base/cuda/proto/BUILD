package(default_visibility = ["//visibility:public"])

load("@com_github_grpc_grpc//bazel:cc_grpc_library.bzl", "cc_grpc_library")
load("@rules_proto//proto:defs.bzl", "proto_library")
load("//build/bazel_rules:walle_proto_library.bzl", "walle_proto_library")

walle_proto_library(
    name = "calib_param_proto",
    srcs = [
        "calib_param.proto",
    ],
)

walle_proto_library(
    name = "model_server_proto",
    srcs = ["model_server.proto"],
)

cc_grpc_library(
    name = "cc_model_server_grpc",
    srcs = [":model_server_proto"],
    grpc_only = True,
    deps = [":cc_model_server_proto"],
)
