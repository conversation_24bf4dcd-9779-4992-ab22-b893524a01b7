syntax = "proto3";

package base.trt;

// Next-id: 10
message CalibrationConfig {
  enum CalibratorType {
    ENTROPY = 0;
    MINMAX = 1;
  }
  enum QuantizeInfoFeedType {
    DataCalibration = 0;
    TensorRange = 1;
    QuantizeLayer = 2;
  }
  string calib_data_path = 1;
  string calib_table_path = 2;
  CalibratorType calib_type = 3;
  int32 calib_batch_size = 4;
  string onnx_model_path = 5;
  string trt_engine_path = 6;
  QuantizeInfoFeedType trt_quantize_feed_type = 7;
  string range_file_path = 8;
  repeated string fallback_layers = 9;
}

// Next-id: 2
message CalibrationData {
  repeated CalibrationDataSample sample = 1;
}

// Next-id: 2
message CalibrationDataSample {
  repeated CalibrationInputNode node = 1;
}

// Next-id: 2
message CalibrationInputNode {
  repeated float data = 1;
}

// Next-id: 2
message CalibrationTable {
  bytes data = 1;
}

// Next-id: 2
message RangeTable {
  map<string, float> data = 1;
}
