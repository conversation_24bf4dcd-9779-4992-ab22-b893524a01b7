syntax = "proto3";

service ModelServer {
  rpc load_model(LoadModelRequest) returns (LoadModelResponse) {}
  rpc heartbeat(HeartbeatRequest) returns (HeartbeatResponse) {}
}

// Next id: 10
message LoadModelRequest {
  string model_name = 1;       // Model name defined in simulation system
  string model_version = 2;    // Model version defined in simulation system
  string model_file_name = 3;  // Model file name
  string repo_key = 4;         // Identify model repositoy
  string uuid = 5;             // first time set empty, then model server will generate it
  // the number of model servers from client to current model server
  int32 hop_count = 6;
  InferMode infer_mode = 7;  // 1: local modelserving, 2: remote modelserving
  string plugin_id = 8;      // model plugin id, used to locate custom plugin
  string model_id = 9;       // used only in simulaiton: model config file md5
}

// Next id: 9
message LoadModelResponse {
  string err_msg = 1;            // Error message
  string ser_model_name = 2;     // Model name defined in inference service
  string ser_model_version = 3;  // Model name defined in inference service
  string infer_ser = 4;     // Inference service address
  string model_ser = 5;     // Model Server address
  string uuid = 6;          // Generated by model server
  int32 gpu_memory_usage = 7;  // The memory usage of the loaded model, in megabytes (M)
  string model_ser_worker_id = 8; // Model Server worker id
}


// Next id: 2
message HeartbeatRequest {
  string uuid = 1;
}

// Next id: 2
message HeartbeatResponse {
  LoadModelResponse load_model_response = 1;
}

enum InferMode {
  NativeGPU = 0;
  LOCAL = 1;
  REMOTE = 2;
}