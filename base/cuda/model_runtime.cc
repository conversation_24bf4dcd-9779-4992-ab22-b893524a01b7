// Copyright @2022 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/cuda/model_runtime.h"

namespace base {

size_t GetDimSize(const nvinfer1::Dims& dims) {
  size_t size = 1;
  for (int i = 0; i < dims.nbDims; ++i) {
    size *= dims.d[i];
  }
  return size;
}

size_t GetTypeSize(nvinfer1::DataType t) {
  switch (t) {
    case nvinfer1::DataType::kINT32:
      return sizeof(int32_t);
    case nvinfer1::DataType::kFLOAT:
      return sizeof(float);
    case nvinfer1::DataType::kHALF:
      return sizeof(float) >> 1;
    case nvinfer1::DataType::kINT8:
      return sizeof(uint8_t);
    case nvinfer1::DataType::kBOOL:
      return sizeof(bool);
    default:
      LOG(FATAL) << "Invalid data type";
  }
}

ModelRuntime::ModelRuntime(const std::string& engine_file, cuda::DeviceContext* context)
    : device_context_(CHECK_NOTNULL(context)), engine_file_(engine_file) {}

void ModelRuntime::UpdateDeviceContext(cuda::DeviceContext* context) { device_context_ = context; }

}  // namespace base
