// Copyright @2024 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

// This file is pre-included in .cu files by NVCC

extern "C" {
/**
 * @brief make nvcc (at least in CUDA 11.4) generate a stable hash ID for anonymous namespaces
 *
 * Basically, `nvvm/bin/cicc` of CUDA generates symbols for CUDA `__global__` kernel functions
 *     and `__constant__` in-GPU variables:
 * 1. If a global kernel or variable is in a named namespace,
 *   a. then its symbol name is just like normal C++ functions and return.
 * 2. Otherwise, its symbol should contain an irregular `ID` to ensure the symbol will be unique
 *   a. the name for an anonymous namespace in symbol is `_GLOBAL__N__${output_file_name}_***_${ID}`
 *   a. and `__constant__` variables are moved to `::_INTERLNAL_${output_file_name}_***_${ID}`
 * 3. Now the remaining question is how to generate the `ID`.
 *
 * According tests on CUDA 11.4, the process is:
 * 1. Try to find a non-`static` and non-template variable or function in all named namespaces
 *   a. Search from top to bottom.
 *   b. If found, then generate a hash ID from its symbol name and return.
 * 2. Generate an ID of 2 parts joined by the `_` character and return:
 *   a. the 1st part is the symbol hash of a first variable or function (in an anonymous namespace)
 *   b. the 2nd part is generated from current timestamp
 *
 * So here use an inline variable to ensure NVCC generate a stable hash ID.
 */
__attribute__((visibility("hidden"))) inline int __cuda_use_stable_id_for_anonymous_ns =
    0;  // NOLINT
}
