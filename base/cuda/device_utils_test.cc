// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include <algorithm>
#include <memory>
#include <numeric>
#include <utility>
#include <vector>

#include "gtest/gtest.h"

#include "base/cuda/device_buffer.h"
#include "base/cuda/device_utils.h"
#include "base/cuda/host_buffer.h"
#include "common/execution_unit_framework/utils/time_recorder.h"

namespace cuda {
namespace {

constexpr int kNumBytes = 100;
constexpr size_t k8MB = 1 << 22;
constexpr int kGpuId = 0;
constexpr int kPriority = -5;

TEST(DeviceUtilsTest, BasicDeviceContext) {
  DeviceContext ctx;
  DeviceBuffer buffer(kNumBytes);
  DeviceBuffer buffer2(kNumBytes);
  std::vector<char> src(kNumBytes);
  std::vector<char> dst(kNumBytes);
  std::iota(src.begin(), src.end(), 0);

  buffer.FromCpu(src.data(), kNumBytes, &ctx);
  buffer2.FromGpu(buffer.data(), kNumBytes, &ctx);
  buffer2.ToCpu(dst.data(), kNumBytes, &ctx);
  ctx.Sync();

  EXPECT_EQ(ctx.gpu_id(), 0);
  for (int i = 0; i < kNumBytes; i++) {
    EXPECT_EQ(src[i], dst[i]);
  }
}

TEST(DeviceUtilsTest, BasicDeviceTimer) {
  DeviceContext ctx1(kGpuId, kPriority);
  DeviceContext ctx2;
  DeviceTimer dev_timer1(ctx1);
  DeviceTimer dev_timer2(ctx2);
  walle::TimeRecorder cpu_timer;

  HostBuffer host_buf1(k8MB);
  DeviceBuffer dev_buf1(k8MB);
  HostBuffer host_buf2(k8MB);
  DeviceBuffer dev_buf2(k8MB);

  for (int i = 0; i < 5; i++) {
    cpu_timer.Start();

    dev_timer1.Start();
    dev_buf1.FromCpu(host_buf1.data(), k8MB, &ctx1);
    dev_timer1.Stop();

    dev_timer2.Start();
    dev_buf2.ToCpu(host_buf2.mutable_data(), k8MB, &ctx2);
    dev_timer2.Stop();

    ctx1.Sync();
    ctx2.Sync();
    cpu_timer.End("Host Latency");
    const float device_time_sum = dev_timer1.ms() + dev_timer2.ms();
    EXPECT_GT(device_time_sum, cpu_timer.GetTimeDiff());
  }
}

TEST(DeviceUtilsTest, ReuseStreamTest) {
  DeviceContext ctx0;
  DeviceContext ctx1(ctx0.gpu_id(), ctx0.stream());
  EXPECT_EQ(ctx0.stream(), ctx1.stream());
  EXPECT_EQ(ctx0.gpu_id(), ctx1.gpu_id());
}


}  // namespace
}  // namespace cuda
