// Copyright @2025 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <memory>
#include <string>
#include <unordered_map>
#include <unordered_set>
#include <vector>

#include "base/cuda/flags.h"
#include "base/cuda/host_buffer.h"
#include "base/cuda/model_runtime.h"
#include "common/execution_unit_framework/utils/time_recorder.h"
#include "common/model/model_config_manager.h"

namespace base {

class TorchInferenceEngine {
 public:
#if defined(__x86_64__) && defined(USE_CUDA)
  TorchInferenceEngine(const walle::ModelConfig& model_config, cuda::DeviceContext* context);
  virtual ~TorchInferenceEngine() = default;

  void Run(int batch_size = 1, bool sync = false);

  void AsyncCopyOutputBuffersD2H(cuda::DeviceContext* context);
  void RegisterBindingBuffer(const std::string& binding_name, void* data);
#else
  TorchInferenceEngine(const walle::ModelConfig& model_config, cuda::DeviceContext* context) {}
  void Run(int batch_size = 1, bool sync = false) {}
  void AsyncCopyOutputBuffersD2H(cuda::DeviceContext* context) {}
  void RegisterBindingBuffer(const std::string& binding_name, void* data) {}
#endif

  // device buffer api
  const cuda::DeviceBuffer& GetInputDeviceBufferOrDie(const std::string& input_buffer_name) const {
    return base::FindOrDie(input_device_buffers_, input_buffer_name);
  }
  cuda::DeviceBuffer* GetMutableInputDeviceBufferOrDie(const std::string& input_buffer_name) {
    return base::FindOrDie(&input_device_buffers_, input_buffer_name);
  }
  const cuda::DeviceBuffer& GetOutputDeviceBufferOrDie(
      const std::string& output_buffer_name) const {
    return base::FindOrDie(output_device_buffers_, output_buffer_name);
  }
  cuda::DeviceBuffer* GetMutableOutputDeviceBufferOrDie(const std::string& output_buffer_name) {
    return base::FindOrDie(&output_device_buffers_, output_buffer_name);
  }

  // host buffer api
  const cuda::HostBuffer& GetInputHostBufferOrDie(const std::string& input_buffer_name) const {
    return base::FindOrDie(input_host_buffers_, input_buffer_name);
  }
  cuda::HostBuffer* GetMutableInputHostBufferOrDie(const std::string& input_buffer_name) {
    return base::FindOrDie(&input_host_buffers_, input_buffer_name);
  }
  const cuda::HostBuffer& GetOutputHostBufferOrDie(const std::string& output_buffer_name) const {
    return base::FindOrDie(output_host_buffers_, output_buffer_name);
  }
  cuda::HostBuffer* GetMutableOutputHostBufferOrDie(const std::string& output_buffer_name) {
    return base::FindOrDie(&output_host_buffers_, output_buffer_name);
  }

  // raw ptr api
  void* GetMutableInputDeviceBufferRawPtrOrDie(const std::string& input_buffer_name) {
    return base::FindOrDie(&input_device_buffers_, input_buffer_name)->mutable_data();
  }
  void* GetMutableInputHostBufferRawPtrOrDie(const std::string& input_buffer_name) {
    return base::FindOrDie(&input_host_buffers_, input_buffer_name)->mutable_data();
  }
  void* GetMutableOutputDeviceBufferRawPtrOrDie(const std::string& output_buffer_name) {
    return base::FindOrDie(&output_device_buffers_, output_buffer_name)->mutable_data();
  }
  void* GetMutableOutputHostBufferRawPtrOrDie(const std::string& output_buffer_name) {
    return base::FindOrDie(&output_host_buffers_, output_buffer_name)->mutable_data();
  }

  const std::unordered_map<std::string, ModelRuntime::BindingInfo>& GetInputBindingInfo() const {
    return model_->input_binding_infos();
  }

  const std::unordered_map<std::string, ModelRuntime::BindingInfo>& GetOutputBindingInfo() const {
    return model_->output_binding_infos();
  }

 private:
  void InitializeBuffers();

  std::unique_ptr<ModelRuntime> model_ = nullptr;
  std::unique_ptr<walle::ModelConfigManager> config_manager_ = nullptr;
  cuda::DeviceContext* device_context_ = nullptr;
  std::unordered_map<std::string, cuda::DeviceBuffer> input_device_buffers_;
  std::unordered_map<std::string, cuda::DeviceBuffer> output_device_buffers_;
  std::unordered_map<std::string, cuda::HostBuffer> input_host_buffers_;
  std::unordered_map<std::string, cuda::HostBuffer> output_host_buffers_;
  std::unique_ptr<walle::TimeRecorder> recorder_;
  std::vector<void*> bindings_;
  std::vector<void*> host_bindings_;
  std::unordered_set<std::string> d2h_copy_output_binding_names_;

  DISALLOW_COPY_AND_ASSIGN(TorchInferenceEngine);
};

}  // namespace base
