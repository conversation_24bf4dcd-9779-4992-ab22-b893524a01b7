// Copyright @2022 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <string>
#include <unordered_map>
#include <vector>

#include "base/common/field_macros.h"
#include "base/container/utils.h"
#include "base/cuda/device_buffer.h"
#include "base/cuda/model_runtime.h"
#include "common/model/model_config_manager.h"
#include "walle/common/proto/model/model_config.pb.h"

namespace base {

class MockData {
  using DeviceDataMap = std::unordered_map<std::string, std::vector<float>>;
  using BindingInfoMap = std::unordered_map<std::string, ModelRuntime::BindingInfo>;

 public:
  MockData() = default;

 private:
  MUTABLE_FIELD(DeviceDataMap, input_datas);
  MUTABLE_FIELD(DeviceDataMap, output_datas);
  MUTABLE_FIELD(BindingInfoMap, input_binding_infos);
  MUTABLE_FIELD(BindingInfoMap, output_binding_infos);
};

class MockModelRuntime : public ModelRuntime {
 public:
  MockModelRuntime(const walle::ModelConfigManager& model_config_manager,
                   cuda::DeviceContext* context);

  virtual ~MockModelRuntime();
  void UpdateMockData(const MockData& mock_data) {
    input_binding_infos_ = mock_data.input_binding_infos();
    output_binding_infos_ = mock_data.output_binding_infos();
  }
  void Enqueue(size_t batch_size, bool use_cuda_graph = false) {}

 private:
  DISALLOW_COPY_AND_ASSIGN(MockModelRuntime);
};

}  // namespace base
