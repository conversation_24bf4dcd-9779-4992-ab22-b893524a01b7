// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/cuda/trt_inference_engine.h"

#include <utility>

#include "base/cuda/operators/tensor_validity_op.h"
#include "common/tools/performance/profiler_shared_resources.h"
#include "walle/common/tracing/tracing.h"

namespace base {

namespace {

constexpr int kDelayBeforeFatalIfAnyInvalidTensor = 10;

}  // namespace

TrtInferenceEngine::TrtInferenceEngine(const walle::ModelConfig& model_config,
                                       cuda::DeviceContext* context,
                                       bool use_model_serving)
    : device_context_(CHECK_NOTNULL(context)) {
  TRACE_EVENT(walle::kTraceCategoryInference, "TrtInferenceEngine::TrtInferenceEngine");
  LOG(INFO) << strings::Format("Construct TrtInferenceEngine with engine_file: {}",
                               model_config.engine_file());
  config_manager_ = std::make_unique<walle::ModelConfigManager>(model_config);
  use_model_serving_ = use_model_serving;
  use_triton_runtime_ = device_context_->use_triton_runtime();
  GenerateEngine();
  if (FLAGS_use_trt_mock_engine) {
    model_ = std::make_unique<base::MockModelRuntime>(*config_manager_, context);
  } else if (cuda::GetInferMode(use_triton_runtime_) != cuda::InferMode::kNativeGPU) {
    model_ = std::make_unique<base::triton::TritonRuntime>(model_config, context);
  } else if (use_model_serving) {
    model_ = std::make_unique<base::ModelServingRuntime>(model_config, context);
  } else {
    model_ = std::make_unique<base::TrtRuntimeModel>(model_config, context);
  }
  InitializeBuffers();
  recorder_ = std::make_unique<walle::TimeRecorder>();
  if (cuda::GetInferMode(use_triton_runtime_) == cuda::InferMode::kNativeGPU) {
#ifdef __x86_64__
    if (config_manager_->max_batch_size() == 1) {
      use_cuda_graph_ = config_manager_->use_cuda_graph();
    }
#else
    use_cuda_graph_ = config_manager_->use_cuda_graph();
#endif
    if (use_cuda_graph_) {
      LOG(ERROR) << "TrtEngine Inferences in CudaGraph Mode";
      model_->InitCudaGraph();
    }
    if (use_model_serving) {
      return;
    }
    this->Run(config_manager_->min_batch_size());
    device_context_->Sync();
  }
}

void TrtInferenceEngine::UpdateMockData(const MockData& mock_data) {
  if (!FLAGS_use_trt_mock_engine) {
    return;
  }
  for (const auto& input_data_pair : mock_data.input_datas()) {
    const std::string& name = input_data_pair.first;
    const std::vector<float>& input_data = input_data_pair.second;
    cuda::DeviceBuffer* input_buffer = GetMutableInputDeviceBufferOrDie(name);
    input_buffer->FromCpu(reinterpret_cast<const void*>(input_data.data()),
                          input_data.size() * sizeof(float),
                          device_context_);
  }
  for (const auto& output_data_pair : mock_data.output_datas()) {
    const std::string& name = output_data_pair.first;
    const std::vector<float>& output_data = output_data_pair.second;
    cuda::DeviceBuffer* output_buffer = GetMutableOutputDeviceBufferOrDie(name);
    output_buffer->FromCpu(reinterpret_cast<const void*>(output_data.data()),
                           output_data.size() * sizeof(float),
                           device_context_);
  }
  device_context_->Sync();
  MockModelRuntime* mock_model = dynamic_cast<MockModelRuntime*>(model_.get());
  if (mock_model) {
    mock_model->UpdateMockData(mock_data);
  }
}

void TrtInferenceEngine::GenerateEngine() {
  if (FLAGS_use_trt_mock_engine) {
    return;
  }
  if (cuda::GetInferMode(use_triton_runtime_) != cuda::InferMode::kNativeGPU) return;
  if (use_model_serving_) return;
  const std::string& weight_file = config_manager_->weight_file();
  const std::string& trt_engine_file_path = config_manager_->engine_file();
  if (file_path::Exists(trt_engine_file_path)) {
    return;
  }
  CHECK(file_path::Exists(weight_file)) << weight_file << " does not exist";
  base::TrtOnnxParser::GenerateTrtEngine(config_manager_.get());
  CHECK(file_path::Exists(trt_engine_file_path)) << trt_engine_file_path << " does not exist";
}

void TrtInferenceEngine::InitializeBuffers() {
  const bool do_check_tensor_validity = !use_triton_runtime_ && config_manager_ != nullptr &&
                                        config_manager_->model_config().check_tensor_validity();
  bindings_.clear();
  bindings_.resize(model_->input_binding_infos().size() + model_->output_binding_infos().size());
  host_bindings_.clear();
  host_bindings_.resize(model_->input_binding_infos().size() +
                        model_->output_binding_infos().size());
  validity_checker_.reset();
  ValidityChecker::BindingInfoMap check_map;

  for (const auto& input_entry : model_->input_binding_infos()) {
    const std::string& binding_name = input_entry.first;
    const ModelRuntime::BindingInfo& binding_info = input_entry.second;
    input_device_buffers_.emplace(
        std::piecewise_construct, std::make_tuple(binding_name), std::make_tuple(binding_info.buffer_size));
    input_host_buffers_.emplace(
        std::piecewise_construct, std::make_tuple(binding_name), std::make_tuple(binding_info.buffer_size));
    input_device_buffers_.at(binding_name).Memset(0, device_context_);
    input_host_buffers_.at(binding_name).Memset(0);
    bindings_[binding_info.binding_index] = input_device_buffers_.at(binding_name).mutable_data();
    host_bindings_[binding_info.binding_index] =
        input_host_buffers_.at(binding_name).mutable_data();
  }
  for (const auto& output_entry : model_->output_binding_infos()) {
    const std::string& binding_name = output_entry.first;
    const ModelRuntime::BindingInfo& binding_info = output_entry.second;
    output_device_buffers_.emplace(
        std::piecewise_construct, std::make_tuple(binding_name), std::make_tuple(binding_info.buffer_size));
    output_device_buffers_.at(binding_name).Memset(0, device_context_);
    bindings_[binding_info.binding_index] = output_device_buffers_.at(binding_name).mutable_data();
    const bool need_check_validity = do_check_tensor_validity || FLAGS_check_gpu_tensor_validity;
    if (need_check_validity && ValidityChecker::DataTypeNeedCheck(binding_info.data_type)) {
      check_map.emplace(binding_name, &binding_info);
    }
  }
  if (config_manager_->output_names().empty() || config_manager_->input_names().empty()) {
    LOG(FATAL) << "Can not get input/output node name from model config file.";
  }
  for (const auto& output_name : config_manager_->output_names()) {
    CHECK(model_->output_binding_infos().count(output_name)) << "Can not find " << output_name <<
      " in engine binding info, please check output node in model config is updated";
    const ModelRuntime::BindingInfo& binding_info = model_->output_binding_infos().at(output_name);
    output_host_buffers_.emplace(
      std::piecewise_construct, std::make_tuple(output_name), std::make_tuple(binding_info.buffer_size));
    output_host_buffers_.at(output_name).Memset(0);
    host_bindings_[binding_info.binding_index] =
        output_host_buffers_.at(output_name).mutable_data();
  }
  if (cuda::GetInferMode(use_triton_runtime_) != cuda::InferMode::kNativeGPU) {
    model_->AssignBindings(bindings_, host_bindings_);
  } else {
    model_->AssignBindings(bindings_);
  }

  if (check_map.size() > 0) {
    validity_checker_ = std::make_unique<ValidityChecker>(
        model_->name(), bindings_, std::move(check_map), device_context_);
  } else if (do_check_tensor_validity) {
    LOG(FATAL) << "Can not check validity of bound tensors in " << model_->name();
  }
}

void TrtInferenceEngine::RegisterBindingBuffer(const std::string& binding_name,
                                               void* data,
                                               bool release_original_device_buffer) {
  if ((cuda::GetInferMode() != cuda::InferMode::kNativeGPU) || use_cuda_graph_) {
    LOG(FATAL) << "RegisterBindingBuffer should only be used in kNativeGPU InferMode + cuda graph disabled.";
    return;
  }
  int binding_index = -1;
  const std::unordered_map<std::string, ModelRuntime::BindingInfo>& input_binding_infos =
      model_->input_binding_infos();
  const std::unordered_map<std::string, ModelRuntime::BindingInfo>& output_binding_infos =
      model_->output_binding_infos();
  if (input_binding_infos.count(binding_name)) {
    binding_index = input_binding_infos.at(binding_name).binding_index;
  } else if (output_binding_infos.count(binding_name)) {
    binding_index = output_binding_infos.at(binding_name).binding_index;
  } else {
    LOG(FATAL) << "Fail to find binding index with name " << binding_name;
  }
  CHECK_GE(binding_index, 0);
  if (release_original_device_buffer && input_device_buffers_.count(binding_name) &&
      input_device_buffers_.at(binding_name).size() > 0) {
    input_device_buffers_.erase(binding_name);
    input_device_buffers_.emplace(binding_name, cuda::DeviceBuffer(0));
  }
  bindings_[binding_index] = CHECK_NOTNULL(data);
  model_->AssignBindings(bindings_);
}

void TrtInferenceEngine::Run(int batch_size, bool sync) {
  TRACE_EVENT(walle::kTraceCategoryInference, "TrtInferenceEngine::Run");
  if (FLAGS_use_trt_mock_engine) {
    return;
  }
  const bool do_sync = sync || FLAGS_trt_log_level > 1;
  if (do_sync) {
#ifdef __x86_64__
    if (FLAGS_cpuprofile_frequency > 0) {
      common::tools::performance::ToggleCpuProfilerPaused(true);
    }
#endif
    recorder_->Start();
  }
  if (validity_checker_) {
    validity_checker_->AssertLastResult();
  }
  model_->Enqueue(batch_size, use_cuda_graph_);
  if (validity_checker_) {
    validity_checker_->CheckAsync();
  }
  if (do_sync) {
    device_context_->Sync();
    recorder_->End(model_->name());
#ifdef __x86_64__
    if (FLAGS_cpuprofile_frequency > 0) {
      common::tools::performance::ToggleCpuProfilerPaused(false);
    }
#endif
  }
}

void TrtInferenceEngine::ConfigureOutputBufferNamesForAsyncCopy(const std::string& output_name) {
  if (d2h_copy_output_binding_names_.count(output_name) == 0) {
    d2h_copy_output_binding_names_.insert(output_name);
  }
}

void TrtInferenceEngine::AsyncCopyOutputBuffersD2H(cuda::DeviceContext* context) {
  if (d2h_copy_output_binding_names_.empty()) {
    for (const auto& output_name : config_manager_->output_names()) {
        cuda::DeviceBuffer* output_buffer_ptr = base::FindOrDie(&output_device_buffers_, output_name);
        void* output_host_buffer_raw_ptr = GetMutableOutputHostBufferRawPtrOrDie(output_name);
        output_buffer_ptr->ToCpu(output_host_buffer_raw_ptr, output_buffer_ptr->size(), context);
    }
  } else {
    for (const auto& output_name : d2h_copy_output_binding_names_) {
        cuda::DeviceBuffer* output_buffer_ptr = base::FindOrDie(&output_device_buffers_, output_name);
        void* output_host_buffer_raw_ptr = GetMutableOutputHostBufferRawPtrOrDie(output_name);
        output_buffer_ptr->ToCpu(output_host_buffer_raw_ptr, output_buffer_ptr->size(), context);
    }
  }
}

bool TrtInferenceEngine::CheckTensorValidity() {
  CHECK(validity_checker_) << ": No tensor validity check enabled for " << model_->name();
  return validity_checker_->GetResult();
}

void TrtInferenceEngine::UpdateDeviceContext(cuda::DeviceContext* context) {
  device_context_ = CHECK_NOTNULL(context);
  model_->UpdateDeviceContext(device_context_);
  if (validity_checker_) {
    validity_checker_->UpdateDeviceContext(context);
  }
  ClearBuffers();
}

void TrtInferenceEngine::ClearBuffers() {
  for (auto& each_item : input_device_buffers_) {
    each_item.second.Memset(0, device_context_);
  }
  for (auto& each_item : output_device_buffers_) {
    each_item.second.Memset(0, device_context_);
  }
  for (auto& each_item : input_host_buffers_) {
    each_item.second.Memset(0);
  }
  for (auto& each_item : output_host_buffers_) {
    each_item.second.Memset(0);
  }
  device_context_->Sync();
}

bool TrtInferenceEngine::ValidityChecker::DataTypeNeedCheck(nvinfer1::DataType data_type) {
  return data_type == nvinfer1::DataType::kFLOAT || data_type == nvinfer1::DataType::kHALF;
}

TrtInferenceEngine::ValidityChecker::ValidityChecker(const std::string& name,
                                                     const std::vector<void*>& bindings,
                                                     BindingInfoMap&& check_map,
                                                     cuda::DeviceContext* device_context)
    : name_(name),
      check_map_(std::move(check_map)),
      validity_device_buffer_(sizeof(int) * 2 * check_map_.size()),
      validity_host_buffer_(sizeof(int) * 2 * check_map_.size()),
      bindings_(bindings),
      device_context_(device_context) {
  validity_device_buffer_.Memset(0, device_context);
  validity_host_buffer_.Memset(0);
  device_context->Sync();
  LOG(ERROR) << "Check validity of " << check_map_.size() << " bindings in " << name_;
}

void TrtInferenceEngine::ValidityChecker::AssertLastResult() {
  if (!GetResult()) {
    if (first_failed_passed_frame_cnt_ == 0) {
      first_failed_passed_frame_cnt_ = 1;
    }
    LOG(ERROR) << "Invalid tensor(s) found in " << name_
               << " at counter = " << first_failed_passed_frame_cnt_;
  }
  if (first_failed_passed_frame_cnt_ > 0) {
    first_failed_passed_frame_cnt_++;
    if (first_failed_passed_frame_cnt_ > kDelayBeforeFatalIfAnyInvalidTensor + 1) {
      LOG(FATAL) << "Invalid tensor(s) found in " << name_ << " , so exit";
    }
  }
}

void TrtInferenceEngine::ValidityChecker::CheckAsync() {
  validity_device_buffer_.Memset(0, device_context_);
  validity_host_buffer_.Memset(0);
  int* const checked_result = reinterpret_cast<int*>(validity_device_buffer_.mutable_data());
  int index = 0;
  for (const auto& iter : check_map_) {
    const ModelRuntime::BindingInfo& binding_info = *iter.second;
    const nvinfer1::DataType data_type = binding_info.data_type;
    const void* const output_data = bindings_[binding_info.binding_index];
    cuda::CheckTensorValidity(output_data,
                              data_type,
                              binding_info.buffer_size,
                              device_context_->stream(),
                              checked_result + index * 2);
    index++;
  }
  validity_device_buffer_.ToCpu(validity_host_buffer_.mutable_data(), validity_device_buffer_.size(), device_context_);
}

bool TrtInferenceEngine::ValidityChecker::GetResult() {
  const int* const all_validity = reinterpret_cast<const int*>(validity_host_buffer_.data());
  int index = 0;
  int error_num = 0;
  for (const auto& iter : check_map_) {
    const std::string& name = iter.first;
    const int* const tensor_validity = all_validity + index * 2;
    const int inf_num = tensor_validity[0];
    const int nan_num = tensor_validity[1];
    if (inf_num > 0 || nan_num > 0) {
      LOG(ERROR) << "Invalid I/O tensor \"" << name << "\" with inf = " << inf_num
                 << " and nan = " << nan_num << " in " << name_;
      error_num++;
    }
    index++;
  }
  return error_num == 0;
}

void TrtInferenceEngine::ValidityChecker::UpdateDeviceContext(cuda::DeviceContext* context) {
  device_context_ = CHECK_NOTNULL(context);
}

}  // namespace base
