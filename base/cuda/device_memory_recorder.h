// Copyright @2023 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>
// Design Doc at: https://km.sankuai.com/collabpage/1612494744

#pragma once

#include <memory>
#include <string>
#include <unordered_map>

#if defined(USE_CUDA) && defined(__x86_64__)
#include "base/cuda/device_buffer.h"
#include "nvml.h"
#endif

#include "base/common/macros.h"
#include "base/cuda/macros.h"

namespace base {

class DeviceMemoryRecorder {
 public:
  DeviceMemoryRecorder();
  virtual ~DeviceMemoryRecorder();
  void Update(const std::string& name, bool print_out = false);
  void IncreaseParentMemoryUsage(const std::string& parent_name, const std::string& child_name) {
    device_memory_usage_map_[parent_name] += device_memory_usage_map_[child_name];
  }
  double Get(const std::string& name) { return device_memory_usage_map_[name]; }

 private:
  double GetDeviceMemoryUsedMB();

#if defined(USE_CUDA) && defined(__x86_64__)
  nvmlDevice_t device_;
  double last_device_memory_used_mb_ = 0.0;
  std::unique_ptr<cuda::DeviceBuffer> dummy_dev_buf_;
  bool init_failure_ = false;
#endif
  std::unordered_map<std::string, double> device_memory_usage_map_;

  DISALLOW_COPY_AND_ASSIGN(DeviceMemoryRecorder);
};

}  // namespace base
