package(default_visibility = ["//visibility:public"])

cc_library(
    name = "model_serving_runtime",
    srcs = ["model_serving_runtime.cc"],
    hdrs = [
        "model_serving_runtime.h",
    ],
    implementation_deps = [
        "//base/cuda/tensorrt:trt_utils",
        "//third_party/triton_client:grpc_client",
        "@com_github_grpc_grpc//:grpc++",
    ],
    deps = [
        "//base/common:environment",
        "//base/cuda:device_buffer",
        "//base/cuda:device_utils",
        "//base/cuda:model_runtime",
        "//common/data_engine/proto:cc_model_serving_grpc",
        "//common/data_engine/proto:cc_model_serving_proto",
        "//walle/common/proto/model:cc_model_config_proto",
        "@com_google_absl//absl/time",
        "@cuda",
        "@glog",
        "@gtest",
    ],
)
