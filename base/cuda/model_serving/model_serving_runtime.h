// Copyright @2024 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <string>
#include <unordered_map>
#include <vector>

#include "base/cuda/model_runtime.h"
#include "common/data_engine/proto/model_serving.grpc.pb.h"
#include "walle/common/proto/model/model_config.pb.h"

namespace base {

class ModelServingRuntime : public ModelRuntime {
 public:
  ModelServingRuntime(const walle::ModelConfig& model_config, cuda::DeviceContext* context);
  ~ModelServingRuntime() override;

  void Enqueue(size_t batch_size, bool use_cuda_graph) override;
  void AssignBindings(const std::vector<void*>& bindings) override;
  void AssignStringInputOutputPtrs(
      std::unordered_map<std::string, std::vector<std::string>>* string_input_map,
      std::unordered_map<std::string, std::vector<std::string>>* string_output_map);

 private:
  void InitializeBindingInfo();
  bool StartCudaIPC();
  bool StopCudaIPC();
  bool CallInference();
  bool ResetCudaIpcHandle();

  walle::ModelConfig model_config_;
  std::string client_token_;
  std::string server_addr_;
  int32_t device_id_ = 0;
  std::vector<cudaIpcMemHandle_t> cuda_ipc_handle_;
  std::unique_ptr<walle::data_engine::model_serving::ModelServing::Stub> stub_;
  std::unordered_map<std::string, std::vector<std::string>*> string_input_map_;
  std::unordered_map<std::string, std::vector<std::string>*> string_output_map_;
};

}  // namespace base
