// Copyright @2024 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/cuda/model_serving/model_serving_runtime.h"

#include <functional>
#include <thread>
#include "grpcpp/grpcpp.h"

#include "base/common/environment.h"
#include "base/cuda/macros.h"
#include "base/cuda/tensorrt/trt_utils.h"
#include "common/data_engine/proto/model_serving_interface.pb.h"

namespace base {
namespace {

constexpr char kClientTokenParamName[] = "client_token";
constexpr char kDeviceIDParamName[] = "device_id";
constexpr int32_t kRetryCount = 2;
constexpr int32_t kCudaSharedMemoryTimeout = 3600;

using walle::data_engine::model_serving_proto::CudaIPCResponse;

template <typename T>
bool CommonChecker(const std::string&, const T& input);

template <>
bool CommonChecker<grpc::Status>(const std::string&, const grpc::Status& input) {
  return input.ok();
}

template <>
bool CommonChecker<CudaIPCResponse>(const std::string&, const CudaIPCResponse& input) {
  return input.success();
}

template <>
bool CommonChecker(const std::string& name, const std::pair<grpc::Status, CudaIPCResponse>& input) {
  return CommonChecker(name, input.first) && CommonChecker(name, input.second);
}

template <typename FuncRetT>
bool CommonRetryPolicy(const std::string& name, int32_t exec_count, const FuncRetT&) {
  if (exec_count > kRetryCount) {
    return false;
  }
  LOG(ERROR) << strings::Format("CommonRetryPolicy@[{}]: retrying {}", name, exec_count);
  std::this_thread::sleep_for(std::chrono::seconds(static_cast<int>(std::pow(2, exec_count - 1))));
  return true;
}

template <>
bool CommonRetryPolicy<std::pair<grpc::Status, CudaIPCResponse>>(
    const std::string& name,
    int32_t exec_count,
    const std::pair<grpc::Status, CudaIPCResponse>& ret) {
  if (!CommonChecker(name, ret.second)) {
    LOG(ERROR) << strings::Format(
        "CommonRetryPolicy@[{}]: CudaIPCResponse check failed {}", name, exec_count);
    return false;
  }
  return CommonRetryPolicy(name, exec_count, ret.first);
}

template <typename T>
void CommonErrorHandler(const std::string& name, const T&);

template <>
void CommonErrorHandler(const std::string& name, const grpc::Status& status) {
  LOG(ERROR) << strings::Format(
      "CommonErrorHandler@[{}]: error msg: {}", name, status.error_message());
}

template <>
void CommonErrorHandler(const std::string& name,
                        const std::pair<grpc::Status, CudaIPCResponse>& ret) {
  return CommonErrorHandler(name, ret.first);
}

template <typename FuncT, typename... ArgsT>
using FuncRetT = std::result_of_t<FuncT(ArgsT...)>;

template <typename RetT, typename ParamT, typename... ArgsT>
using HandlerT = const std::function<RetT(std::string, ArgsT..., ParamT)>&;

template <typename FuncT, typename... ArgsT>
bool Retry(
    const std::string& func_name,
    FuncT func,
    HandlerT<void, FuncRetT<FuncT, ArgsT...>> on_success = nullptr,
    HandlerT<void, FuncRetT<FuncT, ArgsT...>> on_error =
        &CommonErrorHandler<FuncRetT<FuncT, ArgsT...>>,
    HandlerT<bool, FuncRetT<FuncT, ArgsT...>, int32_t> retry_policy =
        &CommonRetryPolicy<FuncRetT<FuncT, ArgsT...>>,
    HandlerT<bool, FuncRetT<FuncT, ArgsT...>> checker = &CommonChecker<FuncRetT<FuncT, ArgsT...>>,
    ArgsT... args) {
  int32_t exec_count = 0;
  bool retry_next = false;
  do {
    auto ret = func(args...);
    exec_count++;
    bool check_ret = checker(func_name, ret);
    if (check_ret) {
      if (on_success != nullptr) {
        on_success(func_name, ret);
      }
      return true;
    }
    if (on_error != nullptr) {
      on_error(func_name, ret);
    }
    retry_next = retry_policy(func_name, exec_count, ret);
  } while (retry_next);
  return false;
}

}  // namespace

ModelServingRuntime::ModelServingRuntime(const walle::ModelConfig& model_config,
                                         cuda::DeviceContext* context)
    : ModelRuntime(model_config.engine_file(), context), model_config_(model_config) {
  base::Environment env;
  std::string device_id_str;
  bool succeed = env.GetVar("CUDA_VISIBLE_DEVICES", &device_id_str);
  if (!succeed) {
    device_id_ = 0;
  } else {
    device_id_ = strings::FromString<int32_t>(device_id_str);
  }
  server_addr_ = FLAGS_grpc_model_server;
  stub_ = walle::data_engine::model_serving::ModelServing::NewStub(
      grpc::CreateChannel(server_addr_, grpc::InsecureChannelCredentials()));
  InitializeBindingInfo();
}

ModelServingRuntime::~ModelServingRuntime() { StopCudaIPC(); }

void ModelServingRuntime::Enqueue(size_t batch_size, bool use_cuda_graph) { CallInference(); }

void ModelServingRuntime::AssignBindings(const std::vector<void*>& bindings) {
  CHECK_EQ(bindings.size(), input_binding_infos_.size() + output_binding_infos_.size());
  bindings_ = bindings;
  ResetCudaIpcHandle();
  if (!client_token_.empty()) {
    StopCudaIPC();
  }
  StartCudaIPC();
  CHECK(!client_token_.empty()) << strings::Format("ModelServingRuntime@{} AssignBindings failed",
                                                   model_config_.model_name());
}

void ModelServingRuntime::InitializeBindingInfo() {
  input_binding_infos_.clear();
  output_binding_infos_.clear();
  const nvinfer1::DataType model_data_type =
      trt::ConvertToTensorRTDataType(model_config_.data_type());
  size_t binding_idx = 0;
  for (const auto& input_node_config : model_config_.input_node()) {
    const std::string& node_name = input_node_config.node_name();
    const walle::IntArray& node_shape = input_node_config.node_shape();
    nvinfer1::Dims dims;
    dims.nbDims = node_shape.dim_size();
    for (int j = 0; j < node_shape.dim_size(); ++j) {
      dims.d[j] = static_cast<int32_t>(node_shape.dim(j));
    }
    const nvinfer1::DataType data_type =
        input_node_config.has_precision()
            ? trt::ConvertToTensorRTDataType(input_node_config.precision())
            : model_data_type;
    const size_t buffer_size = GetDimSize(dims) * GetTypeSize(data_type);
    input_binding_infos_[node_name] = {data_type, dims, buffer_size, binding_idx};
    binding_idx++;
  }
  for (const auto& output_node_config : model_config_.output_node()) {
    const std::string& node_name = output_node_config.node_name();
    output_name_list_.push_back(node_name);
    const walle::IntArray& node_shape = output_node_config.node_shape();
    nvinfer1::Dims dims;
    dims.nbDims = node_shape.dim_size();
    for (int j = 0; j < node_shape.dim_size(); ++j) {
      dims.d[j] = static_cast<int32_t>(node_shape.dim(j));
    }
    const nvinfer1::DataType data_type =
        output_node_config.has_precision()
            ? trt::ConvertToTensorRTDataType(output_node_config.precision())
            : model_data_type;
    const size_t buffer_size = GetDimSize(dims) * GetTypeSize(data_type);
    output_binding_infos_[node_name] = {data_type, dims, buffer_size, binding_idx};
    binding_idx++;
  }
}

void ModelServingRuntime::AssignStringInputOutputPtrs(
    std::unordered_map<std::string, std::vector<std::string>>* string_input_map,
    std::unordered_map<std::string, std::vector<std::string>>* string_output_map) {
  for (auto& pair : *string_input_map) {
    string_input_map_[pair.first] = &pair.second;
  }
  for (auto& pair : *string_output_map) {
    string_output_map_[pair.first] = &pair.second;
  }
}

bool ModelServingRuntime::StartCudaIPC() {
  walle::data_engine::model_serving_proto::StartCudaIPCRequest request;
  request.set_model_name(model_config_.model_name());
  request.set_device_id(device_id_);
  request.set_timeout(kCudaSharedMemoryTimeout);
  for (const auto& input_binding_info : input_binding_infos_) {
    auto* node_config = request.add_node_config();
    const BindingInfo& info = input_binding_info.second;
    node_config->set_name(input_binding_info.first);
    node_config->set_raw_handle((char*)&cuda_ipc_handle_[info.binding_index],
                                sizeof(cudaIpcMemHandle_t));
    node_config->set_buffer_size(info.buffer_size);
  }
  for (const auto& output_binding_info : output_binding_infos_) {
    auto* node_config = request.add_node_config();
    const BindingInfo& info = output_binding_info.second;
    node_config->set_name(output_binding_info.first);
    node_config->set_raw_handle((char*)&cuda_ipc_handle_[info.binding_index],
                                sizeof(cudaIpcMemHandle_t));
    node_config->set_buffer_size(info.buffer_size);
  }
  return Retry(
      "StartCudaIPC",
      [this, &request]() {
        grpc::ClientContext client_context;
        CudaIPCResponse response;
        grpc::Status status = stub_->StartCudaIPC(&client_context, request, &response);
        return std::make_pair(status, response);
      },
      [this](const std::string&, const std::pair<grpc::Status, CudaIPCResponse>& ret) {
        client_token_ = ret.second.client_token();
        LOG(INFO) << strings::Format("StartCudaIPC succeed, client_token: {}", client_token_);
      });
}

bool ModelServingRuntime::StopCudaIPC() {
  walle::data_engine::model_serving_proto::StopCudaIPCRequest request;
  request.set_model_name(model_config_.model_name());
  request.set_device_id(device_id_);
  request.set_client_token(client_token_);
  return Retry(
      "StopCudaIPC",
      [this, &request]() {
        grpc::ClientContext client_context;
        CudaIPCResponse response;
        grpc::Status status = stub_->StopCudaIPC(&client_context, request, &response);
        return std::make_pair(status, response);
      },
      [this](const std::string&, const std::pair<grpc::Status, CudaIPCResponse>&) {
        client_token_.clear();
      });
}

bool ModelServingRuntime::CallInference() {
  if (client_token_.empty()) {
    LOG(ERROR) << strings::Format("CudaIPC not started, model_name: {}",
                                  model_config_.model_name());
    return false;
  }
  walle::data_engine::model_serving_proto::InferenceRequest request;
  request.set_model_name(model_config_.model_name());
  auto* client_token_proto = request.add_input_param();
  client_token_proto->set_name(kClientTokenParamName);
  client_token_proto->set_string_value(client_token_);
  auto* device_id_proto = request.add_input_param();
  device_id_proto->set_name(kDeviceIDParamName);
  device_id_proto->set_int_value(device_id_);
  for (const std::pair<std::string, std::vector<std::string>*>& input_node : string_input_map_) {
    auto* input_param_proto = request.add_input_param();
    input_param_proto->set_name(input_node.first);
    input_param_proto->set_string_value(
        (*input_node.second)[0]);  // Currently we only support batch_size 1.
  }
  return Retry("CallInference", [this, &request]() {
    grpc::ClientContext client_context;
    walle::data_engine::model_serving_proto::InferenceResponse response;
    grpc::Status status = stub_->Inference(&client_context, request, &response);
    for (const auto& output_param : response.output_param()) {
      if (!string_output_map_.count(output_param.name()) || !output_param.has_string_value()) {
        continue;
      }
      std::vector<std::string>* string_output_vector_ = string_output_map_[output_param.name()];
      string_output_vector_->clear();
      string_output_vector_->emplace_back(
          output_param.string_value());  // Currently we only support batch_size 1.
    }
    return status;
  });
}

bool ModelServingRuntime::ResetCudaIpcHandle() {
  cuda_ipc_handle_.resize(bindings_.size());
  for (size_t i = 0; i < bindings_.size(); i++) {
    CUDA_CHECK(cudaIpcGetMemHandle(&cuda_ipc_handle_[i], bindings_[i]));
  }
  return true;
}

}  // namespace base
