// Copyright @2023 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include <numeric>
#include <vector>

#include "gtest/gtest.h"

#include "base/cuda/device_buffer.h"
#include "base/cuda/device_context_manager.h"

namespace cuda {
namespace {

constexpr char kModuleName[] = "Perception";
constexpr int kNumContext = 3;
constexpr int kGpuId = 0;
constexpr int kNumBytes = 100;

TEST(DeviceContextManagerTest, BasicTest) {
  DeviceContextManager ctx_group(kModuleName, kGpuId, kNumContext);
  DeviceContext* ctx = ctx_group.GetDeviceContext();

  DeviceBuffer buffer(kNumBytes);
  DeviceBuffer buffer2(kNumBytes);
  std::vector<char> src(kNumBytes);
  std::vector<char> dst(kNumBytes);
  std::iota(src.begin(), src.end(), 0);

  buffer.FromCpu(src.data(), kNumBytes, ctx);
  buffer2.FromGpu(buffer.data(), kNumBytes, ctx);
  buffer2.ToCpu(dst.data(), kNumBytes, ctx);
  ctx->Sync();
  EXPECT_EQ(ctx->gpu_id(), kGpuId);
  for (int i = 0; i < kNumBytes; i++) {
    EXPECT_EQ(src[i], dst[i]);
  }
}

}  // namespace
}  // namespace cuda