// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/cuda/cuda_channel_manager.h"

#include "base/container/utils.h"

namespace base {

bool CudaChannelManager::AddCudaChannel(const std::string& channel_name,
                                        size_t buffer_size,
                                        int priority) {
  if (HasCudaChannel(channel_name)) {
    return false;
  }
  channel_map_.emplace(std::piecewise_construct,
                       std::make_tuple(channel_name),
                       std::make_tuple(buffer_size, priority));
  return true;
}

CudaChannelManager::CudaChannel* CudaChannelManager::FindMutableCudaChannel(
    const std::string& channel_name) {
  return base::FindOrNull(&channel_map_, channel_name);
}

}  // namespace base
