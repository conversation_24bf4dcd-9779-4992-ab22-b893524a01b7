// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include <algorithm>
#include <memory>
#include <numeric>
#include <utility>
#include <vector>

#include "gtest/gtest.h"

#include "base/cuda/device_buffer.h"

namespace cuda {
namespace {

constexpr int kNumBytes = 100;

TEST(DeviceBufferTest, HostDeviceHost) {
  DeviceBuffer dev_buf(kNumBytes);

  std::vector<char> src(kNumBytes, 1);
  std::vector<char> dst(kNumBytes, -1);
  std::iota(src.begin(), src.end(), 0);

  // test cpy
  dev_buf.FromCpu(src.data(), kNumBytes, cuda::DeviceContext::kDefault);
  dev_buf.ToCpu(dst.data(), kNumBytes, cuda::DeviceContext::kDefault);

  for (int i = 0; i < kNumBytes; i++) {
    EXPECT_EQ(src[i], dst[i]);
  }

  // test clean
  dev_buf.Clean(cuda::DeviceContext::kDefault);
  std::fill(dst.begin(), dst.end(), -1);

  dev_buf.FromCpu(src.data(), kNumBytes, cuda::DeviceContext::kDefault);
  dev_buf.ToCpu(dst.data(), kNumBytes, cuda::DeviceContext::kDefault);

  for (int i = 0; i < kNumBytes; i++) {
    EXPECT_EQ(src[i], dst[i]);
  }
}

TEST(DeviceBufferTest, HostDeviceDeviceHost) {
  DeviceBuffer dev_buf(kNumBytes);
  DeviceBuffer dev_buf2(kNumBytes);

  std::vector<char> src(kNumBytes, 1);
  std::vector<char> dst(kNumBytes, -1);
  std::iota(src.begin(), src.end(), 0);

  dev_buf.FromCpu(src.data(), kNumBytes, cuda::DeviceContext::kDefault);
  dev_buf2.FromGpu(dev_buf.data(), dev_buf.size(), cuda::DeviceContext::kDefault);
  dev_buf2.ToCpu(dst.data(), dev_buf2.size(), cuda::DeviceContext::kDefault);

  for (int i = 0; i < kNumBytes; i++) {
    EXPECT_EQ(src[i], dst[i]);
  }
}

TEST(DeviceBufferTest, FromDevicePtr) {
  std::vector<char> src(kNumBytes, 1);
  std::vector<char> dst(kNumBytes, -1);
  std::iota(src.begin(), src.end(), 0);

  DeviceBuffer dev_buf(kNumBytes);
  dev_buf.FromCpu(src.data(), dev_buf.size(), cuda::DeviceContext::kDefault);

  // construct from raw gpu ptr
  void* raw_ptr = dev_buf.mutable_data();
  DeviceBuffer dev_buf2(raw_ptr, dev_buf.size());
  dev_buf2.ToCpu(dst.data(), dev_buf2.size(), cuda::DeviceContext::kDefault);

  for (int i = 0; i < kNumBytes; i++) {
    EXPECT_EQ(src[i], dst[i]);
  }
}

TEST(DeviceBufferTest, MoveConstruct) {
  std::vector<char> src(kNumBytes, 1);
  std::vector<char> dst(kNumBytes, -1);
  std::iota(src.begin(), src.end(), 0);

  DeviceBuffer dev_buf(kNumBytes);
  dev_buf.FromCpu(src.data(), dev_buf.size(), cuda::DeviceContext::kDefault);

  DeviceBuffer dev_buf2(std::move(dev_buf));
  EXPECT_EQ(dev_buf.size(), 0);
  EXPECT_EQ(dev_buf.data(), nullptr);
  dev_buf2.ToCpu(dst.data(), dev_buf2.size(), cuda::DeviceContext::kDefault);

  for (int i = 0; i < kNumBytes; i++) {
    EXPECT_EQ(src[i], dst[i]);
  }
}

TEST(DeviceBufferTest, MoveAssign) {
  std::vector<char> src(kNumBytes, 1);
  std::vector<char> dst(kNumBytes, -1);
  std::iota(src.begin(), src.end(), 0);

  DeviceBuffer dev_buf(kNumBytes);
  dev_buf.FromCpu(src.data(), dev_buf.size(), cuda::DeviceContext::kDefault);

  DeviceBuffer dev_buf2(kNumBytes * 2);
  dev_buf2 = std::move(dev_buf);
  EXPECT_EQ(dev_buf.size(), kNumBytes * 2);
  EXPECT_NE(dev_buf.data(), nullptr);
  dev_buf2.ToCpu(dst.data(), dev_buf2.size(), cuda::DeviceContext::kDefault);

  for (int i = 0; i < kNumBytes; i++) {
    EXPECT_EQ(src[i], dst[i]);
  }

  DeviceBuffer dev_buf3(&src, kNumBytes * 3);
  dev_buf3 = std::move(dev_buf);
  EXPECT_EQ(dev_buf.size(), kNumBytes * 3);
  EXPECT_EQ(dev_buf.data(), &src);
  EXPECT_FALSE(dev_buf.own_memory());
}

TEST(DeviceBufferTest, SetBytes) {
  std::vector<uint8_t> src(kNumBytes, 255);
  std::vector<uint8_t> dst(kNumBytes, 0);

  DeviceBuffer dev_buf(kNumBytes);
  dev_buf.Memset(255, cuda::DeviceContext::kDefault);
  dev_buf.ToCpu(dst.data(), kNumBytes, cuda::DeviceContext::kDefault);

  for (int i = 0; i < kNumBytes; i++) {
    EXPECT_EQ(src[i], dst[i]);
  }
}

TEST(DeviceBufferTest, DeviceBufferNotOwned) {
  DeviceBuffer dev_buf(kNumBytes);
  int offset = kNumBytes / 2;

  std::vector<char> src(kNumBytes, 1);
  std::vector<char> dst(kNumBytes, -1);
  std::iota(src.begin(), src.end(), 0);

  char* begin = reinterpret_cast<char*>(dev_buf.mutable_data());
  DeviceBuffer dev_buf_0(begin, offset);
  DeviceBuffer dev_buf_1(begin + offset, offset);

  dev_buf_0.FromCpu(src.data(), offset, cuda::DeviceContext::kDefault);
  dev_buf_1.FromCpu(src.data() + offset, offset, cuda::DeviceContext::kDefault);
  dev_buf.ToCpu(dst.data(), dev_buf.size(), cuda::DeviceContext::kDefault);

  for (int i = 0; i < kNumBytes; i++) {
    EXPECT_EQ(src[i], dst[i]);
  }
}

TEST(DeviceBufferTest, DeviceBufferViewTest) {
  DeviceBuffer buffer(100);
  DeviceBufferView<int> buffer_view = buffer.View<int>();
  EXPECT_TRUE(buffer_view.data() != nullptr);
  std::vector<int> x(25, 1);
  std::vector<int> y(25, 0);
  DeviceContext context(0);
  buffer_view.FromCpu(x.data() + 5, 5, 10, &context);
  buffer_view.ToCpu(y.data() + 5, 5, 10, &context);
  context.Sync();
  for (int i = 0; i < 25; ++i) {
    EXPECT_EQ(y[i], i >= 5 && i < 15 ? 1 : 0);
  }

  buffer_view.FromCpu(x.data(), &context);
  buffer_view.ToCpu(y.data(), &context);
  context.Sync();
  for (int i = 0; i < 25; ++i) {
    EXPECT_EQ(y[i], 1);
  }

  std::vector<int> v(25, 0);
  DeviceBuffer buffer2(100);
  DeviceBufferView<int> buffer_view2 = buffer2.View<int>();
  buffer_view2.FromGpu(buffer_view.data(), 5, 10, &context);
  buffer_view2.ToCpu(v.data() + 5, 5, 10, &context);
  context.Sync();
  for (int i = 0; i < 25; ++i) {
    EXPECT_EQ(v[i], i >= 5 && i < 15 ? 1 : 0);
  }

  ConstDeviceBufferView<int> const_device_view = buffer.ConstView<int>();
  std::vector<int> z(25, 0);
  const_device_view.ToCpu(z.data(), &context);
  context.Sync();
  for (int i = 0; i < 25; ++i) {
    EXPECT_EQ(z[i], 1);
  }

  EXPECT_EQ(buffer_view.View(10, 5).size(), 5);
  EXPECT_EQ(buffer_view.ConstView(10, 5).size(), 5);
  EXPECT_EQ(const_device_view.ConstView(10, 5).size(), 5);

  // Test fill
  buffer_view.Fill(0, &context);
  buffer_view.ToCpu(y.data(), &context);
  context.Sync();
  for (int i = 0; i < 25; ++i) {
    EXPECT_EQ(y[i], 0);
  }
}

}  // namespace
}  // namespace cuda
