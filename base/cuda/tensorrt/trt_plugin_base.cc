// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/cuda/tensorrt/trt_plugin_base.h"

namespace base {
namespace trt {

namespace {
constexpr char kOpParamsName[] = "op_params";
}

std::vector<nvinfer1::PluginField> TrtPluginBase::attributes_ = {
    nvinfer1::PluginField(kOpParamsName, nullptr, nvinfer1::PluginFieldType::kUNKNOWN, -1),
};

nvinfer1::PluginFieldCollection TrtPluginBase::field_collection_{
    .nbFields = static_cast<int>(TrtPluginBase::attributes_.size()),
    .fields = TrtPluginBase::attributes_.data()};

}  // namespace trt
}  // namespace base
