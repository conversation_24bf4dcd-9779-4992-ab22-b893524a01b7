// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/cuda/tensorrt/trt_quantize_utils.h"

namespace base {
namespace trt {

std::unordered_map<std::string, float> GetTensorRangeMap(const std::string& range_map_file) {
  CHECK(file_path::Exists(range_map_file)) << "can't find range_map_file, please check the path";
  trt::RangeTable range_table;
  CHECK(base::ReadTextProtoFile(range_map_file, &range_table)) << "fail to read range map file";

  return std::unordered_map<std::string, float>(range_table.data().begin(),
                                                range_table.data().end());
}

void SetNetworkDynamicRange(const std::unordered_map<std::string, float>& tensor_range_map,
                            nvinfer1::INetworkDefinition* network) {
  CHECK(network != nullptr);
  LOG(INFO) << "Setting per tensor dynamic range";

  // set dynamic range for network input tensors
  for (int i = 0; i < network->getNbInputs(); ++i) {
    nvinfer1::ITensor* tensor = CHECK_NOTNULL(network->getInput(i));
    std::string tensor_name = tensor->getName();
    if (tensor_range_map.find(tensor_name) != tensor_range_map.end()) {
      CHECK(tensor->setDynamicRange(-tensor_range_map.at(tensor_name),
                                    tensor_range_map.at(tensor_name)))
        << "Can't set dynamic range for tensor: " << tensor_name;
    }
  }

  // set dynamic range for layer output tensors
  for (int i = 0; i < network->getNbLayers(); ++i) {
    nvinfer1::ILayer* layer = CHECK_NOTNULL(network->getLayer(i));
    for (int j = 0; j < layer->getNbOutputs(); ++j) {
      std::string tensor_name = layer->getOutput(j)->getName();
      if (tensor_range_map.find(tensor_name) != tensor_range_map.end()) {
        CHECK(layer->getOutput(j)->setDynamicRange(-tensor_range_map.at(tensor_name),
                                                   tensor_range_map.at(tensor_name)))
            << "Can't set dynamic range for output tensor: " << tensor_name;
      } else {
        if (layer->getType() == nvinfer1::LayerType::kCONSTANT)
          LOG(ERROR) << "Missing dynamic range for CONSTANT layer output tensor: " << tensor_name;
          // TODO(xiaqing) add support for CONSTANT layer output
      }
    }
  }
}

void QuantizeWithDynamicRange(const trt::CalibrationConfig& calib_config,
                              nvinfer1::INetworkDefinition* network) {
  CHECK(network != nullptr);
  const std::unordered_map<std::string, float> tensor_range_map =
      GetTensorRangeMap(calib_config.range_file_path());
  SetNetworkDynamicRange(tensor_range_map, network);
}

void SetFallbackLayerPrecision(const google::protobuf::RepeatedPtrField<std::string>& fallback_layers_proto,
                               nvinfer1::INetworkDefinition* network) {
  CHECK(network != nullptr);
  if (fallback_layers_proto.empty()) {
    return;
  }
  std::unordered_map<std::string, bool> fallback_layers_map;
  for (auto it = fallback_layers_proto.begin(); it != fallback_layers_proto.end(); it++) {
    fallback_layers_map.emplace(*it, false);
  }
  for (int i = 0; i < network->getNbLayers(); ++i) {
    nvinfer1::ILayer* layer = CHECK_NOTNULL(network->getLayer(i));
    const std::string& layer_name = layer->getName();
    if (fallback_layers_map.count(layer_name) > 0) {
      LOG(INFO) << "Setting layer " << layer_name << " to FP32.";
      layer->setPrecision(nvinfer1::DataType::kFLOAT);
      fallback_layers_map[layer_name] = true;
      for (int j = 0; j < layer->getNbOutputs(); ++j) {
        const std::string& tensor_name = layer->getOutput(j)->getName();
        // Set output type of execution tensors.
        if (layer->getOutput(j)->isExecutionTensor()) {
          LOG(INFO) << "Setting layer " << layer_name << "'s tensor: " << tensor_name << " to FP32.";
          layer->setOutputType(j, nvinfer1::DataType::kFLOAT);
        }
      }
    }
  }
  for (const auto& it : fallback_layers_map) {
    const bool layer_included_in_trt_model = it.second;
    if (!layer_included_in_trt_model) {
      const std::string& layer_name = it.first;
      LOG(FATAL) << "Failed to find layer " << layer_name << " in the current TRT model.";
    }
  }
}

void SetFallbackModulePrecision(const std::vector<std::string>& int8_module_prefix_list,
                                nvinfer1::INetworkDefinition* network) {
  CHECK(network != nullptr);
  if (!int8_module_prefix_list.size()) {
    return;
  }
  for (int i = 0; i < network->getNbLayers(); ++i) {
    nvinfer1::ILayer* layer = CHECK_NOTNULL(network->getLayer(i));
    const std::string& layer_name = layer->getName();
    bool is_fallback_layer = true;
    for (std::string prefix : int8_module_prefix_list) {
      if (layer_name.compare(0, prefix.length(), prefix) == 0) {
        is_fallback_layer = false;
      }
    }
    if (is_fallback_layer) {
      if (layer->getPrecision() == nvinfer1::DataType::kINT32 ||
          layer->getPrecision() == nvinfer1::DataType::kBOOL) {
        continue;
      }
      if (layer->getType() == nvinfer1::LayerType::kCONSTANT &&
          static_cast<nvinfer1::IConstantLayer*>(layer)->getWeights().type ==
              nvinfer1::DataType::kINT32) {
        continue;
      }
      if (layer->getType() == nvinfer1::LayerType::kPLUGIN_V2) {
        continue;
      }
      if (layer->getNbInputs() >= 1 && layer->getInput(0)->isShapeTensor()) {
        continue;
      }
      if (layer->getNbInputs() >= 1 &&
          (layer->getInput(0)->getType() == nvinfer1::DataType::kINT32 ||
           layer->getInput(0)->getType() == nvinfer1::DataType::kBOOL))
        continue;
      if (layer->getNbOutputs() >= 1 &&
          (layer->getOutput(0)->getType() == nvinfer1::DataType::kINT32 ||
           layer->getOutput(0)->getType() == nvinfer1::DataType::kBOOL)) {
        continue;
      }
      LOG(ERROR) << "Setting layer " << layer_name << " to FP16";
      layer->setPrecision(nvinfer1::DataType::kHALF);
      for (int j = 0; j < layer->getNbOutputs(); ++j) {
        const std::string& tensor_name = layer->getOutput(j)->getName();
        if (layer->getOutput(j)->isShapeTensor()) {
          continue;
        }
        if (layer->getOutput(j)->getType() == nvinfer1::DataType::kINT32) {
          continue;
        }
        if (layer->getOutput(j)->getType() == nvinfer1::DataType::kBOOL) {
          continue;
        }
        if (layer->getOutput(j)->isExecutionTensor()) {
          LOG(ERROR) << "Setting layer " << layer_name << "'s tensor: " << tensor_name
                     << " to FP16";
          layer->setOutputType(j, nvinfer1::DataType::kHALF);
        }
      }
    }
  }
}

}  // namespace trt
}  // namespace base
