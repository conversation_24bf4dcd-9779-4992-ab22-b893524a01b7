// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <memory>
#include <string>
#include <vector>

#include "NvInfer.h"
#include "NvOnnxParser.h"

#include "base/cuda/flags.h"
#include "base/cuda/tensorrt/trt_calibrator.h"
#include "base/cuda/tensorrt/trt_profiler.h"
#include "base/cuda/tensorrt/trt_quantize_utils.h"
#include "base/cuda/tensorrt/trt_runtime_model.h"
#include "base/cuda/tensorrt/trt_utils.h"
#include "base/file/file_path_util.h"
#include "base/file/file_util.h"
#include "common/model/model_config_manager.h"

namespace base {

class TrtOnnxParser {
 public:
  using NetworkManipulator = std::function<void(nvinfer1::INetworkDefinition*)>;

  static void GenerateTrtEngine(const walle::ModelConfigManager* config_manager,
                                NetworkManipulator manipulator = nullptr);

  static void GetNetworkInputsOutputsName(const std::string& onnx_file,
                                          std::vector<std::string>* inputs_name,
                                          std::vector<std::string>* outputs_name);
};

}  // namespace base
