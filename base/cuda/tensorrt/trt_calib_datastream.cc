// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/cuda/tensorrt/trt_calib_datastream.h"

namespace base {
namespace trt {

void TrtCalibDataStream::Init() {
  CHECK(file_path::Exists(calib_data_path_)) << " Can't Find CalibData in " << calib_data_path_;
  CHECK_GT(calib_batch_size_, 0);
  ReadCalibData();
  num_batches_ = total_calib_sample_num_ / calib_batch_size_;
  if (total_calib_sample_num_ % calib_batch_size_ != 0) {
    LOG(ERROR) << "[Attention]: calib data in " << calib_data_path_
               << " cannot be divisible by batch_size " << calib_batch_size_;
  }
}

bool TrtCalibDataStream::IsFirstBatch() { return batch_count_ == 1; }

void TrtCalibDataStream::ReadCalibData() {
  CHECK(!file_path::IsDirectory(calib_data_path_))
      << "File path " << calib_data_path_ << " is not a directory. ";
  if (!ReadBinaryProtoFile(calib_data_path_, &calib_data_)) {
    LOG(FATAL) << "Failed reading calib file in " << calib_data_path_;
  }
  CHECK_GT(calib_data_.sample_size(), 0);
  total_calib_sample_num_ = static_cast<int>(calib_data_.sample_size());
}

bool TrtCalibDataStream::GetBatch(std::vector<CalibrationDataSample>* batch_samples) {
  if (batch_count_ >= num_batches_) {
    return false;
  }
  batch_samples->clear();
  for (int batch_sample_idx = 0; batch_sample_idx < calib_batch_size_; ++batch_sample_idx) {
    const int global_idx = batch_count_ * calib_batch_size_ + batch_sample_idx;
    batch_samples->emplace_back(calib_data_.sample(global_idx));
  }
  ++batch_count_;
  return true;
}
}  // namespace trt
}  // namespace base
