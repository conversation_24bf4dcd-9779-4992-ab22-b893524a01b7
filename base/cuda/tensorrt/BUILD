package(default_visibility = ["//visibility:public"])

cc_library(
    name = "trt_runtime_model",
    srcs = ["trt_runtime_model.cc"],
    hdrs = ["trt_runtime_model.h"],
    deps = [
        ":trt_utils",
        ":trt_profiler",
        "//base/container:utils",
        "//base/cuda:device_buffer",
        "//base/cuda:flags",
        "//base/cuda:model_runtime",
        "//base/file:file_path_util",
        "//base/file:file_util",
        "//common/tools/simulation:flags",
        "@cuda",
        "@tensorrt",
    ],
)

cc_library(
    name = "trt_onnx_parser",
    srcs = ["trt_onnx_parser.cc"],
    hdrs = ["trt_onnx_parser.h"],
    deps = [
        ":trt_calibrator",
        ":trt_quantize_utils",
        ":trt_runtime_model",
        ":trt_timing_cache",
        ":trt_utils",
        "//base/cuda:flags",
        "//base/cuda/tensorrt:trt_profiler",
        "//base/file:file_path_util",
        "//base/file:file_util",
        "//common/model:model_config_manager",
        "@tensorrt",
    ],
)

cc_library(
    name = "trt_profiler",
    srcs = ["trt_profiler.cc"],
    hdrs = ["trt_profiler.h"],
    deps = [
        "//base/common:macros",
        "//base/cuda:flags",
        "//base/file:file_path_util",
        "//base/file:file_util",
        "//base/strings:format",
        "@com_google_absl//absl/strings",
        "@cuda",
        "@tensorrt",
    ],
)

cc_test(
    name = "trt_profiler_test",
    srcs = ["trt_profiler_test.cc"],
    data = [
        "//common/test:resources",
    ],
    tags = [
        "ci",
        "ci_gpu",
        "no_asan",
    ],
    deps = [
        ":trt_onnx_parser",
        ":trt_profiler",
        "//base/cuda:trt_inference_engine",
        "//base/cuda/proto:cc_calib_param_proto",
        "//base/testing:test_main",
    ],
)

cc_library(
    name = "trt_utils",
    srcs = ["trt_utils.cc"],
    hdrs = ["trt_utils.h"],
    deps = [
        "//base/common:macros",
        "//base/strings:format",
        "//walle/common/proto/model:cc_model_config_proto",
        "@cuda",
        "@tensorrt",
    ],
)

cc_test(
    name = "trt_utils_test",
    srcs = ["trt_utils_test.cc"],
    data = [
        "//common/test:resources",
    ],
    tags = [
        "ci",
        "ci_gpu",
    ],
    deps = [
        ":trt_onnx_parser",
        ":trt_profiler",
        ":trt_utils",
        "//base/testing:test_main",
    ],
)

cc_library(
    name = "trt_plugin_params",
    srcs = ["trt_plugin_params.cc"],
    hdrs = ["trt_plugin_params.h"],
    deps = [
        "//base/common:macros",
        "//base/container:enum_class_hash",
        "//base/container:utils",
        "//walle/common/proto/cuda:cc_operator_params_proto",
        "@cuda",
        "@gtest",
        "@tensorrt",
    ],
)

cc_test(
    name = "trt_plugin_params_test",
    srcs = ["trt_plugin_params_test.cc"],
    deps = [
        ":trt_plugin_params",
        "//base/cuda/operators/utils:op_params",
        "//base/testing:test_main",
    ],
)

cc_library(
    name = "trt_plugin_base",
    srcs = ["trt_plugin_base.cc"],
    hdrs = ["trt_plugin_base.h"],
    deps = [
        ":trt_plugin_params",
        "//base/common:macros",
        "//base/container:utils",
        "@cuda",
        "@tensorrt",
    ],
)

cc_test(
    name = "trt_plugin_base_test",
    srcs = ["trt_plugin_base_test.cc"],
    tags = [
        "ci",
        "ci_cpu",
    ],
    deps = [
        ":trt_plugin_base",
        "//base/cuda/operators/utils:op_params",
        "//base/testing:test_main",
    ],
)

cc_library(
    name = "trt_plugin_dynamic_base",
    srcs = ["trt_plugin_dynamic_base.cc"],
    hdrs = ["trt_plugin_dynamic_base.h"],
    deps = [
        ":trt_plugin_base",
        ":trt_plugin_params",
        "//base/common:macros",
        "//base/container:utils",
        "//base/cuda/operators/utils:op_params",
        "@cuda",
        "@tensorrt",
    ],
)

cc_test(
    name = "trt_plugin_dynamic_base_test",
    srcs = ["trt_plugin_dynamic_base_test.cc"],
    tags = [
        "ci",
        "ci_cpu",
    ],
    deps = [
        ":trt_plugin_dynamic_base",
        "//base/cuda/operators/utils:op_params",
        "//base/testing:test_main",
    ],
)

cc_library(
    name = "trt_calibrator",
    srcs = [
        "trt_calibrator.cc",
    ],
    hdrs = ["trt_calibrator.h"],
    deps = [
        ":trt_calib_datastream",
        "//base/cuda:device_buffer",
        "//base/cuda/proto:cc_calib_param_proto",
        "//base/file:file_path_util",
        "//base/file:file_util",
        "//common/model:model_config_manager",
        "@tensorrt",
    ],
)

cc_library(
    name = "trt_calib_datastream",
    srcs = [
        "trt_calib_datastream.cc",
    ],
    hdrs = ["trt_calib_datastream.h"],
    deps = [
        "//base/cuda:tensor",
        "//base/cuda/proto:cc_calib_param_proto",
        "//base/file:file_path_util",
        "//base/file:file_util",
    ],
)

cc_library(
    name = "trt_quantize_utils",
    srcs = [
        "trt_quantize_utils.cc",
    ],
    hdrs = ["trt_quantize_utils.h"],
    deps = [
        "//base/cuda/proto:cc_calib_param_proto",
        "//base/file:file_path_util",
        "//base/file:file_util",
        "@cuda",
        "@tensorrt",
    ],
)

cc_library(
    name = "trt_timing_cache",
    srcs = [
        "trt_timing_cache.cc",
    ],
    hdrs = ["trt_timing_cache.h"],
    deps = [
        "//base/common:macros",
        "@cuda",
        "@glog",
        "@tensorrt",
    ],
)

cc_test(
    name = "trt_quantize_utils_test",
    srcs = ["trt_quantize_utils_test.cc"],
    data = [
        "//common/test:resources",
    ],
    tags = [
        "ci",
        "ci_cpu",
    ],
    deps = [
        ":trt_quantize_utils",
        "//base/testing:test_main",
    ],
)

cc_binary(
    name = "trt_engine_generator_main",
    srcs = ["trt_engine_generator_main.cc"],
    linkopts = ["-Wl,--strip-all"],
    deps = [
        "//base/cuda:device_utils",
        "//base/cuda:trt_inference_engine",
        "//common/model:model_config_manager",
        "//modules/perception/onboard/common/custom_ops/tensorrt_plugins:all_tensorrt_plugins",
    ],
)
