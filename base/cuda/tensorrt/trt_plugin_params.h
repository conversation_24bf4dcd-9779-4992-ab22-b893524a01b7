// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <memory>
#include <string>
#include <unordered_map>
#include <vector>

#include "NvInfer.h"
#include "gtest/gtest.h"

#include "base/common/macros.h"
#include "base/container/utils.h"
#include "walle/common/proto/cuda/operator_params.pb.h"

namespace base {
namespace trt {

class TrtPluginParams {
 public:
  TrtPluginParams();
  virtual ~TrtPluginParams();

  void CopyDataTo(TrtPluginParams* dst_param) const;
  void Serialize(void* buffer) const;
  void Deserialize(const void* buffer, int length);
  void CreateFromPluginFC(const nvinfer1::PluginFieldCollection& fc);

  size_t size() const { return size_; }

  const cuda::GroupOpParamsMeta& group_op_params_meta() const {
    return *CHECK_NOTNULL(group_op_params_meta_).get();
  }

 private:
  std::unique_ptr<cuda::GroupOpParamsMeta> group_op_params_meta_ = nullptr;
  int size_ = 0;

  FRIEND_TEST(TrtPluginParamsTest, BasicTest);
  DISALLOW_COPY_AND_ASSIGN(TrtPluginParams);
};

}  // namespace trt
}  // namespace base
