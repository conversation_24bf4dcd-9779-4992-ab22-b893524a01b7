// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/cuda/tensorrt/trt_utils.h"

namespace base {
namespace trt {

namespace {

constexpr int kBorderLength = 150;

std::string GetDimString(const nvinfer1::Dims& dims) {
  std::stringstream ss;
  ss << "[";
  for (int i = 0; i < dims.nbDims; i++) {
    if (i > 0) {
      ss << ", ";
    }
    ss << dims.d[i];
  }
  ss << "]";
  return ss.str();
}

std::string GetTypeString(nvinfer1::DataType t) {
  switch (t) {
    case nvinfer1::DataType::kINT32:
      return "kINT32";
    case nvinfer1::DataType::kFLOAT:
      return "kFLOAT";
    case nvinfer1::DataType::kHALF:
      return "kHALF";
    case nvinfer1::DataType::kINT8:
      return "kINT8";
    case nvinfer1::DataType::kBOOL:
      return "kBOOL";
    default:
      return "kUNKNOWN";
  }
}
}  // namespace

nvinfer1::DataType ConvertToTensorRTDataType(const walle::DataType& precision) {
  switch (precision) {
    case walle::DataType::FLOAT32:
      return nvinfer1::DataType::kFLOAT;
    case walle::DataType::FLOAT16:
      return nvinfer1::DataType::kHALF;
    case walle::DataType::INT8:
      return nvinfer1::DataType::kINT8;
    case walle::DataType::INT32:
      return nvinfer1::DataType::kINT32;
    default:
      return nvinfer1::DataType::kFLOAT;
  }
}

// visualize parsed network layout before building engine
void PrintNetwork(const nvinfer1::INetworkDefinition& network) {
  const std::string kBorder = std::string(kBorderLength, '-');
  LOG(ERROR) << kBorder;
  LOG(ERROR) << "Network Layout";
  LOG(ERROR) << kBorder;
  for (int i = 0; i < network.getNbLayers(); i++) {
    const nvinfer1::ILayer* layer = network.getLayer(i);
    if (layer == nullptr) continue;
    const nvinfer1::DataType layer_precision = layer->getPrecision();
    LOG(ERROR) << strings::Format("Layer   | {:3d} | {:60} | {:8} |",
        i, layer->getName(), GetTypeString(layer_precision));

    for (int j = 0; j < layer->getNbInputs(); j++) {
      const nvinfer1::ITensor* tensor = layer->getInput(j);
      if (tensor == nullptr) continue;
      const nvinfer1::Dims dims = tensor->getDimensions();
      const nvinfer1::DataType dtype = layer->getPrecision();
      LOG(ERROR) << strings::Format(">>> in  | {:3d} | {:60} | {:8} | {:<30} |",
          j, tensor->getName(), GetTypeString(dtype), GetDimString(dims));
    }
    for (int j = 0; j < layer->getNbOutputs(); j++) {
      const nvinfer1::ITensor* tensor = layer->getOutput(j);
      if (tensor == nullptr) continue;
      const nvinfer1::Dims dims = tensor->getDimensions();
      const nvinfer1::DataType dtype = layer->getPrecision();
      LOG(ERROR) << strings::Format("<<< out | {:3d} | {:60} | {:8} | {:<30} |",
          j, tensor->getName(), GetTypeString(dtype), GetDimString(dims));
    }
    LOG(ERROR) << kBorder;
  }
}

// suppress op fusion for layers in whitelist
void MarkNetworkOutputs(const std::unordered_set<std::string>& whitelist,
                        nvinfer1::INetworkDefinition* network) {
  if (whitelist.empty()) return;
  CHECK(network != nullptr);
  for (int i = 0; i < network->getNbLayers(); i++) {
    nvinfer1::ILayer* layer = network->getLayer(i);
    if (layer == nullptr) continue;
    const std::string name = layer->getName();
    if (whitelist.count(name)) {
      for (int j = 0; j < layer->getNbOutputs(); j++) {
        nvinfer1::ITensor* tensor = layer->getOutput(j);
        if (tensor == nullptr) continue;
        network->markOutput(*tensor);
        LOG(ERROR) << "Marked layer <" << name << "> output [" << j << "] as output.";
      }
    }
  }
}

// for fast int8 conversion
void SetNetworkDynamicRangeDebug(float min, float max, nvinfer1::INetworkDefinition* network) {
  CHECK(network != nullptr);
  CHECK_LE(min, max);
  for (int i = 0; i < network->getNbInputs(); i++) {
    nvinfer1::ITensor* tensor = network->getInput(i);
    if (tensor == nullptr) continue;
    CHECK(tensor->setDynamicRange(min, max)) << tensor->getName() << " range set failed";
  }
  for (int i = 0; i < network->getNbLayers(); i++) {
    nvinfer1::ILayer* layer = network->getLayer(i);
    if (layer == nullptr) continue;
    const std::string name = layer->getName();
    for (int j = 0; j < layer->getNbOutputs(); j++) {
      nvinfer1::ITensor* tensor = layer->getOutput(j);
      if (tensor == nullptr) continue;
      CHECK(tensor->setDynamicRange(min, max)) << name << "[" << tensor->getName() << "] range set failed";
    }
  }
}

// get Gpu architecture
int GetGpuArch() {
  cudaDeviceProp deviceProp;
  cudaGetDeviceProperties(&deviceProp, 0);
  return deviceProp.major * 10 + deviceProp.minor;
}

} // namespace trt
} // namespace base

