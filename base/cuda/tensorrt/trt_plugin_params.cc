// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/cuda/tensorrt/trt_plugin_params.h"

#include <google/protobuf/io/zero_copy_stream_impl_lite.h>
#include <google/protobuf/text_format.h>

namespace base {
namespace trt {

TrtPluginParams::TrtPluginParams() {}

TrtPluginParams::~TrtPluginParams() {}

void TrtPluginParams::Serialize(void* buffer) const {
  CHECK_NOTNULL(group_op_params_meta_)->SerializeToArray(buffer, size_);
}

void TrtPluginParams::Deserialize(const void* buffer, int length) {
  // tensorrt uses ParseFromArray inside plugin to keep the length of buffer fixed
  group_op_params_meta_ = std::make_unique<cuda::GroupOpParamsMeta>();
  if (length > 0) {
    group_op_params_meta_->ParseFromArray(CHECK_NOTNULL(buffer), length);
  }
  CHECK_EQ(group_op_params_meta_->ByteSize(), length);
  size_ = length;
}

void TrtPluginParams::CopyDataTo(TrtPluginParams* dst_params) const {
  dst_params->group_op_params_meta_ = std::make_unique<cuda::GroupOpParamsMeta>();
  dst_params->group_op_params_meta_->CopyFrom(this->group_op_params_meta());
  dst_params->size_ = size_;
}

void TrtPluginParams::CreateFromPluginFC(const nvinfer1::PluginFieldCollection& fc) {
  group_op_params_meta_ = std::make_unique<cuda::GroupOpParamsMeta>();
  if (fc.nbFields == 0) {
    LOG(INFO) << "No field exists!";
    CHECK_EQ(group_op_params_meta_->ByteSize(), 0);
    size_ = 0;
  } else {
    // use google::protobuf::TextFormat to deserialize attribute info, same as torch
    CHECK_EQ(fc.nbFields, 1);
    const nvinfer1::PluginField* fields = CHECK_NOTNULL(fc.fields);
    if (fields[0].length != 0) {
      google::protobuf::io::ArrayInputStream input(CHECK_NOTNULL(fields[0].data), fields[0].length);
      google::protobuf::TextFormat::Parse(&input, group_op_params_meta_.get());
      size_ = group_op_params_meta_->ByteSize();
    } else {
      CHECK_EQ(group_op_params_meta_->ByteSize(), 0);
      size_ = 0;
    }
  }
}

}  // namespace trt
}  // namespace base
