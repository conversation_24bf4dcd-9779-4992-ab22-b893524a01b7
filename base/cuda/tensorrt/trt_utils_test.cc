// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "gtest/gtest.h"

#include "base/cuda/tensorrt/trt_onnx_parser.h"
#include "base/cuda/tensorrt/trt_profiler.h"
#include "base/cuda/tensorrt/trt_utils.h"

namespace base {
namespace trt {
namespace {

constexpr char kTestOnnxFile[] =
    "common/test/resources/onnx_models/dummy.onnx";
constexpr uint32_t kNetworkFlags = 1U << static_cast<uint32_t>(
    nvinfer1::NetworkDefinitionCreationFlag::kEXPLICIT_BATCH);

} // namespace

class TrtUtilsTest : public ::testing::Test {
 public:
  void SetUp() override {
    builder_.reset(nvinfer1::createInferBuilder(logger_));
    CHECK_NOTNULL(builder_);
    network_.reset(builder_->createNetworkV2(kNetworkFlags));
    CHECK_NOTNULL(network_);
    parser_.reset(nvonnxparser::createParser(*network_, logger_));
    CHECK_NOTNULL(parser_);
    CHECK(parser_->parseFromFile(kTestOnnxFile, FLAGS_trt_log_level));
  }

  void TearDown() override {
  }

 protected:
  TrtLogger logger_;
  base::trt::TrtUniquePtr<nvinfer1::IBuilder> builder_{nullptr};
  base::trt::TrtUniquePtr<nvinfer1::INetworkDefinition> network_{nullptr};
  base::trt::TrtUniquePtr<nvonnxparser::IParser> parser_{nullptr};
};

TEST_F(TrtUtilsTest, LayoutTest) {
  PrintNetwork(*network_);
}

TEST_F(TrtUtilsTest, MarkOutputTest) {
  nvinfer1::ILayer* layer = network_->getLayer(0);
  layer->setName("Test");
  std::unordered_set<std::string> whitelist = {"Test"};
  MarkNetworkOutputs(whitelist, network_.get());
}

TEST_F(TrtUtilsTest, SetNetworkDynamicRangeDebugTest) {
  const float kMin = -10.0f;
  const float kMax = 10.0f;
  SetNetworkDynamicRangeDebug(kMin, kMax, network_.get());
}

}  // namespace trt
}  // namespace base
