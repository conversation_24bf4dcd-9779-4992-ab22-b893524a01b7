// Copyright @2024 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include <fstream>
#include <vector>

#include "base/common/macros.h"
#include "glog/logging.h"

#include "base/cuda/tensorrt/trt_timing_cache.h"

namespace base {
namespace trt {

std::vector<char> LoadTimingCacheFile(const std::string& timing_cache_path) {
  std::ifstream timing_cache_file(timing_cache_path, std::ios::in | std::ios::binary);
  if (!timing_cache_file) {
    LOG(ERROR) << "Create new TimingCache file with " << timing_cache_path;
    return std::vector<char>();
  }
  timing_cache_file.seekg(0, std::ifstream::end);
  size_t fsize = timing_cache_file.tellg();
  timing_cache_file.seekg(0, std::ifstream::beg);
  std::vector<char> content(fsize);
  timing_cache_file.read(content.data(), fsize);
  timing_cache_file.close();
  LOG(ERROR) << "Loaded TimingCache from " << timing_cache_path;
  return content;
}

std::unique_ptr<nvinfer1::ITimingCache> BuildTimingCacheFromFile(
    const std::string& timing_cache_path,
    nvinfer1::IBuilderConfig* config,
    bool* need_save_timing_cache) {
  CHECK(need_save_timing_cache != nullptr);
  std::unique_ptr<nvinfer1::ITimingCache> timing_cache{};
  auto timing_cache_contents = LoadTimingCacheFile(timing_cache_path);
  *need_save_timing_cache = (timing_cache_contents.size() == 0);
  timing_cache.reset(
      config->createTimingCache(timing_cache_contents.data(), timing_cache_contents.size()));
  CHECK(timing_cache != nullptr) << "Create TimingCache failed!";
  config->clearFlag(nvinfer1::BuilderFlag::kDISABLE_TIMING_CACHE);
  CHECK(config->setTimingCache(*timing_cache, false)) << "IBuilderConfig set TimingCache failed";
  return timing_cache;
}

void SaveTimingCacheFile(const std::string& timing_cache_path,
                         const nvinfer1::ITimingCache* timing_cache) {
  const nvinfer1::IHostMemory* blob = timing_cache->serialize();
  std::ofstream timing_cache_file(timing_cache_path, std::ios::out | std::ios::binary);
  if (!timing_cache_file) {
    LOG(ERROR) << "Could not write TimingCache to: " << timing_cache_path;
    return;
  }
  timing_cache_file.write(reinterpret_cast<char*>(blob->data()), blob->size());
  timing_cache_file.close();
  LOG(ERROR) << "Saved " << blob->size() << " bytes of TimingCache to " << timing_cache_path;
}

void UpdateTimingCacheFile(const std::string& timing_cache_path,
                           const nvinfer1::ITimingCache* timing_cache,
                           nvinfer1::IBuilder* builder) {
  std::unique_ptr<nvinfer1::IBuilderConfig> config{builder->createBuilderConfig()};
  std::unique_ptr<nvinfer1::ITimingCache> file_timing_cache{
      config->createTimingCache(static_cast<void const*>(nullptr), 0)};
  auto timing_cache_contents = LoadTimingCacheFile(timing_cache_path);
  file_timing_cache.reset(config->createTimingCache(
      static_cast<void const*>(timing_cache_contents.data()), timing_cache_contents.size()));
  CHECK(file_timing_cache) << "Failed to create timingCache from " + timing_cache_path;
  LOG(ERROR) << "Loaded " << timing_cache_contents.size() << " bytes of TimingCache from "
             << timing_cache_path;

  file_timing_cache->combine(*timing_cache, false);
  std::unique_ptr<nvinfer1::IHostMemory> blob{file_timing_cache->serialize()};
  CHECK(blob) << "Failed to serialize ITimingCache!";

  std::ofstream new_timing_cache_file(timing_cache_path, std::ios::out | std::ios::binary);
  new_timing_cache_file.write(reinterpret_cast<char*>(blob->data()), blob->size());
  new_timing_cache_file.close();
  LOG(ERROR) << "Saved " << blob->size() << " bytes of timing cache to " << timing_cache_path;
}

}  // namespace trt
}  // namespace base
