// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <algorithm>
#include <numeric>
#include <string>
#include <vector>
#include <unordered_map>
#include <unordered_set>

#include "NvInfer.h"
#include "glog/logging.h"

#include "base/common/macros.h"
#include "base/cuda/flags.h"

namespace base {

class TrtProfiler : public nvinfer1::IProfiler {
 public:
  struct LayerProfile {
    std::string name;
    float avg = 0.0f;
    float p99 = 0.0f;
    float p999 = 0.0f;
    int frames = 0;
  };

  explicit TrtProfiler(const std::string& name) : name_(name) {}
  virtual ~TrtProfiler() {
    const std::vector<TrtProfiler::LayerProfile> results = GetLayerwiseLatencySummary();
    PrintLayerwiseLatencySummary(results);
    SaveLayerwiseLatencySummary(results);
  }

  // callback interface offered by tensorrt
  void reportLayerTime(const char* layer_name, float latency_ms) noexcept override;
  void PrintLayerwiseLatencySummary(const std::vector<TrtProfiler::LayerProfile>& results);
  void SaveLayerwiseLatencySummary(const std::vector<TrtProfiler::LayerProfile>& results);

 private:
  std::vector<LayerProfile> GetLayerwiseLatencySummary();

  const std::string kEngineTotal = "EngineTotal";
  std::unordered_map<std::string, std::vector<float>> latency_map_;
  std::vector<std::string> layer_name_list_;
  std::unordered_set<std::string> layer_name_set_;
  std::string name_;

  DISALLOW_COPY_AND_ASSIGN(TrtProfiler);
};

class TrtLogger final : public nvinfer1::ILogger {
 public:
  void log(Severity severity, const char* msg) noexcept override;
};

}  // namespace base
