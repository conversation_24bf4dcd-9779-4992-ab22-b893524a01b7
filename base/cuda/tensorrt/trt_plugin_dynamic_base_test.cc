// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/cuda/tensorrt/trt_plugin_dynamic_base.h"

#include <google/protobuf/text_format.h>

#include <unordered_map>
#include <vector>

#include "base/cuda/operators/utils/op_params.h"

namespace base {
namespace trt {

class ExampleTrtPlugin : public TrtPluginDynamicBase {
 protected:
  void GetParams() override {
    cuda::OpParams op_params(plugin_params_.group_op_params_meta().op_params_meta_map().at("test"));
    op_params.PopParam<int>("attr0", 0, &attr0)
        .PopParam<std::vector<int>>("attr1", {}, &attr1)
        .PopParam<float>("attr2", 0.0f, &attr2)
        .CheckEmpty();
  }

  size_t Enqueue(const nvinfer1::PluginTensorDesc* input_desc,
                 const nvinfer1::PluginTensorDesc* output_desc, const void* const* inputs,
                 void* const* outputs, void* workspace, cudaStream_t stream) override {
    if (attr0 != 0) {
      return 1;
    }
    if (attr1.size() != 3 || attr1[0] != 1 || attr1[1] != 2 || attr1[2] != 3) {
      return 1;
    }
    if (attr2 != 1.0) {
      return 1;
    }
    return 0;
  }

 private:
  int attr0 = 0;
  std::vector<int> attr1;
  float attr2 = 0.0f;
  REGISTER_TRT_PLUGIN_CREATOR_EXTENTION(TrtPluginDynamicBase, ExampleTrtPlugin);
  DISALLOW_COPY_AND_ASSIGN(ExampleTrtPlugin);
};

REGISTER_TRT_PLUGIN_CREATOR(ExampleTrtPlugin);

TEST(TrtPluginDynamicBaseTest, BasicTest) {
  nvinfer1::IPluginCreator* creator =
      getPluginRegistry()->getPluginCreator("ExampleTrtPlugin", "1");
  EXPECT_TRUE(creator != nullptr);

  cuda::GroupOpParamsMeta group_op_params_meta;
  cuda::OpParamsMeta& op_params_meta = (*group_op_params_meta.mutable_op_params_meta_map())["test"];
  (*op_params_meta.mutable_attrs())["attr0"].set_int_value(0);
  (*op_params_meta.mutable_attrs())["attr1"].mutable_int_array()->add_data(1);
  (*op_params_meta.mutable_attrs())["attr1"].mutable_int_array()->add_data(2);
  (*op_params_meta.mutable_attrs())["attr1"].mutable_int_array()->add_data(3);
  (*op_params_meta.mutable_attrs())["attr2"].set_double_value(1.0);

  std::string op_params_str;
  google::protobuf::TextFormat::PrintToString(group_op_params_meta, &op_params_str);
  const size_t serial_length = group_op_params_meta.ByteSize();

  std::vector<nvinfer1::PluginField> plugin_fields;
  plugin_fields.emplace_back("op_params", static_cast<const void*>(op_params_str.data()),
                             nvinfer1::PluginFieldType::kUNKNOWN, op_params_str.size());
  nvinfer1::PluginFieldCollection plugin_collection{
      .nbFields = static_cast<int>(plugin_fields.size()), .fields = plugin_fields.data()};

  nvinfer1::IPluginV2* plugin = creator->createPlugin("example", &plugin_collection);
  nvinfer1::IPluginV2DynamicExt* plugin_ext = dynamic_cast<nvinfer1::IPluginV2DynamicExt*>(plugin);
  cudaStream_t cuda_stream = nullptr;
  size_t result = plugin_ext->enqueue(nullptr, nullptr, nullptr, nullptr, nullptr, cuda_stream);
  EXPECT_EQ(result, 0);
  EXPECT_EQ(plugin_ext->getSerializationSize(), serial_length);

  nvinfer1::IPluginV2DynamicExt* clone_plugin = plugin_ext->clone();
  result = clone_plugin->enqueue(nullptr, nullptr, nullptr, nullptr, nullptr, cuda_stream);
  EXPECT_EQ(result, 0);
  EXPECT_EQ(clone_plugin->getSerializationSize(), serial_length);

  std::vector<uint8_t> serial_data(serial_length);
  clone_plugin->serialize(static_cast<void*>(serial_data.data()));
  nvinfer1::IPluginV2DynamicExt* deserialize_plugin = dynamic_cast<nvinfer1::IPluginV2DynamicExt*>(
      creator->deserializePlugin("example", static_cast<void*>(serial_data.data()), serial_length));
  result = deserialize_plugin->enqueue(nullptr, nullptr, nullptr, nullptr, nullptr, cuda_stream);
  EXPECT_EQ(result, 0);
  EXPECT_EQ(deserialize_plugin->getSerializationSize(), serial_length);

  plugin->destroy();
  clone_plugin->destroy();
  deserialize_plugin->destroy();
  plugin = nullptr;
  clone_plugin = nullptr;
  deserialize_plugin = nullptr;
}

}  // namespace trt
}  // namespace base
