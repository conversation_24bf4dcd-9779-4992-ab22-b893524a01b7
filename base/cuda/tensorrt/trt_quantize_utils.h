// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <string>
#include <unordered_map>
#include <vector>

#include "NvInfer.h"
#include "glog/logging.h"

#include "base/cuda/proto/calib_param.pb.h"
#include "base/file/file_path_util.h"
#include "base/file/file_util.h"

namespace base {
namespace trt {

std::unordered_map<std::string, float> GetTensorRangeMap(const std::string& range_map_file);

void SetNetworkDynamicRange(const std::unordered_map<std::string, float>& tensor_range_map,
                            nvinfer1::INetworkDefinition* network);

void QuantizeWithDynamicRange(const trt::CalibrationConfig& calib_config,
                              nvinfer1::INetworkDefinition* network);

void SetFallbackLayerPrecision(const google::protobuf::RepeatedPtrField<std::string>& fallback_layers_proto,
                               nvinfer1::INetworkDefinition* network);

void SetFallbackModulePrecision(const std::vector<std::string>& int8_module_prefix_list,
                                nvinfer1::INetworkDefinition* network);
}  // namespace trt
}  // namespace base
