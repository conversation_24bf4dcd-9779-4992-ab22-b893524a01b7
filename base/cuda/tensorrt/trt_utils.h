// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <memory>
#include <string>
#include <unordered_set>

#include "NvInfer.h"
#include "glog/logging.h"

#include "base/strings/format.h"
#include "walle/common/proto/model/model_config.pb.h"

namespace base {
namespace trt {

template <typename T>
struct TrtDestroyer {
  void operator()(T* t) {
    delete t;
  }
};
template <typename T>
using TrtUniquePtr = std::unique_ptr<T, TrtDestroyer<T>>;

nvinfer1::DataType ConvertToTensorRTDataType(const walle::DataType& precision);

void PrintNetwork(const nvinfer1::INetworkDefinition& network);

void MarkNetworkOutputs(const std::unordered_set<std::string>& whitelist,
                        nvinfer1::INetworkDefinition* network);

void SetNetworkDynamicRangeDebug(float min, float max, nvinfer1::INetworkDefinition* network);

int GetGpuArch();

} // namespace trt
} // namespace base

