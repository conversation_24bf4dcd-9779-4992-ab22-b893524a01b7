// Copyright @2023 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include <memory>
#include <string>
#include <utility>

#include "gflags/gflags.h"
#include "glog/logging.h"

#include "base/cuda/device_utils.h"
#include "base/cuda/tensorrt/trt_profiler.h"
#include "base/cuda/trt_inference_engine.h"
#include "common/model/model_config_manager.h"

int main(int argc, char* argv[]) {
  google::InitGoogleLogging(argv[0]);
  google::ParseCommandLineFlags(&argc, &argv, false);
  CHECK(argc == 2) << "Usage: " << argv[0] << " MODEL_CONFIG_PATH.";
  FLAGS_trt_log_level = 4;
  const walle::ModelConfigManager config_manager(argv[1]);
  cuda::DeviceContext ctx;
  const walle::ModelConfig& model_config = config_manager.model_config();
  LOG(INFO) << strings::Format("model_precision: {}, max_batch_size: {}",
                               walle::DataType_Name(model_config.model_precision()),
                               model_config.max_batch_size());
  base::TrtInferenceEngine trt_inference_engine(model_config, &ctx);
  const std::unordered_map<std::string, base::TrtRuntimeModel::BindingInfo>& input_map =
      trt_inference_engine.GetInputBindingInfo();
  const std::unordered_map<std::string, base::TrtRuntimeModel::BindingInfo>& output_map =
      trt_inference_engine.GetOutputBindingInfo();
  size_t total_buffer_size = 0;
  auto print_binding_info =
      [&](const std::string& binding_type,
          const std::unordered_map<std::string, base::TrtRuntimeModel::BindingInfo>& binding_map) {
        for (const auto& entry : binding_map) {
          LOG(INFO) << strings::Format("{} name: {}, binding_index:{}, buffer_size: {}",
                                       binding_type,
                                       entry.first,
                                       entry.second.binding_index,
                                       entry.second.buffer_size);
          total_buffer_size += entry.second.buffer_size;
        }
      };
  print_binding_info("input", input_map);
  print_binding_info("output", output_map);
  LOG(INFO) << strings::Format("total_buffer_size: {} MB", total_buffer_size / (1024 * 1024));
  for (int i = 0; i < 100; ++i) {
    trt_inference_engine.Run(model_config.max_batch_size(), true);
  }
  return 0;
}
