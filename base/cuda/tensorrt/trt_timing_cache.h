// Copyright @2024 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <memory>
#include <string>

#include "NvInfer.h"

namespace base {
namespace trt {

std::unique_ptr<nvinfer1::ITimingCache> BuildTimingCacheFromFile(
    const std::string& timing_cache_path,
    nvinfer1::IBuilderConfig* config,
    bool* need_save_timing_cache);

void SaveTimingCacheFile(const std::string& timing_cache_path,
                         const nvinfer1::ITimingCache* timing_cache);

void UpdateTimingCacheFile(const std::string& timing_cache_path,
                           const nvinfer1::ITimingCache* timing_cache,
                           nvinfer1::IBuilder* builder);

}  // namespace trt
}  // namespace base
