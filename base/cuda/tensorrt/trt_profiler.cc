// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/cuda/tensorrt/trt_profiler.h"

#include <map>
#include <utility>

#include "absl/strings/str_replace.h"
#include "glog/logging.h"

#include "base/file/file_path_util.h"
#include "base/file/file_util.h"
#include "base/strings/format.h"

namespace base {

namespace {

constexpr int kTopK = 10;
constexpr int kBorderLength = 150;
constexpr int kNumWarmupFrames = 50;
constexpr char kDefaultLogDir[] = "/tmp";
constexpr char kLogDirFlagName[] = "log_dir";

float Average(const std::vector<float>& sequence) {
  if (sequence.empty()) return 0.0f;
  return std::accumulate(sequence.begin(), sequence.end(), 0.0f) / sequence.size();
}

float Percentile(float percentile, std::vector<float>* sequence) {
  CHECK_GE(percentile, 0.0f);
  CHECK_LE(percentile, 1.0f);
  CHECK(sequence != nullptr);
  if (sequence->empty()) return 0.0f;

  const int idx = static_cast<int>(percentile * sequence->size());
  std::nth_element(sequence->begin(), sequence->begin() + idx, sequence->end());
  return sequence->at(idx);
}
}  // namespace

// callback interface offered by tensorrt
void TrtProfiler::reportLayerTime(const char* layer_name, float latency_ms) noexcept {
  CHECK(layer_name != nullptr);
  latency_map_[layer_name].push_back(latency_ms);
  const size_t entry_idx = latency_map_[layer_name].size();
  if (latency_map_[kEngineTotal].size() < entry_idx) {
    latency_map_[kEngineTotal].push_back(latency_ms);
  } else {
    latency_map_[kEngineTotal].at(entry_idx - 1) += latency_ms;
  }
  if (!layer_name_set_.count(layer_name)) {
    layer_name_list_.push_back(layer_name);
    layer_name_set_.insert(layer_name);
  }
}

std::vector<TrtProfiler::LayerProfile> TrtProfiler::GetLayerwiseLatencySummary() {
  std::vector<TrtProfiler::LayerProfile> profile;
  layer_name_list_.push_back(kEngineTotal);
  for (const auto& name : layer_name_list_) {
    if (!latency_map_.count(name)) continue;
    std::vector<float>* records = &latency_map_[name];
    if (records->size() <= kNumWarmupFrames) continue;
    records->erase(records->begin(), records->begin() + kNumWarmupFrames);
    TrtProfiler::LayerProfile layer;
    layer.name = name;
    layer.avg = Average(*records);
    layer.p99 = Percentile(0.99, records);
    layer.p999 = Percentile(0.999, records);
    layer.frames = records->size();
    profile.emplace_back(std::move(layer));
  }
  return profile;
}

void TrtProfiler::PrintLayerwiseLatencySummary(
    const std::vector<TrtProfiler::LayerProfile>& profile_results) {
  std::vector<TrtProfiler::LayerProfile> results = profile_results;
  if (results.empty()) return;
  const std::map<std::string, std::string> kNameReplacement = {{" || ", " and "}};
  const std::string kBorder = std::string(kBorderLength, '-');
  LOG(ERROR) << kBorder;
  LOG(ERROR) << "TensorRT Engine <" << name_ << "> Layerwise Profiling";
  LOG(ERROR) << kBorder;
  LOG(ERROR) << strings::Format(
      "| {} | {:100} | {:>6} | {:>6} | {:>6} |", "idx", "name", "AVG", "P99", "P999");
  for (size_t i = 0; i < results.size(); i++) {
    const std::string name = absl::StrReplaceAll(results[i].name, kNameReplacement);
    LOG(ERROR) << strings::Format("| {:3d} | {:100} | {:6.3f} | {:6.3f} | {:6.3f} |",
                                  i,
                                  name,
                                  results[i].avg,
                                  results[i].p99,
                                  results[i].p999);
  }

  // TopK by P99
  LOG(ERROR) << kBorder;
  LOG(ERROR) << strings::Format("Top{} Expensive Layers (Sort Descending by P99)", kTopK);
  LOG(ERROR) << kBorder;
  std::stable_sort(results.begin(), results.end(), [](const auto& left, const auto& right) {
    return left.p99 > right.p99;
  });
  const int topk = std::min(static_cast<int>(results.size()), kTopK);
  for (int i = 0; i < topk; i++) {
    const std::string name = absl::StrReplaceAll(results[i].name, kNameReplacement);
    LOG(ERROR) << strings::Format("| {:3d} | {:100} | {:6.3f} | {:6.3f} | {:6.3f} |",
                                  i,
                                  name,
                                  results[i].avg,
                                  results[i].p99,
                                  results[i].p999);
  }
}

void TrtProfiler::SaveLayerwiseLatencySummary(
    const std::vector<TrtProfiler::LayerProfile>& results) {
  if (results.empty()) return;
  const std::string model_name = absl::StrReplaceAll(
      absl::StrReplaceAll(name_, {{"/autocar/data/models/", ""}, {".engine", ""}, {".trt", ""}}),
      {{"/", "@"}});
  std::string log_dir = FLAGS_log_dir;
  google::CommandLineFlagInfo info;
  if (GetCommandLineFlagInfo(kLogDirFlagName, &info) && info.is_default) {
    log_dir = kDefaultLogDir;
  }
  log_dir = file_path::Join(log_dir, "layer_latency");
  if (!file_path::CreateDirectoryIfNotExists(log_dir, true)) {
    LOG(ERROR) << strings::Format("CreateDirectory fail:{}", log_dir);
    return;
  }
  std::string file_name = strings::Format("{}.layer_latency.csv", model_name);
  std::string file_path = file_path::Join(log_dir, file_name);
  int cnt = 0;
  while (file_path::Exists(file_path)) {
    file_name = strings::Format("{}[{}].layer_latency.csv", model_name, cnt + 1);
    file_path = file_path::Join(log_dir, file_name);
    cnt += 1;
  }
  LOG(INFO) << strings::Format("Save layer_latency to {}.", file_path);
  std::ostringstream stream;
  stream << "Name,Avg,P99,P999,Frames\n";
  for (size_t i = 0; i < results.size(); i++) {
    const std::string name = absl::StrReplaceAll(results[i].name, {{",", "|"}});
    stream << strings::Format("{:100},{:6.3f},{:6.3f},{:6.3f},{}\n",
                              name,
                              results[i].avg,
                              results[i].p99,
                              results[i].p999,
                              results[i].frames);
  }
  base::WriteContentToFile(file_path, stream.str());
}

void TrtLogger::log(Severity severity, const char* msg) noexcept {
  // Suppress messages with severity enum value greater than the reportable severity.
  if (static_cast<int>(severity) > FLAGS_trt_log_level) {
    return;
  }
  switch (severity) {
    case Severity::kINTERNAL_ERROR:
      LOG(ERROR) << "INTERNAL_ERROR: " << msg;
      break;
    case Severity::kERROR:
      LOG(ERROR) << "ERROR: " << msg;
      break;
    case Severity::kWARNING:
      LOG(WARNING) << "WARNING: " << msg;
      break;
    case Severity::kINFO:
      LOG(INFO) << "INFO: " << msg;
      break;
    case Severity::kVERBOSE:
      LOG(INFO) << "VERBOSE: " << msg;
      break;
    default:
      LOG(INFO) << "UNKNOWN: " << msg;
      break;
  }
}

}  // namespace base
