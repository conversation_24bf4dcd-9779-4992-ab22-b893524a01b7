// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <algorithm>
#include <memory>
#include <string>
#include <vector>

#include "base/cuda/proto/calib_param.pb.h"
#include "base/cuda/tensor.h"
#include "base/file/file_path_util.h"
#include "base/file/file_util.h"

namespace base {
namespace trt {

class TrtCalibDataStream {
 public:
  explicit TrtCalibDataStream(const CalibrationConfig& model_calib_config) {
    calib_data_path_ = model_calib_config.calib_data_path();
    calib_batch_size_ = model_calib_config.calib_batch_size();
  }
  virtual ~TrtCalibDataStream() = default;

  void Init();
  bool GetBatch(std::vector<CalibrationDataSample>* batch_samples);
  int GetBatchSize() const { return calib_batch_size_; }
  bool IsFirstB<PERSON>();

 private:
  void ReadCalibData();

  std::string calib_data_path_;
  int calib_batch_size_ = 0;
  int total_calib_sample_num_ = 0;
  int num_batches_ = 0;
  int batch_count_ = 0;
  CalibrationData calib_data_;

  DISALLOW_COPY_AND_ASSIGN(TrtCalibDataStream);
};
}  // namespace trt
}  // namespace base
