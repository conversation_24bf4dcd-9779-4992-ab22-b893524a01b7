// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/cuda/tensorrt/trt_calibrator.h"

#include <cuda_fp16.h>

namespace base {
namespace trt {
namespace {

size_t GetDataTypeSize(walle::DataType t) {
  switch (t) {
    case walle::DataType::FLOAT32:
      return sizeof(float);
    case walle::DataType::FLOAT16:
      return sizeof(float) >> 1;
    case walle::DataType::INT8:
      return sizeof(uint8_t);
    default:
      return sizeof(float);
  }
}

}  // namespace

const void* TrtCalibrator::readCalibrationCache(size_t& length) noexcept {
  const bool status =
      ReadTextProtoFile(model_config_manager_->calib_config().calib_table_path(), &calib_table_);
  length = calib_table_.data().length();
  if (!status || length == 0) {
    LOG(ERROR) << "Calibration table unavailable. Require loading calibration data from "
               << model_config_manager_->calib_config().calib_data_path() << ".";
    stream_->Init();
  } else {
    LOG(ERROR) << "Calibration table exist. Build INT8 engine without calibration data.";
  }
  return length ? calib_table_.data().c_str() : nullptr;
}

void TrtCalibrator::writeCalibrationCache(const void* cache, size_t length) noexcept {
  calib_table_.set_data(cache, length);
  const bool status =
      WriteTextProtoToFile(model_config_manager_->calib_config().calib_table_path(), calib_table_);
  if (!status) {
    LOG(FATAL) << "Failed to save calibration table to "
               << model_config_manager_->calib_config().calib_table_path();
  }
}

bool TrtCalibrator::getBatch(void* bindings[], const char* names[], int nBindings) noexcept {
  std::vector<CalibrationDataSample> batch_samples;
  if (!stream_->GetBatch(&batch_samples)) {
    return false;
  }
  CHECK_EQ(batch_samples.size(), model_config_manager_->calib_config().calib_batch_size())
      << "Batchsize mismatched for calib data "
      << model_config_manager_->calib_config().calib_data_path();
  CHECK_GT(batch_samples[0].node_size(), 0);
  CHECK_GT(batch_samples[0].node(0).data_size(), 0);
  const int nbinding = batch_samples[0].node_size();
  const std::vector<std::string>& input_names = model_config_manager_->input_names();
  if (nbinding < input_names.size()) {
    LOG(ERROR) << "Please note that the calibration data is incomplete!";
  }
  const std::unordered_map<std::string, walle::DataType>& node_precision_map =
      model_config_manager_->model_precision_config().node_precision_map;
  // 1. Allocate only in first batch
  if (stream_->IsFirstBatch()) {
    device_buffers_.reserve(input_names.size());
    CHECK(device_buffers_.empty());
    for (int binding_idx = 0; binding_idx < input_names.size(); ++binding_idx) {
      size_t type_size = sizeof(float);
      if (node_precision_map.count(input_names[binding_idx]) > 0) {
        type_size = GetDataTypeSize(node_precision_map.at(input_names[binding_idx]));
      }
      const std::vector<int>& input_shape = model_config_manager_->input_shapes()[binding_idx];
      size_t dim_size = 1;
      for (int d : input_shape) {
        dim_size *= d;
      }
      const size_t binding_bytes =
          model_config_manager_->calib_config().calib_batch_size() * dim_size * type_size;
      device_buffers_.emplace_back(binding_bytes);
    }
  }
  // 2. Binding device buffer
  for (int binding_idx = 0; binding_idx < input_names.size(); ++binding_idx) {
    if (binding_idx < nbinding) {
      size_t device_offset_bytes = 0;
      size_t type_size = sizeof(float);
      if (node_precision_map.count(input_names[binding_idx]) > 0) {
        type_size = GetDataTypeSize(node_precision_map.at(input_names[binding_idx]));
      }
      const size_t sample_binding_bytes =
          batch_samples[0].node(binding_idx).data_size() * type_size;
      for (int sample_idx = 0;
           sample_idx < model_config_manager_->calib_config().calib_batch_size();
           ++sample_idx) {
        CHECK_EQ(batch_samples[sample_idx].node_size(), nbinding);
        CHECK_GT(batch_samples[sample_idx].node(binding_idx).data_size(), 0);
        const float* batch_data_ptr = batch_samples[sample_idx].node(binding_idx).data().data();
        if (node_precision_map.count(input_names[binding_idx]) > 0 &&
            node_precision_map.at(input_names[binding_idx]) == walle::DataType::FLOAT16) {
          std::vector<half> batch_data(
              batch_data_ptr,
              batch_data_ptr + batch_samples[sample_idx].node(binding_idx).data_size());
          cuda::DeviceBufferView<half> device_buffers_view =
              device_buffers_[binding_idx].View<half>(device_offset_bytes, sample_binding_bytes);
          device_buffers_view.FromCpu(batch_data.data(), cuda::DeviceContext::kDefault);
        } else {
          std::vector<float> batch_data(
              batch_data_ptr,
              batch_data_ptr + batch_samples[sample_idx].node(binding_idx).data_size());
          cuda::DeviceBufferView<float> device_buffers_view =
              device_buffers_[binding_idx].View<float>(device_offset_bytes, sample_binding_bytes);
          device_buffers_view.FromCpu(batch_data.data(), cuda::DeviceContext::kDefault);
        }
        device_offset_bytes += sample_binding_bytes;
      }
    }
    bindings[binding_idx] = device_buffers_[binding_idx].mutable_data();
  }
  return true;
}

nvinfer1::CalibrationAlgoType TrtCalibrator::getAlgorithm() noexcept {
  if (model_config_manager_->calib_config().calib_type() == CalibrationConfig::MINMAX) {
    return nvinfer1::CalibrationAlgoType::kMINMAX_CALIBRATION;
  } else if (model_config_manager_->calib_config().calib_type() == CalibrationConfig::ENTROPY) {
    return nvinfer1::CalibrationAlgoType::kENTROPY_CALIBRATION_2;
  } else {
    LOG(FATAL) << "Unsupported Calibration Type.";
  }
}

}  // namespace trt
}  // namespace base
