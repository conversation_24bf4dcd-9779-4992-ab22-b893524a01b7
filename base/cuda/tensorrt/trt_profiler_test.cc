// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "gtest/gtest.h"

#include "base/cuda/tensorrt/trt_onnx_parser.h"
#include "base/cuda/tensorrt/trt_profiler.h"
#include "base/cuda/trt_inference_engine.h"

namespace base {
namespace {

constexpr char kModelConfigFile[] = "common/test/resources/model_configs/dummy.pb.txt";
constexpr int kInputShape = 3 * 224 * 224;
constexpr int kOutputShape = 4;

}  // namespace

TEST(TrtProfiler, BasicTest) {
  FLAGS_trt_log_level = 4;
  walle::ModelConfig model_config;
  CHECK(base::ReadTextProtoFile(kModelConfigFile, &model_config))
      << kModelConfigFile << " not found!";
  walle::ModelConfigManager model_manager(model_config);
  TrtOnnxParser::GenerateTrtEngine(&model_manager);
  cuda::DeviceContext ctx;
  TrtInferenceEngine trt_inference_engine(model_manager.model_config(), &ctx);

  cuda::DeviceBuffer* input_buffer =
      trt_inference_engine.GetMutableInputBufferOrDie(model_manager.input_names()[0]);
  EXPECT_EQ(model_manager.max_batch_size() * kInputShape * sizeof(float), input_buffer->size());
  cuda::DeviceBuffer* output_buffer =
      trt_inference_engine.GetMutableOutputBufferOrDie(model_manager.output_names()[0]);
  EXPECT_EQ(model_manager.max_batch_size() * kOutputShape * sizeof(float), output_buffer->size());

  for (int i = 0; i < 5; i++) {
    const std::vector<float> input_data(kInputShape, i);
    input_buffer->FromCpu(input_data.data(), input_data.size() * sizeof(float), &ctx);
    trt_inference_engine.Run();
    ctx.Sync();
  }
  // test should pass gracefully
}

}  // namespace base
