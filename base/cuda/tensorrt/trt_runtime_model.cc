// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/cuda/tensorrt/trt_runtime_model.h"
#include "common/tools/simulation/flags.h"

#include <cuda_runtime.h>
#include <cstddef>

namespace base {

namespace {
constexpr size_t MEGA_BYTE = 1048576;
}  // namespace

std::atomic<cuda::DeviceBuffer*> TrtRuntimeModel::shared_enqueue_buffer{nullptr};
std::mutex TrtRuntimeModel::shared_buffer_mutex;

void TrtRuntimeModel::InitializeSharedBuffer() {
  auto* buffer = shared_enqueue_buffer.load(std::memory_order_acquire);
  if (!buffer) {
    std::lock_guard<std::mutex> lock(shared_buffer_mutex);
    buffer = shared_enqueue_buffer.load(std::memory_order_relaxed);
    if (!buffer) {
      shared_enqueue_buffer.store(
          new cuda::DeviceBuffer(FLAGS_trt_enqueue_device_buffer_size_mb * MEGA_BYTE),
          std::memory_order_release);
    }
  }
}

TrtRuntimeModel::TrtRuntimeModel(const walle::ModelConfig& model_config,
                                 cuda::DeviceContext* device_context)
    : ModelRuntime(model_config.engine_file(), device_context) {
  const std::string& engine_file = model_config.engine_file();
  max_batch_size_ = model_config.max_batch_size();
  CHECK_GE(max_batch_size_, 1);
  std::string engine_content;
  LOG(ERROR) << "Loading TensorRT Engine: " << engine_file;
  CHECK(ReadFileToString(engine_file, &engine_content));
  runtime_ = std::unique_ptr<nvinfer1::IRuntime>(nvinfer1::createInferRuntime(logger_));
  CHECK(runtime_ != nullptr)
      << "(Create TensorRT Runtime Failed. Please check current device memory usage.)";
  if (static_cast<nvinfer1::DeviceType>(model_config.device_type()) == nvinfer1::DeviceType::kDLA){
    const int dla_core = model_config.dla_core();
    CHECK((dla_core == 0) || (dla_core == 1)) << "dla core should be 0 or 1.";
    runtime_->setDLACore(dla_core);
    LOG(ERROR) << "TrtRuntimeModel using DLA Core: " << dla_core;
  }
  engine_ = runtime_->deserializeCudaEngine(engine_content.data(), engine_content.size());
  CHECK(engine_ != nullptr) << "(Deserialize TensorRT Engine Failed. Please check engine file "
                               "integrity and current device memory usage.)";
  if (device_context->reuse_device_memory()) {
    const size_t device_memory_size_mb = engine_->getDeviceMemorySize() / MEGA_BYTE;
    CHECK(device_memory_size_mb <= FLAGS_trt_enqueue_device_buffer_size_mb) << strings::Format(
        "Trt engine's device memory is greater than shared enqueue buffer: {} MB vs {} MB",
        device_memory_size_mb,
        FLAGS_trt_enqueue_device_buffer_size_mb);
    InitializeSharedBuffer();
    context_ = engine_->createExecutionContextWithoutDeviceMemory();
    LOG(ERROR) << strings::Format("Reuse device memory for enqueue: {} MB", device_memory_size_mb);
    cuda::DeviceBuffer* buffer = shared_enqueue_buffer.load(std::memory_order_acquire);
    context_->setDeviceMemory(buffer->mutable_data());
  } else {
    context_ = engine_->createExecutionContext();
  }

  CHECK(context_ != nullptr)
      << "(Create TensorRT Execution Context Failed. Please check current device memory usage.)";
  name_ = engine_file;
  if (FLAGS_trt_log_level == 4) {
    profiler_ = std::make_unique<TrtProfiler>(engine_file);
    context_->setProfiler(profiler_.get());
  }
  InitializeBindingInfo();
}

TrtRuntimeModel::~TrtRuntimeModel() {
  delete context_;
  delete engine_;
  std::lock_guard<std::mutex> lock(shared_buffer_mutex);
  auto* buffer = shared_enqueue_buffer.load(std::memory_order_relaxed);
  if (!buffer) {
    delete buffer;
  }
  LOG(INFO) << name_ << " destroyed.";
}

void TrtRuntimeModel::InitializeBindingInfo() {
  const size_t total_bindings = engine_->getNbBindings();
  for (size_t i = 0; i < total_bindings; ++i) {
    nvinfer1::Dims dims = engine_->getBindingDimensions(i);
    bool static_batch_size = false;
    // -1: dynamic batch size
    if (dims.d[0] == -1) {
      dims.d[0] = max_batch_size_;
    } else {
      static_batch_size = true;
    }
    const std::string name = engine_->getBindingName(i);
    const nvinfer1::DataType data_type = engine_->getBindingDataType(i);
    const size_t buffer_size = GetDimSize(dims) * GetTypeSize(data_type);
    if (engine_->bindingIsInput(i)) {
      if (!static_batch_size) {
        const nvinfer1::Dims max_dims =
            engine_->getProfileDimensions(i, 0, nvinfer1::OptProfileSelector::kMAX);
        CHECK_EQ(max_dims.d[0], max_batch_size_);
      }
      input_binding_infos_[name] = {data_type, dims, buffer_size, i, "", static_batch_size};
    } else {
      output_binding_infos_[name] = {data_type, dims, buffer_size, i};
      output_name_list_.push_back(name);
    }
  }
}

void TrtRuntimeModel::InitCudaGraph() {
  CHECK(!bindings_.empty());
  const int num_graph = std::ceil(std::log2(max_batch_size_)) + 1;
  graphs_.reserve(num_graph);
  for (int i = 0; i < num_graph; ++i) {
    cudaGraph_t graph;
    const int graph_batch_size = std::min(1 << i, max_batch_size_);
    for (const auto& entry : input_binding_infos_) {
      const BindingInfo& input_binding_info = entry.second;
      nvinfer1::Dims dims = input_binding_info.dims;
      if (!input_binding_info.static_batch_size) {
        dims.d[0] = graph_batch_size;
      }
      context_->setBindingDimensions(input_binding_info.binding_index, dims);
    }
    CUDA_CHECK(cudaStreamBeginCapture(device_context_->stream(), cudaStreamCaptureModeRelaxed));
    context_->enqueueV2(bindings_.data(), device_context_->stream(), nullptr);
    CUDA_CHECK(cudaStreamEndCapture(device_context_->stream(), &graph));
    device_context_->Sync();
    CUDA_CHECK(cudaGraphInstantiate(&graphs_[i], graph, nullptr, nullptr, 0));
  }
}

void TrtRuntimeModel::Enqueue(size_t batch_size, bool use_cuda_graph) {
  CHECK_GT(batch_size, 0);
  CHECK_LE(batch_size, max_batch_size_);
  CHECK(!bindings_.empty());
  for (const auto& entry : input_binding_infos_) {
    const BindingInfo& input_binding_info = entry.second;
    nvinfer1::Dims dims = input_binding_info.dims;
    if (!input_binding_info.static_batch_size) {
      dims.d[0] = FLAGS_trt_inference_by_max_batch_size ? max_batch_size_ : batch_size;
    }
    context_->setBindingDimensions(input_binding_info.binding_index, dims);
  }
  if (use_cuda_graph) {
    const int index = std::ceil(std::log2(batch_size));
    CUDA_CHECK(cudaGraphLaunch(graphs_[index], device_context_->stream()));
  } else {
    context_->enqueueV2(bindings_.data(), device_context_->stream(), nullptr);
  }
}

}  // namespace base
