// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <algorithm>
#include <memory>
#include <string>
#include <vector>

#include "NvInfer.h"
#include "NvInferPlugin.h"
#include "absl/strings/str_split.h"

#include "base/cuda/proto/calib_param.pb.h"
#include "base/cuda/tensor.h"
#include "base/cuda/tensorrt/trt_calib_datastream.h"
#include "base/file/file_path_util.h"
#include "base/file/file_util.h"
#include "common/model/model_config_manager.h"

namespace base {
namespace trt {

class TrtCalibrator : public nvinfer1::IInt8Calibrator {
 public:
  explicit TrtCalibrator(const walle::ModelConfigManager* model_config_manager) {
    model_config_manager_ = model_config_manager;
    stream_ = std::make_unique<TrtCalibDataStream>(model_config_manager->calib_config());
  }
  virtual ~TrtCalibrator() = default;

  const void* readCalibrationCache(size_t& length) noexcept override;
  void writeCalibrationCache(const void* cache, size_t length) noexcept override;
  int getBatchSize() const noexcept override { return stream_->GetBatchSize(); }
  bool getBatch(void* bindings[], const char* names[], int nBindings) noexcept override;
  nvinfer1::CalibrationAlgoType getAlgorithm() noexcept override;

 private:
  const walle::ModelConfigManager* model_config_manager_;  // Not owned
  CalibrationTable calib_table_;
  std::unique_ptr<TrtCalibDataStream> stream_ = nullptr;
  std::vector<cuda::DeviceBuffer> device_buffers_;

  DISALLOW_COPY_AND_ASSIGN(TrtCalibrator);
};

}  // namespace trt
}  // namespace base
