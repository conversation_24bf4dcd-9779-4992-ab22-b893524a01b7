// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <atomic>
#include <memory>
#include <mutex>
#include <string>
#include <unordered_map>
#include <vector>

#include "NvInfer.h"
#include "NvInferPlugin.h"

#include "base/container/utils.h"
#include "base/cuda/device_buffer.h"
#include "base/cuda/flags.h"
#include "base/cuda/model_runtime.h"
#include "base/cuda/tensorrt/trt_profiler.h"
#include "base/cuda/tensorrt/trt_utils.h"
#include "base/file/file_path_util.h"
#include "base/file/file_util.h"

namespace base {
class TrtRuntimeModel : public ModelRuntime {
 public:
  TrtRuntimeModel(const walle::ModelConfig& model_config, cuda::DeviceContext* context);

  virtual ~TrtRuntimeModel();

  void InitCudaGraph();
  void Enqueue(size_t batch_size, bool use_cuda_graph = false);

 private:
  void InitializeBindingInfo();
  static void InitializeSharedBuffer();
  static std::atomic<cuda::DeviceBuffer*> shared_enqueue_buffer;
  static std::mutex shared_buffer_mutex;
  TrtLogger logger_;
  std::unique_ptr<nvinfer1::IRuntime> runtime_;
  nvinfer1::ICudaEngine* engine_ = nullptr;
  nvinfer1::IExecutionContext* context_ = nullptr;
  std::vector<cudaGraphExec_t> graphs_;

  std::unique_ptr<TrtProfiler> profiler_;

  DISALLOW_COPY_AND_ASSIGN(TrtRuntimeModel);
};

}  // namespace base
