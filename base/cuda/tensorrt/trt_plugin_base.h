// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <memory>
#include <string>
#include <unordered_map>
#include <vector>

#include "NvInfer.h"
#include "NvInferPlugin.h"
#include "glog/logging.h"

#include "base/common/macros.h"
#include "base/container/utils.h"
#include "base/cuda/tensorrt/trt_plugin_params.h"

namespace base {
namespace trt {

// TrtPluginBase is a wrapper of IPluginV2DynamicExt. To make sure the same code style, we
// can inherit TrtPluginBase instead of inherit IPluginV2Ext directly.
class TrtPluginBase : public nvinfer1::IPluginV2Ext {
 public:
  TrtPluginBase() = default;
  virtual ~TrtPluginBase() = default;

  // Return the plugin type. Should match the plugin name returned by the corresponding plugin
  // creator.
  const char* getPluginType() const noexcept final { return GetPluginType(); }

  // Return the plugin version. Should match the plugin version returned by the corresponding plugin
  // creator.
  const char* getPluginVersion() const noexcept final { return GetPluginVersion(); }
  // Get the number of outputs from the layer.
  //
  // Returns
  //    The number of outputs.
  //
  // This function is called by the implementations of INetworkDefinition and IBuilder. In
  // particular, it is called prior to any call to initialize().
  int getNbOutputs() const noexcept final { return GetNumOutputs(); }
  // Get the dimension of an output tensor.
  //
  // Parameters
  //    index The index of the output tensor.
  //    inputs The input tensors.
  //    nbInputDims  The number of input tensors.
  // This function is called by the implementations of INetworkDefinition and IBuilder. In
  // particular, it is called prior to any call to initialize().
  //
  // Implemented in nvinfer1::IPluginV2DynamicExt.
  nvinfer1::Dims getOutputDimensions(int index, const nvinfer1::Dims* inputs,
                                     int nbInputDims) noexcept final {
    return GetOutputDimensions(index, inputs, nbInputDims);
  }

  bool supportsFormat(nvinfer1::DataType type, nvinfer1::PluginFormat format) const noexcept final {
    return SupportsFormat(type, format);
  }

  // Initialize the layer for execution. This is called when the engine is created.
  //
  // Returns
  //    0 for success, else non-zero (which will cause engine termination).
  int initialize() noexcept final { return Initialize(); }

  // Release resources acquired during plugin layer initialization. This is called when the engine
  // is destroyed.
  void terminate() noexcept final {
    // TODO(wanglong): add some monitor to initialize() and terminate().
    Terminate();
  }
  // Find the workspace size required by the layer.
  //
  // This function is called after the plugin is configured, and possibly during execution. The
  // result should be a sufficient workspace size to deal with inputs and outputs of the given size
  // or any smaller problem.
  //
  // Returns
  //    The workspace size.
  size_t getWorkspaceSize(int maxBatchSize) const noexcept final {
    return GetWorkspaceSize(maxBatchSize);
  }
  // Execute the layer.
  //
  // Parameters
  //    batchSize The number of inputs in the batch.
  //    inputs The memory for the input tensors.
  //    outputs The memory for the output tensors.
  //    workspace Workspace for execution.
  //    stream The stream in which to execute the kernels.
  //
  // Returns
  //    0 for success, else non-zero (which will cause engine termination).
  int32_t enqueue(int32_t batchSize, const void* const* inputs, void* const* outputs,
                  void* workspace, cudaStream_t stream) noexcept final {
    return Enqueue(batchSize, inputs, outputs, workspace, stream);
  }
  // Find the size of the serialization buffer required.
  //
  // Returns
  //    The size of the serialization buffer.
  size_t getSerializationSize() const noexcept final { return GetSerializationSize(); }

  // Serialize the layer.
  //
  // Parameters
  // buffer A pointer to a buffer to serialize data. Size of buffer must be equal to value returned
  // by getSerializationSize.
  void serialize(void* buffer) const noexcept final { Serialize(buffer); }

  // Destroy the plugin object. This will be called when the network, builder or engine is
  // destroyed.
  void destroy() noexcept final { Destroy(); }

  // Set the namespace that this plugin object belongs to. Ideally, all plugin objects from the same
  // plugin library should have the same namespace.
  void setPluginNamespace(const char* pluginNamespace) noexcept final {
    plugin_namespace_ = pluginNamespace;
  }

  // Return the namespace of the plugin object.
  const char* getPluginNamespace() const noexcept { return plugin_namespace_.c_str(); }

  // Return the DataType of the plugin output at the requested index. The default behavior should be
  // to return the type of the first input, or DataType::kFLOAT if the layer has no inputs. The
  // returned data type must have a format that is supported by the plugin.
  // Warning
  //    DataType:kBOOL not supported.
  nvinfer1::DataType getOutputDataType(int index, const nvinfer1::DataType* inputTypes,
                                       int nbInputs) const noexcept final {
    return GetOutputDataType(index, inputTypes, nbInputs);
  }

  // Return true if output tensor is broadcast across a batch.
  //
  // Parameters
  //   - outputIndex  The index of the output
  //   - inputIsBroadcasted The ith element is true if the tensor for the ith input is broadcast
  //    across a batch.
  //   - nbInputs The number of inputs
  //
  // The values in inputIsBroadcasted refer to broadcasting at the semantic level, i.e. are
  // unaffected by whether method canBroadcastInputAcrossBatch requests physical replication of the
  // values.
  bool isOutputBroadcastAcrossBatch(int32_t outputIndex, const bool* inputIsBroadcasted,
                                    int32_t nbInputs) const noexcept final {
    return IsOutputBroadcastAcrossBatch(outputIndex, inputIsBroadcasted, nbInputs);
  }

  // Return true if plugin can use input that is broadcast across batch without replication.
  //
  // Parameters
  //   - inputIndex Index of input that could be broadcast.
  //
  // For each input whose tensor is semantically broadcast across a batch, TensorRT calls this
  // method before calling configurePlugin. If canBroadcastInputAcrossBatch returns true, TensorRT
  // will not replicate the input tensor; i.e., there will be a single copy that the plugin should
  // share across the batch. If it returns false, TensorRT will replicate the input tensor so that
  // it appears like a non-broadcasted tensor.
  // This method is called only for inputs that can be broadcast.
  bool canBroadcastInputAcrossBatch(int32_t inputIndex) const noexcept final {
    return CanBroadcastInputAcrossBatch(inputIndex);
  }

  // Configure the layer with input and output data types.
  //
  // This function is called by the builder prior to initialize(). It provides an opportunity for
  // the layer to make algorithm choices on the basis of its weights, dimensions, data types and
  // maximum batch size.
  //
  // Parameters
  //    inputDims  The input tensor dimensions.
  //    nbInputs The number of inputs.
  //    outputDims The output tensor dimensions.
  //    nbOutputs  The number of outputs.
  //    inputTypes The data types selected for the plugin inputs.
  //    outputTypes  The data types selected for the plugin outputs.
  //    inputIsBroadcast True for each input that the plugin must broadcast across the batch.
  //    outputIsBroadcast  True for each output that TensorRT will broadcast across the batch.
  //    floatFormat  The format selected for the engine for the floating point inputs/outputs.
  //    maxBatchSize The maximum batch size.
  //
  // The dimensions passed here do not include the outermost batch size (i.e. for 2-D image
  // networks, they will be 3-dimensional CHW dimensions). When inputIsBroadcast or
  // outputIsBroadcast is true, the outermost batch size for that input or output should be treated
  // as if it is one. inputIsBroadcast[i] is true only if the input is semantically broadcast across
  // the batch and canBroadcastInputAcrossBatch(i) returned true. outputIsBroadcast[i] is true only
  // if isOutputBroadcastAcrossBatch(i) returned true.
  //
  // Warning
  //   for the floatFormat field, the values PluginFormat::kCHW4, PluginFormat::kCHW16, and
  // PluginFormat::kCHW32 will not be passed in, this is to keep backward compatibility with
  // TensorRT 5.x series. Use PluginV2IOExt or PluginV2DynamicExt for other PluginFormats.
  void configurePlugin(const nvinfer1::Dims* inputDims, int nbInputs,
                       const nvinfer1::Dims* outputDims, int nbOutputs,
                       const nvinfer1::DataType* inputTypes, const nvinfer1::DataType* outputTypes,
                       const bool* inputIsBroadcast, const bool* outputIsBroadcast,
                       nvinfer1::PluginFormat floatFormat, int maxBatchSize) noexcept final {
    std::vector<TrtTensorDesc> input_tensors_desc;
    std::vector<TrtTensorDesc> output_tensors_desc;
    for (int i = 0; i < nbInputs; ++i) {
      input_tensors_desc.emplace_back(TrtTensorDesc{
          .dims = inputDims[i], .type = inputTypes[i], .is_broadcast = inputIsBroadcast[i]});
    }
    for (int i = 0; i < nbOutputs; ++i) {
      output_tensors_desc.emplace_back(TrtTensorDesc{
          .dims = outputDims[i], .type = outputTypes[i], .is_broadcast = outputIsBroadcast[i]});
    }
    ConfigurePlugin(input_tensors_desc, output_tensors_desc, floatFormat, maxBatchSize);
  }

  // Attach the plugin object to an execution context and grant the plugin the access to some
  // context resource.
  //
  // Parameters
  //    cudnn  The CUDNN context handle of the execution context
  //    cublas The cublas context handle of the execution context
  //    allocator  The allocator used by the execution context
  //
  // This function is called automatically for each plugin when a new execution context is created.
  // If the context was created without resources, this method is not called until the resources are
  // assigned. It is also called if new resources are assigned to the context.
  // If the plugin needs per-context resource, it can be allocated here. The plugin can also get
  // context-owned CUDNN and CUBLAS context here.
  // Note: In the automotive safety context, the CUDNN and CUBLAS parameters will be nullptr because
  // CUDNN and CUBLAS is not used by the safe runtime.
  void attachToContext(cudnnContext* cudnn_context, cublasContext* cublas_context,
                       nvinfer1::IGpuAllocator* gpu_allocator) noexcept final {
    AttachToContext(cudnn_context, cublas_context, gpu_allocator);
  }

  // Detach the plugin object from its execution context.
  // This function is called automatically for each plugin when a execution context is destroyed or
  // the context resources are unassigned from the context.
  // If the plugin owns per-context resource, it can be released here.
  void detachFromContext() noexcept final { DetachFromContext(); }

  // Clone the plugin object. This copies over internal plugin parameters as well and returns a new
  // plugin object with these parameters. If the source plugin is pre-configured with
  // configurePlugin(), the returned object should also be pre-configured. The returned object
  // should allow attachToContext() with a new execution context. Cloned plugin objects can share
  // the same per-engine immutable resource (e.g. weights) with the source object (e.g. via
  // ref-counting) to avoid duplication.
  IPluginV2Ext* clone() const noexcept final {
    TrtPluginBase* plugin = Clone();
    plugin->SetPluginName(plugin_name_);
    plugin->setPluginNamespace(plugin_namespace_.c_str());
    plugin_params_.CopyDataTo(&plugin->plugin_params_);
    plugin->GetParams();
    return plugin;
  }

 protected:
  // Plugin execution order:
  //   1. Constructor
  //     - construct plugin with different methods(clone, deserialize);
  //     - initialize plugin params;
  //   2. ConfigurePlugin
  //     - config the plugin input and output;
  //     - the plugin sets up its internal state and selects the most appropriate algorithm and data
  //      structures for the given configuration.
  //   3. Initialize
  //     - set up internal data structures and prepare for execution according to configuration.
  //   4. Enqueue
  //     - Execute the plugin.
  //   5. Terminate
  //     - The engine context is destroyed, and all the resources held by the plugin should be
  //     released.
  //   6. Destroy
  //     - Used to destroy the plugin object and/or other memory allocated each time a new plugin
  //     object is created.
  //   7. Destructor(called by Destroy)

  // Functions inherited from IPluginV2
  virtual const char* GetPluginType() const = 0;
  virtual const char* GetPluginVersion() const = 0;
  // Assume the plugin has only 1 output if GetNumOutputs not override.
  virtual int GetNumOutputs() const { return 1; }
  // Support (float, NCHW) only if not override.
  virtual bool SupportsFormat(nvinfer1::DataType type, nvinfer1::PluginFormat format) const {
    return type == nvinfer1::DataType::kFLOAT && format == nvinfer1::PluginFormat::kLINEAR;
  }
  virtual int Initialize() { return 0; }
  virtual void Terminate() {}
  virtual size_t GetSerializationSize() const { return plugin_params_.size(); }
  virtual void Serialize(void* buffer) const { plugin_params_.Serialize(buffer); }
  virtual void Destroy() { delete this; }

  // Functions inherited from IPluginV2Ext
  // Return the datatype of first input if not override.
  virtual nvinfer1::DataType GetOutputDataType(int index, const nvinfer1::DataType* input_types,
                                               int num_inputs) const {
    CHECK_GT(num_inputs, 0);
    return input_types[0];
  }
  virtual bool IsOutputBroadcastAcrossBatch(int32_t output_index, const bool* input_is_broadcast,
                                            int32_t num_inputs) const {
    return false;
  }
  virtual bool CanBroadcastInputAcrossBatch(int32_t inputIndex) const { return false; }

  struct TrtTensorDesc {
    nvinfer1::Dims dims;
    nvinfer1::DataType type;
    bool is_broadcast = false;
  };
  virtual void ConfigurePlugin(const std::vector<TrtTensorDesc>& input_tensors_desc,
                               const std::vector<TrtTensorDesc>& output_tensors_desc,
                               nvinfer1::PluginFormat float_format, int max_batch_size) {}

  virtual void AttachToContext(cudnnContext* cudnn_context, cublasContext* cublas_context,
                               nvinfer1::IGpuAllocator* gpu_allocator) {}
  virtual void DetachFromContext() {}
  // Functions inherited from IPluginV2DynamicExt
  virtual TrtPluginBase* Clone() const = 0;
  // Return the dimensions of first input if not override.
  virtual nvinfer1::Dims GetOutputDimensions(int index, const nvinfer1::Dims* inputs,
                                             int num_inputs) {
    CHECK_GT(num_inputs, 0);
    return inputs[0];
  }

  virtual size_t GetWorkspaceSize(int max_batch_size) const { return 0; }
  virtual size_t Enqueue(int batch_size, const void* const* inputs, void* const* outputs,
                         void* workspace, cudaStream_t stream) = 0;

  const std::string& GetPluginName() const { return plugin_name_; }
  void SetPluginName(const std::string& plugin_name) { plugin_name_ = plugin_name; }
  virtual void GetParams() = 0;

  TrtPluginParams plugin_params_;

  static nvinfer1::PluginFieldCollection field_collection_;
  static std::vector<nvinfer1::PluginField> attributes_;

 private:
  std::string plugin_name_;
  std::string plugin_namespace_;

  DISALLOW_COPY_AND_ASSIGN(TrtPluginBase);
};

}  // namespace trt
}  // namespace base

#define REGISTER_TRT_PLUGIN_CREATOR(plugin_type)                                                   \
  class plugin_type##Creator : public nvinfer1::IPluginCreator {                                   \
   public:                                                                                         \
    plugin_type##Creator() {}                                                                      \
    ~plugin_type##Creator() override = default;                                                    \
    const char* getPluginName() const noexcept override { return #plugin_type; }                   \
    const char* getPluginVersion() const noexcept override { return "1"; }                         \
    const nvinfer1::PluginFieldCollection* getFieldNames() noexcept override {                     \
      return &plugin_type::field_collection_;                                                      \
    }                                                                                              \
    nvinfer1::IPluginV2* createPlugin(                                                             \
        const char* name, const nvinfer1::PluginFieldCollection* fc) noexcept override {           \
      plugin_type* plugin = new plugin_type(name, fc);                                             \
      plugin->setPluginNamespace(plugin_namespace_.c_str());                                       \
      return plugin;                                                                               \
    }                                                                                              \
    nvinfer1::IPluginV2* deserializePlugin(const char* name,                                       \
                                           const void* serialData,                                 \
                                           size_t serialLength) noexcept override {                \
      plugin_type* plugin = new plugin_type(name, serialData, serialLength);                       \
      plugin->setPluginNamespace(plugin_namespace_.c_str());                                       \
      return plugin;                                                                               \
    }                                                                                              \
    void setPluginNamespace(const char* pluginNamespace) noexcept override {                       \
      plugin_namespace_ = pluginNamespace;                                                         \
    }                                                                                              \
    const char* getPluginNamespace() const noexcept override { return plugin_namespace_.c_str(); } \
                                                                                                   \
   private:                                                                                        \
    std::string plugin_namespace_;                                                                 \
  };                                                                                               \
  REGISTER_TENSORRT_PLUGIN(plugin_type##Creator)

#define REGISTER_TRT_PLUGIN_CREATOR_EXTENTION(base_plugin_type, plugin_type)     \
 protected:                                                                      \
  base_plugin_type* Clone() const override { return new plugin_type(); }         \
                                                                                 \
 private:                                                                        \
  plugin_type() {}                                                               \
  plugin_type(const char* name, const nvinfer1::PluginFieldCollection* fc) {     \
    plugin_params_.CreateFromPluginFC(*fc);                                      \
    GetParams();                                                                 \
    SetPluginName(name);                                                         \
  }                                                                              \
  plugin_type(const char* name, const void* serial_data, size_t serial_length) { \
    plugin_params_.Deserialize(serial_data, serial_length);                      \
    GetParams();                                                                 \
    SetPluginName(name);                                                         \
  }                                                                              \
  const char* GetPluginType() const override { return #plugin_type; }            \
  const char* GetPluginVersion() const override { return "1"; }                  \
  friend class plugin_type##Creator

#define TRT_PLUGIN_POST_CHECK()                               \
  do {                                                        \
    cudaError_t err = cudaGetLastError();                     \
    if (err != cudaSuccess) {                                 \
      LOG(FATAL) << "TensorRT Plugin \"" << GetPluginName()   \
                 << "\" failed: " << cudaGetErrorString(err); \
      return static_cast<size_t>(err);                        \
    }                                                         \
  } while (false)
