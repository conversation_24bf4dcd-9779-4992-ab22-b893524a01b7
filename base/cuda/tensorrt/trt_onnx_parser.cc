// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include <unordered_set>
#include <vector>

#include "base/cuda/tensorrt/trt_onnx_parser.h"
#include "base/cuda/tensorrt/trt_timing_cache.h"

namespace base {
namespace {

using base::trt::TrtUniquePtr;
constexpr uint32_t kNetworkFlags =
    1U << static_cast<uint32_t>(nvinfer1::NetworkDefinitionCreationFlag::kEXPLICIT_BATCH);

void WriteEngineToFile(const std::string& file_name, nvinfer1::IHostMemory* serialized_engine) {
  const std::string engine_content(reinterpret_cast<char*>(serialized_engine->data()),
                                   serialized_engine->size());
  CHECK(WriteContentToFile(file_name, engine_content));
}

void ConfigINT8Profile(const trt::CalibrationConfig& model_calib_config,
                       const nvinfer1::INetworkDefinition& network,
                       nvinfer1::IBuilder* builder,
                       nvinfer1::IBuilderConfig* config) {
  CHECK(builder != nullptr);
  CHECK(config != nullptr);
  nvinfer1::IOptimizationProfile* profile_calib = builder->createOptimizationProfile();
  for (int i = 0; i < network.getNbInputs(); ++i) {
    const nvinfer1::ITensor* input_tensor = network.getInput(i);
    const char* tensor_name = input_tensor->getName();
    nvinfer1::Dims calib_dims = input_tensor->getDimensions();
    calib_dims.d[0] = model_calib_config.calib_batch_size();
    profile_calib->setDimensions(tensor_name, nvinfer1::OptProfileSelector::kMIN, calib_dims);
    profile_calib->setDimensions(tensor_name, nvinfer1::OptProfileSelector::kOPT, calib_dims);
    profile_calib->setDimensions(tensor_name, nvinfer1::OptProfileSelector::kMAX, calib_dims);
  }
  config->setCalibrationProfile(profile_calib);
}

}  // namespace

void TrtOnnxParser::GetNetworkInputsOutputsName(const std::string& onnx_file,
                                                std::vector<std::string>* inputs_name,
                                                std::vector<std::string>* outputs_name) {
  CHECK(inputs_name != nullptr);
  CHECK(outputs_name != nullptr);
  CHECK(file_path::Exists(onnx_file)) << onnx_file;
  TrtLogger logger;
  TrtUniquePtr<nvinfer1::IBuilder> builder{nvinfer1::createInferBuilder(logger)};
  CHECK(builder != nullptr);
  TrtUniquePtr<nvinfer1::INetworkDefinition> network{builder->createNetworkV2(kNetworkFlags)};
  CHECK(network != nullptr);
  TrtUniquePtr<nvonnxparser::IParser> parser{nvonnxparser::createParser(*network, logger)};
  CHECK(parser != nullptr);
  CHECK(parser->parseFromFile(onnx_file.c_str(), FLAGS_trt_log_level));

  for (int i = 0; i < network->getNbInputs(); ++i) {
    nvinfer1::ITensor* input_tensor = network->getInput(i);
    inputs_name->push_back(input_tensor->getName());
  }
  for (int i = 0; i < network->getNbOutputs(); ++i) {
    nvinfer1::ITensor* output_tensor = network->getOutput(i);
    outputs_name->push_back(std::string(output_tensor->getName()));
  }
}

void TrtOnnxParser::GenerateTrtEngine(const walle::ModelConfigManager* config_manager,
                                      NetworkManipulator manipulator) {
  const std::string& onnx_file = config_manager->weight_file();
  const std::string& trt_engine_file = config_manager->engine_file();
  const std::vector<std::string>& int8_module_prefix_list =
      config_manager->int8_module_prefix_list();
  const std::vector<std::string>& dla_fallback_gpu_module_prefix_list =
      config_manager->dla_fallback_gpu_module_prefix_list();  
  const walle::ModelPrecisionConfig& precision_config = config_manager->model_precision_config();
  const int min_batch_size = config_manager->min_batch_size();
  const int max_batch_size = config_manager->max_batch_size();
  const std::unordered_set<std::string>& static_batch_size_list =
      config_manager->static_batch_size_list();
  const size_t workspace = static_cast<size_t>(config_manager->workspace() * (1 << 30));
  const std::unordered_set<std::string>& mark_output_list = config_manager->mark_output_list();
  const base::trt::CalibrationConfig& calib_config = config_manager->calib_config();
  CHECK(FLAGS_enable_generate_trt_engine) << trt_engine_file;
  CHECK_GT(min_batch_size, 0);
  CHECK_GT(max_batch_size, 0);
  CHECK(file_path::Exists(onnx_file)) << onnx_file;

  TrtLogger logger;
  TrtUniquePtr<nvinfer1::IBuilder> builder{nvinfer1::createInferBuilder(logger)};
  CHECK(builder != nullptr);
  TrtUniquePtr<nvinfer1::IBuilderConfig> config{builder->createBuilderConfig()};
  CHECK(config != nullptr);
  TrtUniquePtr<nvinfer1::INetworkDefinition> network{builder->createNetworkV2(kNetworkFlags)};
  CHECK(network != nullptr);
  TrtUniquePtr<nvonnxparser::IParser> parser{nvonnxparser::createParser(*network, logger)};
  CHECK(parser != nullptr);
  auto calibrator = std::make_unique<trt::TrtCalibrator>(config_manager);
  CHECK(calibrator != nullptr);

  if (config_manager->enable_detailed_profiling()) {
    LOG(ERROR) << "Set Profiling Verbosity: kDETAILED";
    config->setProfilingVerbosity(nvinfer1::ProfilingVerbosity::kDETAILED);
  }
  CHECK(parser->parseFromFile(onnx_file.c_str(), FLAGS_trt_log_level));

  if (manipulator) {
    manipulator(network.get());
  }

  if (FLAGS_trt_log_level == 4) {
    base::trt::PrintNetwork(*network);
  }

  // DLA is only available for tensorRT8.6+SM87 platform
  const bool use_dla = (builder->getNbDLACores() > 0) && (NV_TENSORRT_MINOR >= 6) &&
                 (static_cast<nvinfer1::DeviceType>(config_manager->model_config().device_type()) ==
                  nvinfer1::DeviceType::kDLA);
  if (use_dla) {
    config->setDefaultDeviceType(nvinfer1::DeviceType::kDLA);
    for (int i = 0; i < network->getNbLayers(); ++i) {
      nvinfer1::ILayer* layer = CHECK_NOTNULL(network->getLayer(i));
      const std::string& layer_name = layer->getName();
      bool is_fallback_layer = false;
      for (const auto& prefix : dla_fallback_gpu_module_prefix_list) {
        if (layer_name.compare(0, prefix.length(), prefix) == 0) {
          is_fallback_layer = true;
        }
      }
      if (is_fallback_layer) {
        LOG(ERROR) << strings::Format("DLA: Setting layer {} fallback to gpu", layer_name);
        config->setDeviceType(layer, nvinfer1::DeviceType::kGPU);
      }
    }
    config->setDLACore(config_manager->model_config().dla_core());
    config->setFlag(nvinfer1::BuilderFlag::kGPU_FALLBACK);
  } else {
    config->setDefaultDeviceType(nvinfer1::DeviceType::kGPU);
  }
  config->setMemoryPoolLimit(nvinfer1::MemoryPoolType::kWORKSPACE, workspace);

  // Set range of batch size for input nodes
  nvinfer1::IOptimizationProfile* profile = builder->createOptimizationProfile();
  for (int i = 0; i < network->getNbInputs(); ++i) {
    const nvinfer1::ITensor* input_tensor = network->getInput(i);
    const char* tensor_name = input_tensor->getName();
    const nvinfer1::Dims dims = input_tensor->getDimensions();
    if (static_batch_size_list.count(tensor_name) > 0) {
      profile->setDimensions(tensor_name, nvinfer1::OptProfileSelector::kMIN, dims);
      profile->setDimensions(tensor_name, nvinfer1::OptProfileSelector::kOPT, dims);
      profile->setDimensions(tensor_name, nvinfer1::OptProfileSelector::kMAX, dims);
      continue;
    }
    nvinfer1::Dims min_dims = dims;
    min_dims.d[0] = min_batch_size;
    nvinfer1::Dims max_dims = dims;
    max_dims.d[0] = max_batch_size;
    profile->setDimensions(tensor_name, nvinfer1::OptProfileSelector::kMIN, min_dims);
    profile->setDimensions(tensor_name, nvinfer1::OptProfileSelector::kOPT, max_dims);
    profile->setDimensions(tensor_name, nvinfer1::OptProfileSelector::kMAX, max_dims);
  }
  config->addOptimizationProfile(profile);

  // Close All Tactic Sources
  config->setTacticSources(0);

  const int sm = trt::GetGpuArch();
  // Unified Engine for A100, 3080, 4090
  if (sm >= 80 && sm != 87) {
    LOG(ERROR) << "Building TensorRT Unified Engine on sm" << sm;
    config->setHardwareCompatibilityLevel(nvinfer1::HardwareCompatibilityLevel::kAMPERE_PLUS);
  }

  // TimingCache config
  bool need_save_timing_cache = false;
  std::unique_ptr<nvinfer1::ITimingCache> timing_cache{};
  const std::string timing_cache_path = config_manager->model_config().timing_cache_path();
  if (!timing_cache_path.empty()) {
    timing_cache =
        trt::BuildTimingCacheFromFile(timing_cache_path, config.get(), &need_save_timing_cache);
  }

  // Prefer that layers execute in specified precisions
  config->setFlag(nvinfer1::BuilderFlag::kPREFER_PRECISION_CONSTRAINTS);
  const walle::DataType& model_precision = precision_config.precision;
  const std::unordered_map<std::string, walle::DataType>& node_precision_map =
      precision_config.node_precision_map;
  switch (model_precision) {
    case walle::DataType::FLOAT16:
      config->setFlag(nvinfer1::BuilderFlag::kFP16);
      if (config->getDefaultDeviceType() == nvinfer1::DeviceType::kDLA) {
        LOG(FATAL) << "FP16 in DLA is too slow, please check model_precision.";
      }
      break;
    case walle::DataType::INT8:
      if (!calib_config.fallback_layers().empty()) {
        config->setFlag(nvinfer1::BuilderFlag::kSTRICT_TYPES);
      }
      config->setFlag(nvinfer1::BuilderFlag::kINT8);
      config->setFlag(nvinfer1::BuilderFlag::kFP16);
      base::trt::SetFallbackModulePrecision(int8_module_prefix_list, network.get());
      switch (calib_config.trt_quantize_feed_type()) {
        case trt::CalibrationConfig::DataCalibration:
          ConfigINT8Profile(calib_config, *network, builder.get(), config.get());
          config->setInt8Calibrator(calibrator.get());
          base::trt::SetFallbackLayerPrecision(calib_config.fallback_layers(), network.get());
          break;
        case trt::CalibrationConfig::TensorRange:
          config->setInt8Calibrator(nullptr);
          if (use_dla) {
            ConfigINT8Profile(calib_config, *network, builder.get(), config.get());
          }
          base::trt::SetFallbackLayerPrecision(calib_config.fallback_layers(), network.get());
          QuantizeWithDynamicRange(calib_config, network.get());
          break;
        case trt::CalibrationConfig::QuantizeLayer:
          config->setInt8Calibrator(nullptr);
          base::trt::SetFallbackLayerPrecision(calib_config.fallback_layers(), network.get());
          break;
        default:
          break;
      }
      break;
    default:
      break;
  }

  // set input node precision
  for (int i = 0; i < network->getNbInputs(); ++i) {
    nvinfer1::ITensor* input_tensor = network->getInput(i);
    const std::string& input_name = input_tensor->getName();
    if (node_precision_map.count(input_name) > 0) {
      const walle::DataType& node_precision = node_precision_map.at(input_name);
      input_tensor->setType(trt::ConvertToTensorRTDataType(node_precision));
    }
  }
  // set output node precision
  for (int i = 0; i < network->getNbOutputs(); ++i) {
    nvinfer1::ITensor* output_tensor = network->getOutput(i);
    const std::string& output_name = output_tensor->getName();
    if (node_precision_map.count(output_name) > 0) {
      const walle::DataType& node_precision = node_precision_map.at(output_name);
      output_tensor->setType(trt::ConvertToTensorRTDataType(node_precision));
    }
  }
  // set inner node precision
  for (int i = 0; i < network->getNbLayers(); ++i) {
    nvinfer1::ILayer* layer = network->getLayer(i);
    const std::string& layer_name = layer->getName();
    if (node_precision_map.count(layer_name) > 0) {
      const walle::DataType& precision = node_precision_map.at(layer_name);
      layer->setPrecision(trt::ConvertToTensorRTDataType(precision));
    }
    if (mark_output_list.count(layer_name) > 0) {
      network->markOutput(*layer->getOutput(0));
      LOG(ERROR) << "Building TensorRT Engine with MarkOutput layer: " << layer_name;
    }
  }

  LOG(ERROR) << "Building TensorRT Engine: " << trt_engine_file;
  TrtUniquePtr<nvinfer1::IHostMemory> serialized_engine{
      builder->buildSerializedNetwork(*network, *config)};
  WriteEngineToFile(trt_engine_file, CHECK_NOTNULL(serialized_engine.get()));
  LOG(ERROR) << "Building TensorRT Engine: " << trt_engine_file << " done.";

  if (need_save_timing_cache) {
    auto new_timing_cache = config->getTimingCache();
    trt::SaveTimingCacheFile(timing_cache_path, new_timing_cache);
  }
}

}  // namespace base
