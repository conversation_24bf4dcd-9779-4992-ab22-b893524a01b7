// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <memory>
#include <string>
#include <unordered_map>
#include <vector>

#include "NvInfer.h"
#include "NvInferPlugin.h"
#include "glog/logging.h"

#include "base/common/macros.h"
#include "base/container/utils.h"
#include "base/cuda/operators/utils/op_params.h"
#include "base/cuda/tensorrt/trt_plugin_base.h"
#include "base/cuda/tensorrt/trt_plugin_params.h"

namespace base {
namespace trt {

// TrtPluginDynamicBase is a wrapper of IPluginV2DynamicExt. To make sure the same code style, we
// can inherit TrtPluginDynamicBase instead of inherit IPluginV2DynamicExt directly.
class TrtPluginDynamicBase : public nvinfer1::IPluginV2DynamicExt {
 public:
  TrtPluginDynamicBase() = default;
  virtual ~TrtPluginDynamicBase() = default;

  // Return the plugin type. Should match the plugin name returned by the corresponding plugin
  // creator.
  const char* getPluginType() const noexcept final { return GetPluginType(); }

  // Return the plugin version. Should match the plugin version returned by the corresponding plugin
  // creator.
  const char* getPluginVersion() const noexcept final { return GetPluginVersion(); }

  // Initialize the layer for execution. This is called when the engine is created.
  //
  // Returns
  //    0 for success, else non-zero (which will cause engine termination).
  int initialize() noexcept final { return Initialize(); }

  // Release resources acquired during plugin layer initialization. This is called when the engine
  // is destroyed.
  void terminate() noexcept final {
    // TODO(wanglong): add some monitor to initialize() and terminate().
    Terminate();
  }

  // Find the size of the serialization buffer required.
  //
  // Returns
  //    The size of the serialization buffer.
  size_t getSerializationSize() const noexcept final { return GetSerializationSize(); }

  // Serialize the layer.
  //
  // Parameters
  // buffer A pointer to a buffer to serialize data. Size of buffer must be equal to value returned
  // by getSerializationSize.
  void serialize(void* buffer) const noexcept final { Serialize(buffer); }

  // Destroy the plugin object. This will be called when the network, builder or engine is
  // destroyed.
  void destroy() noexcept final { Destroy(); }

  // Set the namespace that this plugin object belongs to. Ideally, all plugin objects from the same
  // plugin library should have the same namespace.
  void setPluginNamespace(const char* pluginNamespace) noexcept final {
    plugin_namespace_ = pluginNamespace;
  }

  // Return the namespace of the plugin object.
  const char* getPluginNamespace() const noexcept { return plugin_namespace_.c_str(); }

  // Get the number of outputs from the layer.
  //
  // Returns
  //    The number of outputs.
  //
  // This function is called by the implementations of INetworkDefinition and IBuilder. In
  // particular, it is called prior to any call to initialize().
  int getNbOutputs() const noexcept final { return GetNumOutputs(); }

  // Return the DataType of the plugin output at the requested index. The default behavior should be
  // to return the type of the first input, or DataType::kFLOAT if the layer has no inputs. The
  // returned data type must have a format that is supported by the plugin.
  // Warning
  //    DataType:kBOOL not supported.
  nvinfer1::DataType getOutputDataType(int index, nvinfer1::DataType const* inputTypes,
                                       int nbInputs) const noexcept final {
    return GetOutputDataType(index, inputTypes, nbInputs);
  }

  // Attach the plugin object to an execution context and grant the plugin the access to some
  // context resource.
  //
  // Parameters
  //    cudnn  The CUDNN context handle of the execution context
  //    cublas The cublas context handle of the execution context
  //    allocator  The allocator used by the execution context
  //
  // This function is called automatically for each plugin when a new execution context is created.
  // If the context was created without resources, this method is not called until the resources are
  // assigned. It is also called if new resources are assigned to the context.
  // If the plugin needs per-context resource, it can be allocated here. The plugin can also get
  // context-owned CUDNN and CUBLAS context here.
  // Note: In the automotive safety context, the CUDNN and CUBLAS parameters will be nullptr because
  // CUDNN and CUBLAS is not used by the safe runtime.
  void attachToContext(cudnnContext* cudnn_context, cublasContext* cublas_context,
                       nvinfer1::IGpuAllocator* gpu_allocator) noexcept final {
    AttachToContext(cudnn_context, cublas_context, gpu_allocator);
  }

  // Detach the plugin object from its execution context.
  // This function is called automatically for each plugin when a execution context is destroyed or
  // the context resources are unassigned from the context.
  // If the plugin owns per-context resource, it can be released here.
  void detachFromContext() noexcept final { DetachFromContext(); }

  // Clone the plugin object. This copies over internal plugin parameters as well and returns a new
  // plugin object with these parameters. If the source plugin is pre-configured with
  // configurePlugin(), the returned object should also be pre-configured. The returned object
  // should allow attachToContext() with a new execution context. Cloned plugin objects can share
  // the same per-engine immutable resource (e.g. weights) with the source object (e.g. via
  // ref-counting) to avoid duplication.
  IPluginV2DynamicExt* clone() const noexcept final {
    TrtPluginDynamicBase* plugin = Clone();
    plugin->SetPluginName(plugin_name_);
    plugin->setPluginNamespace(plugin_namespace_.c_str());
    plugin_params_.CopyDataTo(&plugin->plugin_params_);
    plugin->GetParams();
    return plugin;
  }

  // Get expressions for computing dimensions of an output tensor from dimensions of the input
  // tensors.
  //
  // Parameters
  //    outputIndex  The index of the output tensor
  //    inputs Expressions for dimensions of the input tensors
  //    nbInputs The number of input tensors
  //    exprBuilder  Object for generating new expressions
  //
  // This function is called by the implementations of IBuilder during analysis of the network.
  // Example #1: A plugin has a single output that transposes the last two dimensions of the
  // plugin's single input. The body of the override of getOutputDimensions can be:
  //    DimsExprs output(inputs[0]);
  //    std::swap(output.d[output.nbDims-1], output.d[output.nbDims-2]);
  //    return output;
  //
  // Example #2: A plugin concatenates its two inputs along the first dimension. The body of the
  // override of getOutputDimensions can be:
  //    DimsExprs output(inputs[0]);
  //    output.d[0] = exprBuilder.operation(DimensionOperation::kSUM, *inputs[0].d[0],
  //    *inputs[1].d[0]); return output;
  // using nvinfer1::IPluginV2DynamicExt::getOutputDimensions;
  nvinfer1::DimsExprs getOutputDimensions(int outputIndex, const nvinfer1::DimsExprs* inputs,  // NOLINT
                                          int nbInputs,
                                          nvinfer1::IExprBuilder& exprBuilder) noexcept final {  // NOLINT
    return GetOutputDimensions(outputIndex, inputs, nbInputs, exprBuilder);
  }

  // Return true if plugin supports the format and datatype for the input/output indexed by pos.
  //
  // For this method inputs are numbered 0..(nbInputs-1) and outputs are numbered
  // nbInputs..(nbInputs+nbOutputs-1). Using this numbering, pos is an index into InOut, where 0 <=
  // pos < nbInputs+nbOutputs-1.
  //
  // TensorRT invokes this method to ask if the input/output indexed by pos supports the
  // format/datatype specified by inOut[pos].format and inOut[pos].type. The override should return
  // true if that format/datatype at inOut[pos] are supported by the plugin. If support is
  // conditional on other input/output formats/datatypes, the plugin can make its result conditional
  // on the formats/datatypes in inOut[0..pos-1], which will be set to values that the plugin
  // supports. The override should not inspect inOut[pos+1..nbInputs+nbOutputs-1], which will have
  // invalid values. In other words, the decision for pos must be based on inOut[0..pos] only.
  //
  // Some examples:
  //
  // - A definition for a plugin that supports only FP16 NCHW:
  //     return inOut.format[pos] == TensorFormat::kLINEAR && inOut.type[pos] == DataType::kHALF;
  //
  // - A definition for a plugin that supports only FP16 NCHW for its two inputs, and FP32 NCHW for
  //   its single output:
  //     return inOut.format[pos] == TensorFormat::kLINEAR && (inOut.type[pos] == pos < 2 ?
  //     DataType::kHALF : DataType::kFLOAT);
  //
  // - A definition for a "polymorphic" plugin with two inputs and one output that supports any
  //   format or type, but the inputs and output must have the same format and type:
  //     return pos == 0 || (inOut.format[pos] == inOut.format[0] && inOut.type[pos] ==
  //     inOut.type[0]);
  //
  // Warning: TensorRT will stop asking for formats once it finds kFORMAT_COMBINATION_LIMIT on
  // combinations.
  bool supportsFormatCombination(int pos, const nvinfer1::PluginTensorDesc* inOut, int nbInputs,
                                 int nbOutputs) noexcept final {
    return SupportsFormatCombination(pos, inOut, nbInputs, nbOutputs);
  }

  // Configure the plugin.
  //
  // configurePlugin() can be called multiple times in both the build and execution phases. The
  // build phase happens before initialize() is called and only occurs during creation of an engine
  // by IBuilder. The execution phase happens after initialize() is called and occurs during both
  // creation of an engine by IBuilder and execution of an engine by IExecutionContext.
  //
  // Build phase: IPluginV2DynamicExt->configurePlugin is called when a plugin is being prepared for
  // profiling but not for any specific input size. This provides an opportunity for the plugin to
  // make algorithmic choices on the basis of input and output formats, along with the bound of
  // possible dimensions. The min and max value of the DynamicPluginTensorDesc correspond to the
  // kMIN and kMAX value of the current profile that the plugin is being profiled for, with the
  // desc.dims field corresponding to the dimensions of plugin specified at network creation.
  // Wildcard dimensions will exist during this phase in the desc.dims field.
  //
  // Execution phase: IPluginV2DynamicExt->configurePlugin is called when a plugin is being prepared
  // for executing the plugin for a specific dimensions. This provides an opportunity for the plugin
  // to change algorithmic choices based on the explicit input dimensions stored in desc.dims field.
  //
  //   - IBuilder will call this function once per profile, with desc.dims resolved to the values
  // specified by the kOPT field of the current profile. Wildcard dimensions will not exist during
  // this phase.
  //   - IExecutionContext will call this during the next subsequent instance enqueue[V2]()
  // or execute[V2]() if:
  //     - The batch size is changed from previous call of execute()/enqueue() if
  //     hasImplicitBatchDimension() returns true.
  //     - The optimization profile is changed via setOptimizationProfile() or
  //     setOptimizationProfileAsync().
  //     - An input shape binding is changed via setInputShapeBinding().
  //     - An input execution binding is changed via setBindingDimensions().
  // Warning
  //    The execution phase is timing critical during IExecutionContext but is not part of the
  //    timing loop when called from IBuilder. Performance bottlenecks of configurePlugin won't show
  //    up during  engine building but will be visible during execution after calling functions that
  //    trigger layer resource updates.
  //
  // Parameters:
  //   in The input tensors attributes that are used for configuration.
  //   nbInputs Number of input tensors.
  //   out The output tensors attributes that are used for configuration.
  //   nbOutputs Number of output tensors.
  //
  // using nvinfer1::IPluginV2DynamicExt::configurePlugin;
  void configurePlugin(const nvinfer1::DynamicPluginTensorDesc* in, int nbInputs,
                       const nvinfer1::DynamicPluginTensorDesc* out, int nbOutputs) noexcept final {
    ConfigurePlugin(in, nbInputs, out, nbOutputs);
  }

  // Find the workspace size required by the layer.
  //
  // This function is called after the plugin is configured, and possibly during execution. The
  // result should be a sufficient workspace size to deal with inputs and outputs of the given size
  // or any smaller problem.
  //
  // Returns
  // The workspace size.
  // using nvinfer1::IPluginV2DynamicExt::getWorkspaceSize;
  size_t getWorkspaceSize(const nvinfer1::PluginTensorDesc* inputs, int32_t nbInputs,
                          const nvinfer1::PluginTensorDesc* outputs, int32_t nbOutputs) const
      noexcept final {
    return GetWorkspaceSize(inputs, nbInputs, outputs, nbOutputs);
  }

  // Execute the layer.
  //
  // Parameters
  //    inputDesc how to interpret the memory for the input tensors.
  //    outputDesc how to interpret the memory for the output tensors.
  //    inputs The memory for the input tensors.
  //    outputs The memory for the output tensors.
  //    workspace Workspace for execution.
  //    stream The stream in which to execute the kernels.
  //
  // Returns
  //    0 for success, else non-zero (which will cause engine termination).
  int32_t enqueue(const nvinfer1::PluginTensorDesc* inputDesc,
                  const nvinfer1::PluginTensorDesc* outputDesc, const void* const* inputs,
                  void* const* outputs, void* workspace, cudaStream_t stream) noexcept final {
    return Enqueue(inputDesc, outputDesc, inputs, outputs, workspace, stream);
  }

 protected:
  // Plugin execution order:
  //   1. Constructor
  //     - construct plugin with different methods(clone, deserialize);
  //     - initialize plugin params;
  //   2. ConfigurePlugin
  //     - config the plugin input and output;
  //     - the plugin sets up its internal state and selects the most appropriate algorithm and data
  //      structures for the given configuration.
  //   3. Initialize
  //     - set up internal data structures and prepare for execution according to configuration.
  //   4. Enqueue
  //     - Execute the plugin.
  //   5. Terminate
  //     - The engine context is destroyed, and all the resources held by the plugin should be
  //     released.
  //   6. Destroy
  //     - Used to destroy the plugin object and/or other memory allocated each time a new plugin
  //     object is created.
  //   7. Destructor(called by Destroy)

  // Functions inherited from IPluginV2
  virtual const char* GetPluginType() const = 0;
  virtual const char* GetPluginVersion() const = 0;
  // Assume the plugin has only 1 output if GetNumOutputs not override.
  virtual int GetNumOutputs() const { return 1; }
  virtual int Initialize() { return 0; }
  virtual void Terminate() {}
  virtual size_t GetSerializationSize() const { return plugin_params_.size(); }
  virtual void Serialize(void* buffer) const { plugin_params_.Serialize(buffer); }
  virtual void Destroy() { delete this; }

  // Functions inherited from IPluginV2Ext
  // Return the datatype of first input if not override.
  virtual nvinfer1::DataType GetOutputDataType(int index, nvinfer1::DataType const* input_types,
                                               int num_inputs) const {
    CHECK_GT(num_inputs, 0);
    return input_types[0];
  }
  virtual void AttachToContext(cudnnContext* cudnn_context, cublasContext* cublas_context,
                               nvinfer1::IGpuAllocator* gpu_allocator) {}
  virtual void DetachFromContext() {}
  // Functions inherited from IPluginV2DynamicExt
  virtual TrtPluginDynamicBase* Clone() const = 0;
  // Return the dimensions of first input if not override.
  virtual nvinfer1::DimsExprs GetOutputDimensions(int output_index,
                                                  const nvinfer1::DimsExprs* inputs, int num_inputs,
                                                  nvinfer1::IExprBuilder& expr_builder) {  // NOLINT
    CHECK_GT(num_inputs, 0);
    return inputs[0];
  }
  virtual bool SupportsFormatCombination(int pos, const nvinfer1::PluginTensorDesc* in_out,
                                         int num_inputs, int num_outputs) {
    return true;
  }
  virtual void ConfigurePlugin(const nvinfer1::DynamicPluginTensorDesc* in, int num_inputs,
                               const nvinfer1::DynamicPluginTensorDesc* out, int num_outputs) {}
  virtual size_t GetWorkspaceSize(const nvinfer1::PluginTensorDesc* inputs, int32_t num_inputs,
                                  const nvinfer1::PluginTensorDesc* outputs,
                                  int32_t num_outpus) const {
    return 0;
  }
  virtual size_t Enqueue(const nvinfer1::PluginTensorDesc* input_desc,
                         const nvinfer1::PluginTensorDesc* output_desc, const void* const* inputs,
                         void* const* outputs, void* workspace, cudaStream_t stream) = 0;

  const std::string& GetPluginName() const { return plugin_name_; }
  void SetPluginName(const std::string& plugin_name) { plugin_name_ = plugin_name; }

  virtual void GetParams() = 0;
  TrtPluginParams plugin_params_;

  static nvinfer1::PluginFieldCollection field_collection_;
  static std::vector<nvinfer1::PluginField> attributes_;
  static std::unordered_map<std::string, std::unique_ptr<cuda::OpParams>>
      mutable_plugin_params_map_;

 public:
  static cuda::OpParams* GetMutablePluginParams(const std::string plugin_key);
  static void SetMutablePluginParams(const std::string& plugin_key,
                                     const cuda::OpParamsMeta& op_params_meta);

 private:
  std::string plugin_name_;
  std::string plugin_namespace_;

  DISALLOW_COPY_AND_ASSIGN(TrtPluginDynamicBase);
};

}  // namespace trt
}  // namespace base
