// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/cuda/tensorrt/trt_quantize_utils.h"

#include <unordered_map>

#include "glog/logging.h"
#include "gtest/gtest.h"

namespace base {
namespace trt {

namespace {
  const char kRangeMapFile[] = "common/test/resources/tensorrt_models/dummy_range.prototxt";
}  // namespace

TEST(TrtDynamicRange, ReadRangeTest) {
  const std::unordered_map<std::string, float> tensor_range_map =
      base::trt::GetTensorRangeMap(kRangeMapFile);
  for (auto pair : tensor_range_map) {
    LOG(ERROR) << pair.first << " " << pair.second;
  }
}

}  // namespace trt
}  // namespace base
