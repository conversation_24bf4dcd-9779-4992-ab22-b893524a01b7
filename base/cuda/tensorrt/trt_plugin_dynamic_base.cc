// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/cuda/tensorrt/trt_plugin_dynamic_base.h"

namespace base {
namespace trt {

namespace {
constexpr char kOpParamsName[] = "op_params";
}

std::vector<nvinfer1::PluginField> TrtPluginDynamicBase::attributes_ = {
    nvinfer1::PluginField(kOpParamsName, nullptr, nvinfer1::PluginFieldType::kUNKNOWN, -1),
};

nvinfer1::PluginFieldCollection TrtPluginDynamicBase::field_collection_{
    .nbFields = static_cast<int>(TrtPluginDynamicBase::attributes_.size()),
    .fields = TrtPluginDynamicBase::attributes_.data()};

std::unordered_map<std::string, std::unique_ptr<cuda::OpParams>>
    base::trt::TrtPluginDynamicBase::mutable_plugin_params_map_;

cuda::OpParams* TrtPluginDynamicBase::GetMutablePluginParams(const std::string plugin_key) {
  if (TrtPluginDynamicBase::mutable_plugin_params_map_.count(plugin_key)) {
    return TrtPluginDynamicBase::mutable_plugin_params_map_.at(plugin_key).get();
  } else {
    return nullptr;
  }
}

void TrtPluginDynamicBase::SetMutablePluginParams(const std::string& plugin_key,
                                                  const cuda::OpParamsMeta& op_params_meta) {
  TrtPluginDynamicBase::mutable_plugin_params_map_[plugin_key] =
      std::make_unique<cuda::OpParams>(op_params_meta);
}

}  // namespace trt
}  // namespace base
