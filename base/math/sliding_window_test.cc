// Copyright @2022 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/math/sliding_window.h"

#include <limits>
#include <vector>

#include "gtest/gtest.h"

#include "base/math/math_util.h"

namespace math {

TEST(SlidingWindowTest, DefaultFunction) {
  const std::vector<double> inputs = {1.0, 2.0, 3.0, 5.0, 4.0, 6.0, 7.0};
  {
    SlidingWindowAverage<double> sliding_window = SlidingWindowAverage<double>(3);
    const std::vector<double> expected_outputs = {1.0, 3.0 / 2.0, 2.0,       10.0 / 3.0,
                                                  4.0, 5.0,       17.0 / 3.0};
    for (int i = 0; i < static_cast<int>(inputs.size()); ++i) {
      sliding_window.Add(inputs[i]);
      EXPECT_NEAR(sliding_window.GetAverage(), expected_outputs[i], math::kEpsilon);
    }
    EXPECT_EQ(sliding_window.capacity(), 3);
    EXPECT_EQ(sliding_window.size(), 3);
  }
  {
    const std::vector<double> expected_outputs = {
        1.0, 3.0 / 2.0, 2.0, 10.0 / 3.0, 4.0, 5.0, 17.0 / 3.0};
    SlidingWindowAverage<double> sliding_window = SlidingWindowAverage<double>(3);
    sliding_window.Add(inputs[0]);
    EXPECT_NEAR(sliding_window.GetAverage(), expected_outputs[0], math::kEpsilon);
    base::CircularQueue<double> elements = sliding_window.elements();
    int index = sliding_window.element_index();
    double sum = sliding_window.sum();
    for (int i = 1; i < static_cast<int>(inputs.size()); ++i) {
      sliding_window = SlidingWindowAverage<double>(elements, index, sum);
      sliding_window.Add(inputs[i]);
      elements = sliding_window.elements();
      index = sliding_window.element_index();
      sum = sliding_window.sum();
      EXPECT_NEAR(sliding_window.GetAverage(), expected_outputs[i], math::kEpsilon);
    }
    EXPECT_EQ(sliding_window.capacity(), 3);
    EXPECT_EQ(sliding_window.size(), 3);
  }
  {
    SlidingWindowMax<double> sliding_window = SlidingWindowMax<double>(3);
    const std::vector<double> expected_outputs = {1.0, 2.0, 3.0, 5.0, 5.0, 6.0, 7.0};
    for (int i = 0; i < static_cast<int>(inputs.size()); ++i) {
      sliding_window.Add(inputs[i]);
      EXPECT_NEAR(sliding_window.GetMax(), expected_outputs[i], math::kEpsilon);
    }
  }
  {
    SlidingWindowMin<double> sliding_window = SlidingWindowMin<double>(3);
    const std::vector<double> expected_outputs = {1.0, 1.0, 1.0, 2.0, 3.0, 4.0, 4.0};
    for (int i = 0; i < static_cast<int>(inputs.size()); ++i) {
      sliding_window.Add(inputs[i]);
      EXPECT_NEAR(sliding_window.GetMin(), expected_outputs[i], math::kEpsilon);
    }
  }
  {
    const std::vector<double> weights = {1.0, 1.0, 1.0};
    SlidingWindowWeightedAverage<double> sliding_window =
        SlidingWindowWeightedAverage<double>(3, weights);
    const std::vector<double> expected_outputs = {
        1.0, 3.0 / 2.0, 2.0, 10.0 / 3.0, 4.0, 5.0, 17.0 / 3.0};
    for (int i = 0; i < static_cast<int>(inputs.size()); ++i) {
      sliding_window.Add(inputs[i]);
      EXPECT_NEAR(sliding_window.GetWeightedAverage(), expected_outputs[i], math::kEpsilon);
    }
    EXPECT_EQ(sliding_window.capacity(), 3);
    EXPECT_EQ(sliding_window.size(), 3);
  }
  {
    const std::vector<double> weights = {0.5, 2.0, 0.5};
    SlidingWindowWeightedAverage<double> sliding_window =
        SlidingWindowWeightedAverage<double>(3, weights);
    const std::vector<double> expected_outputs = {
        1.0, 4.5 / 2.5, 2.0, 9.5 / 3.0, 13.5 / 3.0, 13.5 / 3.0, 17.5 / 3.0};
    for (int i = 0; i < static_cast<int>(inputs.size()); ++i) {
      sliding_window.Add(inputs[i]);
      EXPECT_NEAR(sliding_window.GetWeightedAverage(), expected_outputs[i], math::kEpsilon);
    }
    EXPECT_EQ(sliding_window.capacity(), 3);
    EXPECT_EQ(sliding_window.size(), 3);
  }
}

namespace {

struct SInterval {
  double start_s = 0.0;
  double end_s = 0.0;
};
bool operator<(const SInterval& lhs, const SInterval& rhs) {
  return lhs.start_s + lhs.end_s < rhs.start_s + rhs.end_s;
}
SInterval operator+(const SInterval& lhs, const SInterval& rhs) {
  return SInterval{.start_s = lhs.start_s + rhs.start_s, .end_s = lhs.end_s + rhs.end_s};
}
SInterval operator-(const SInterval& lhs, const SInterval& rhs) {
  return SInterval{.start_s = lhs.start_s - rhs.start_s, .end_s = lhs.end_s - rhs.end_s};
}
SInterval operator*(const SInterval& lhs, double rhs) {
  return SInterval{.start_s = lhs.start_s * rhs, .end_s = lhs.end_s * rhs};
}

}  // namespace

TEST(SlidingWindowTest, SelfDefinedStruct) {
  std::vector<SInterval> inputs;
  for (int i = 0; i < 3; ++i) {
    inputs.emplace_back(SInterval{.start_s = 0.0 + 10.0 * i, .end_s = 2.0 + 10.0 * i});
  }
  {
    SlidingWindowAverage<SInterval> sliding_window = SlidingWindowAverage<SInterval>(3);
    SInterval output;
    for (int i = 0; i < static_cast<int>(inputs.size()); ++i) {
      sliding_window.Add(inputs[i]);
      output = sliding_window.GetAverage();
    }
    EXPECT_NEAR(output.start_s, 10.0, math::kEpsilon);
    EXPECT_NEAR(output.end_s, 12.0, math::kEpsilon);
  }
  {
    SlidingWindowMax<SInterval> sliding_window = SlidingWindowMax<SInterval>(3);
    SInterval output;
    for (int i = 0; i < static_cast<int>(inputs.size()); ++i) {
      sliding_window.Add(inputs[i]);
      output = sliding_window.GetMax();
    }
    EXPECT_NEAR(output.start_s, 20.0, math::kEpsilon);
    EXPECT_NEAR(output.end_s, 22.0, math::kEpsilon);
  }
  {
    SlidingWindowMin<SInterval> sliding_window = SlidingWindowMin<SInterval>(3);
    SInterval output;
    for (int i = 0; i < static_cast<int>(inputs.size()); ++i) {
      sliding_window.Add(inputs[i]);
      output = sliding_window.GetMin();
    }
    EXPECT_NEAR(output.start_s, 0.0, math::kEpsilon);
    EXPECT_NEAR(output.end_s, 2.0, math::kEpsilon);
  }
  {
    std::vector<double> weights = {1.0, 1.0, 1.0};
    SlidingWindowWeightedAverage<SInterval> sliding_window =
        SlidingWindowWeightedAverage<SInterval>(3, weights);
    SInterval output;
    for (int i = 0; i < static_cast<int>(inputs.size()); ++i) {
      sliding_window.Add(inputs[i]);
      output = sliding_window.GetWeightedAverage();
    }
    EXPECT_NEAR(output.start_s, 10.0, math::kEpsilon);
    EXPECT_NEAR(output.end_s, 12.0, math::kEpsilon);
  }
}

TEST(SlidingWindowTest, OverflowTest) {
  const std::vector<double> inputs = {1.0, 2.0,  3.0, 5.0, 4.0, 6.0, 7.0,
                                      8.0, 10.0, 9.0, 3.0, 2.0, 1.0};
  {
    SlidingWindowMax<double> sliding_window = SlidingWindowMax<double>(3);
    sliding_window.next_element_index_ = std::numeric_limits<int>::max() - 3;
    const std::vector<double> expected_outputs = {1.0, 2.0,  3.0,  5.0,  5.0, 6.0, 7.0,
                                                  8.0, 10.0, 10.0, 10.0, 9.0, 3.0};
    for (int i = 0; i < static_cast<int>(inputs.size()); ++i) {
      sliding_window.Add(inputs[i]);
      EXPECT_NEAR(sliding_window.GetMax(), expected_outputs[i], math::kEpsilon);
    }
    EXPECT_EQ(sliding_window.capacity(), 3);
    EXPECT_EQ(sliding_window.size(), 3);
  }
}

}  // namespace math
