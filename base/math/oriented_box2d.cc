// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>
//          <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)

#include "base/math/oriented_box2d.h"

#include <algorithm>
#include <cmath>
#include <numeric>
#include <sstream>
#include <utility>
#include <vector>

#include "glog/logging.h"

#include "base/math/internal/convex_overlap.h"
#include "base/math/math_util.h"
#include "base/math/polygon2d_utils.h"

namespace math {
namespace {

inline int ToLowerGridIndex(double value, double grid_resolution_inverse) {
  return static_cast<int>(std::floor(value * grid_resolution_inverse - math::kEpsilon));
}

inline int ToFloorGridIndex(double value, double grid_resolution_inverse) {
  return static_cast<int>(std::floor(value * grid_resolution_inverse));
}

}  // namespace

void OrientedBox2d::Init() {
  CHECK_LE(-math::kEpsilon, half_length_);
  CHECK_LE(-math::kEpsilon, half_width_);
  half_length_ = std::max(0.0, half_length_);
  half_width_ = std::max(0.0, half_width_);

  direction_ = Vector2d::FromAngle(heading_);

  const double abs_cos_heading = std::abs(direction_.x);
  const double abs_sin_heading = std::abs(direction_.y);
  const double offset_x = abs_cos_heading * half_length_ + abs_sin_heading * half_width_;
  const double offset_y = abs_sin_heading * half_length_ + abs_cos_heading * half_width_;
  const Vector2d offset(offset_x, offset_y);
  aabox_ = AxisAlignBox2d(center_ - offset, center_ + offset);
}

std::vector<LineSegment2d> OrientedBox2d::GetLineSegments() const {
  const std::array<Vector2d, 4> corners = GetCornersAsArray();
  return {
      LineSegment2d(corners[0], corners[1]),
      LineSegment2d(corners[1], corners[2]),
      LineSegment2d(corners[2], corners[3]),
      LineSegment2d(corners[3], corners[0]),
  };
}

LineSegment2d OrientedBox2d::GetFrontLineSegment() const {
  const Vector2d d0 = direction_ * half_length_;
  const Vector2d d1 = direction_.Rotate90() * half_width_;
  return LineSegment2d(center_ + d0 - d1, center_ + d0 + d1);
}

LineSegment2d OrientedBox2d::GetLeftLineSegment() const {
  const Vector2d d0 = direction_ * half_length_;
  const Vector2d d1 = direction_.Rotate90() * half_width_;
  return LineSegment2d(center_ + d0 + d1, center_ - d0 + d1);
}

LineSegment2d OrientedBox2d::GetBackLineSegment() const {
  const Vector2d d0 = direction_ * half_length_;
  const Vector2d d1 = direction_.Rotate90() * half_width_;
  return LineSegment2d(center_ - d0 + d1, center_ - d0 - d1);
}

LineSegment2d OrientedBox2d::GetRightLineSegment() const {
  const Vector2d d0 = direction_ * half_length_;
  const Vector2d d1 = direction_.Rotate90() * half_width_;
  return LineSegment2d(center_ - d0 - d1, center_ + d0 - d1);
}

void OrientedBox2d::LongitudinalExtend(double extension_length) { Extend(extension_length, 0.0); }

void OrientedBox2d::LateralExtend(double extension_width) { Extend(0.0, extension_width); }

std::vector<Vector2i> OrientedBox2d::GetTouchedGrids(double grid_resolution,
                                                     const Vector2d& base_point) const {
  CHECK_GE(grid_resolution, math::kEpsilon);
  if (half_length_ < math::kEpsilon || half_width_ < math::kEpsilon) {
    return {};
  }
  const double grid_resolution_inverse = 1.0 / grid_resolution;
  // ensure grids cover obb box
  const int min_xi = ToLowerGridIndex(aabox_.min_x() - base_point.x, grid_resolution_inverse);
  const int max_xi = ToFloorGridIndex(aabox_.max_x() - base_point.x, grid_resolution_inverse);
  const int min_yi = ToLowerGridIndex(aabox_.min_y() - base_point.y, grid_resolution_inverse);
  const int max_yi = ToFloorGridIndex(aabox_.max_y() - base_point.y, grid_resolution_inverse);
  std::vector<Vector2i> touched_grids;
  touched_grids.reserve((max_yi - min_yi) * (max_xi - min_xi));
  const std::array<Vector2d, 4> corner_points = GetCornersAsArray();
  // find the initial_point_x corresponding to smallest initial_point_y among four points in the box
  double initial_point_x = aabox_.min_x();
  double initial_point_y = aabox_.max_y();
  for (const Vector2d& point : corner_points) {
    if (point.y < initial_point_y) {
      initial_point_x = point.x;
      initial_point_y = point.y;
    }
  }
  int initial_xi_index = ToFloorGridIndex(initial_point_x - base_point.x, grid_resolution_inverse);
  initial_xi_index = math::Clamp(initial_xi_index, min_xi, max_xi);
  DCHECK(IsGridIn(base_point, grid_resolution, initial_xi_index, min_yi));
  int left_index = initial_xi_index - 1;
  int right_index = initial_xi_index + 1;
  bool is_grid_in = false;
  bool previous_is_grid_in = false;
  const int left_op_array[2] = {1, -1};
  const int right_op_array[2] = {-1, 1};
  for (int yi = min_yi; yi <= max_yi; ++yi) {
    previous_is_grid_in = IsGridIn(base_point, grid_resolution, left_index, yi);
    left_index += left_op_array[static_cast<int>(previous_is_grid_in)];
    while (left_index <= max_xi) {
      is_grid_in = IsGridIn(base_point, grid_resolution, left_index, yi);
      left_index += left_op_array[static_cast<int>(is_grid_in)];
      if (is_grid_in != previous_is_grid_in) {
        break;
      }
      previous_is_grid_in = is_grid_in;
    }
    if (is_grid_in) {
      left_index++;  // from is not in grid to in grid
    }
    previous_is_grid_in = IsGridIn(base_point, grid_resolution, right_index, yi);
    right_index += right_op_array[static_cast<int>(previous_is_grid_in)];
    while (right_index >= min_xi) {
      is_grid_in = IsGridIn(base_point, grid_resolution, right_index, yi);
      right_index += right_op_array[static_cast<int>(is_grid_in)];
      if (is_grid_in != previous_is_grid_in) {
        break;
      }
      previous_is_grid_in = is_grid_in;
    }
    if (is_grid_in) {
      right_index--;  // from is not in grid to in grid
    }
    CHECK_LE(left_index, right_index);
    for (int xi = left_index; xi <= right_index; ++xi) {
      touched_grids.emplace_back(xi, yi);
    }
  }
  return touched_grids;
}

std::vector<Vector2d> OrientedBox2d::GetTouchedGridsCenters(double grid_resolution,
                                                            const Vector2d& base_point) const {
  const std::vector<Vector2i> touched_grids = GetTouchedGrids(grid_resolution, base_point);
  std::vector<Vector2d> centers(touched_grids.size());
  for (int i = 0; i < static_cast<int>(touched_grids.size()); ++i) {
    centers[i].x = (touched_grids[i].x + 0.5) * grid_resolution;
    centers[i].y = (touched_grids[i].y + 0.5) * grid_resolution;
  }
  return centers;
}

// Implemented as specialization of OrientedBox2d::HasOverlap(OrientedBox2d).
bool OrientedBox2d::HasOverlap(const Vector2d& segment_start, const Vector2d& segment_end) const {
  const Vector2d offset = (segment_start + segment_end) * 0.5 - center_;
  const Vector2d segment = segment_end - segment_start;
  const double abs_inner = std::abs(direction_.InnerProd(segment));
  const double abs_outer = std::abs(direction_.CrossProd(segment));
  const double max_outer_onto_this = abs_outer * 0.5 + half_width_;
  if (std::abs(direction_.CrossProd(offset)) - max_outer_onto_this > math::kEpsilon) {
    return false;
  }
  const double max_inner_onto_this = abs_inner * 0.5 + half_length_;
  if (std::abs(direction_.InnerProd(offset)) - max_inner_onto_this > math::kEpsilon) {
    return false;
  }
  const double max_outer_onto_other = abs_outer * half_length_ + abs_inner * half_width_;
  if (std::abs(offset.CrossProd(segment)) - max_outer_onto_other > math::kEpsilon) {
    return false;
  }
  return true;
}

// Implemented as specialization of OrientedBox2d::HasOverlap(OrientedBox2d).
bool OrientedBox2d::HasOverlap(const AxisAlignBox2d& other_box) const {
  if (!aabox_.HasOverlap(other_box)) {
    return false;
  }
  const Vector2d offset = other_box.center() - center_;
  const double abs_inner = std::abs(direction_.x);
  const double abs_outer = std::abs(direction_.y);
  const double max_outer_onto_this =
      abs_outer * other_box.half_x_length() + abs_inner * other_box.half_y_length() + half_width_;
  if (std::abs(direction_.CrossProd(offset)) - max_outer_onto_this > math::kEpsilon) {
    return false;
  }
  const double max_inner_onto_this =
      abs_inner * other_box.half_x_length() + abs_outer * other_box.half_y_length() + half_length_;
  if (std::abs(direction_.InnerProd(offset)) - max_inner_onto_this > math::kEpsilon) {
    return false;
  }
  const double max_outer_onto_other =
      abs_outer * half_length_ + abs_inner * half_width_ + other_box.half_y_length();
  if (std::abs(offset.y) - max_outer_onto_other > math::kEpsilon) {
    return false;
  }
  const double max_inner_onto_other =
      abs_inner * half_length_ + abs_outer * half_width_ + other_box.half_x_length();
  if (std::abs(offset.x) - max_inner_onto_other > math::kEpsilon) {
    return false;
  }
  return true;
}

bool OrientedBox2d::HasOverlap(const OrientedBox2d& other_box) const {
  if (!aabox_.HasOverlap(other_box.aabox_)) {
    return false;
  }
  const Vector2d offset = other_box.center_ - center_;
  const double abs_inner = std::abs(direction_.InnerProd(other_box.direction_));
  const double abs_outer = std::abs(direction_.CrossProd(other_box.direction_));
  const double max_outer_onto_this =
      abs_outer * other_box.half_length_ + abs_inner * other_box.half_width_ + half_width_;
  if (std::abs(direction_.CrossProd(offset)) - max_outer_onto_this > math::kEpsilon) {
    return false;
  }
  const double max_inner_onto_this =
      abs_inner * other_box.half_length_ + abs_outer * other_box.half_width_ + half_length_;
  if (std::abs(direction_.InnerProd(offset)) - max_inner_onto_this > math::kEpsilon) {
    return false;
  }
  const double max_outer_onto_other =
      abs_outer * half_length_ + abs_inner * half_width_ + other_box.half_width_;
  if (std::abs(offset.CrossProd(other_box.direction_)) - max_outer_onto_other > math::kEpsilon) {
    return false;
  }
  const double max_inner_onto_other =
      abs_inner * half_length_ + abs_outer * half_width_ + other_box.half_length_;
  if (std::abs(offset.InnerProd(other_box.direction_)) - max_inner_onto_other > math::kEpsilon) {
    return false;
  }
  return true;
}

// Implemented as specialization of
// OrientedBox2d::HasOverlapAlongDirection(OrientedBox2d，Direction).
bool OrientedBox2d::HasOverlapAlongDirection(const AxisAlignBox2d& other_box,
                                             const math::Vector2d& direction) const {
  const double abs_inner_this = std::abs(direction_.InnerProd(direction));
  const double abs_outer_this = std::abs(direction_.CrossProd(direction));
  const double max_outer_onto_this = abs_inner_this * half_width_ + abs_outer_this * half_length_;
  const double max_outer_onto_other = std::abs(direction.y) * other_box.half_x_length() +
                                      std::abs(direction.x) * other_box.half_y_length();
  const Vector2d offset = center_ - other_box.center();
  return std::abs(offset.CrossProd(direction)) <
         (max_outer_onto_this + max_outer_onto_other + math::kEpsilon);
}

bool OrientedBox2d::HasOverlapAlongDirection(const OrientedBox2d& other_box,
                                             const math::Vector2d& direction) const {
  const double abs_inner_this = std::abs(direction_.InnerProd(direction));
  const double abs_outer_this = std::abs(direction_.CrossProd(direction));
  const double max_outer_onto_this = abs_inner_this * half_width_ + abs_outer_this * half_length_;
  const double abs_inner_other = std::abs(other_box.direction_.InnerProd(direction));
  const double abs_outer_other = std::abs(other_box.direction_.CrossProd(direction));
  const double max_outer_onto_other =
      abs_inner_other * other_box.half_width_ + abs_outer_other * other_box.half_length_;
  const Vector2d offset = center_ - other_box.center_;
  return std::abs(offset.CrossProd(direction)) <
         (max_outer_onto_this + max_outer_onto_other + math::kEpsilon);
}

double OrientedBox2d::DistanceTo(const LineSegment2d& segment) const {
  if (HasOverlap(segment.start(), segment.end())) {
    return 0.0;
  }
  double min_dist2 = std::min(DistanceSquareTo(segment.start()), DistanceSquareTo(segment.end()));
  for (const Vector2d& p : GetCornersAsArray()) {
    min_dist2 = std::min(min_dist2, segment.DistanceSquareTo(p));
  }
  return std::sqrt(min_dist2);
}

double OrientedBox2d::DistanceSquareTo(const Vector2d& segment_start,
                                       const Vector2d& segment_end) const {
  if (HasOverlap(segment_start, segment_end)) {
    return 0.0;
  }
  double min_dist2 = std::min(DistanceSquareTo(segment_start), DistanceSquareTo(segment_end));
  for (const Vector2d& p : GetCornersAsArray()) {
    min_dist2 = std::min(min_dist2, LineSegment2d::DistanceSquareTo(p, segment_start, segment_end));
  }
  return min_dist2;
}

double OrientedBox2d::DistanceTo(const OrientedBox2d& other_box) const {
  if (HasOverlap(other_box)) {
    return 0.0;
  }
  double min_dist2 = std::numeric_limits<double>::infinity();
  for (const Vector2d& p : other_box.GetCornersAsArray()) {
    min_dist2 = std::min(min_dist2, DistanceSquareTo(p));
  }
  for (const Vector2d& p : GetCornersAsArray()) {
    min_dist2 = std::min(min_dist2, other_box.DistanceSquareTo(p));
  }
  return std::sqrt(min_dist2);
}

double OrientedBox2d::DistanceTo(const AxisAlignBox2d& other_box) const {
  if (HasOverlap(other_box)) {
    return 0.0;
  }
  double min_dist2 = std::numeric_limits<double>::infinity();
  for (const Vector2d& p : other_box.GetCornersAsArray()) {
    min_dist2 = std::min(min_dist2, DistanceSquareTo(p));
  }
  for (const Vector2d& p : GetCornersAsArray()) {
    min_dist2 = std::min(min_dist2, other_box.DistanceSquareTo(p));
  }
  return std::sqrt(min_dist2);
}

double OrientedBox2d::DistanceTo(const base::ConstSpan<Vector2d>& polyline) const {
  CHECK(!polyline.empty());
  const int num_segments = polyline.size() - 1;
  if (num_segments == 0) {
    return DistanceTo(polyline[0]);
  }
  const std::array<Vector2d, 4> corners = GetCornersAsArray();
  double min_dist2 = DistanceSquareTo(polyline[0]);
  for (int i = 0; i < num_segments; ++i) {
    if (HasOverlap(polyline[i], polyline[i + 1])) {
      return 0.0;
    }
    for (const Vector2d& p : corners) {
      min_dist2 =
          std::min(min_dist2, LineSegment2d::DistanceSquareTo(p, polyline[i], polyline[i + 1]));
    }
    min_dist2 = std::min(min_dist2, DistanceSquareTo(polyline[i + 1]));
  }
  return std::sqrt(min_dist2);
}

double OrientedBox2d::ComputeOverlapAreaWith(const OrientedBox2d& other_box) const {
  const double min_area = std::min(area(), other_box.area());
  if (min_area < math::kEpsilon) {
    return 0.0;
  }
  if (!HasOverlap(other_box)) {
    return 0.0;
  }
  const std::vector<Vector2d> overlap_points = math_internal::convex::ComputeOverlapConvex(
      GetCornersAsArray(), other_box.GetCornersAsArray());
  return ComputePolygonAreaAsPoints(overlap_points);
}

double OrientedBox2d::ComputeIoUWith(const OrientedBox2d& other_box) const {
  const double min_area = std::min(area(), other_box.area());
  if (min_area < math::kEpsilon) {
    return 0.0;
  }
  const double overlap_area = ComputeOverlapAreaWith(other_box);
  const double union_area = area() + other_box.area() - std::min(min_area, overlap_area);
  return overlap_area / union_area;
}

Eigen::Matrix<double, 5, 1> OrientedBox2d::ToEigen() const {
  Eigen::Matrix<double, 5, 1> matrix;
  matrix[0] = center_.x;
  matrix[1] = center_.y;
  matrix[2] = 2.0 * half_length_;
  matrix[3] = 2.0 * half_width_;
  matrix[4] = heading_;
  return matrix;
}

std::string OrientedBox2d::ToString() const {
  std::ostringstream ss;
  ss << std::fixed;
  ss << "OrientedBox2d(c=" << center_ << ", size(" << 2.0 * half_length_ << ", "
     << 2.0 * half_width_ << "))";
  return ss.str();
}

math_proto::OrientedBox2d OrientedBox2d::ToProto() const {
  math_proto::OrientedBox2d proto;
  proto.mutable_center()->set_x(center_.x);
  proto.mutable_center()->set_y(center_.y);
  proto.set_heading(heading_);
  proto.set_length(2.0 * half_length_);
  proto.set_width(2.0 * half_width_);
  return proto;
}

}  // namespace math
