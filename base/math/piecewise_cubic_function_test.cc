// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/math/piecewise_cubic_function.h"

#include <utility>

#include "gtest/gtest.h"

#include "base/strings/macros.h"

namespace math {

namespace {

constexpr double kInfinity = std::numeric_limits<double>::infinity();

}  // namespace

TEST(PiecewiseCubicFunctionTest, Basic) {
  const std::vector<double> y0 = {0.0, 0.0, 1.0, 3.0, 0.0, 0.0};
  const auto f0 = PiecewiseCubicFunction::Create(MonotonicityType::kDefault, 3.0, 1.0, y0);
  ASSERT_NEAR(f0.min_x(), 3.0, kEpsilon);
  ASSERT_NEAR(f0.max_x(), 8.0, kEpsilon);
  ASSERT_NEAR(f0.min_y(), 0.0, kEpsilon);
  ASSERT_NEAR(f0.max_y(), 3.0, kEpsilon);
  ASSERT_EQ(f0.n_samples(), 6);
  ASSERT_NEAR(f0.x_step(), 1.0, kEpsilon);
  ASSERT_NEAR(f0.x_range(), 5.0, kEpsilon);
  ASSERT_NEAR(f0.x_step_inverse_, 1.0, kEpsilon);
  ASSERT_EQ(f0.y_.size(), 6);
  ASSERT_EQ(f0.m_.size(), 6);
  ASSERT_NEAR(f0.m_[0], 0.0, kEpsilon);
  ASSERT_NEAR(f0.m_[5], 0.0, kEpsilon);
  for (int i = 0; i < f0.n_samples(); ++i) {
    double x = f0.min_x() + i * f0.x_step();
    ASSERT_NEAR(f0.Interpolate(x, true), y0[i], kEpsilon);
  }
  ASSERT_FALSE(f0.IsXInside(+kInfinity));
  ASSERT_FALSE(f0.IsYInside(+kInfinity));
  ASSERT_FALSE(f0.IsXInside(-kInfinity));
  ASSERT_FALSE(f0.IsYInside(-kInfinity));

  const std::vector<double> y1 = {0.0, 0.0, 0.0, 1.0, 2.0, 3.0};
  const auto f1 = PiecewiseCubicFunction::Create(MonotonicityType::kNonDecreasing, 3.0, 0.2, y1);
  ASSERT_NEAR(f1.min_x(), 3.0, kEpsilon);
  ASSERT_NEAR(f1.max_x(), 4.0, kEpsilon);
  ASSERT_NEAR(f1.min_y(), 0.0, kEpsilon);
  ASSERT_NEAR(f1.max_y(), 3.0, kEpsilon);
  ASSERT_EQ(f1.n_samples(), 6);
  ASSERT_NEAR(f1.x_step(), 0.2, kEpsilon);
  ASSERT_NEAR(f1.x_range(), 1.0, kEpsilon);
  ASSERT_NEAR(f1.x_step_inverse_, 5.0, kEpsilon);
  ASSERT_EQ(f1.y_.size(), 6);
  ASSERT_EQ(f1.m_.size(), 6);
  ASSERT_NEAR(f1.m_[0], 0.0, kEpsilon);
  ASSERT_NEAR(f1.m_[5], 1.0, kEpsilon);
  for (int i = 0; i < f1.n_samples(); ++i) {
    double x = f1.min_x() + i * f1.x_step();
    ASSERT_NEAR(f1.Interpolate(x, true), y1[i], kEpsilon);
  }
  ASSERT_FALSE(std::isfinite(f1.Interpolate(+kInfinity, true)));

  const std::vector<double> y2 = {3.0, 2.0, 1.0, 0.0, 0.0, 0.0};
  const auto f2 = PiecewiseCubicFunction::Create(MonotonicityType::kNonIncreasing, 3.0, 0.4, y2);
  ASSERT_NEAR(f2.min_x(), 3.0, kEpsilon);
  ASSERT_NEAR(f2.max_x(), 5.0, kEpsilon);
  ASSERT_NEAR(f2.min_y(), 0.0, kEpsilon);
  ASSERT_NEAR(f2.max_y(), 3.0, kEpsilon);
  ASSERT_EQ(f2.n_samples(), 6);
  ASSERT_NEAR(f2.x_step(), 0.4, kEpsilon);
  ASSERT_NEAR(f2.x_range(), 2.0, kEpsilon);
  ASSERT_NEAR(f2.x_step_inverse_, 2.5, kEpsilon);
  ASSERT_EQ(f2.y_.size(), 6);
  ASSERT_EQ(f2.m_.size(), 6);
  ASSERT_NEAR(f2.m_[0], -1.0, kEpsilon);
  ASSERT_NEAR(f2.m_[5], 0.0, kEpsilon);
  for (int i = 0; i < f2.n_samples(); ++i) {
    double x = f2.min_x() + i * f2.x_step();
    ASSERT_NEAR(f2.Interpolate(x, true), y2[i], kEpsilon);
  }
  ASSERT_FALSE(std::isfinite(f2.Interpolate(-kInfinity, true)));
}

TEST(PiecewiseCubicFunctionTest, Line) {
  const auto line0 = [](double x) { return 3.0 * x - 2.5; };
  const auto f0 =
      PiecewiseCubicFunction::Create(MonotonicityType::kNonDecreasing, 1.0, 0.1, 41, line0);
  ASSERT_NEAR(f0.min_x(), 1.0, kEpsilon);
  ASSERT_NEAR(f0.max_x(), 5.0, kEpsilon);
  ASSERT_NEAR(f0.x_step(), 0.1, kEpsilon);
  ASSERT_NEAR(f0.x_range(), 4.0, kEpsilon);
  ASSERT_EQ(f0.n_samples(), 41);
  ASSERT_NEAR(f0.min_y(), line0(1.0), kEpsilon);
  ASSERT_NEAR(f0.max_y(), line0(5.0), kEpsilon);
  ASSERT_TRUE(f0.IsXInside(1.0));
  ASSERT_TRUE(f0.IsXInside(5.0));
  ASSERT_TRUE(f0.IsYInside(1.0));
  ASSERT_FALSE(f0.IsXInside(0.0));
  ASSERT_FALSE(f0.IsXInside(6.0));
  for (int i = 0; i <= 500; ++i) {
    double x = f0.min_x() + i * f0.x_range() / 500;
    ASSERT_NEAR(line0(x), f0.Interpolate(x, false), kEpsilon);
    ASSERT_NEAR(3.0, f0.Derivative(x, false), kEpsilon);
    ASSERT_NEAR(0.0, f0.SecondDerivative(x, false), kEpsilon);
    ASSERT_NEAR(0.0, f0.ThirdDerivative(x, false), kEpsilon);
    double result_x = 0.0;
    double delta_y = 0.0;
    ASSERT_TRUE(f0.FindXByY(line0(x), &result_x, &delta_y));
    ASSERT_NEAR(x, result_x, kEpsilon);
    ASSERT_NEAR(0.0, delta_y, kEpsilon);
  }
  ASSERT_NEAR(3.0, f0.GetMinDerivative(), kEpsilon);
  ASSERT_NEAR(3.0, f0.GetMaxDerivative(), kEpsilon);
  ASSERT_NEAR(0.0, f0.GetMinSecondDerivative(), kEpsilon);
  ASSERT_NEAR(0.0, f0.GetMaxSecondDerivative(), kEpsilon);

  const auto line1 = [](double /* x */) { return 1.0; };
  const auto f1 =
      PiecewiseCubicFunction::Create(MonotonicityType::kNonIncreasing, 1.0, 0.1, 11, line1);
  ASSERT_NEAR(f1.min_x(), 1.0, kEpsilon);
  ASSERT_NEAR(f1.max_x(), 2.0, kEpsilon);
  ASSERT_NEAR(f1.x_step(), 0.1, kEpsilon);
  ASSERT_NEAR(f1.x_range(), 1.0, kEpsilon);
  ASSERT_EQ(f1.n_samples(), 11);
  ASSERT_NEAR(f1.min_y(), 1.0, kEpsilon);
  ASSERT_NEAR(f1.max_y(), 1.0, kEpsilon);
  ASSERT_TRUE(f1.IsXInside(1.0));
  ASSERT_TRUE(f1.IsXInside(2.0));
  ASSERT_TRUE(f1.IsYInside(1.0));
  for (int i = -100; i <= 600; ++i) {
    double x = f1.min_x() + i * f1.x_range() / 500;
    ASSERT_NEAR(line1(x), f1.Interpolate(x, true), kEpsilon);
    ASSERT_NEAR(0.0, f1.Derivative(x, true), kEpsilon);
    ASSERT_NEAR(0.0, f1.SecondDerivative(x, true), kEpsilon);
    ASSERT_NEAR(0.0, f1.ThirdDerivative(x, true), kEpsilon);
    double result_x = 0.0;
    double delta_y = 0.0;
    ASSERT_TRUE(f1.FindXByY(line1(x), &result_x, &delta_y));
    ASSERT_NEAR(1.0, result_x, kEpsilon);
    ASSERT_NEAR(0.0, delta_y, kEpsilon);
  }
  ASSERT_NEAR(0.0, f1.GetMinDerivative(), kEpsilon);
  ASSERT_NEAR(0.0, f1.GetMaxDerivative(), kEpsilon);
  ASSERT_NEAR(0.0, f1.GetMinSecondDerivative(), kEpsilon);
  ASSERT_NEAR(0.0, f1.GetMaxSecondDerivative(), kEpsilon);

  const auto line2 = [](double x) { return -3.0 * x + 7.0; };
  const auto f2 = PiecewiseCubicFunction::Create(MonotonicityType::kDefault, 0.0, 0.1, 11, line2);
  ASSERT_NEAR(f2.min_x(), 0.0, kEpsilon);
  ASSERT_NEAR(f2.max_x(), 1.0, kEpsilon);
  ASSERT_NEAR(f2.x_step(), 0.1, kEpsilon);
  ASSERT_NEAR(f2.x_range(), 1.0, kEpsilon);
  ASSERT_NEAR(f2.n_samples(), 11, kEpsilon);
  ASSERT_NEAR(f2.min_y(), 4.0, kEpsilon);
  ASSERT_NEAR(f2.max_y(), 7.0, kEpsilon);
  for (int i = -100; i <= 600; ++i) {
    double x = f2.min_x() + i * f2.x_range() / 500;
    ASSERT_NEAR(line2(x), f2.Interpolate(x, true), kEpsilon);
    ASSERT_NEAR(-3.0, f2.Derivative(x, true), kEpsilon);
    ASSERT_NEAR(0.0, f2.SecondDerivative(x, true), kEpsilon);
    ASSERT_NEAR(0.0, f2.ThirdDerivative(x, true), kEpsilon);
  }
  ASSERT_NEAR(-3.0, f2.GetMinDerivative(), kEpsilon);
  ASSERT_NEAR(-3.0, f2.GetMaxDerivative(), kEpsilon);
  ASSERT_NEAR(0.0, f1.GetMinSecondDerivative(), kEpsilon);
  ASSERT_NEAR(0.0, f1.GetMaxSecondDerivative(), kEpsilon);
}

TEST(PiecewiseCubicFunctionTest, Curve) {
  const auto f0 = PiecewiseCubicFunction::Create(
      MonotonicityType::kNonDecreasing,
      0.0,
      0.1,
      11,
      [](double x) { return std::exp(x); },
      std::exp(0.0),
      std::exp(1.0));
  ASSERT_NEAR(f0.min_x(), 0.0, kEpsilon);
  ASSERT_NEAR(f0.max_x(), 1.0, kEpsilon);
  ASSERT_NEAR(f0.x_step(), 0.1, kEpsilon);
  ASSERT_NEAR(f0.x_range(), 1.0, kEpsilon);
  ASSERT_EQ(f0.n_samples(), 11);
  for (int i = 10; i <= 90; ++i) {
    double x = f0.min_x() + i * f0.x_range() / 100;
    ASSERT_NEAR(std::exp(x), f0.Interpolate(x, false), 1e-4);
    ASSERT_NEAR(std::exp(x), f0.Derivative(x, false), 1e-2);
    ASSERT_NEAR(std::exp(x), f0.SecondDerivative(x, false), 1e-2);
    double result_x = 0.0;
    double delta_y = 0.0;
    ASSERT_TRUE(f0.FindXByY(std::exp(x), &result_x, &delta_y));
    ASSERT_NEAR(x, result_x, 1e-4);
    ASSERT_NEAR(0.0, delta_y, 1e-5);
  }
  for (int i = 20; i <= 80; ++i) {
    double x = f0.min_x() + i * f0.x_range() / 100;
    ASSERT_NEAR(std::exp(x), f0.ThirdDerivative(x, false), 1e-2);
  }

  const auto c1 = [](double x) { return std::exp(-x); };
  const auto f1 = PiecewiseCubicFunction::Create(
      MonotonicityType::kNonIncreasing, 0.0, 0.1, 11, c1, -std::exp(0.0), -std::exp(-1.0));
  ASSERT_NEAR(f1.min_x(), 0.0, kEpsilon);
  ASSERT_NEAR(f1.max_x(), 1.0, kEpsilon);
  ASSERT_NEAR(f1.x_step(), 0.1, kEpsilon);
  ASSERT_NEAR(f1.x_range(), 1.0, kEpsilon);
  ASSERT_EQ(f1.n_samples(), 11);
  for (int i = 10; i <= 90; ++i) {
    double x = f1.min_x() + i * f1.x_range() / 100;
    ASSERT_NEAR(std::exp(-x), f1.Interpolate(x, false), 1e-4);
    ASSERT_NEAR(-std::exp(-x), f1.Derivative(x, false), 1e-2);
    ASSERT_NEAR(std::exp(-x), f1.SecondDerivative(x, false), 1e-2);
    double result_x = 0.0;
    double delta_y = 0.0;
    ASSERT_TRUE(f1.FindXByY(std::exp(-x), &result_x, &delta_y));
    ASSERT_NEAR(x, result_x, 1e-4);
    ASSERT_NEAR(0.0, delta_y, 1e-5);
  }
  for (int i = 20; i <= 80; ++i) {
    double x = f1.min_x() + i * f1.x_range() / 100;
    ASSERT_NEAR(-std::exp(-x), f1.ThirdDerivative(x, false), 1e-2);
  }

  const auto f2 = PiecewiseCubicFunction::Create(
      MonotonicityType::kNonIncreasing,
      0.0,
      M_PI / 20,
      21,
      [](double x) { return std::cos(x); },
      0.0,
      0.0);
  ASSERT_NEAR(f2.min_x(), 0.0, kEpsilon);
  ASSERT_NEAR(f2.max_x(), M_PI, kEpsilon);
  ASSERT_NEAR(f2.x_step(), M_PI / 20, kEpsilon);
  ASSERT_NEAR(f2.x_range(), M_PI, kEpsilon);
  ASSERT_EQ(f2.n_samples(), 21);
  for (int i = 10; i <= 90; ++i) {
    double x = f2.min_x() + i * f2.x_range() / 100;
    ASSERT_NEAR(std::cos(x), f2.Interpolate(x, false), 1e-4);
    ASSERT_NEAR(-std::sin(x), f2.Derivative(x, false), 1e-2);
    ASSERT_NEAR(-std::cos(x), f2.SecondDerivative(x, false), 1e-2);
    double result_x = 0.0;
    double delta_y = 0.0;
    ASSERT_TRUE(f2.FindXByY(std::cos(x), &result_x, &delta_y));
    ASSERT_NEAR(x, result_x, 1e-4);
    ASSERT_NEAR(0.0, delta_y, 1e-5);
  }
  for (int i = 20; i <= 80; ++i) {
    double x = f2.min_x() + i * f2.x_range() / 100;
    ASSERT_NEAR(std::sin(x), f2.ThirdDerivative(x, false), 1e-2);
  }

  const auto c3 = [](double x) { return x < -1.0 ? -1.0 : x > 1.0 ? 1.0 : x; };
  const auto f3 =
      PiecewiseCubicFunction::Create(MonotonicityType::kNonDecreasing, -2.0, 0.1, 41, c3);
  ASSERT_NEAR(f3.min_x(), -2.0, kEpsilon);
  ASSERT_NEAR(f3.max_x(), +2.0, kEpsilon);
  ASSERT_NEAR(f3.x_step(), 0.1, kEpsilon);
  ASSERT_NEAR(f3.x_range(), 4.0, kEpsilon);
  ASSERT_EQ(f3.n_samples(), 41);
  for (int i = 0; i <= 100; ++i) {
    double x = f3.min_x() + i * f3.x_range() / 100;
    // We set the threshold to 2e-2 because the monotonicity leads to interpolation inaccuracy for
    // second order discontinuity.
    ASSERT_NEAR(c3(x), f3.Interpolate(x, false), 2e-2);
    if (math::IsInside(x, -0.7, +0.7)) {
      ASSERT_NEAR(1.0, f3.Derivative(x, false), 1e-2);
    } else if (x < -1.3 || x > +1.3) {
      ASSERT_NEAR(0.0, f3.Derivative(x, false), 1e-2);
    }
    double result_x = 0.0;
    double delta_y = 0.0;
    ASSERT_TRUE(f3.FindXByY(c3(x), &result_x, &delta_y));
    if (x <= -1.0) {
      ASSERT_NEAR(result_x, -2.0, kEpsilon);
      ASSERT_NEAR(0.0, delta_y, kEpsilon);
    } else if (x >= 1.0) {
      ASSERT_NEAR(result_x, +1.0, kEpsilon);
      ASSERT_NEAR(0.0, delta_y, kEpsilon);
    } else if (math::IsInside(x, -0.8, +0.8)) {
      ASSERT_NEAR(result_x, x, 1e-3);
      ASSERT_NEAR(0.0, delta_y, 1e-3);
    } else {
      ASSERT_NEAR(result_x, x, 2e-2);
      ASSERT_NEAR(0.0, delta_y, 1e-2);
    }
  }

  {
    const auto c4 = [](double t) -> double { return -0.5 * t * t * t + 2.0 * t * t + 4.0 * t; };
    const auto f4 = PiecewiseCubicFunction::Create(
        MonotonicityType::kNonDecreasing, 0.0, 0.1, 11, c4, 4.0, 6.5);
    ASSERT_NEAR(1.3, f4.GetMinSecondDerivative(), kEpsilon);
    ASSERT_NEAR(3.7, f4.GetMaxSecondDerivative(), kEpsilon);
  }

  const auto cubic_curve_func = PiecewiseCubicFunction::Create(
      MonotonicityType::kNonDecreasing,
      0.0,
      0.1,
      11,
      [](double x) { return std::pow(x, 3.0); },
      0.0,
      3.0);
  ASSERT_NEAR(cubic_curve_func.min_x(), 0.0, kEpsilon);
  ASSERT_NEAR(cubic_curve_func.max_x(), 1.0, kEpsilon);
  ASSERT_NEAR(cubic_curve_func.x_step(), 0.1, kEpsilon);
  ASSERT_NEAR(cubic_curve_func.x_range(), 1.0, kEpsilon);
  ASSERT_EQ(cubic_curve_func.n_samples(), 11);
  for (int i = 10; i <= 90; ++i) {
    const double x = cubic_curve_func.min_x() + i * cubic_curve_func.x_range() / 100.0;
    ASSERT_NEAR(std::pow(x, 3.0), cubic_curve_func.Interpolate(x, false), 1e-3);
    ASSERT_NEAR(3 * std::pow(x, 2.0), cubic_curve_func.Derivative(x, false), 2e-2);
    ASSERT_NEAR(6 * x, cubic_curve_func.SecondDerivative(x, false), kEpsilon);
  }
  for (int i = 20; i <= 80; ++i) {
    const double x = cubic_curve_func.min_x() + i * cubic_curve_func.x_range() / 100.0;
    ASSERT_NEAR(6.0, cubic_curve_func.ThirdDerivative(x, false), kEpsilon);
  }
}

TEST(PiecewiseCubicFunctionTest, Cubic) {
  const auto c0 = [](double x) -> double { return 2.0 * x * x * x; };
  const PiecewiseCubicFunction f0 =
      PiecewiseCubicFunction::Create(MonotonicityType::kNonDecreasing, 0.0, 0.1, 11, c0);
  ASSERT_DOUBLE_EQ(f0.min_x(), 0.0);
  ASSERT_DOUBLE_EQ(f0.max_x(), 1.0);
  ASSERT_EQ(f0.IsXInside(0.0), true);
  ASSERT_EQ(f0.IsXInside(-0.1), false);
  ASSERT_EQ(f0.IsXInside(1.0), true);
  ASSERT_EQ(f0.IsXInside(1.1), false);
  ASSERT_EQ(f0.IsXInside(+kInfinity), false);
  ASSERT_EQ(f0.IsXInside(-kInfinity), false);

  // Check values at y_samples. Values should exactly match.
  ASSERT_DOUBLE_EQ(f0.Interpolate(0.0, false), c0(0.0));
  ASSERT_DOUBLE_EQ(f0.Interpolate(0.5, false), c0(0.5));
  ASSERT_DOUBLE_EQ(f0.Interpolate(1.0, false), c0(1.0));
  // Check values in between y_samples.
  ASSERT_NEAR(f0.Interpolate(0.15, false), c0(0.15), 1e-3);

  const auto dc0 = [](double x) -> double { return 6.0 * x * x; };
  // Check derivatives at y_samples.
  ASSERT_NEAR(f0.Derivative(0.5, false), dc0(0.5), 0.1);
  ASSERT_NEAR(f0.Derivative(0.9, false), dc0(0.9), 0.1);
  // Check derivatives in between y_samples.
  ASSERT_NEAR(f0.Derivative(0.15, false), dc0(0.15), 0.01);

  const auto d2c0 = [](double x) -> double { return 12.0 * x; };
  // Check second derivatives at y_samples.
  ASSERT_NEAR(f0.SecondDerivative(0.5, false), d2c0(0.5), 1e-6);
  ASSERT_NEAR(f0.SecondDerivative(0.1, false), d2c0(0.1), 1e-6);
  // Check second derivatives in between y_samples
  ASSERT_NEAR(f0.SecondDerivative(0.15, false), d2c0(0.15), 1e-6);
  ASSERT_NEAR(f0.SecondDerivative(0.65, false), d2c0(0.65), 1e-6);
  // Check second derivatives outside boundaries.
  ASSERT_NEAR(f0.SecondDerivative(-0.01, true), 0.0, 1e-6);
  ASSERT_NEAR(f0.SecondDerivative(1.01, true), 0.0, 1e-6);

  // Check third derivatives at y_samples.
  ASSERT_NEAR(f0.ThirdDerivative(0.5, false), 12.0, 1e-6);
  ASSERT_NEAR(f0.ThirdDerivative(0.3, false), 12.0, 1e-6);
  // Check third derivatives in between y_samples
  ASSERT_NEAR(f0.ThirdDerivative(0.35, false), 12.0, 1e-6);
  ASSERT_NEAR(f0.ThirdDerivative(0.65, false), 12.0, 1e-6);
  // Check third derivatives outside boundaries.
  ASSERT_NEAR(f0.ThirdDerivative(-0.01, true), 0.0, 1e-6);
  ASSERT_NEAR(f0.ThirdDerivative(1.01, true), 0.0, 1e-6);

  double result_x = 0.0;
  double delta_y = 0.0;
  ASSERT_TRUE(f0.FindXByY(c0(0.15), &result_x, &delta_y));
  ASSERT_NEAR(result_x, 0.15, 1e-2);
  ASSERT_NEAR(delta_y, 0.0, 1e-6);
  ASSERT_TRUE(f0.FindXByY(c0(0.65), &result_x, &delta_y));
  ASSERT_NEAR(result_x, 0.65, 1e-6);
  ASSERT_NEAR(delta_y, 0.0, 1e-6);
}

TEST(PiecewiseCubicFunctionTest, Exponential) {
  const auto c0 = [](double x) -> double { return std::exp(x); };
  const PiecewiseCubicFunction f0 =
      PiecewiseCubicFunction::Create(MonotonicityType::kNonDecreasing, 0.0, 0.01, 101, c0);
  ASSERT_DOUBLE_EQ(f0.min_x(), 0.0);
  ASSERT_DOUBLE_EQ(f0.max_x(), 1.0);
  ASSERT_EQ(f0.IsXInside(0.0), true);
  ASSERT_EQ(f0.IsXInside(-0.1), false);
  ASSERT_EQ(f0.IsXInside(1.0), true);
  ASSERT_EQ(f0.IsXInside(1.1), false);
  ASSERT_EQ(f0.IsXInside(+kInfinity), false);
  ASSERT_EQ(f0.IsXInside(-kInfinity), false);

  // Check values at y_samples. Values should exactly match.
  ASSERT_DOUBLE_EQ(f0.Interpolate(0.0, false), std::exp(0.0));
  ASSERT_DOUBLE_EQ(f0.Interpolate(0.5, false), std::exp(0.5));
  ASSERT_DOUBLE_EQ(f0.Interpolate(1.0, false), std::exp(1.0));
  // Check values in between y_samples.
  ASSERT_NEAR(f0.Interpolate(0.15, false), std::exp(0.15), 1e-5);

  // Check derivatives at y_samples.
  ASSERT_NEAR(f0.Derivative(0.5, false), std::exp(0.5), 1e-2);
  ASSERT_NEAR(f0.Derivative(0.9, false), std::exp(0.9), 1e-2);
  // Check derivatives in between y_samples.
  ASSERT_NEAR(f0.Derivative(0.15, false), std::exp(0.15), 1e-2);

  // Check second derivatives at y_samples.
  ASSERT_NEAR(f0.SecondDerivative(0.5, false), std::exp(0.5), 1e-2);
  ASSERT_NEAR(f0.SecondDerivative(0.9, false), std::exp(0.9), 1e-2);
  // Check second derivatives outside boundaries.
  ASSERT_NEAR(f0.SecondDerivative(-0.01, true), 0.0, 1e-6);
  ASSERT_NEAR(f0.SecondDerivative(1.01, true), 0.0, 1e-6);

  // Check third derivatives at y_samples.
  ASSERT_NEAR(f0.ThirdDerivative(0.5, false), std::exp(0.5), 1e-2);
  ASSERT_NEAR(f0.ThirdDerivative(0.9, false), std::exp(0.9), 1e-2);
  // Check third derivatives outside boundaries.
  ASSERT_NEAR(f0.ThirdDerivative(-0.01, true), 0.0, 1e-6);
  ASSERT_NEAR(f0.ThirdDerivative(1.01, true), 0.0, 1e-6);

  double result_x = 0.0;
  double delta_y = 0.0;
  ASSERT_TRUE(f0.FindXByY(c0(0.5), &result_x, &delta_y));
  ASSERT_NEAR(result_x, 0.5, 1e-6);
  ASSERT_NEAR(delta_y, 0.0, 1e-6);
  ASSERT_TRUE(f0.FindXByY(c0(0.9), &result_x, &delta_y));
  ASSERT_NEAR(result_x, 0.9, 1e-6);
  ASSERT_NEAR(delta_y, 0.0, 1e-6);
}

TEST(PiecewiseCubicFunctionTest, Cosine) {
  const auto c0 = [](double x) -> double { return std::cos(x); };
  const double x_step = M_PI / 20.0;
  const int n_samples = 21;
  const PiecewiseCubicFunction f0 =
      PiecewiseCubicFunction::Create(MonotonicityType::kNonIncreasing, 0.0, x_step, n_samples, c0);
  ASSERT_DOUBLE_EQ(f0.min_x(), 0.0);
  ASSERT_DOUBLE_EQ(f0.max_x(), M_PI);
  ASSERT_EQ(f0.IsXInside(0.0), true);
  ASSERT_EQ(f0.IsXInside(-0.1), false);
  ASSERT_EQ(f0.IsXInside(M_PI), true);
  ASSERT_EQ(f0.IsXInside(M_PI + 0.1), false);
  ASSERT_EQ(f0.IsXInside(kInfinity), false);
  ASSERT_EQ(f0.IsXInside(-kInfinity), false);

  // Check values at y_samples. Values should exactly match.
  ASSERT_DOUBLE_EQ(f0.Interpolate(x_step, false), std::cos(x_step));
  ASSERT_DOUBLE_EQ(f0.Interpolate(5 * x_step, false), std::cos(5 * x_step));
  ASSERT_DOUBLE_EQ(f0.Interpolate(18 * x_step, false), std::cos(18 * x_step));
  ASSERT_DOUBLE_EQ(f0.Interpolate(20 * x_step, false), std::cos(20 * x_step));
  // Check values in between y_samples.
  ASSERT_NEAR(f0.Interpolate(4.5 * x_step, false), std::cos(4.5 * x_step), 1e-3);
  ASSERT_NEAR(f0.Interpolate(10.5 * x_step, false), std::cos(10.5 * x_step), 1e-3);

  // Check derivatives at y_samples.
  ASSERT_NEAR(f0.Derivative(x_step, false), -std::sin(x_step), 1e-3);
  ASSERT_NEAR(f0.Derivative(5 * x_step, false), -std::sin(5 * x_step), 1e-2);
  ASSERT_NEAR(f0.Derivative(18 * x_step, false), -std::sin(18 * x_step), 1e-2);
  ASSERT_NEAR(f0.Derivative(20 * x_step, false), -std::sin(20 * x_step), 1e-1);
  // Check derivatives in between y_samples.
  ASSERT_NEAR(f0.Derivative(4.5 * x_step, false), -std::sin(4.5 * x_step), 1e-2);

  // Check second derivatives at y_samples.
  ASSERT_NEAR(f0.SecondDerivative(5 * x_step, false), -std::cos(5 * x_step), 1e-2);
  ASSERT_NEAR(f0.SecondDerivative(18 * x_step, false), -std::cos(18 * x_step), 1e-2);
  // Check second derivatives in between y_samples.
  ASSERT_NEAR(f0.SecondDerivative(17.5 * x_step, false), -std::cos(17.5 * x_step), 1e-2);

  // Check third derivatives at y_samples.
  ASSERT_NEAR(f0.ThirdDerivative(5 * x_step, false), std::sin(5 * x_step), 1e-2);
  ASSERT_NEAR(f0.ThirdDerivative(18 * x_step, false), std::sin(18 * x_step), 1e-2);
  // Check third derivatives in between y_samples.
  ASSERT_NEAR(f0.ThirdDerivative(17.5 * x_step, false), std::sin(17.5 * x_step), 1e-2);

  double result_x = 0.0;
  double delta_y = 0.0;
  ASSERT_TRUE(f0.FindXByY(c0(5 * x_step), &result_x, &delta_y));
  ASSERT_NEAR(result_x, 5 * x_step, 1e-6);
  ASSERT_NEAR(delta_y, 0.0, 1e-6);
  ASSERT_TRUE(f0.FindXByY(c0(17.5 * x_step), &result_x, &delta_y));
  ASSERT_NEAR(result_x, 17.5 * x_step, 1e-4);
  ASSERT_NEAR(delta_y, 0.0, 1e-6);
}

TEST(PiecewiseCubicFunctionTest, NonDecreasing) {
  const double x_step = 0.69;
  // Sharp increase for the 4th data points.
  std::vector<double> y_samples{0.0, 0.29, 0.61, 3.71};
  const PiecewiseCubicFunction f0 = PiecewiseCubicFunction::Create(
      MonotonicityType::kNonDecreasing, 0.0, x_step, std::move(y_samples));

  const double resample_x_step = 0.01;
  const int n_samples =
      CastToFloorInteger<int, double>((f0.max_x() - f0.min_x()) / resample_x_step);
  for (int i = 1; i < n_samples; ++i) {
    ASSERT_GE(f0.Interpolate(i * resample_x_step, false),
              f0.Interpolate((i - 1) * resample_x_step, false));
    ASSERT_GE(f0.Derivative(i * resample_x_step, false), 0.0);
  }
}

TEST(PiecewiseCubicFunctionTest, NonIncreasing) {
  const double x_step = 1.0;
  std::vector<double> y_samples{100.0, 0.0, 0.0, 0.0};
  const PiecewiseCubicFunction f0 = PiecewiseCubicFunction::Create(
      MonotonicityType::kNonIncreasing, 0.0, x_step, std::move(y_samples));

  const double resample_x_step = 0.01;
  const int n_samples =
      CastToFloorInteger<int, double>((f0.max_x() - f0.min_x()) / resample_x_step);
  for (int i = 1; i < n_samples; ++i) {
    ASSERT_LE(f0.Interpolate(i * resample_x_step, false),
              f0.Interpolate((i - 1) * resample_x_step, false));
    //    LOG(ERROR) << DUMP_TO_STREAM(i, f0.Interpolate((i - 1) * resample_x_step, false));
    ASSERT_LE(f0.Derivative(i * resample_x_step, false), 0.0);
  }
}

TEST(PiecewiseCubicFunctionTest, C2Smooth) {
  const auto c0 = [](double x) -> double { return std::cos(x); };
  const double x_step = M_PI / 20.0;
  const int n_samples = 21;
  const PiecewiseCubicFunction f0 =
      PiecewiseCubicFunction::Create(MonotonicityType::kNonIncreasing, 0.0, x_step, n_samples, c0);
  ASSERT_DOUBLE_EQ(f0.min_x(), 0.0);
  ASSERT_DOUBLE_EQ(f0.max_x(), M_PI);

  for (int i = 1; i < 19; ++i) {
    ASSERT_NEAR(f0.SecondDerivative(i * x_step - kEpsilon, false),
                f0.SecondDerivative(i * x_step + kEpsilon, false),
                1e-4)
        << DUMP_TO_STREAM(i);
  }
}

TEST(PiecewiseCubicFunctionTest, Shift) {
  const auto f0 = PiecewiseCubicFunction::Create(
      MonotonicityType::kNonDecreasing, -3.0, 0.1, 61, [](double x) { return math::Sigmoid(x); });
  auto f1 = f0;
  f1.ShiftX(-1.0);
  ASSERT_NEAR(f0.min_x(), -3.0, kEpsilon);
  ASSERT_NEAR(f0.max_x(), +3.0, kEpsilon);
  ASSERT_NEAR(f1.min_x(), -1.0, kEpsilon);
  ASSERT_NEAR(f1.max_x(), +5.0, kEpsilon);
  ASSERT_NEAR(f0.x_range(), f1.x_range(), kEpsilon);
  for (int i = 0; i <= 100; ++i) {
    double d = i * f0.x_range() / 100;
    ASSERT_NEAR(f0.Interpolate(f0.min_x() + d), f1.Interpolate(f1.min_x() + d), kEpsilon);
  }

  auto f2 = f0;
  f2.ShiftX(-10.0, 0.5);
  ASSERT_NEAR(0.5, f2.Interpolate(-10.0), kEpsilon);
  ASSERT_NEAR(f0.min_y(), f2.min_y(), kEpsilon);
  ASSERT_NEAR(f0.max_y(), f2.max_y(), kEpsilon);
  for (int i = 0; i <= 100; ++i) {
    double d = i * f0.x_range() / 100;
    ASSERT_NEAR(f0.Interpolate(f0.min_x() + d), f2.Interpolate(f2.min_x() + d), kEpsilon);
  }

  auto f3 = f0;
  f3.ShiftY(7.0);
  ASSERT_NEAR(f3.min_y(), 7.0, kEpsilon);
  ASSERT_NEAR(f3.max_y() - f3.min_y(), f0.max_y() - f0.min_y(), kEpsilon);
  for (int i = 0; i <= 100; ++i) {
    double x = f0.min_x() + i * f0.x_step() / 100;
    ASSERT_NEAR(f0.Interpolate(x) - f0.min_y(), f3.Interpolate(x) - f3.min_y(), kEpsilon);
  }
  f3.ShiftY(0.0);
  ASSERT_NEAR(f3.min_y(), 0.0, kEpsilon);

  auto f4 = f0;
  f4.ShiftY(0.0, -3.0);
  ASSERT_NEAR(-3.0, f4.Interpolate(0.0), kEpsilon);
  ASSERT_NEAR(f4.x_range(), f0.x_range(), kEpsilon);
  ASSERT_NEAR(f4.max_y() - f4.min_y(), f0.max_y() - f0.min_y(), kEpsilon);
  for (int i = 0; i <= 100; ++i) {
    double d = i * f0.x_step() / 100;
    ASSERT_NEAR(f0.Interpolate(f0.min_x() + d) - f0.min_y(),
                f4.Interpolate(f4.min_x() + d) - f4.min_y(),
                kEpsilon);
  }
}

TEST(PiecewiseCubicFunctionTest, LowerBoundByY) {
  const auto f0 = PiecewiseCubicFunction::Create(
      MonotonicityType::kNonDecreasing, 0.0, 1.0, {1.0, 1.0, 3.0, 4.0, 4.0});
  ASSERT_EQ(f0.LowerBoundByY(0.0), 0);
  ASSERT_EQ(f0.LowerBoundByY(1.0), 0);
  ASSERT_EQ(f0.LowerBoundByY(1.1), 2);
  ASSERT_EQ(f0.LowerBoundByY(2.0), 2);
  ASSERT_EQ(f0.LowerBoundByY(3.0), 2);
  ASSERT_EQ(f0.LowerBoundByY(4.0), 3);
  ASSERT_EQ(f0.LowerBoundByY(5.0), 5);

  const auto f1 = PiecewiseCubicFunction::Create(
      MonotonicityType::kNonIncreasing, 0.0, 1.0, {4.0, 4.0, 3.0, 1.0, 1.0});
  ASSERT_EQ(f1.LowerBoundByY(5.0), 0);
  ASSERT_EQ(f1.LowerBoundByY(4.0), 0);
  ASSERT_EQ(f1.LowerBoundByY(3.9), 2);
  ASSERT_EQ(f1.LowerBoundByY(3.0), 2);
  ASSERT_EQ(f1.LowerBoundByY(2.0), 3);
  ASSERT_EQ(f1.LowerBoundByY(1.0), 3);
  ASSERT_EQ(f1.LowerBoundByY(0.0), 5);
}

TEST(PiecewiseCubicFunctionTest, Create) {
  {
    const auto line = [](double x) { return 3.0 * x + 7.0; };
    std::vector<PiecewiseCubicFunction> fn;
    fn.push_back(PiecewiseCubicFunction::Create(
        MonotonicityType::kDefault, 0.0, 1.0, {line(0.0), line(1.0), line(2.0)}));
    fn.push_back(PiecewiseCubicFunction::Create(
        MonotonicityType::kDefault, 0.0, 1.0, {line(0.0), line(1.0), line(2.0)}, 3.0, 3.0));
    fn.push_back(PiecewiseCubicFunction::Create(MonotonicityType::kDefault, 0.0, 1.0, 3, line));
    fn.push_back(
        PiecewiseCubicFunction::Create(MonotonicityType::kDefault, 0.0, 1.0, 3, line, 3.0, 3.0));
    for (int j = 0; j < static_cast<int>(fn.size()); ++j) {
      ASSERT_NEAR(fn[j].min_x(), 0.0, kEpsilon);
      ASSERT_NEAR(fn[j].max_x(), 2.0, kEpsilon);
      ASSERT_EQ(fn[j].n_samples(), 3);
      for (int i = 0; i <= 100; ++i) {
        double x = fn[j].min_x() + i * fn[j].x_range() / 100;
        ASSERT_NEAR(fn[j].Interpolate(x, true), line(x), kEpsilon);
        ASSERT_NEAR(fn[j].Derivative(x, true), 3.0, kEpsilon);
      }
    }
  }
  {
    const std::vector<double> x = {0.0, 1.0, 20.0, 100.0};
    const std::vector<double> y = {10.0, 0.0, 0.0, 0.0};
    const auto f = PiecewiseCubicFunction::Create(MonotonicityType::kNonIncreasing, x, y, 0.5);
    ASSERT_NEAR(f.min_x(), 0.0, kEpsilon);
    ASSERT_NEAR(f.max_x(), 100.0, kEpsilon);
  }
  {
    const std::vector<double> x = {0.0, 1.0, 20.0, 100.0};
    const std::vector<double> y = {10.0, 13.0, 13.0, 13.0};
    const auto f = PiecewiseCubicFunction::Create(MonotonicityType::kNonDecreasing, x, y, 0.5, 3.0);
    ASSERT_NEAR(f.min_x(), 0.0, kEpsilon);
    ASSERT_NEAR(f.max_x(), 100.0, kEpsilon);
    ASSERT_NEAR(f.Derivative(0.0), 3.0, kEpsilon);
  }
}

TEST(PiecewiseCubicFunctionTest, Resample) {
  const auto line = [](double x) { return 3.0 * x + 5.0; };
  const auto f0 = PiecewiseCubicFunction::Create(MonotonicityType::kDefault, 0.0, 1.0, 11, line);
  const auto f1 = f0.Resample(0.17);
  ASSERT_NEAR(f1.min_x(), f0.min_x(), kEpsilon);
  ASSERT_NEAR(f1.max_x(), f0.max_x(), kEpsilon);
  for (int i = 0; i <= 100; ++i) {
    double x = f0.min_x() + i * f0.x_range() / 100;
    ASSERT_NEAR(f0.Interpolate(x, true), line(x), kEpsilon);
    ASSERT_NEAR(f1.Interpolate(x, true), line(x), kEpsilon);
  }
}

TEST(PiecewiseCubicFunctionTest, FromAndToProto) {
  const auto compare = [](const PiecewiseCubicFunction& f, const PiecewiseCubicFunction& f0) {
    ASSERT_NEAR(f.min_x(), f0.min_x(), kEpsilon);
    ASSERT_NEAR(f.max_x(), f0.max_x(), kEpsilon);
    ASSERT_NEAR(f.min_y(), f0.min_y(), kEpsilon);
    ASSERT_NEAR(f.max_y(), f0.max_y(), kEpsilon);
    ASSERT_EQ(f.n_samples(), f0.n_samples());
    ASSERT_NEAR(f.x_step(), f0.x_step(), kEpsilon);
    ASSERT_NEAR(f.x_range(), f0.x_range(), kEpsilon);
    ASSERT_NEAR(f.x_step_inverse_, f0.x_step_inverse_, kEpsilon);
    for (int i = 0; i < f0.n_samples(); ++i) {
      ASSERT_NEAR(f.y_samples()[i], f0.y_samples()[i], kEpsilon);
      ASSERT_NEAR(f.m_[i], f0.m_[i], kEpsilon);
    }
  };
  const std::vector<double> y0 = {0.0, 0.0, 1.0, 3.0, 0.0, 0.0};
  const auto f0 = PiecewiseCubicFunction::Create(MonotonicityType::kDefault, 3.0, 1.0, y0);
  compare(PiecewiseCubicFunction::FromProto(f0.ToProto()), f0);

  const std::vector<double> y1 = {0.0, 0.0, 0.0, 1.0, 2.0, 3.0};
  const auto f1 = PiecewiseCubicFunction::Create(MonotonicityType::kNonDecreasing, 3.0, 0.2, y1);
  compare(PiecewiseCubicFunction::FromProto(f1.ToProto()), f1);

  const std::vector<double> y2 = {3.0, 2.0, 1.0, 0.0, 0.0, 0.0};
  const auto f2 = PiecewiseCubicFunction::Create(MonotonicityType::kNonIncreasing, 3.0, 0.4, y2);
  compare(PiecewiseCubicFunction::FromProto(f2.ToProto()), f2);

  const std::vector<double> y3 = {0.0, 0.0, 1.0, 3.0, 0.0, 3.0};
  const auto f3 =
      PiecewiseCubicFunction::Create(MonotonicityType::kDefault, 3.0, 1.0, y3, 1.0, -3.0);
  compare(PiecewiseCubicFunction::FromProto(f3.ToProto()), f3);
}

TEST(PiecewiseCubicFunctionTest, Flip) {
  const auto f0 = PiecewiseCubicFunction::Create(
      MonotonicityType::kNonDecreasing, -3.0, 0.1, 61, [](double x) { return math::Sigmoid(x); });
  auto f1 = f0;
  f1.FlipY();
  ASSERT_NEAR(f1.min_x(), -3.0, kEpsilon);
  ASSERT_NEAR(f1.max_x(), +3.0, kEpsilon);
  ASSERT_NEAR(f1.min_y(), -f0.max_y(), kEpsilon);
  ASSERT_NEAR(f1.max_y(), -f0.min_y(), kEpsilon);
  ASSERT_EQ(f1.mode(), MonotonicityType::kNonIncreasing);
  for (int i = 0; i <= 100; ++i) {
    double x = f1.min_x() + i * f1.x_range() / 100;
    ASSERT_NEAR(f1.Interpolate(x, false), -f0.Interpolate(x, false), kEpsilon);
    ASSERT_NEAR(f1.Derivative(x, false), -f0.Derivative(x, false), kEpsilon);
    ASSERT_NEAR(f1.SecondDerivative(x, false), -f0.SecondDerivative(x, false), kEpsilon);
    ASSERT_NEAR(f1.ThirdDerivative(x, false), -f0.ThirdDerivative(x, false), kEpsilon);
  }

  const auto line0 = [](double x) { return 3.0 * x - 2.5; };
  const auto f2 =
      PiecewiseCubicFunction::Create(MonotonicityType::kNonDecreasing, 1.0, 0.1, 41, line0);
  auto f3 = f2;
  f3.FlipY();
  ASSERT_EQ(f3.mode(), MonotonicityType::kNonIncreasing);
  for (int i = 0; i <= 100; ++i) {
    double x = f3.min_x() + i * f3.x_range() / 100;
    ASSERT_NEAR(f3.Interpolate(x, false), -f2.Interpolate(x, false), kEpsilon);
    ASSERT_NEAR(f3.Derivative(x, false), -f2.Derivative(x, false), kEpsilon);
    ASSERT_NEAR(f3.SecondDerivative(x, false), -f2.SecondDerivative(x, false), kEpsilon);
    ASSERT_NEAR(f3.ThirdDerivative(x, false), -f2.ThirdDerivative(x, false), kEpsilon);
  }

  const auto curve0 = [](double x) { return 3.0 * x * x; };
  const auto f4 =
      PiecewiseCubicFunction::Create(MonotonicityType::kNonDecreasing, 1.0, 0.1, 41, curve0);
  auto f5 = f4;
  f5.FlipY();
  ASSERT_EQ(f5.mode(), MonotonicityType::kNonIncreasing);
  for (int i = 0; i <= 100; ++i) {
    double x = f5.min_x() + i * f5.x_range() / 100;
    ASSERT_NEAR(f5.Interpolate(x, false), -f4.Interpolate(x, false), kEpsilon);
    ASSERT_NEAR(f5.Derivative(x, false), -f4.Derivative(x, false), kEpsilon);
    ASSERT_NEAR(f5.SecondDerivative(x, false), -f4.SecondDerivative(x, false), kEpsilon);
    ASSERT_NEAR(f5.ThirdDerivative(x, false), -f4.ThirdDerivative(x, false), kEpsilon);
  }

  const auto line1 = [](double x) { return -3.0 * x - 2.5; };
  const auto f6 =
      PiecewiseCubicFunction::Create(MonotonicityType::kNonIncreasing, 1.0, 0.1, 41, line1);
  auto f7 = f6;
  f7.FlipY();
  ASSERT_EQ(f7.mode(), MonotonicityType::kNonDecreasing);
  for (int i = 0; i <= 100; ++i) {
    double x = f7.min_x() + i * f7.x_range() / 100;
    ASSERT_NEAR(f7.Interpolate(x, false), -f6.Interpolate(x, false), kEpsilon);
    ASSERT_NEAR(f7.Derivative(x, false), -f6.Derivative(x, false), kEpsilon);
    ASSERT_NEAR(f7.SecondDerivative(x, false), -f6.SecondDerivative(x, false), kEpsilon);
    ASSERT_NEAR(f7.ThirdDerivative(x, false), -f6.ThirdDerivative(x, false), kEpsilon);
  }
}

TEST(PiecewiseCubicFunctionTest, MakeSubFunction) {
  const auto f0 = PiecewiseCubicFunction::Create(
      MonotonicityType::kNonDecreasing, -3.0, 0.1, 61, [](double x) { return math::Sigmoid(x); });
  {
    const double x_min = 0.0;
    const double x_max = 2.5;
    const auto f1 = f0.MakeSubFunction(x_min, x_max);
    ASSERT_NEAR(f1.min_x(), x_min, kEpsilon);
    ASSERT_NEAR(f1.max_x(), x_max, kEpsilon);
    ASSERT_NEAR(f1.x_step(), f0.x_step(), kEpsilon);
    for (double x = f1.min_x(); x <= f1.max_x(); x += f1.x_step()) {
      ASSERT_NEAR(f1.Interpolate(x, true), f0.Interpolate(x, true), kEpsilon);
    }
  }
  {
    const double x_min = -3.0;
    const double x_max = -2.0;
    const auto f1 = f0.MakeSubFunction(x_min, x_max);
    ASSERT_NEAR(f1.min_x(), x_min, kEpsilon);
    ASSERT_NEAR(f1.max_x(), x_max, kEpsilon);
    ASSERT_NEAR(f1.x_step(), f0.x_step(), kEpsilon);
    for (double x = f1.min_x(); x <= f1.max_x(); x += f1.x_step()) {
      ASSERT_NEAR(f1.Interpolate(x, true), f0.Interpolate(x, true), kEpsilon);
    }
  }
  {
    const double x_min = 2.0;
    const double x_max = 3.0;
    const auto f1 = f0.MakeSubFunction(x_min, x_max);
    ASSERT_NEAR(f1.min_x(), x_min, kEpsilon);
    ASSERT_NEAR(f1.max_x(), x_max, kEpsilon);
    ASSERT_NEAR(f1.x_step(), f0.x_step(), kEpsilon);
    for (double x = f1.min_x(); x <= f1.max_x(); x += f1.x_step()) {
      ASSERT_NEAR(f1.Interpolate(x, true), f0.Interpolate(x, true), kEpsilon);
    }
  }
}

}  // namespace math
