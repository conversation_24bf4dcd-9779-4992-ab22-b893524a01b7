// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>
//          <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)

#pragma once

#include <cmath>
#include <ostream>
#include <string>

#include "glog/logging.h"

#include "base/math/vector3.h"

namespace math {

template <typename T>
class Vector4 final {
 public:
  static_assert(std::is_floating_point<T>::value,
                "Template Vector4 only supports floating-point types.");

  Vector4() : x(0), y(0), z(0), w(0) {}
  Vector4(T x, T y, T z, T w) : x(x), y(y), z(z), w(w) {}
  Vector4(const Vector3<T>& xyz, T w) : Vector4(xyz.x, xyz.y, xyz.z, w) {}

  template <typename V>
  explicit Vector4(const Vector4<V>& v) : x(v.x), y(v.y), z(v.z), w(v.w) {}

  template <typename PointType>
  explicit Vector4(const PointType& v) : x(v.x()), y(v.y()), z(v.z()), w(v.w()) {}

  T operator[](int index) const;
  T& operator[](int index);

  std::string ToString() const;

  union {
    struct {
      T x;
      T y;
      T z;
      T w;
    };
    T elements[4];
  };

  // Shallow copy and move are OK.
};

template <typename T>
T Vector4<T>::operator[](int index) const {
  DCHECK(index >= 0 && index < 4);
  return elements[index];
}

template <typename T>
T& Vector4<T>::operator[](int index) {
  DCHECK(index >= 0 && index < 4);
  return elements[index];
}

template <typename T>
std::string Vector4<T>::ToString() const {
  return strings::Format("({:8f}, {:8f}, {:8f}, {:8f})", x, y, z, w);
}

using Vector4d = Vector4<double>;
using Vector4f = Vector4<float>;
using Vector4i = glm::i32vec4;
using Vector4l = glm::i64vec4;

static_assert(sizeof(Vector4d) == 4 * sizeof(double));
static_assert(sizeof(Vector4f) == 4 * sizeof(float));

// =================================== Helper Functions ===================================

template <typename T>
inline std::ostream& operator<<(std::ostream& os, const Vector4<T>& p) {
  return os << p.ToString();
}

}  // namespace math
