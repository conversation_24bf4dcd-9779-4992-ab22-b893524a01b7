// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>
//          <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)

#include "base/math/polygon2d_utils.h"

#include <algorithm>

#include "gmock/gmock.h"
#include "gtest/gtest.h"

#include "base/math/axis_align_box2d.h"
#include "base/testing/geometry.h"
#include "base/testing/random.h"

namespace math {

TEST(Polygon2dUtilsTest, ComputePolygonAreaAsPoints) {
  ASSERT_NEAR(
      ComputePolygonAreaAsPoints({{0.0, 0.0}, {0.0, 4.0}, {3.0, 4.0}, {3.0, 0.0}}), -12.0, 1e-6);
  ASSERT_NEAR(
      ComputePolygonAreaAsPoints({{3.0, 0.0}, {3.0, 4.0}, {0.0, 4.0}, {0.0, 0.0}}), +12.0, 1e-6);
}

TEST(Polygon2dUtilsTest, ComputePolygonCentroidAsPoints) {
  std::vector<Vector2d> points0 = AxisAlignBox2d(Vector2d(0.0, 0.0), 2.0, 2.0).GetCorners();
  ASSERT_NEAR(0.0, (ComputePolygonCentroidAsPoints(points0) - Vector2d(0.0, 0.0)).Length(), 1e-6);

  std::vector<Vector2d> points1 = AxisAlignBox2d(Vector2d(1.0, 3.0), 3.15, 9.82).GetCorners();
  ASSERT_NEAR(0.0, (ComputePolygonCentroidAsPoints(points1) - Vector2d(1.0, 3.0)).Length(), 1e-6);

  std::vector<Vector2d> points2 = {{0.0, 1.0}, {1.0, 0.0}, {0.0, -1.0}, {-1.0, 0.0}};
  ASSERT_NEAR(0.0, (ComputePolygonCentroidAsPoints(points2) - Vector2d(0.0, 0.0)).Length(), 1e-6);

  std::vector<Vector2d> points3 = {{10.0, 10.0}, {20.0, 10.0}, {330.0, 160.0}};
  ASSERT_NEAR(
      0.0, (ComputePolygonCentroidAsPoints(points3) - Vector2d(120.0, 60.0)).Length(), 1e-6);

  std::vector<Vector2d> points4 = points3;
  std::reverse(points4.begin(), points4.end());
  ASSERT_NEAR(
      0.0, (ComputePolygonCentroidAsPoints(points4) - Vector2d(120.0, 60.0)).Length(), 1e-6);

  std::vector<Vector2d> points5 = {{60.0, 0.0},
                                   {30.0, 30.0 * std::sqrt(3.0)},
                                   {-30.0, 30.0 * std::sqrt(3.0)},
                                   {-60.0, 0.0},
                                   {-30.0, -30.0 * std::sqrt(3.0)},
                                   {30.0, -30.0 * std::sqrt(3.0)}};
  ASSERT_NEAR(0.0, (ComputePolygonCentroidAsPoints(points5) - Vector2d(0.0, 0.0)).Length(), 1e-6);

  std::vector<Vector2d> points6 = points5;
  std::reverse(points6.begin(), points6.end());
  ASSERT_NEAR(0.0, (ComputePolygonCentroidAsPoints(points6) - Vector2d(0.0, 0.0)).Length(), 1e-6);

  std::vector<Vector2d> points7 = {{60.0, 0.0},
                                   {-30.0, 30.0 * std::sqrt(3.0)},
                                   {-30.0, -30.0 * std::sqrt(3.0)},
                                   {30.0, 30.0 * std::sqrt(3.0)},
                                   {-60.0, 0.0},
                                   {30.0, -30.0 * std::sqrt(3.0)}};
  ASSERT_NEAR(0.0, (ComputePolygonCentroidAsPoints(points7) - Vector2d(0.0, 0.0)).Length(), 1e-6);

  std::vector<Vector2d> points8;
  std::transform(
      points7.rbegin(), points7.rend(), std::back_inserter(points8), [](const Vector2d& p) {
        return p + Vector2d(1.5, -3.4);
      });
  ASSERT_NEAR(0.0, (ComputePolygonCentroidAsPoints(points8) - Vector2d(1.5, -3.4)).Length(), 1e-6);

  std::vector<Vector2d> bad_points0 = {{8.0, 5.0}, {2.0, 5.0}, {7.0, 1.0}, {5.0, 4.0}, {3.0, 1.0}};
  Vector2d bad_center0 = ComputePolygonCentroidAsPoints(bad_points0);
  ASSERT_GE(bad_center0.x, 3.0);
  ASSERT_GE(bad_center0.y, 1.0);
  ASSERT_LE(bad_center0.x, 8.0);
  ASSERT_LE(bad_center0.y, 5.0);

  std::vector<Vector2d> bad_points1 = {{0.0, 0.0}, {2.0, 2.0}};
  ASSERT_NEAR(
      0.0, (ComputePolygonCentroidAsPoints(bad_points1) - Vector2d(1.0, 1.0)).Length(), 1e-6);
}

TEST(Polygon2dUtilsTest, SortPointsInCCWOrder) {
  std::vector<Vector2d> points = {{0.0, 0.0}, {0.0, 4.0}, {3.0, 0.0}, {3.0, 4.0}};
  SortPointsInCCWOrder(&points);
  ASSERT_THAT(points,
              testing::ElementsAre(
                  Vector2d(0.0, 0.0), Vector2d(3.0, 0.0), Vector2d(3.0, 4.0), Vector2d(0.0, 4.0)));
}

TEST(Polygon2dUtilsTest, ShiftPolygonAsPoints) {
  const std::vector<Vector2d> points0 = testing::RandomPoints(30, 100.0);
  for (int i = 0; i < 10; ++i) {
    const math::Vector2d offset = testing::RandomPoint(1000.0);
    std::vector<Vector2d> points1 = points0;
    math::ShiftPolygonAsPoints(base::MakeSpan(points1), offset);
    for (int j = 0; j < static_cast<int>(points0.size()); ++j) {
      const Vector2d& p0 = points0[j];
      const Vector2d& p1 = points1[j];
      ASSERT_LE(p1.DistanceTo(p0 + offset), 1e-6);
    }
  }
}

TEST(Polygon2dUtilsTest, RotatePolygonAsPoints) {
  const std::vector<Vector2d> points0 = testing::RandomPoints(30, 100.0);
  for (int i = 0; i < 10; ++i) {
    const Vector2d center = testing::RandomPoint(1000.0);
    const double angle = testing::RandomDouble() * M_PI * 2.0;
    std::vector<Vector2d> points1 = points0;
    math::RotatePolygonAsPoints(base::MakeSpan(points1), center, angle);
    for (int j = 0; j < static_cast<int>(points0.size()); ++j) {
      const Vector2d p0 = points0[j] - center;
      const Vector2d p1 = points1[j] - center;
      ASSERT_LE(p1.DistanceTo(p0.Rotate(angle)), 1e-6);
    }
  }
}

TEST(Polygon2dUtilsTest, ShiftAndRotatePolygonAsPoints) {
  const std::vector<Vector2d> points0 = testing::RandomPoints(30, 100.0);
  for (int i = 0; i < 10; ++i) {
    const Vector2d old_center = testing::RandomPoint(1000.0);
    const Vector2d new_center = testing::RandomPoint(1000.0);
    const double angle = testing::RandomDouble() * M_PI * 2.0;
    std::vector<Vector2d> points1 = points0;
    math::ShiftAndRotatePolygonAsPoints(base::MakeSpan(points1), old_center, new_center, angle);
    for (int j = 0; j < static_cast<int>(points0.size()); ++j) {
      const Vector2d p0 = points0[j] - old_center;
      const Vector2d p1 = points1[j] - new_center;
      ASSERT_LE(p1.DistanceTo(p0.Rotate(angle)), 1e-6);
    }
  }
}

}  // namespace math
