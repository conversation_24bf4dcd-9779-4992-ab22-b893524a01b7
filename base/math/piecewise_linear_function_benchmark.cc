// Copyright @2020 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "benchmark/benchmark.h"

#include "base/math/piecewise_linear_function.h"
#include "base/testing/random.h"

namespace math {

namespace {

void CustomArguments(benchmark::internal::Benchmark* b) {
  CHECK(b != nullptr);
  for (int i = 1; i < 10; ++i) {
    b->Args({1 << i, 1000});
  }
}

}  // namespace

void BM_PiecewiseLinearFunction(benchmark::State& state, /* NOLINT(runtime/references) */
                                bool ordered_access, bool uniform_sampling) {
  int n_samples = state.range(0);
  double x_step = 100.0 / n_samples;
  PiecewiseLinearFunction<double> linear_function(0.0, x_step, n_samples + 1,
                                                  [](double x) { return x; });
  if (!uniform_sampling) {
    std::vector<double> x_samples(n_samples, 0.0);
    for (int i = 0; i < n_samples; ++i) {
      x_samples[i] = i * x_step;
    }
    linear_function =
        PiecewiseLinearFunction<double>(std::move(x_samples), [](double x) { return x; });
  }

  std::vector<double> x_access;
  std::generate_n(std::back_inserter(x_access), state.range(1),
                  []() { return testing::RandomDouble(0.0, 100.0); });
  if (ordered_access) {
    std::sort(x_access.begin(), x_access.end());
  }

  double sum = 0.0;
  while (state.KeepRunning()) {
    for (double x : x_access) {
      sum += linear_function(x);
    }
    benchmark::DoNotOptimize(sum);
  }
}

BENCHMARK_CAPTURE(BM_PiecewiseLinearFunction, RandomSampling / OrderedAccess, true, false)
    ->Apply(CustomArguments)
    ->MinTime(1.0);
BENCHMARK_CAPTURE(BM_PiecewiseLinearFunction, RandomSampling / RandomAccess, false, false)
    ->Apply(CustomArguments)
    ->MinTime(1.0);
BENCHMARK_CAPTURE(BM_PiecewiseLinearFunction, UniformSampling / RandomAccess, false, true)
    ->Apply(CustomArguments)
    ->MinTime(1.0);

}  // namespace math

BENCHMARK_MAIN();

// clang-format off
/*
Run on (16 X 4900 MHz CPU s)
CPU Caches:
  L1 Data 48K (x8)
  L1 Instruction 32K (x8)
  L2 Unified 512K (x8)
  L3 Unified 16384K (x1)
Load Average: 6.00, 3.57, 2.81
***WARNING*** CPU scaling is enabled, the benchmark real time measurements may be noisy and will incur extra overhead.
----------------------------------------------------------------------------------------------------------------------------
Benchmark                                                                                  Time             CPU   Iterations
----------------------------------------------------------------------------------------------------------------------------
BM_PiecewiseLinearFunction/RandomSampling / OrderedAccess/2/1000/min_time:1.000         6917 ns         6917 ns       202684
BM_PiecewiseLinearFunction/RandomSampling / OrderedAccess/4/1000/min_time:1.000         7454 ns         7453 ns       188191
BM_PiecewiseLinearFunction/RandomSampling / OrderedAccess/8/1000/min_time:1.000         7768 ns         7768 ns       180936
BM_PiecewiseLinearFunction/RandomSampling / OrderedAccess/16/1000/min_time:1.000        8175 ns         8175 ns       175695
BM_PiecewiseLinearFunction/RandomSampling / OrderedAccess/32/1000/min_time:1.000        8527 ns         8527 ns       170459
BM_PiecewiseLinearFunction/RandomSampling / OrderedAccess/64/1000/min_time:1.000        8657 ns         8657 ns       161941
BM_PiecewiseLinearFunction/RandomSampling / OrderedAccess/128/1000/min_time:1.000       9349 ns         9349 ns       153004
BM_PiecewiseLinearFunction/RandomSampling / OrderedAccess/256/1000/min_time:1.000      10102 ns        10102 ns       136287
BM_PiecewiseLinearFunction/RandomSampling / OrderedAccess/512/1000/min_time:1.000      10567 ns        10567 ns       132353
BM_PiecewiseLinearFunction/RandomSampling / RandomAccess/2/1000/min_time:1.000          9438 ns         9438 ns       145666
BM_PiecewiseLinearFunction/RandomSampling / RandomAccess/4/1000/min_time:1.000          7763 ns         7763 ns       180458
BM_PiecewiseLinearFunction/RandomSampling / RandomAccess/8/1000/min_time:1.000          8410 ns         8410 ns       167162
BM_PiecewiseLinearFunction/RandomSampling / RandomAccess/16/1000/min_time:1.000         8917 ns         8917 ns       150351
BM_PiecewiseLinearFunction/RandomSampling / RandomAccess/32/1000/min_time:1.000         9506 ns         9506 ns       149030
BM_PiecewiseLinearFunction/RandomSampling / RandomAccess/64/1000/min_time:1.000         9917 ns         9917 ns       139538
BM_PiecewiseLinearFunction/RandomSampling / RandomAccess/128/1000/min_time:1.000       11047 ns        11047 ns       125054
BM_PiecewiseLinearFunction/RandomSampling / RandomAccess/256/1000/min_time:1.000       18245 ns        18245 ns        77973
BM_PiecewiseLinearFunction/RandomSampling / RandomAccess/512/1000/min_time:1.000       22346 ns        22346 ns        60783
BM_PiecewiseLinearFunction/UniformSampling / RandomAccess/2/1000/min_time:1.000         5265 ns         5265 ns       269094
BM_PiecewiseLinearFunction/UniformSampling / RandomAccess/4/1000/min_time:1.000         5220 ns         5220 ns       268238
BM_PiecewiseLinearFunction/UniformSampling / RandomAccess/8/1000/min_time:1.000         5262 ns         5262 ns       268584
BM_PiecewiseLinearFunction/UniformSampling / RandomAccess/16/1000/min_time:1.000        5196 ns         5196 ns       268190
BM_PiecewiseLinearFunction/UniformSampling / RandomAccess/32/1000/min_time:1.000        5219 ns         5219 ns       268280
BM_PiecewiseLinearFunction/UniformSampling / RandomAccess/64/1000/min_time:1.000        5229 ns         5229 ns       254554
BM_PiecewiseLinearFunction/UniformSampling / RandomAccess/128/1000/min_time:1.000       5297 ns         5297 ns       267700
BM_PiecewiseLinearFunction/UniformSampling / RandomAccess/256/1000/min_time:1.000       5287 ns         5287 ns       264672
BM_PiecewiseLinearFunction/UniformSampling / RandomAccess/512/1000/min_time:1.000       5317 ns         5317 ns       260750
*/
