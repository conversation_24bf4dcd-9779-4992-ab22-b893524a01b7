package(default_visibility = ["//visibility:public"])

load("//build/bazel_rules:walle_proto_library.bzl", "walle_proto_library")

walle_proto_library(
    name = "math_proto",
    srcs = [
        "math.proto",
        "point.proto",
    ],
    py_libs = [
        ":py_init",
    ],
)

cc_library(
    name = "proto_util",
    hdrs = ["proto_util.h"],
    deps = [
        ":cc_math_proto",
        "//base/math:geometry3d",
        "//base/math:vector",
    ],
)

py_library(
    name = "py_init",
    srcs = [
        "__init__.py",
    ],
)
