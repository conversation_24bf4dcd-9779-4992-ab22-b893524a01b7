syntax = "proto3";

package math_proto;

// Next ID: 3
message Vector2 {
  double x = 1;
  double y = 2;
}

// Next ID: 4
message Vector3 {
  double x = 1;
  double y = 2;
  double z = 3;
}

// Next ID: 5
message Vector4 {
  double x = 1;
  double y = 2;
  double z = 3;
  double w = 4;
}

// Next ID: 3
message Vector2i {
  int32 x = 1;
  int32 y = 2;
}

// Next ID: 5
message Quaternion {
  double x = 1;
  double y = 2;
  double z = 3;
  double w = 4;
}

// Next ID: 4
message EulerAngle {
  double roll = 1;
  double pitch = 2;
  double yaw = 3;
}

// Next ID: 4
message AABox2d {
  Vector2 center = 1;
  double length = 2;
  double width = 3;
}

// Next ID: 3
message Line2d {
  Vector2 direction = 1;
  double offset = 2;
}

// Next ID: 3
message LineSegment2d {
  Vector2 start = 1;
  Vector2 end = 2;
}

// Next ID: 2
message Polygon2d {
  repeated Vector2 polygon_point = 1;
}

// Next ID: 11
message Curve2d {
  reserved 4;
  reserved "tangent";
  double s0 = 1;
  double s_step = 2;

  bool has_heading = 5;
  double head_heading = 6;
  double tail_heading = 7;

  bool has_m = 8;
  Vector2 head_m = 9;
  Vector2 tail_m = 10;

  repeated Vector2 point = 3;
}

// Next ID: 5
message OrientedBox2d {
  Vector2 center = 1;
  double length = 2;
  double width = 3;
  double heading = 4;
}

// Next ID: 3
message Circle2d {
  Vector2 center = 1;
  double radius = 2;
}

// Next ID: 5
message Ellipse2d {
  Vector2 center = 1;
  double major_radius = 2;
  double minor_radius = 3;
  double angle = 4;
}

// Next-id: 5
message Box2d {
  double top_left_x = 1;
  double top_left_y = 2;
  double w = 3;
  double h = 4;
}

message Rigid3d {
  Vector3 translation = 1;
  oneof message {
    Quaternion quaternion = 2;
    EulerAngle euler_angle = 3;
  }
}

// Next ID: 3
enum MonotonicityType {
  DEFAULT = 0;
  NON_DECREASING = 1;
  NON_INCREASING = 2;
}

// Next ID: 9
message PiecewiseCubicFunction {
  reserved 5;
  reserved "tangent";
  MonotonicityType mode = 1;
  double x0 = 2;
  double x_step = 3;
  bool has_tangent = 6;
  double head_t = 7;
  double tail_t = 8;
  repeated double y_sample = 4;
}
