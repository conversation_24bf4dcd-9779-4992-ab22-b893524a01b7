// Copyright @2020 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <vector>

#include "Eigen/Core"
#include "Eigen/Geometry"

#include "base/math/proto/math.pb.h"
#include "base/math/proto/point.pb.h"
#include "base/math/quaternion.h"
#include "base/math/vector2.h"
#include "base/math/vector3.h"
#include "base/math/vector4.h"

namespace math {

inline Vector2d ToVector2d(const math_proto::Vector2& proto) {
  return Vector2d(proto.x(), proto.y());
}

inline Vector2i ToVector2i(const math_proto::Vector2i& proto) {
  return Vector2i(proto.x(), proto.y());
}

inline Vector3d ToVector3d(const math_proto::Vector3& proto) {
  return Vector3d(proto.x(), proto.y(), proto.z());
}

inline Vector4d ToVector4d(const math_proto::Vector4& proto) {
  return Vector4d(proto.x(), proto.y(), proto.z(), proto.w());
}

inline void ToVectorProto(const Vector2d& vec, math_proto::Vector2* v) {
  v->set_x(vec.x);
  v->set_y(vec.y);
}

inline void ToVectorProto(const Vector2i& vec, math_proto::Vector2i* v) {
  v->set_x(vec.x);
  v->set_y(vec.y);
}

inline void ToVectorProto(const Vector3d& vec, math_proto::Vector3* v) {
  v->set_x(vec.x);
  v->set_y(vec.y);
  v->set_z(vec.z);
}

inline void ToVectorProto(const Vector4d& vec, math_proto::Vector4* v) {
  v->set_x(vec.x);
  v->set_y(vec.y);
  v->set_z(vec.z);
  v->set_w(vec.w);
}

inline void ToQuaternionProto(const Vector4d& vec, math_proto::Quaternion* q) {
  q->set_x(vec.x);
  q->set_y(vec.y);
  q->set_z(vec.z);
  q->set_w(vec.w);
}

inline math_proto::Vector2 ToProto(const Vector2d& vec) {
  math_proto::Vector2 v;
  ToVectorProto(vec, &v);
  return v;
}

inline math_proto::Vector2i ToProto(const Vector2i& vec) {
  math_proto::Vector2i v;
  ToVectorProto(vec, &v);
  return v;
}

inline math_proto::Vector3 ToProto(const Vector3d& vec) {
  math_proto::Vector3 v;
  ToVectorProto(vec, &v);
  return v;
}

inline math_proto::Vector4 ToProto(const Vector4d& vec) {
  math_proto::Vector4 v;
  ToVectorProto(vec, &v);
  return v;
}

inline walle::Point3D ToPoint3D(const Vector3d& point) {
  walle::Point3D point_3d;
  point_3d.set_x(point.x);
  point_3d.set_y(point.y);
  point_3d.set_z(point.z);
  return point_3d;
}

inline walle::Point3D ToPoint3D(const Vector2d& point) {
  walle::Point3D point_3d;
  point_3d.set_x(point.x);
  point_3d.set_y(point.y);
  point_3d.set_z(0.0);
  return point_3d;
}

inline walle::Point2D ToPoint2D(const Vector2d& point) {
  walle::Point2D point_2d;
  point_2d.set_x(point.x);
  point_2d.set_y(point.y);
  return point_2d;
}

inline walle::PointENU ToPointENU(const Vector2d& vector) {
  walle::PointENU point_enu;
  point_enu.set_x(vector.x);
  point_enu.set_y(vector.y);
  return point_enu;
}

inline walle::PointENU ToPointENU(const Vector3d& vector) {
  walle::PointENU point_enu;
  point_enu.set_x(vector.x);
  point_enu.set_y(vector.y);
  point_enu.set_z(vector.z);
  return point_enu;
}

inline Vector2d ToVector2d(const walle::Point2D& vector) {
  return Vector2d(vector.x(), vector.y());
}

inline Vector3d ToVector3d(const walle::Point3D& vector) {
  return Vector3d(vector.x(), vector.y(), vector.z());
}

inline Vector3d ToVector3d(const walle::PointENU& vector) {
  return Vector3d(vector.x(), vector.y(), vector.z());
}

inline Vector2d ToVector2d(const walle::PointENU& vector) {
  return Vector2d(vector.x(), vector.y());
}

inline Quaternion ToQuaternion(const walle::Quaternion& quat) {
  return Quaternion(quat.qw(), quat.qx(), quat.qy(), quat.qz());
}

inline Quaternion ToQuaternion(const math_proto::Quaternion& quat) {
  return Quaternion(quat.w(), quat.x(), quat.y(), quat.z());
}

inline Eigen::Vector3d ToEigenVector3d(const Vector3d& vector) {
  return Eigen::Vector3d(vector[0], vector[1], vector[2]);
}

inline Eigen::Quaterniond ToEigenQuaternion(const Quaternion& quaternion) {
  return Eigen::Quaterniond(quaternion[3], quaternion[0], quaternion[1], quaternion[2]);
}

}  // namespace math
