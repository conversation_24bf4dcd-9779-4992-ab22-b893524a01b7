// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include <algorithm>
#include <random>
#include <vector>

#include "benchmark/benchmark.h"
#include "glog/logging.h"

#include "base/math/transform/rigid_transform.h"

namespace {

constexpr int kNumPointsPerPose = 128;
constexpr int kNumPose = 50;

}  // namespace

namespace math {
namespace transform {

template <typename FloatType>
class Rigid3TransformBenchmark {
 public:
  using Vec = Eigen::Matrix<FloatType, 3, 1>;
  Rigid3TransformBenchmark();

  Rigid3<FloatType> GetRandomRigid3();

  auto& pose_list() { return pose_list_; }
  auto& point_data() { return point_data_; }
  auto& transformed_point() { return transformed_point_; }

 private:
  std::vector<Rigid3<FloatType>> pose_list_;
  std::vector<std::vector<Vec>> point_data_;
  std::vector<std::vector<Vec>> transformed_point_;

  std::mt19937 rand_ = std::mt19937(42);
  std::uniform_real_distribution<FloatType> distribution_ =
      std::uniform_real_distribution<FloatType>(-1.0, 1.0);
  std::uniform_real_distribution<FloatType> distribution_positive_ =
      std::uniform_real_distribution<FloatType>(0.0, 200.0);
};

template <typename FloatType>
Rigid3TransformBenchmark<FloatType>::Rigid3TransformBenchmark()
    : pose_list_(kNumPose),
      point_data_(kNumPose, std::vector<Vec>(kNumPointsPerPose)),
      transformed_point_(kNumPose, std::vector<Vec>(kNumPointsPerPose)) {
  for (int i = 0; i < kNumPose; ++i) {
    pose_list_[i] = GetRandomRigid3();
    for (int j = 0; j < kNumPointsPerPose; ++j) {
      point_data_[i][j] << distribution_positive_(rand_), distribution_positive_(rand_),
          distribution_positive_(rand_);
    }
  }
}

template <typename FloatType>
Rigid3<FloatType> Rigid3TransformBenchmark<FloatType>::GetRandomRigid3() {
  const FloatType x = 0.7 * distribution_(rand_);
  const FloatType y = 0.7 * distribution_(rand_);
  const FloatType z = 0.7 * distribution_(rand_);
  const FloatType theta = M_PI_4 * distribution_(rand_);
  const FloatType w = cos(theta);
  const FloatType sin_theta = sin(theta);
  const FloatType rx = sin_theta * distribution_(rand_);
  const FloatType ry = sin_theta * distribution_(rand_);
  const FloatType rz = sin_theta * distribution_(rand_);
  return Rigid3<FloatType>(typename Rigid3<FloatType>::Vector(x, y, z),
                           typename Rigid3<FloatType>::Quaternion(w, rx, ry, rz));
}

}  // namespace transform
}  // namespace math

static void BM_Rigid3f(benchmark::State& state) {  // NOLINT
  math::transform::Rigid3TransformBenchmark<float> bench_data;
  for (auto _ : state) {
    for (int i = 0; i < kNumPose; ++i) {
      auto& pose = bench_data.pose_list()[i];
      for (int j = 0; j < kNumPointsPerPose; ++j) {
        bench_data.transformed_point()[i][j] = pose * bench_data.point_data()[i][j];
      }
    }
  }
}

static void BM_Rigid3d(benchmark::State& state) {  // NOLINT
  math::transform::Rigid3TransformBenchmark<double> bench_data;
  for (auto _ : state) {
    for (int i = 0; i < kNumPose; ++i) {
      auto& pose = bench_data.pose_list()[i];
      for (int j = 0; j < kNumPointsPerPose; ++j) {
        bench_data.transformed_point()[i][j] = pose * bench_data.point_data()[i][j];
      }
    }
  }
}

BENCHMARK(BM_Rigid3f);
BENCHMARK(BM_Rigid3d);

BENCHMARK_MAIN();

// Run on (16 X 5000 MHz CPU s)
// CPU Caches:
//   L1 Data 32K (x8)
//   L1 Instruction 32K (x8)
//   L2 Unified 256K (x8)
//   L3 Unified 16384K (x1)
// Load Average: 0.25, 0.56, 0.73

//$ bazel run --compiler=gcc //base/math/transform:rigid_transform_benchmark
// -----------------------------------------------------
// Benchmark           Time             CPU   Iterations
// -----------------------------------------------------
// BM_Rigid3f      24421 ns        24371 ns        28508
// BM_Rigid3d      24384 ns        24228 ns        28510

//$ bazel run --compiler=llvm //base/math/transform:rigid_transform_benchmark
// -----------------------------------------------------
// Benchmark           Time             CPU   Iterations
// -----------------------------------------------------
// BM_Rigid3f       6222 ns         6222 ns       106664
// BM_Rigid3d      11258 ns        11258 ns        62147
