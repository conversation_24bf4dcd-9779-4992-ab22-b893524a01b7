// Copyright @2020 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <iostream>
#include <string>

#include "Eigen/Core"
#include "Eigen/Geometry"
#include "absl/strings/substitute.h"

#include "base/math/math_util.h"
#include "base/math/vector3.h"

namespace math {
namespace transform {

template <typename FloatType>
class Rigid2 {
 public:
  static_assert(std::is_floating_point<FloatType>::value, "Rigid2 only supports floating types.");

  using Vector = Eigen::Matrix<FloatType, 2, 1>;
  using Rotation2D = Eigen::Rotation2D<FloatType>;

  Rigid2() : translation_(Vector::Zero()), rotation_(Rotation2D::Identity()) {}
  Rigid2(const Vector& translation, const Rotation2D& rotation)
      : translation_(translation), rotation_(rotation) {}
  Rigid2(const Vector& translation, double rotation)
      : translation_(translation), rotation_(rotation) {}

  static Rigid2<FloatType> Identity() { return Rigid2<FloatType>(); }

  template <typename OtherType>
  Rigid2<OtherType> cast() const {
    return Rigid2<OtherType>(translation_.template cast<OtherType>(),
                             rotation_.template cast<OtherType>());
  }

  const Vector& translation() const { return translation_; }

  Rotation2D rotation() const { return rotation_; }

  double normalized_angle() const {
    return math::NormalizeAngle(rotation().angle());
  }

  Rigid2 inverse() const {
    const Rotation2D rotation = rotation_.inverse();
    const Vector translation = -(rotation * translation_);
    return Rigid2(translation, rotation);
  }

  std::string DebugString() const {
    return absl::Substitute("{ t: [$0, $1], r: [$2] }",
        translation().x(), translation().y(), rotation().angle());
  }

 private:
  Vector translation_;
  Rotation2D rotation_;
};

template <typename FloatType>
Rigid2<FloatType> operator*(const Rigid2<FloatType>& lhs,
                            const Rigid2<FloatType>& rhs) {
  return Rigid2<FloatType>(
      lhs.rotation() * rhs.translation() + lhs.translation(),
      lhs.rotation() * rhs.rotation());
}

template <typename FloatType>
typename Rigid2<FloatType>::Vector operator*(
    const Rigid2<FloatType>& rigid,
    const typename Rigid2<FloatType>::Vector& point) {
  return rigid.rotation() * point + rigid.translation();
}

using Rigid2d = Rigid2<double>;
using Rigid2f = Rigid2<float>;

template <typename FloatType>
class Rigid3 {
 public:
  static_assert(std::is_floating_point<FloatType>::value, "Rigid3 only supports floating types.");

  using Vector = Eigen::Matrix<FloatType, 3, 1>;
  using Quaternion = Eigen::Quaternion<FloatType>;
  using Affine = Eigen::Transform<FloatType, 3, Eigen::Affine>;
  using Matrix = Eigen::Matrix<FloatType, 4, 4>;
  using Translation = Eigen::Translation<FloatType, 3>;
  using AngleAxis = Eigen::AngleAxis<FloatType>;

  Rigid3() : translation_(Vector::Zero()), rotation_(Quaternion::Identity()) {}
  Rigid3(const Vector& translation, const Quaternion& rotation)
      : translation_(translation), rotation_(rotation.normalized()) {}
  Rigid3(const Vector& translation, const AngleAxis& rotation)
      : translation_(translation), rotation_(rotation) {}

  static Rigid3<FloatType> Identity() { return Rigid3<FloatType>(); }

  // Rigid transform array: x, y, z, qw, qx, qy, qz.
  static Rigid3 FromArray(const FloatType* array) {
    CHECK(array != nullptr);
    return Rigid3(Vector(array[0], array[1], array[2]),
                  Quaternion(array[3], array[4], array[5], array[6]));
  }

  // Rigid transform array: x, y, z, qw, qx, qy, qz.
  void ToArray(FloatType* array) const {
    CHECK(array != nullptr);
    array[0] = translation_.x();
    array[1] = translation_.y();
    array[2] = translation_.z();
    array[3] = rotation_.w();
    array[4] = rotation_.x();
    array[5] = rotation_.y();
    array[6] = rotation_.z();
  }

  static Rigid3 FromAffine(const Affine& affine) {
    return Rigid3(Vector(affine.translation()), Quaternion(affine.rotation()));
  }

  static Rigid3 FromMatrix(const Matrix& matrix) {
    Affine affine;
    affine.matrix() = matrix;
    return Rigid3(Vector(affine.translation()), Quaternion(affine.rotation()));
  }

  Matrix ToMatrix() const {
    Affine affine = Translation(translation_) * rotation_;
    return affine.matrix();
  }

  Eigen::Isometry3d ToIsometry3d() const { return Translation(translation_) * rotation_; }
  Eigen::Affine3d ToAffine3d() const { return Translation(translation_) * rotation_; }

  template <typename OtherType>
  Rigid3<OtherType> cast() const {
    return Rigid3<OtherType>(translation_.template cast<OtherType>(),
                             rotation_.template cast<OtherType>());
  }

  const Vector& translation() const { return translation_; }
  FloatType* mutable_translation() { return translation_.data(); }

  const Quaternion& rotation() const { return rotation_; }
  FloatType* mutable_rotation() { return rotation_.coeffs().data(); }

  void Translate(const math::Vector3<FloatType>& t) {
    translation_ -= Eigen::Map<const Vector>(t.elements);
  }

  void PreTranslate(const Vector& t) {
    translation_ += t;
  }

  Rigid3 inverse() const {
    const Quaternion rotation = rotation_.conjugate();
    const Vector translation = -(rotation * translation_);
    return Rigid3(translation, rotation);
  }

  std::string DebugString() const {
    return absl::Substitute("{ t: [$0, $1, $2], q: [$3, $4, $5, $6] }",
                            translation().x(), translation().y(),
                            translation().z(), rotation().w(), rotation().x(),
                            rotation().y(), rotation().z());
  }

 private:
  Vector translation_;
  Quaternion rotation_;
};

template <typename FloatType>
Rigid3<FloatType> operator*(const Rigid3<FloatType>& lhs,
                            const Rigid3<FloatType>& rhs) {
  return Rigid3<FloatType>(
      lhs.rotation() * rhs.translation() + lhs.translation(),
      (lhs.rotation() * rhs.rotation()).normalized());
}

template <typename FloatType>
typename Rigid3<FloatType>::Vector operator*(
    const Rigid3<FloatType>& rigid,
    const typename Rigid3<FloatType>::Vector& point) {
  return rigid.rotation() * point + rigid.translation();
}

template <typename FloatType>
Vector3<FloatType> operator*(
    const Rigid3<FloatType>& rigid,
    const Vector3<FloatType>& point) {
  Eigen::Map<const typename Rigid3<FloatType>::Vector> v(point.elements);
  return Vector3<FloatType>(rigid * v);
}

using Rigid3d = Rigid3<double>;
using Rigid3f = Rigid3<float>;

}  // namespace transform
}  // namespace math
