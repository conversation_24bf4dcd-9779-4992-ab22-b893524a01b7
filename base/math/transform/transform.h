// Copyright @2020 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <cmath>

#include "Eigen/Core"
#include "Eigen/Geometry"

#include "base/math/proto/math.pb.h"
#include "base/math/transform/rigid_transform.h"

namespace math {
namespace transform {

// Returns the non-negative rotation angle in radians of the 3D transformation
// 'transform'.
template <typename FloatType>
FloatType GetAngle(const Rigid3<FloatType>& transform) {
  return FloatType(2) * std::atan2(transform.rotation().vec().norm(),
                                   std::abs(transform.rotation().w()));
}

template <typename FloatType>
FloatType GetAngle(const Eigen::Quaternion<FloatType>& quaternion) {
  return FloatType(2) * std::atan2(quaternion.vec().norm(), std::abs(quaternion.w()));
}

// Returns a quaternion representing the same rotation as the given 'angle_axis'
// vector.
template <typename T>
Eigen::Quaternion<T> AngleAxisVectorToRotationQuaternion(
    const Eigen::Matrix<T, 3, 1>& angle_axis) {
  T scale = T(0.5);
  T w = T(1.0);
  constexpr double kCutoffAngle = 1e-8;  // We linearize below this angle.
  if (angle_axis.squaredNorm() > kCutoffAngle) {
    const T norm = angle_axis.norm();
    scale = std::sin(norm / 2.0) / norm;
    w = std::cos(norm / 2.0);
  }
  const Eigen::Matrix<T, 3, 1> quaternion_xyz = scale * angle_axis;
  return Eigen::Quaternion<T>(w, quaternion_xyz.x(), quaternion_xyz.y(), quaternion_xyz.z());
}

// Rotation is composed of three rotations around X (roll), then Y (pitch), then Z (yaw).
Eigen::Quaterniond RollPitchYaw(double roll, double pitch, double yaw);

// Returns the roll component in radians of the given 3D 'rotation'. Assuming
// 'rotation' is composed of three rotations around X, then Y, then Z, returns
// the angle of the X rotation.
template <typename T>
T GetRoll(const Eigen::Quaternion<T>& rotation) {
  const double sin_roll = T(2.0) * (rotation.w() * rotation.x() + rotation.y() * rotation.z());
  const double cos_roll =
      T(1.0) - T(2.0) * (rotation.x() * rotation.x() + rotation.y() * rotation.y());
  return std::atan2(sin_roll, cos_roll);
}

// Returns the pitch component in radians of the given 3D 'rotation'. Assuming
// 'rotation' is composed of three rotations around X, then Y, then Z, returns
// the angle of the Y rotation.
template <typename T>
T GetPitch(const Eigen::Quaternion<T>& rotation) {
  const Eigen::Matrix<T, 3, 1> direction = rotation * Eigen::Matrix<T, 3, 1>::UnitX();
  return -std::atan2(direction.z(), std::hypot(direction.x(), direction.y()));
}

// Returns the yaw component in radians of the given 3D 'rotation'. Assuming
// 'rotation' is composed of three rotations around X, then Y, then Z, returns
// the angle of the Z rotation.
template <typename T>
T GetYaw(const Eigen::Quaternion<T>& rotation) {
  const Eigen::Matrix<T, 3, 1> direction = rotation * Eigen::Matrix<T, 3, 1>::UnitX();
  return std::atan2(direction.y(), direction.x());
}

// Returns the roll component in radians of the given 3D transformation
// 'transform'.
template <typename T>
T GetRoll(const Rigid3<T>& transform) {
  return GetRoll(transform.rotation());
}

// Returns the pitch component in radians of the given 3D transformation
// 'transform'.
template <typename T>
T GetPitch(const Rigid3<T>& transform) {
  return GetPitch(transform.rotation());
}

// Returns the yaw component in radians of the given 3D transformation
// 'transform'.
template <typename T>
T GetYaw(const Rigid3<T>& transform) {
  return GetYaw(transform.rotation());
}

// Projects 'transform' onto the XY plane.
template <typename T>
Rigid2<T> Project2D(const Rigid3<T>& transform) {
  return Rigid2<T>(transform.translation().template head<2>(), GetYaw(transform));
}

Eigen::Vector3d ToEigen(const math_proto::Vector3& vector);
Eigen::Quaterniond ToEigen(const math_proto::Quaternion& quaternion);
Eigen::Quaterniond ToEigen(const math_proto::EulerAngle& euler_angle);
math_proto::Vector3 ToProto(const Eigen::Vector3d& vector);
math_proto::Quaternion ToProto(const Eigen::Quaterniond& quaternion);
math_proto::Rigid3d ToProto(const Rigid3d& rigid);
Rigid3d ToRigid3d(const math_proto::Rigid3d& rigid_proto);

}  // namespace transform
}  // namespace math
