// Copyright @2020 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/math/transform/transform.h"
#include <random>

#include "gtest/gtest.h"

namespace math {
namespace transform {
namespace {

TEST(TransformTest, GetAngle) {
  std::mt19937 rng(42);
  std::uniform_real_distribution<double> angle_distribution(0.0, M_PI);
  std::uniform_real_distribution<double> position_distribution(-1.0, 1.0);

  for (int i = 0; i != 100; ++i) {
    const double angle = angle_distribution(rng);
    const double x = position_distribution(rng);
    const double y = position_distribution(rng);
    const double z = position_distribution(rng);
    const Eigen::Vector3d axis = Eigen::Vector3d(x, y, z).normalized();
    EXPECT_NEAR(angle,
                GetAngle(AngleAxisVectorToRotationQuaternion(Eigen::Vector3d(angle * axis))),
                1e-6f);
  }
}

TEST(TransformTest, GetYaw) {
  const auto rotation =
      Rigid3d::Quaternion(Eigen::AngleAxisd(1.2345, Eigen::Vector3d::UnitZ()));
  EXPECT_NEAR(1.2345, GetYaw(rotation), 1e-9);
  EXPECT_NEAR(-1.2345, GetYaw(rotation.inverse()), 1e-9);
}

TEST(TransformTest, GetRollPitchYaw) {
  const auto rotation = Rigid3d::Quaternion(Eigen::AngleAxisd(1.0, Eigen::Vector3d::UnitZ())) *
                        Rigid3d::Quaternion(Eigen::AngleAxisd(0.4, Eigen::Vector3d::UnitY())) *
                        Rigid3d::Quaternion(Eigen::AngleAxisd(0.2, Eigen::Vector3d::UnitX()));
  EXPECT_NEAR(0.2, GetRoll(rotation), 1e-6);
  EXPECT_NEAR(0.4, GetPitch(rotation), 1e-6);
  EXPECT_NEAR(1.0, GetYaw(rotation), 1e-6);
}

TEST(TransformTest, GetYawAxisOrdering) {
  const auto rotation =
      Rigid3d::Quaternion(Eigen::AngleAxisd(1.2345, Eigen::Vector3d::UnitZ()) *
                          Eigen::AngleAxisd(0.4321, Eigen::Vector3d::UnitY()) *
                          Eigen::AngleAxisd(0.6789, Eigen::Vector3d::UnitX()));
  EXPECT_NEAR(1.2345, GetYaw(rotation), 1e-9);
}

TEST(TransformTest, Proto) {
  math_proto::Vector3 vec_proto;
  vec_proto.set_x(1.0);
  vec_proto.set_y(2.0);
  vec_proto.set_z(3.0);

  math_proto::Quaternion quat_proto;
  quat_proto.set_w(1.0);
  quat_proto.set_x(0.0);
  quat_proto.set_y(0.0);
  quat_proto.set_z(0.0);

  math_proto::Rigid3d proto1;
  *proto1.mutable_translation() = vec_proto;
  *proto1.mutable_quaternion() = quat_proto;

  const math_proto::Rigid3d proto2 = ToProto(ToRigid3d(proto1));
  EXPECT_NEAR(proto1.translation().x(), proto2.translation().x(), 1e-6);
  EXPECT_NEAR(proto1.translation().y(), proto2.translation().y(), 1e-6);
  EXPECT_NEAR(proto1.translation().z(), proto2.translation().z(), 1e-6);
  EXPECT_NEAR(proto1.quaternion().x(), proto2.quaternion().x(), 1e-6);
  EXPECT_NEAR(proto1.quaternion().y(), proto2.quaternion().y(), 1e-6);
  EXPECT_NEAR(proto1.quaternion().z(), proto2.quaternion().z(), 1e-6);
  EXPECT_NEAR(proto1.quaternion().w(), proto2.quaternion().w(), 1e-6);
}

}  // namespace
}  // namespace transform
}  // namespace math
