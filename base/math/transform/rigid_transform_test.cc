// Copyright @2020 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/math/transform/rigid_transform.h"

#include <random>

#include "gtest/gtest.h"

namespace math {
namespace transform {
namespace {

class RigidTransformTest : public ::testing::Test {
 protected:
  void CheckRigidTransform(const Rigid3d& t1, const Rigid3d& t2) {
    EXPECT_NEAR(t1.translation().x(), t2.translation().x(), 1e-6);
    EXPECT_NEAR(t1.translation().y(), t2.translation().y(), 1e-6);
    EXPECT_NEAR(t1.translation().z(), t2.translation().z(), 1e-6);
    EXPECT_NEAR(t1.rotation().x(), t2.rotation().x(), 1e-6);
    EXPECT_NEAR(t1.rotation().y(), t2.rotation().y(), 1e-6);
    EXPECT_NEAR(t1.rotation().z(), t2.rotation().z(), 1e-6);
    EXPECT_NEAR(t1.rotation().w(), t2.rotation().w(), 1e-6);
  }

  Rigid3d GetRandomRigid3d() {
    const double x = 0.7 * distribution_(prng_);
    const double y = 0.7 * distribution_(prng_);
    const double z = 0.7 * distribution_(prng_);
    const double theta = M_PI_4 * distribution_(prng_);
    const double w = cos(theta);
    const double sin_theta = sin(theta);
    const double rx = sin_theta * distribution_(prng_);
    const double ry = sin_theta * distribution_(prng_);
    const double rz = sin_theta * distribution_(prng_);
    return transform::Rigid3d(Rigid3d::Vector(x, y, z), Rigid3d::Quaternion(w, rx, ry, rz));
  }

  void GetRandomNormalizedArray(double* array) {
    const double x = 0.7 * distribution_(prng_);
    const double y = 0.7 * distribution_(prng_);
    const double z = 0.7 * distribution_(prng_);
    const double theta = M_PI_4 * distribution_(prng_);
    const double w = cos(theta);
    const double sin_theta = sin(theta);
    const double vx = sin_theta * distribution_(prng_);
    const double vy = sin_theta * distribution_(prng_);
    const double vz = sin_theta * distribution_(prng_);
    const double r_scale = sin_theta / sqrt(vx * vx + vy * vy + vz * vz);
    array[0] = x;
    array[1] = y;
    array[2] = z;
    array[3] = w;
    array[4] = vx * r_scale;
    array[5] = vy * r_scale;
    array[6] = vz * r_scale;
  }

  std::mt19937 prng_ = std::mt19937(42);
  std::uniform_real_distribution<double> distribution_ = std::uniform_real_distribution<double>(-1.0, 1.0);
};

TEST_F(RigidTransformTest, IdentityTest) {
  const Rigid3d pose = GetRandomRigid3d();
  CheckRigidTransform(pose * Rigid3d::Identity(), pose);
  CheckRigidTransform(Rigid3d::Identity() * pose, pose);

  CheckRigidTransform(pose * Rigid3d(), pose);
  CheckRigidTransform(Rigid3d() * pose, pose);
}

TEST_F(RigidTransformTest, InverseTest) {
  const Rigid3d pose = GetRandomRigid3d();
  CheckRigidTransform(pose.inverse() * pose, Rigid3d::Identity());
  CheckRigidTransform(pose * pose.inverse(), Rigid3d::Identity());
}

TEST_F(RigidTransformTest, ArrayTest) {
  double source_array[7];
  GetRandomNormalizedArray(source_array);
  Rigid3d rigid3d = Rigid3d::FromArray(source_array);
  double target_array[7];
  rigid3d.ToArray(target_array);
  for (int i = 0; i < 7; i++)
    CHECK_NEAR(source_array[i], target_array[i], 1e-6);
}

TEST_F(RigidTransformTest, MatrixTest) {
  const Rigid3d pose = GetRandomRigid3d();
  CheckRigidTransform(pose, Rigid3d::FromMatrix(pose.ToMatrix()));
}

TEST_F(RigidTransformTest, MulVector3Test) {
  const Rigid3d rigid = Rigid3d::Identity();
  math::Vector3d v(1.0, 0.0, 0.0);
  math::Vector3d v1 = rigid * v;
  EXPECT_NEAR((v1 - v).Length(), 0.0, 1e-6);
}

}  // namespace
}  // namespace transform
}  // namespace math
