package(default_visibility = ["//visibility:public"])

cc_library(
    name = "transform",
    srcs = ["transform.cc"],
    hdrs = ["transform.h"],
    deps = [
        ":rigid_transform",
        "//base/math/proto:cc_math_proto",
        "@com_google_absl//absl/strings",
        "@eigen",
    ],
)

cc_test(
    name = "transform_test",
    srcs = ["transform_test.cc"],
    tags = [
        "ci",
        "ci_cpu",
    ],
    deps = [
        ":rigid_transform",
        ":transform",
        "//base/testing:test_main",
        "@gtest",
    ],
)

cc_library(
    name = "rigid_transform",
    hdrs = ["rigid_transform.h"],
    deps = [
        "//base/math",
        "@com_google_absl//absl/strings",
        "@eigen",
    ],
)

cc_test(
    name = "rigid_transform_test",
    srcs = ["rigid_transform_test.cc"],
    tags = [
        "ci",
        "ci_cpu",
    ],
    deps = [
        ":rigid_transform",
        ":transform",
        "//base/testing:test_main",
        "@gtest",
    ],
)

cc_binary(
    name = "rigid_transform_benchmark",
    srcs = ["rigid_transform_benchmark.cc"],
    deps = [
        ":rigid_transform",
        "@benchmark",
        "@eigen",
    ],
)
