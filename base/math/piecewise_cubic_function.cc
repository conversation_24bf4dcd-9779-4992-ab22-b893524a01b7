// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/math/piecewise_cubic_function.h"

#include <algorithm>
#include <utility>

#include "base/math/cubic_hermite_spline.h"
#include "base/math/newton_method.h"
#include "base/strings/macros.h"

namespace math {
namespace {

// https://en.wikipedia.org/wiki/Monotone_cubic_interpolation
// https://jbrd.github.io/2020/12/27/monotone-cubic-interpolation.html for more insights.
std::vector<double> CreateMonotoneTangents(
    const std::vector<double>& y, double x_step, int n_samples, double head_t, double tail_t) {
  DCHECK_GE(n_samples, 2);
  const double x_step_inverse = 1.0 / x_step;
  std::vector<double> m_in_step(n_samples - 1);
  for (int i = 0; i < n_samples - 1; ++i) {
    m_in_step[i] = (y[i + 1] - y[i]) * x_step_inverse;
  }
  std::vector<double> m(n_samples);
  m[0] = head_t;
  m[n_samples - 1] = tail_t;
  for (int i = 1; i < n_samples - 1; ++i) {
    if (m_in_step[i - 1] * m_in_step[i] < 0.0) {
      m[i] = 0.0;
    } else {
      m[i] = (m_in_step[i - 1] + m_in_step[i]) * 0.5;
    }
  }
  for (int i = 0; i < n_samples - 1; ++i) {
    if (math::IsZero(m_in_step[i])) {
      m[i] = 0.0;
      m[i + 1] = 0.0;
    } else {
      const double alpha_i = m[i] / m_in_step[i];
      const double beta_i = m[i + 1] / m_in_step[i];
      const double distance_square = math::square(alpha_i) + math::square(beta_i);
      if (distance_square > 9.0) {
        const double tau_i = 3.0 / std::sqrt(distance_square);
        m[i] *= tau_i;
        m[i + 1] *= tau_i;
      }
    }
  }
  for (int i = 0; i < n_samples; ++i) {
    m[i] *= x_step;
  }
  return m;
}

}  // namespace

PiecewiseCubicFunction::PiecewiseCubicFunction(MonotonicityType mode,
                                               double x0,
                                               double x_step,
                                               std::vector<double> y_samples,
                                               double head_t,
                                               double tail_t)
    : mode_(mode), x0_(x0), x_step_(x_step), y_(std::move(y_samples)) {
  CHECK(std::isfinite(x0_));
  CHECK(std::isfinite(x_step_));
  CHECK_GE(x_step_, kEpsilon);
  CHECK_GE(y_.size(), 2);
  CHECK(std::isfinite(head_t));
  CHECK(std::isfinite(tail_t));

  x_step_inverse_ = 1.0 / x_step_;
  x_range_ = x_step_ * (y_.size() - 1);
  n_samples_ = y_.size();

  if (mode_ == MonotonicityType::kDefault) {
    const auto iter = std::minmax_element(y_.begin(), y_.end());
    min_y_ = *iter.first;
    max_y_ = *iter.second;
  } else if (mode_ == MonotonicityType::kNonDecreasing) {
    CHECK_GE(head_t, -kEpsilon);
    CHECK_GE(tail_t, -kEpsilon);
    for (int i = 1; i < n_samples_; ++i) {
      CHECK_GE(y_[i] - y_[i - 1], -kEpsilon) << DUMP_TO_STREAM(i, y_[i], y_[i - 1]);
      y_[i] = std::max(y_[i], y_[i - 1]);
    }
    min_y_ = y_[0];
    max_y_ = y_[n_samples_ - 1];
    head_t = std::max(0.0, head_t);
    tail_t = std::max(0.0, tail_t);
  } else if (mode_ == MonotonicityType::kNonIncreasing) {
    CHECK_LE(head_t, +kEpsilon);
    CHECK_LE(tail_t, +kEpsilon);
    for (int i = 1; i < n_samples_; ++i) {
      CHECK_LE(y_[i] - y_[i - 1], +kEpsilon) << DUMP_TO_STREAM(i, y_[i], y_[i - 1]);
      y_[i] = std::min(y_[i], y_[i - 1]);
    }
    min_y_ = y_[n_samples_ - 1];
    max_y_ = y_[0];
    head_t = std::min(0.0, head_t);
    tail_t = std::min(0.0, tail_t);
  } else {
    LOG(FATAL) << "Unknown monotonicity mode=" << static_cast<int>(mode_);
  }
  head_t_ = head_t;
  tail_t_ = tail_t;

  if (mode_ == MonotonicityType::kNonDecreasing || mode_ == MonotonicityType::kNonIncreasing) {
    m_ = CreateMonotoneTangents(y_, x_step, n_samples_, head_t, tail_t);
  } else {
    m_ = CubicHermiteSpline<double>::ComputeTangentMatrix(head_t * x_step_, tail_t * x_step_, y_);
  }
  for (int i = 0; i < n_samples_; ++i) {
    CHECK(std::isfinite(m_[i])) << DUMP_TO_STREAM(i, m_[i]);
  }
}

void PiecewiseCubicFunction::ShiftX(double x) {
  CHECK(std::isfinite(x));
  x0_ = x;
}

void PiecewiseCubicFunction::ShiftX(double x, double y) {
  CHECK(std::isfinite(x));
  CHECK(std::isfinite(y) && IsYInside(y)) << DUMP_TO_STREAM(x, y, min_y(), max_y());
  double x_by_y = 0.0;
  FindXByY(y, &x_by_y);
  ShiftX(x - (x_by_y - x0_));
}

void PiecewiseCubicFunction::ShiftY(double y) {
  CHECK(std::isfinite(y));
  const double delta = y - min_y_;
  for (int i = 0; i < n_samples_; ++i) {
    y_[i] += delta;
  }
  min_y_ += delta;
  max_y_ += delta;
}

void PiecewiseCubicFunction::ShiftY(double x, double y) {
  CHECK(std::isfinite(x));
  CHECK(std::isfinite(y));
  const double y_by_x = Interpolate(x, true);
  ShiftY(y - (y_by_x - min_y_));
}

void PiecewiseCubicFunction::FlipY() {
  if (mode_ == MonotonicityType::kNonIncreasing) {
    mode_ = MonotonicityType::kNonDecreasing;
  } else if (mode_ == MonotonicityType::kNonDecreasing) {
    mode_ = MonotonicityType::kNonIncreasing;
  }
  std::swap(min_y_, max_y_);
  min_y_ = -min_y_;
  max_y_ = -max_y_;
  head_t_ = -head_t_;
  tail_t_ = -tail_t_;
  std::for_each(y_.begin(), y_.end(), [](double& y) { y = -y; });
  std::for_each(m_.begin(), m_.end(), [](double& m) { m = -m; });
}

int PiecewiseCubicFunction::LowerBoundByY(double y) const {
  switch (mode_) {
    default:
      LOG(FATAL) << "Non-monotonic function.";
    case MonotonicityType::kNonDecreasing:
      return std::lower_bound(y_.begin(), y_.end(), y) - y_.begin();
    case MonotonicityType::kNonIncreasing:
      return n_samples_ - (std::upper_bound(y_.rbegin(), y_.rend(), y) - y_.rbegin());
  }
}

bool PiecewiseCubicFunction::FindXByY(double y, double* x, double* delta_y) const {
  CHECK(std::isfinite(y));
  double result_x = 0.0;
  const int lo = LowerBoundByY(y);
  if (lo == 0) {
    result_x = (std::abs(m_[0]) < kEpsilon) ? min_x() : min_x() + x_step_ * ((y - y_[0]) / m_[0]);
  } else if (lo == n_samples_) {
    const int i = n_samples_ - 1;
    result_x = (std::abs(m_[i]) < kEpsilon) ? max_x() : max_x() + x_step_ * ((y - y_[i]) / m_[i]);
  } else {
    constexpr int kMaxIterations = 10;
    const auto value = [this](double x) { return Interpolate(x, true); };
    const auto derivative = [this](double x) { return Derivative(x, true); };
    const double ub = x0_ + lo * x_step_;
    NewtonMethod(ub - x_step_, ub, y, kMaxIterations, value, derivative, &result_x);
  }
  if (x != nullptr) {
    *x = result_x;
  }
  if (delta_y != nullptr) {
    *delta_y = y - Interpolate(result_x, true);
  }
  return IsXInside(result_x);
}

double PiecewiseCubicFunction::Interpolate(double x, bool extrapolate) const {
  CHECK(extrapolate || IsXInside(x)) << DUMP_TO_STREAM(x, min_x(), max_x());
  const double x_index = (x - x0_) * x_step_inverse_;
  const int i0 = CastToFloorInteger<int>(x_index);
  const int i1 = i0 + 1;
  if (i0 < 0) {
    return y_[0] + m_[0] * x_index;
  }
  if (i0 > n_samples_ - 2) {
    return y_[n_samples_ - 1] + m_[n_samples_ - 1] * (x_index - (n_samples_ - 1));
  }
  const double r = x_index - i0;
  return CubicHermiteSpline<double>::Interpolate(y_[i0], m_[i0], y_[i1], m_[i1], r);
}

double PiecewiseCubicFunction::Derivative(double x, bool extrapolate) const {
  CHECK(extrapolate || IsXInside(x)) << DUMP_TO_STREAM(x, min_x(), max_x());
  const double x_index = (x - x0_) * x_step_inverse_;
  const int i0 = CastToFloorInteger<int>(x_index);
  const int i1 = i0 + 1;
  if (i0 < 0) {
    return m_[0] * x_step_inverse_;
  }
  if (i0 > n_samples_ - 2) {
    return m_[n_samples_ - 1] * x_step_inverse_;
  }
  const double r = x_index - i0;
  const double d =
      CubicHermiteSpline<double>::Derivative(y_[i0], m_[i0], y_[i1], m_[i1], r) * x_step_inverse_;
  switch (mode_) {
    default:
      return d;
    case MonotonicityType::kNonDecreasing:
      return std::max(0.0, d);
    case MonotonicityType::kNonIncreasing:
      return std::min(0.0, d);
  }
}

// Second-order derivative for monotone is computed by numeric difference of zero-order values
// instead of using Hermite coefficients due to the following considerations.
// 1. keep second-order derivative smooth (traded off by self-consistence property, which is less
// important)
// 2. better capability to recorver higher-order information given a vector of relatively dense
// sample points (e.g. a typical use-case would be a constant accelerating motion with x_step=0.1)
double PiecewiseCubicFunction::SecondDerivative(double x, bool extrapolate) const {
  CHECK(extrapolate || IsXInside(x)) << DUMP_TO_STREAM(x, min_x(), max_x());
  const double x_index = (x - x0_) * x_step_inverse_;
  if (mode_ == MonotonicityType::kNonDecreasing || mode_ == MonotonicityType::kNonIncreasing) {
    const int i1 = CastToFloorInteger<int, double>(x_index);
    const int i0 = i1 - 1;
    const int i2 = i1 + 1;
    const int i3 = i1 + 2;
    if (i1 < 0 || i2 > n_samples_ - 1) {
      return 0.0;
    }
    const double d12 = (y_[i2] - y_[i1]) * x_step_inverse_;
    double sd012 = 0.0;
    if (i0 >= 0) {
      const double d01 = (y_[i1] - y_[i0]) * x_step_inverse_;
      sd012 = (d12 - d01) * x_step_inverse_;
    }
    double sd123 = 0.0;
    if (i3 <= n_samples_ - 1) {
      const double d23 = (y_[i3] - y_[i2]) * x_step_inverse_;
      sd123 = (d23 - d12) * x_step_inverse_;
    }
    const double r = x_index - i1;
    return math::Lerp(sd012, sd123, r);
  }

  const int i0 = CastToFloorInteger<int>(x_index);
  const int i1 = i0 + 1;
  if (i0 < 0) {
    return 0.0;
  }
  if (i0 > n_samples_ - 2) {
    return 0.0;
  }
  const double r = x_index - i0;
  return CubicHermiteSpline<double>::SecondDerivative(y_[i0], m_[i0], y_[i1], m_[i1], r) *
         math::square(x_step_inverse_);
}

double PiecewiseCubicFunction::ThirdDerivative(double x, bool extrapolate) const {
  CHECK(extrapolate || IsXInside(x)) << DUMP_TO_STREAM(x, min_x(), max_x());
  const double x_index = (x - x0_) * x_step_inverse_;
  if (mode_ == MonotonicityType::kNonDecreasing || mode_ == MonotonicityType::kNonIncreasing) {
    const int i2 = CastToNearestInteger<int, double>(x_index);
    const int i0 = i2 - 2;
    const int i1 = i2 - 1;
    const int i3 = i2 + 1;
    const int i4 = i2 + 2;
    if (i1 < 0 || i3 > n_samples_ - 1) {
      return 0.0;
    }
    const double x_step_inverse_square = x_step_inverse_ * x_step_inverse_;
    double sd123 = (y_[i3] - 2.0 * y_[i2] + y_[i1]) * x_step_inverse_square;
    double td0123 = 0.0;
    if (i0 >= 0) {
      const double sd012 = (y_[i2] - 2.0 * y_[i1] + y_[i0]) * x_step_inverse_square;
      td0123 = (sd123 - sd012) * x_step_inverse_;
    }
    double td1234 = 0.0;
    if (i4 <= n_samples_ - 1) {
      const double sd234 = (y_[i4] - 2.0 * y_[i3] + y_[i2]) * x_step_inverse_square;
      td1234 = (sd234 - sd123) * x_step_inverse_;
    }
    const double r = x_index - i2 + 0.5;
    return math::Lerp(td0123, td1234, r);
  }
  const int i1 = CastToFloorInteger<int>(x_index + 0.5);
  const int i0 = i1 - 1;
  const int i2 = i1 + 1;
  const double sd0 = SecondDerivative(x0_ + i0 * x_step_, true);
  const double sd1 = SecondDerivative(x0_ + i1 * x_step_, true);
  const double sd2 = SecondDerivative(x0_ + i2 * x_step_, true);
  const double td01s = sd1 - sd0;
  const double td12s = sd2 - sd1;
  const double r = x_index - (i0 + 0.5);
  return math::Lerp(td01s, td12s, r) * x_step_inverse_;
}

double PiecewiseCubicFunction::GetMinDerivative() const {
  double min_m = +std::numeric_limits<double>::infinity();
  for (int i = 0; i < n_samples_; ++i) {
    min_m = std::min(min_m, m_[i]);
  }
  return min_m * x_step_inverse_;
}

double PiecewiseCubicFunction::GetMaxDerivative() const {
  double max_m = -std::numeric_limits<double>::infinity();
  for (int i = 0; i < n_samples_; ++i) {
    max_m = std::max(max_m, m_[i]);
  }
  return max_m * x_step_inverse_;
}

double PiecewiseCubicFunction::GetMinSecondDerivative() const {
  double min_sd = +std::numeric_limits<double>::infinity();
  const int min_step_num = mode_ == MonotonicityType::kDefault ? 0 : 1;
  const int max_step_num = mode_ == MonotonicityType::kDefault ? n_samples_ - 1 : n_samples_ - 2;
  if (min_step_num > max_step_num) {
    return 0.0;
  }
  for (int i = min_step_num; i <= max_step_num; ++i) {
    const double x = x0_ + i * x_step_;
    min_sd = std::min(min_sd, SecondDerivative(x, true));
  }
  return min_sd;
}

double PiecewiseCubicFunction::GetMaxSecondDerivative() const {
  double max_sd = -std::numeric_limits<double>::infinity();
  const int min_step_num = mode_ == MonotonicityType::kDefault ? 0 : 1;
  const int max_step_num = mode_ == MonotonicityType::kDefault ? n_samples_ - 1 : n_samples_ - 2;
  if (min_step_num > max_step_num) {
    return 0.0;
  }
  for (int i = min_step_num; i <= max_step_num; ++i) {
    const double x = x0_ + i * x_step_;
    max_sd = std::max(max_sd, SecondDerivative(x, true));
  }
  return max_sd;
}

PiecewiseCubicFunction PiecewiseCubicFunction::Resample(double x_step_hint) const {
  CHECK(std::isfinite(x_step_hint));
  CHECK_GE(x_step_hint, kEpsilon);
  const int n_steps = std::max(1, math::CastToCeilInteger(x_range_ / x_step_hint - math::kEpsilon));
  const auto generator = [this](double x) { return Interpolate(x, true); };
  return Create(mode_,
                x0_,
                x_range_ / n_steps,
                n_steps + 1,
                generator,
                m_[0] * x_step_inverse_,
                m_[n_samples_ - 1] * x_step_inverse_);
}

PiecewiseCubicFunction PiecewiseCubicFunction::MakeSubFunction(double min_x,
                                                               double max_x_hint) const {
  CHECK_LE(min_x + x_step_, max_x_hint);
  const int n_samples =
      math::CastToCeilInteger((max_x_hint - min_x) * x_step_inverse_ - kEpsilon) + 1;
  std::vector<double> y_samples;
  y_samples.reserve(n_samples);
  for (int i = 0; i < n_samples; ++i) {
    y_samples.push_back(Interpolate(min_x + i * x_step_, true));
  }
  const double head_t = Derivative(min_x, true);
  const double tail_t = Derivative(min_x + (n_samples - 1) * x_step_, true);
  return Create(mode_, min_x, x_step_, std::move(y_samples), head_t, tail_t);
}

math_proto::PiecewiseCubicFunction PiecewiseCubicFunction::ToProto() const {
  math_proto::PiecewiseCubicFunction proto;
  proto.set_mode(static_cast<math_proto::MonotonicityType>(mode_));
  proto.set_x0(x0_);
  proto.set_x_step(x_step_);
  proto.set_has_tangent(true);
  proto.set_head_t(head_t_);
  proto.set_tail_t(tail_t_);
  proto.mutable_y_sample()->Reserve(n_samples_);
  for (int i = 0; i < n_samples_; ++i) {
    proto.mutable_y_sample()->Add(y_[i]);
  }
  return proto;
}

PiecewiseCubicFunction PiecewiseCubicFunction::FromProto(
    const math_proto::PiecewiseCubicFunction& proto) {
  const MonotonicityType mode = static_cast<MonotonicityType>(proto.mode());
  std::vector<double> y_samples;
  y_samples.reserve(proto.y_sample_size());
  for (double y : proto.y_sample()) {
    y_samples.push_back(y);
  }
  base::Optional<double> head_t;
  base::Optional<double> tail_t;
  if (proto.has_tangent()) {
    head_t = proto.head_t();
    tail_t = proto.tail_t();
  }
  return Create(mode, proto.x0(), proto.x_step(), std::move(y_samples), head_t, tail_t);
}

PiecewiseCubicFunction PiecewiseCubicFunction::Create(MonotonicityType mode,
                                                      double x0,
                                                      double x_step,
                                                      std::vector<double> y_samples,
                                                      base::Optional<double> head_t,
                                                      base::Optional<double> tail_t) {
  CHECK_GE(x_step, kEpsilon);
  CHECK_GE(y_samples.size(), 2);
  const int n = y_samples.size();
  if (!head_t) {
    head_t = (y_samples[1] - y_samples[0]) / x_step;
  }
  if (!tail_t) {
    tail_t = (y_samples[n - 1] - y_samples[n - 2]) / x_step;
  }
  return PiecewiseCubicFunction(mode, x0, x_step, std::move(y_samples), *head_t, *tail_t);
}

PiecewiseCubicFunction PiecewiseCubicFunction::Create(
    MonotonicityType mode,
    double x0,
    double x_step,
    int n_samples,
    const std::function<double(double)>& generator,
    base::Optional<double> head_t,
    base::Optional<double> tail_t) {
  CHECK_GE(n_samples, 2);
  std::vector<double> y_samples(n_samples);
  for (int i = 0; i < n_samples; ++i) {
    y_samples[i] = generator(x0 + x_step * i);
  }
  return Create(mode, x0, x_step, std::move(y_samples), head_t, tail_t);
}

PiecewiseCubicFunction PiecewiseCubicFunction::Create(MonotonicityType mode,
                                                      const std::vector<double>& x_samples,
                                                      const std::vector<double>& y_samples,
                                                      double x_step_hint,
                                                      base::Optional<double> head_t,
                                                      base::Optional<double> tail_t) {
  CHECK_GE(x_samples.size(), 2);
  double x0 = x_samples.front();
  double x_step = 0.0;
  std::vector<double> resample_y = CubicHermiteSpline<double>::ResampleToUniformPoints(
      x_samples, y_samples, x_step_hint, &x_step);
  const int resample_y_size = resample_y.size();
  if (mode == MonotonicityType::kNonDecreasing) {
    for (int i = 1; i < resample_y_size; ++i) {
      resample_y[i] = std::max(resample_y[i], resample_y[i - 1]);
    }
  } else if (mode == MonotonicityType::kNonIncreasing) {
    for (int i = 1; i < resample_y_size; ++i) {
      resample_y[i] = std::min(resample_y[i], resample_y[i - 1]);
    }
  }
  return Create(mode, x0, x_step, std::move(resample_y), head_t, tail_t);
}

}  // namespace math
