// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "benchmark/benchmark.h"

#include "base/math/line_segment2d.h"
#include "base/math/polygon2d.h"

#include <random>

// TODO(wangnaizheng): refactor this file.
namespace math {
namespace {

Polygon2d GetRandomPolygon(int num_points, double max_radius, bool is_convex) {
  const double kMinRadius = 0.1;
  CHECK_GT(max_radius, kMinRadius);
  CHECK_GE(num_points, 3);
  const double angle_resolution = 2.0 * M_PI / num_points;
  CHECK_GT(max_radius * angle_resolution, kMinRadius);
  std::mt19937 random_gen;
  std::uniform_real_distribution<double> length_distribution(kMinRadius, max_radius);
  std::vector<math::Vector2d> points;
  int counter = 0;
  while (counter < num_points) {
    const double angle = counter * angle_resolution;
    const double length = is_convex ? max_radius : length_distribution(random_gen);
    math::Vector2d point = {length * std::cos(angle), length * std::sin(angle)};
    bool is_good_candidate = true;
    for (int i = 0; i < static_cast<int>(points.size()); ++i) {
      if (points[i].DistanceTo(point) < kMinRadius) {
        is_good_candidate = false;
      }
    }
    if (is_good_candidate) {
      points.push_back(point);
      ++counter;
    }
  }
  Polygon2d polygon(points);
  CHECK_EQ(is_convex, polygon.is_convex());
  return polygon;
}

LineSegment2d GetRandomSegment2d(double max_length) {
  const double kMinLength = 0.1;
  CHECK_GT(max_length, kMinLength);
  std::mt19937 random_gen;
  std::uniform_real_distribution<double> length_distribution(kMinLength, max_length);
  std::uniform_real_distribution<double> angle_distribution(0.0, M_2_PI);
  const double length = length_distribution(random_gen);
  const double angle = angle_distribution(random_gen);
  math::Vector2d start_point = {0.0, 0.0};
  math::Vector2d end_point = {length * std::cos(angle), length * std::sin(angle)};
  LineSegment2d line_segment2d(start_point, end_point);
  return line_segment2d;
}

}  // namespace

void BM_IsPointIn(benchmark::State& state, bool is_convex) {  // NOLINT(runtime/references)
  const int kNumLookup = 1000;
  const int num_points = state.range(0);
  const math::Vector2d point = {0.0, 0.0};
  const Polygon2d polygon = GetRandomPolygon(num_points, 10.0, is_convex);
  while (state.KeepRunning()) {
    for (int i = 0; i < kNumLookup; ++i) {
      const bool ret = polygon.IsPointIn(point);
      benchmark::DoNotOptimize(ret);
    }
  }
}
BENCHMARK_CAPTURE(BM_IsPointIn, IsPointInConvex, true)->Arg(500)->Arg(100)->Arg(50)->Arg(10);
BENCHMARK_CAPTURE(BM_IsPointIn, IsPointInNonConvex, false)->Arg(500)->Arg(100)->Arg(50)->Arg(10);

void BM_ComputeOverlap(benchmark::State& state) {  // NOLINT(runtime/references)
  const int kNumLookup = 100;
  const int num_points = state.range(0);
  const Polygon2d polygon1 = GetRandomPolygon(num_points, 10000.0, true);
  const Polygon2d polygon2 = GetRandomPolygon(num_points, 10000.0, true).Shifted({5000.0, 5000.0});
  Polygon2d polygon3;
  while (state.KeepRunning()) {
    for (int i = 0; i < kNumLookup; ++i) {
      const bool ret = polygon1.ComputeOverlap(polygon2, &polygon3);
      benchmark::DoNotOptimize(ret);
    }
  }
}
BENCHMARK(BM_ComputeOverlap)->Arg(5000)->Arg(3000)->Arg(2000)->Arg(1000);

void BM_DistanceTo(benchmark::State& state) {  // NOLINT(runtime/references)
  const int kNumLookup = 100;
  const int num_points = state.range(0);
  const Polygon2d polygon1 = GetRandomPolygon(num_points, 10.0, true);
  const Polygon2d polygon2 = GetRandomPolygon(num_points, 10.0, true).Shifted({50.0, 50.0});
  while (state.KeepRunning()) {
    for (int i = 0; i < kNumLookup; ++i) {
      const bool ret = polygon1.DistanceTo(polygon2) < math::kEpsilon;
      benchmark::DoNotOptimize(ret);
    }
  }
}
BENCHMARK(BM_DistanceTo)->Arg(500)->Arg(100)->Arg(50)->Arg(10);

void BM_Contains(benchmark::State& state) { // NOLINT(runtime/references)
  const int kNumLookup = 100;
  const int num_points = state.range(0);
  const Polygon2d polygon = GetRandomPolygon(num_points, 10.0, false);
  const LineSegment2d linesegment = GetRandomSegment2d(10.0);
  while (state.KeepRunning()) {
    for (int i = 0; i < kNumLookup; ++i) {
      const bool ret = polygon.Contains(linesegment);
      benchmark::DoNotOptimize(ret);
    }
  }
}
BENCHMARK(BM_Contains)->Arg(500)->Arg(100)->Arg(50)->Arg(10);

void BM_GetAllOverlaps(benchmark::State& state) { // NOLINT(runtime/references)
  const int kNumLookup = 100;
  const int num_points = state.range(0);
  const Polygon2d polygon = GetRandomPolygon(num_points, 10.0, true);
  const LineSegment2d linesegment = GetRandomSegment2d(10.0);
  std::vector<LineSegment2d> overlap_line_segments;
  while (state.KeepRunning()) {
    for (int i = 0; i < kNumLookup; ++i) {
      overlap_line_segments = polygon.GetAllOverlaps(linesegment);
      const bool ret = (overlap_line_segments.size() > 1);
      benchmark::DoNotOptimize(ret);
    }
  }
}
BENCHMARK(BM_GetAllOverlaps)->Arg(500)->Arg(100)->Arg(50)->Arg(10);

void BM_GetPerimeter(benchmark::State& state) { // NOLINT(runtime/references)
  const int kNumLookup = 100;
  const int num_points = state.range(0);
  const Polygon2d polygon = GetRandomPolygon(num_points, 10.0, true);
  while (state.KeepRunning()) {
    for (int i = 0; i < kNumLookup; ++i) {
      const double ret = polygon.GetPerimeter();
      benchmark::DoNotOptimize(ret);
    }
  }
}
BENCHMARK(BM_GetPerimeter)->Arg(500)->Arg(100)->Arg(50)->Arg(10);

void BM_GetTouhedGridsConvex(benchmark::State& state) {  // NOLINT(runtime/references)
  const int kNumLookup = 100;
  const int num_points = state.range(0);
  const Polygon2d polygon = GetRandomPolygon(num_points, 10.0, true);
  while (state.KeepRunning()) {
    for (int i = 0; i < kNumLookup; ++i) {
      const std::vector<Vector2i> ret = polygon.GetTouchedGrids(0.25);
      benchmark::DoNotOptimize(ret);
    }
  }
}
BENCHMARK(BM_GetTouhedGridsConvex)->Arg(500)->Arg(100)->Arg(50)->Arg(10);

void BM_GetTouhedGridsNonConvex(benchmark::State& state) {  // NOLINT(runtime/references)
  const int kNumLookup = 100;
  const int num_points = state.range(0);
  const Polygon2d polygon = GetRandomPolygon(num_points, 10.0, false);
  while (state.KeepRunning()) {
    for (int i = 0; i < kNumLookup; ++i) {
      const std::vector<Vector2i> ret = polygon.GetTouchedGrids(0.25);
      benchmark::DoNotOptimize(ret);
    }
  }
}
BENCHMARK(BM_GetTouhedGridsNonConvex)->Arg(500)->Arg(100)->Arg(50)->Arg(10);

}  // namespace math

BENCHMARK_MAIN();

// clang-format off
/*
Run on (20 X 5200 MHz CPU s)
CPU Caches:
  L1 Data 32K (x10)
  L1 Instruction 32K (x10)
  L2 Unified 256K (x10)
  L3 Unified 20480K (x1)
Load Average: 2.64, 4.25, 2.83
***WARNING*** CPU scaling is enabled, the benchmark real time measurements may be noisy and will incur extra overhead.
------------------------------------------------------------------------------
Benchmark                                    Time             CPU   Iterations
------------------------------------------------------------------------------
BM_IsPointIn/IsPointInConvex/500         19487 ns        19487 ns        35553
BM_IsPointIn/IsPointInConvex/100         18605 ns        18605 ns        37687
BM_IsPointIn/IsPointInConvex/50          16152 ns        16152 ns        43766
BM_IsPointIn/IsPointInConvex/10          11425 ns        11425 ns        63935
BM_IsPointIn/IsPointInNonConvex/500    2382062 ns      2382056 ns          292
BM_IsPointIn/IsPointInNonConvex/100     496827 ns       496824 ns         1376
BM_IsPointIn/IsPointInNonConvex/50      250711 ns       250710 ns         2808
BM_IsPointIn/IsPointInNonConvex/10       53825 ns        53825 ns        12972
BM_ComputeOverlap/5000                22788648 ns     22788394 ns           31
BM_ComputeOverlap/3000                13264103 ns     13263913 ns           53
BM_ComputeOverlap/2000                 8886200 ns      8886116 ns           78
BM_ComputeOverlap/1000                 4593185 ns      4593143 ns          154
BM_DistanceTo/500                      7886458 ns      7886437 ns           89
BM_DistanceTo/100                       918103 ns       918100 ns          757
BM_DistanceTo/50                        415332 ns       415331 ns         1678
BM_DistanceTo/10                         62275 ns        62275 ns        11170
BM_Contains/500                        1373699 ns      1373696 ns          518
BM_Contains/100                         293157 ns       293156 ns         2359
BM_Contains/50                          149226 ns       149225 ns         4651
BM_Contains/10                           39326 ns        39326 ns        19196
BM_GetAllOverlaps/500                    14238 ns        14238 ns        47317
BM_GetAllOverlaps/100                    13756 ns        13756 ns        49433
BM_GetAllOverlaps/50                     12959 ns        12959 ns        52617
BM_GetAllOverlaps/10                     11668 ns        11668 ns        57912
BM_GetPerimeter/500                      75224 ns        75224 ns         8548
BM_GetPerimeter/100                      16007 ns        16007 ns        43503
BM_GetPerimeter/50                        7913 ns         7913 ns        84264
BM_GetPerimeter/10                        1716 ns         1716 ns       420387
BM_GetTouhedGridsConvex/500            3310597 ns      3310571 ns          204
BM_GetTouhedGridsConvex/100            1782225 ns      1782183 ns          376
BM_GetTouhedGridsConvex/50             1596702 ns      1596691 ns          435
BM_GetTouhedGridsConvex/10             1379763 ns      1379752 ns          497
BM_GetTouhedGridsNonConvex/500      1063416243 ns   1063375937 ns            1
BM_GetTouhedGridsNonConvex/100        49813850 ns     49811418 ns           14
BM_GetTouhedGridsNonConvex/50         14576552 ns     14575954 ns           47
BM_GetTouhedGridsNonConvex/10          1374573 ns      1374490 ns          495
*/
