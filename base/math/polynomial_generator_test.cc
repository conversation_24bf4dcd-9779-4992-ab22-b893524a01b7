// Copyright @2022 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "gtest/gtest.h"

#include "base/math/polynomial_generator.h"

namespace math {

TEST(PolynomialGeneratorTest, BasicTest) {
  std::vector<double> coefs{0.0, 1.0, 0.0, 0.0};
  PolynomialGenerator generator(coefs);
  for (int i = 0; i < 10; ++i) {
    EXPECT_NEAR(static_cast<double>(i), generator.Compute(i), 1e-6);
    EXPECT_NEAR(1.0, generator.ComputeDerivative(i, 1), 1e-6);
    EXPECT_NEAR(0.0, generator.ComputeDerivative(i, 2), 1e-6);
  }
  std::vector<double> coefs1{1.0, 2.0, 3.0};
  PolynomialGenerator generator_1(coefs1);
  EXPECT_NEAR(1.0 + 2.0 * std::pow(5.0, 1) + 3.0 * std::pow(5.0, 2), generator_1.Compute(5.0),
              1e-6);
  EXPECT_NEAR(2.0 + 3.0 * 2.0 * std::pow(5.0, 1), generator_1.ComputeDerivative(5.0, 1), 1e-6);
  EXPECT_NEAR(3.0 * 2.0, generator_1.ComputeDerivative(5.0, 2), 1e-6);
  std::vector<double> coefs2{1.0, 2.0, 3.0, 4.0};
  PolynomialGenerator generator_2(coefs2);
  EXPECT_NEAR(1.0 + 2.0 * std::pow(5.0, 1) + 3.0 * std::pow(5.0, 2) + 4.0 * std::pow(5.0, 3),
              generator_2.Compute(5.0), 1e-6);
  EXPECT_NEAR(2.0 + 3.0 * 2.0 * std::pow(5.0, 1) + 4.0 * 3.0 * std::pow(5.0, 2),
              generator_2.ComputeDerivative(5.0, 1), 1e-6);
  EXPECT_NEAR(3.0 * 2.0 + 4.0 * 3.0 * 2.0 * std::pow(5.0, 1), generator_2.ComputeDerivative(5.0, 2),
              1e-6);
}

}  // namespace math
