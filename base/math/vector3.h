// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>
//          <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)

#pragma once

#include <cmath>
#include <ostream>
#include <string>

#include "Eigen/Core"
#include "glm/glm.hpp"
#include "glog/logging.h"

#include "base/math/constants.h"
#include "base/math/math_util.h"
#include "base/math/vector2.h"
#include "base/strings/format.h"

namespace math {

// Presentation of points in 3D space.
template <typename T>
class Vector3 final {
 public:
  static_assert(std::is_floating_point<T>::value,
                "Template Vector3 only supports floating-point types.");

  Vector3() : x(0), y(0), z(0) {}
  Vector3(T x, T y, T z) : x(x), y(y), z(z) {}
  Vector3(const Vector2<T>& xy, T z) : x(xy.x), y(xy.y), z(z) {}

  template <typename V>
  explicit Vector3(const Vector3<V>& v) : x(v.x), y(v.y), z(v.z) {}

  template <typename PointType>
  explicit Vector3(const PointType& v) : x(v.x()), y(v.y()), z(v.z()) {}

  T operator[](int index) const;
  T& operator[](int index);

  Vector2<T> xy() const { return Vector2<T>(x, y); }

  T Length() const;
  T LengthSquare() const;

  Vector3<T> CrossProd(const Vector3<T>& other) const;
  T InnerProd(const Vector3<T>& other) const;

  T DistanceTo(const Vector3<T>& other) const;
  T DistanceTo(T other_x, T other_y, T other_z) const;
  T DistanceSquareTo(const Vector3<T>& other) const;
  T DistanceSquareTo(T other_x, T other_y, T other_z) const;

  bool IsNear(const Vector3<T>& other, double epsilon = math::kEpsilon) const;
  bool IsNear(T other_x, T other_y, T other_z, double epsilon = math::kEpsilon) const;

  Vector3<T> Normalized() const;
  void Normalize();
  bool TryNormalize();

  Vector3<T> operator+(const Vector3<T>& other) const;
  Vector3<T> operator-(const Vector3<T>& other) const;
  Vector3<T>& operator+=(const Vector3<T>& other);
  Vector3<T>& operator-=(const Vector3<T>& other);
  template <typename R>
  Vector3<T>& operator*=(R r);
  template <typename R>
  Vector3<T>& operator/=(R r);

  Eigen::Matrix<T, 3, 1> ToEigen() const;

  std::string ToString() const;

  union {
    struct {
      T x;
      T y;
      T z;
    };
    T elements[3];
  };

  // Shallow copy and move are OK.
};

template <typename T>
T Vector3<T>::operator[](int index) const {
  DCHECK(index >= 0 && index < 3);
  return elements[index];
}

template <typename T>
T& Vector3<T>::operator[](int index) {
  DCHECK(index >= 0 && index < 3);
  return elements[index];
}

template <typename T>
T Vector3<T>::Length() const {
  return std::sqrt(LengthSquare());
}

template <typename T>
T Vector3<T>::LengthSquare() const {
  return math::square(x) + math::square(y) + math::square(z);
}

template <typename T>
Vector3<T> Vector3<T>::CrossProd(const Vector3<T>& other) const {
  T result_x = y * other.z - z * other.y;
  T result_y = z * other.x - x * other.z;
  T result_z = x * other.y - y * other.x;
  return Vector3<T>(result_x, result_y, result_z);
}

template <typename T>
T Vector3<T>::InnerProd(const Vector3<T>& other) const {
  return x * other.x + y * other.y + z * other.z;
}

template <typename T>
T Vector3<T>::DistanceTo(const Vector3<T>& other) const {
  return std::sqrt(DistanceSquareTo(other.x, other.y, other.z));
}

template <typename T>
T Vector3<T>::DistanceTo(T other_x, T other_y, T other_z) const {
  return std::sqrt(DistanceSquareTo(other_x, other_y, other_z));
}

template <typename T>
T Vector3<T>::DistanceSquareTo(const Vector3<T>& other) const {
  return math::square(x - other.x) + math::square(y - other.y) + math::square(z - other.z);
}

template <typename T>
T Vector3<T>::DistanceSquareTo(T other_x, T other_y, T other_z) const {
  return math::square(x - other_x) + math::square(y - other_y) + math::square(z - other_z);
}

template <typename T>
bool Vector3<T>::IsNear(const Vector3<T>& other, double epsilon) const {
  return DistanceSquareTo(other.x, other.y, other.z) <= math::square(epsilon);
}

template <typename T>
bool Vector3<T>::IsNear(T other_x, T other_y, T other_z, double epsilon) const {
  return DistanceSquareTo(other_x, other_y, other_z) <= math::square(epsilon);
}

template <typename T>
Vector3<T> Vector3<T>::Normalized() const {
  const T l = Length();
  CHECK_NE(l, 0);
  return Vector3<T>(x / l, y / l, z / l);
}

template <typename T>
void Vector3<T>::Normalize() {
  CHECK(TryNormalize());
}

template <typename T>
bool Vector3<T>::TryNormalize() {
  const T l = Length();
  if (l == 0) {
    return false;
  }
  x /= l;
  y /= l;
  z /= l;
  return true;
}

template <typename T>
Vector3<T> Vector3<T>::operator+(const Vector3<T>& other) const {
  return Vector3<T>(x + other.x, y + other.y, z + other.z);
}

template <typename T>
Vector3<T> Vector3<T>::operator-(const Vector3<T>& other) const {
  return Vector3<T>(x - other.x, y - other.y, z - other.z);
}

template <typename T>
Vector3<T>& Vector3<T>::operator+=(const Vector3<T>& other) {
  x += other.x;
  y += other.y;
  z += other.z;
  return *this;
}

template <typename T>
Vector3<T>& Vector3<T>::operator-=(const Vector3<T>& other) {
  x -= other.x;
  y -= other.y;
  z -= other.z;
  return *this;
}

template <typename T>
template <typename R>
Vector3<T>& Vector3<T>::operator*=(R r) {
  x *= r;
  y *= r;
  z *= r;
  return *this;
}

template <typename T>
template <typename R>
Vector3<T>& Vector3<T>::operator/=(R r) {
  CHECK_NE(r, 0);
  x /= r;
  y /= r;
  z /= r;
  return *this;
}

template <typename T>
Eigen::Matrix<T, 3, 1> Vector3<T>::ToEigen() const {
  return {x, y, z};
}

template <typename T>
std::string Vector3<T>::ToString() const {
  return strings::Format("({:8f}, {:8f}, {:8f})", x, y, z);
}

using Vector3d = Vector3<double>;
using Vector3f = Vector3<float>;
using Vector3i = glm::i32vec3;
using Vector3l = glm::i64vec3;

static_assert(sizeof(Vector3d) == 3 * sizeof(double));
static_assert(sizeof(Vector3f) == 3 * sizeof(float));

// =================================== Helper Functions ===================================

template <typename T>
inline bool operator==(const Vector3<T>& p0, const Vector3<T>& p1) {
  return p0.IsNear(p1);
}

template <typename T>
inline bool operator!=(const Vector3<T>& p0, const Vector3<T>& p1) {
  return !(p0 == p1);
}

template <typename T, typename R>
inline Vector3<T> operator*(const Vector3<T>& p, R r) {
  return Vector3<T>(p.x * r, p.y * r, p.z * r);
}

template <typename T, typename R>
inline Vector3<T> operator*(R r, const Vector3<T>& p) {
  return Vector3<T>(p.x * r, p.y * r, p.z * r);
}

template <typename T, typename R>
inline Vector3<T> operator/(const Vector3<T>& p, R r) {
  CHECK_NE(r, 0);
  return Vector3<T>(p.x / r, p.y / r, p.z / r);
}

template <typename T>
inline Vector3<T> operator-(const Vector3<T>& p) {
  return Vector3<T>(-p.x, -p.y, -p.z);
}

template <typename T>
inline std::ostream& operator<<(std::ostream& os, const Vector3<T>& p) {
  return os << p.ToString();
}

template <typename T, typename R>
inline Vector3<T> Lerp(const Vector3<T>& p0, const Vector3<T>& p1, R r) {
  return Vector3<T>(math::Lerp<T>(p0.x, p1.x, r), math::Lerp<T>(p0.y, p1.y, r),
                    math::Lerp<T>(p0.z, p1.z, r));
}

}  // namespace math
