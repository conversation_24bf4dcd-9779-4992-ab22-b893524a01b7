// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <functional>
#include <limits>
#include <vector>

#include "gtest/gtest.h"

#include "base/common/optional.h"
#include "base/math/math_util.h"
#include "base/math/proto/math.pb.h"

namespace math {

enum class MonotonicityType {
  kDefault,
  kNonDecreasing,
  kNonIncreasing,
};

// Represents a function from x to y with cubic interpolation. y(x), y'(x), and y''(x) are
// continuous in range [min_x, max_x].
class PiecewiseCubicFunction final {
 public:
  MonotonicityType mode() const { return mode_; }

  double min_x() const { return x0_; }
  double max_x() const { return x0_ + x_range_; }
  double min_y() const { return min_y_; }
  double max_y() const { return max_y_; }

  int n_samples() const { return n_samples_; }
  double x_step() const { return x_step_; }
  double x_range() const { return x_range_; }

  const std::vector<double>& y_samples() const { return y_; }

  bool IsXInside(double x, double epsilon = math::kEpsilon) const {
    return math::IsInside(x, min_x(), max_x(), epsilon);
  }
  bool IsYInside(double y, double epsilon = math::kEpsilon) const {
    return math::IsInside(y, min_y(), max_y(), epsilon);
  }

  // Shift the x axis, s.t.
  //  - new_function.min_x() = x
  //  - new_function.y(x) = old_function.y(old_function.min_x() + x - new_function.min_x())
  // Complexity is O(1).
  void ShiftX(double x);

  // Shift the x axis, s.t.
  //  - new_function.y(x) = y
  // Complexity is O(1).
  void ShiftX(double x, double y);

  // Shift the y axis, s.t.
  //  - new_function.min_y() = y
  // Complexity is O(N).
  void ShiftY(double y);

  // Shift the y axis, s.t.
  //  - new_function.y(x) = y
  // Complexity is O(N).
  void ShiftY(double x, double y);

  // Flip the y axis, s.t.
  //  - new_function.y(x) = -old_function.y(x)
  // Complexity is O(N).
  void FlipY();

  // Find x by y. Returns true if *x is inside range [min_x,max_x].
  // Requires that the function's mode is kNonDecreasing or kNonIncreasing.
  // If y is out of value range, x will be extrapolated.
  // Complexity is O(1), by Newton's method.
  bool FindXByY(double y, double* x = nullptr, double* delta_y = nullptr) const;

  double Interpolate(double x, bool extrapolate = false) const;
  double Derivative(double x, bool extrapolate = false) const;
  double SecondDerivative(double x, bool extrapolate = false) const;
  double ThirdDerivative(double x, bool extrapolate = false) const;

  // Compute the min/max estimated Derivative/SecondDerivative among all sampling points.
  double GetMinDerivative() const;
  double GetMinSecondDerivative() const;
  double GetMaxDerivative() const;
  double GetMaxSecondDerivative() const;

  // x_step_hint is the hint of the sample resolution.
  PiecewiseCubicFunction Resample(double x_step_hint) const;

  PiecewiseCubicFunction MakeSubFunction(double min_x, double max_x_hint) const;

  math_proto::PiecewiseCubicFunction ToProto() const;

  static PiecewiseCubicFunction FromProto(const math_proto::PiecewiseCubicFunction& proto);

  static PiecewiseCubicFunction Create(MonotonicityType mode,
                                       double x0,
                                       double x_step,
                                       std::vector<double> y_samples,
                                       base::Optional<double> head_t = base::none,
                                       base::Optional<double> tail_t = base::none);

  static PiecewiseCubicFunction Create(MonotonicityType mode,
                                       double x0,
                                       double x_step,
                                       int n_samples,
                                       const std::function<double(double)>& generator,
                                       base::Optional<double> head_t = base::none,
                                       base::Optional<double> tail_t = base::none);

  static PiecewiseCubicFunction Create(MonotonicityType mode,
                                       const std::vector<double>& x_samples,
                                       const std::vector<double>& y_samples,
                                       double x_step_hint,
                                       base::Optional<double> head_t = base::none,
                                       base::Optional<double> tail_t = base::none);

 private:
  PiecewiseCubicFunction(MonotonicityType mode,
                         double x0,
                         double x_step,
                         std::vector<double> y_samples,
                         double head_t,
                         double tail_t);

  int LowerBoundByY(double y) const;

  MonotonicityType mode_ = MonotonicityType::kDefault;

  double x0_ = 0.0;
  double x_step_ = 0.0;
  double x_step_inverse_ = 0.0;
  double x_range_ = 0.0;
  int n_samples_ = 0;

  double min_y_ = 0.0;
  double max_y_ = 0.0;

  double head_t_ = 0.0;
  double tail_t_ = 0.0;

  std::vector<double> y_;
  std::vector<double> m_;

  FRIEND_TEST(PiecewiseCubicFunctionTest, Basic);
  FRIEND_TEST(PiecewiseCubicFunctionTest, LowerBoundByY);
  FRIEND_TEST(PiecewiseCubicFunctionTest, FromAndToProto);

  // Shallow copy and move are OK.
};

}  // namespace math
