// Copyright @2024 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include "base/math/vector2.h"

namespace math {
namespace internal {

union PackedIndexData {
  math::Vector2i index;
  int64_t packed_index;
};

static_assert(sizeof(internal::PackedIndexData) == sizeof(int64_t));

}  // namespace internal

inline int64_t PackedIndex(const math::Vector2i& index) {
  internal::PackedIndexData p;
  p.index = index;
  return p.packed_index;
}

inline int64_t PackedIndex(int x, int y) { return PackedIndex(math::Vector2i(x, y)); }

inline math::Vector2i UnpackIndex(int64_t packed_index) {
  internal::PackedIndexData p;
  p.packed_index = packed_index;
  return p.index;
}

}  // namespace math
