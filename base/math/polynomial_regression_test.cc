// Copyright @2022 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "gtest/gtest.h"

#include "base/math/polynomial_generator.h"
#include "base/math/polynomial_regression.h"
#include "base/math/vector2.h"
#include "base/math/vector3.h"

namespace math {

TEST(PolynomialRegressionTest, StraightLineFittingTest) {
  std::vector<math::Vector2d> points;
  points.push_back({1.0, 1.0});
  points.push_back({2.0, 2.0});
  points.push_back({3.0, 3.0});
  points.push_back({4.0, 4.0});
  points.push_back({5.0, 5.0});
  points.push_back({6.0, 6.0});

  PolynomialRegression polynomial_regression(3);
  const std::vector<double> coefs = polynomial_regression.Fit(points);

  PolynomialGenerator generator(coefs);
  for (int i = 0; i < 10; ++i) {
    EXPECT_NEAR(static_cast<double>(i), generator.Compute(i), 1e-6);
  }
}

TEST(PolynomialRegressionTest, RandomTest) {
  std::vector<math::Vector2d> points;
  points.push_back({0.0, 1.0});
  points.push_back({1.0, 1.8});
  points.push_back({2.0, 1.3});
  points.push_back({3.0, 2.5});
  points.push_back({4.0, 6.3});

  PolynomialRegression polynomial_regression(2);
  const std::vector<double> coefs = polynomial_regression.Fit(points);
  PolynomialGenerator generator(coefs);
  EXPECT_NEAR(1.42, generator.Compute(0.0), 1e-6);
  EXPECT_NEAR(3.16, generator.Compute(3.0), 1e-6);
}

TEST(PolynomialRegressionTest, FormularTest) {
  std::vector<math::Vector2d> points;
  for (int i = 0; i < 20; ++i) {
    points.push_back({static_cast<double>(i), 2.0 + 0.9 * i + 0.8 * i * i});
  }

  PolynomialRegression polynomial_regression(2);
  const std::vector<double> coefs = polynomial_regression.Fit(points);
  EXPECT_NEAR(2.0, coefs[0], 1e-6);
  EXPECT_NEAR(0.9, coefs[1], 1e-6);
  EXPECT_NEAR(0.8, coefs[2], 1e-6);

  PolynomialRegression polynomial_regression1(4);
  const std::vector<double> coefs1 = polynomial_regression1.Fit(points);
  EXPECT_NEAR(2.0, coefs1[0], 1e-6);
  EXPECT_NEAR(0.9, coefs1[1], 1e-6);
  EXPECT_NEAR(0.8, coefs1[2], 1e-6);
  EXPECT_NEAR(0.0, coefs1[3], 1e-6);
  EXPECT_NEAR(0.0, coefs1[4], 1e-6);
}

TEST(PolynomialRegressionTest, FitWithFixedPoints) {
  std::vector<math::Vector2d> points;
  points.push_back({0.0, 0.0});
  points.push_back({1.0, 1.0});
  points.push_back({2.0, 1.8});
  points.push_back({3.0, 1.3});
  points.push_back({4.0, 2.5});
  points.push_back({5.0, 6.3});
  points.push_back({6.0, 7.0});

  std::vector<math::Vector2d> fixed_points;
  fixed_points.push_back({0.0, 0.0});
  fixed_points.push_back({6.0, 7.0});
  PolynomialRegression polynomial_regression(2);
  const std::vector<double> coefs = polynomial_regression.FitWithFixedPoints(points, fixed_points);
  PolynomialGenerator generator(coefs);
  EXPECT_NEAR(0.0, generator.Compute(0.0), 1e-6);
  EXPECT_NEAR(0.159684, generator.Compute(0.5), 1e-6);
  EXPECT_NEAR(1.568693, generator.Compute(2.5), 1e-6);
  EXPECT_NEAR(2.73536, generator.Compute(3.5), 1e-6);
  EXPECT_NEAR(7.0, generator.Compute(6.0), 1e-6);
}

TEST(PolynomialRegressionTest, StraightLine3dFittingTest) {
  std::vector<math::Vector3d> points;
  points.push_back({1.0, 1.0, 0.0});
  points.push_back({2.0, 2.0, 0.0});
  points.push_back({3.0, 3.0, 0.0});
  points.push_back({4.0, 4.0, 0.0});
  points.push_back({5.0, 5.0, 0.0});
  points.push_back({6.0, 6.0, 0.0});

  PolynomialRegression polynomial_regression(3);
  const std::vector<double> coefs = polynomial_regression.Fit(points);

  PolynomialGenerator generator(coefs);
  for (int i = 0; i < 10; ++i) {
    EXPECT_NEAR(static_cast<double>(i), generator.Compute(i), 1e-6);
  }
}

}  // namespace math
