// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>
//          <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)

#pragma once

#include <cmath>
#include <ostream>
#include <string>
#include <vector>

#include "Eigen/Core"
#include "glm/glm.hpp"
#include "glog/logging.h"

#include "base/math/constants.h"
#include "base/math/fast_math.h"
#include "base/math/math_util.h"
#include "base/strings/format.h"
#include "base/strings/macros.h"

namespace math {

// Presentation of points in 2D space.
template <typename T>
class Vector2 final {
 public:
  static_assert(std::is_floating_point<T>::value,
                "Template Vector2 only supports floating-point types.");

  constexpr Vector2() = default;
  constexpr Vector2(T x, T y) : x(x), y(y) {}

  template <typename V>
  explicit Vector2(const Vector2<V>& v) : x(v.x), y(v.y) {}

  template <typename PointType>
  explicit Vector2(const PointType& v) : x(v.x()), y(v.y()) {}

  static Vector2<T> FromAngle(T angle);
  static Vector2<T> FromAngleAndLength(T angle, T length);

  T Length() const;
  T LengthSquare() const;
  T Angle() const;

  T CrossProd(const Vector2<T>& other) const;
  T InnerProd(const Vector2<T>& other) const;

  T DistanceTo(const Vector2<T>& other) const;
  T DistanceTo(T other_x, T other_y) const;
  T DistanceSquareTo(const Vector2<T>& other) const;
  T DistanceSquareTo(T other_x, T other_y) const;

  bool IsNear(const Vector2<T>& other, double epsilon = math::kEpsilon) const;
  bool IsNear(T other_x, T other_y, double epsilon = math::kEpsilon) const;

  Vector2<T> Rotate(T angle) const;
  Vector2<T> Rotate(T cos_angle, T sin_angle) const;
  Vector2<T> Rotate90() const;
  Vector2<T> Rotate180() const;
  Vector2<T> Rotate270() const;

  Vector2<T> Normalized() const;
  bool Normalize();

  // TODO(wangnaizheng): deprecate this API.
  bool TryNormalize() { return Normalize(); }

  Vector2<T> operator+(const Vector2<T>& other) const;
  Vector2<T> operator-(const Vector2<T>& other) const;
  Vector2<T>& operator+=(const Vector2<T>& other);
  Vector2<T>& operator-=(const Vector2<T>& other);
  template <typename R>
  Vector2<T>& operator*=(R r);
  template <typename R>
  Vector2<T>& operator/=(R r);

  Eigen::Matrix<T, 2, 1> ToEigen() const;

  std::string ToString() const;

  T x = 0;
  T y = 0;

  // Shallow copy and move are OK.
};

template <typename T>
Vector2<T> Vector2<T>::FromAngle(T angle) {
  const auto sin_and_cos = math::SinCos(angle);
  const double sin_angle = sin_and_cos.first;
  const double cos_angle = sin_and_cos.second;
  return Vector2<T>(cos_angle, sin_angle);
}

template <typename T>
Vector2<T> Vector2<T>::FromAngleAndLength(T angle, T length) {
  const auto sin_and_cos = math::SinCos(angle);
  const double sin_angle = sin_and_cos.first;
  const double cos_angle = sin_and_cos.second;
  return Vector2<T>(cos_angle * length, sin_angle * length);
}

template <typename T>
T Vector2<T>::Length() const {
  return std::sqrt(LengthSquare());
}

template <typename T>
T Vector2<T>::LengthSquare() const {
  return math::square(x) + math::square(y);
}

template <typename T>
T Vector2<T>::Angle() const {
  return math::Atan2(y, x);
}

template <typename T>
T Vector2<T>::CrossProd(const Vector2<T>& other) const {
  return x * other.y - y * other.x;
}

template <typename T>
T Vector2<T>::InnerProd(const Vector2<T>& other) const {
  return x * other.x + y * other.y;
}

template <typename T>
T Vector2<T>::DistanceTo(const Vector2<T>& other) const {
  return std::sqrt(DistanceSquareTo(other.x, other.y));
}

template <typename T>
T Vector2<T>::DistanceTo(T other_x, T other_y) const {
  return std::sqrt(DistanceSquareTo(other_x, other_y));
}

template <typename T>
T Vector2<T>::DistanceSquareTo(const Vector2<T>& other) const {
  return math::square(x - other.x) + math::square(y - other.y);
}

template <typename T>
T Vector2<T>::DistanceSquareTo(T other_x, T other_y) const {
  return math::square(x - other_x) + math::square(y - other_y);
}

template <typename T>
bool Vector2<T>::IsNear(const Vector2<T>& other, double epsilon) const {
  return DistanceSquareTo(other.x, other.y) <= math::square(epsilon);
}

template <typename T>
bool Vector2<T>::IsNear(T other_x, T other_y, double epsilon) const {
  return DistanceSquareTo(other_x, other_y) <= math::square(epsilon);
}

template <typename T>
Vector2<T> Vector2<T>::Rotate(T angle) const {
  const auto sin_and_cos = math::SinCos(angle);
  const double sin_angle = sin_and_cos.first;
  const double cos_angle = sin_and_cos.second;
  return Rotate(cos_angle, sin_angle);
}

template <typename T>
Vector2<T> Vector2<T>::Rotate(T cos_angle, T sin_angle) const {
  DCHECK_LE(std::abs(math::square(cos_angle) + math::square(sin_angle) - 1.0), math::kEpsilon)
      << DUMP_TO_STREAM(cos_angle, sin_angle);
  const T result_x = cos_angle * x - sin_angle * y;
  const T result_y = sin_angle * x + cos_angle * y;
  return Vector2<T>(result_x, result_y);
}

template <typename T>
Vector2<T> Vector2<T>::Rotate90() const {
  return Vector2<T>(-y, +x);
}

template <typename T>
Vector2<T> Vector2<T>::Rotate180() const {
  return Vector2<T>(-x, -y);
}

template <typename T>
Vector2<T> Vector2<T>::Rotate270() const {
  return Vector2<T>(+y, -x);
}

template <typename T>
Vector2<T> Vector2<T>::Normalized() const {
  const T l = Length();
  if (l == static_cast<T>(0)) {
    return *this;
  }
  return Vector2<T>(x / l, y / l);
}

template <typename T>
bool Vector2<T>::Normalize() {
  const T l = Length();
  if (l == static_cast<T>(0)) {
    return false;
  }
  x /= l;
  y /= l;
  return true;
}

template <typename T>
Vector2<T> Vector2<T>::operator+(const Vector2<T>& other) const {
  return Vector2<T>(x + other.x, y + other.y);
}

template <typename T>
Vector2<T> Vector2<T>::operator-(const Vector2<T>& other) const {
  return Vector2<T>(x - other.x, y - other.y);
}

template <typename T>
Vector2<T>& Vector2<T>::operator+=(const Vector2<T>& other) {
  x += other.x;
  y += other.y;
  return *this;
}

template <typename T>
Vector2<T>& Vector2<T>::operator-=(const Vector2<T>& other) {
  x -= other.x;
  y -= other.y;
  return *this;
}

template <typename T>
template <typename R>
Vector2<T>& Vector2<T>::operator*=(R r) {
  x *= r;
  y *= r;
  return *this;
}

template <typename T>
template <typename R>
Vector2<T>& Vector2<T>::operator/=(R r) {
  DCHECK_NE(r, 0);
  x /= r;
  y /= r;
  return *this;
}

template <typename T>
Eigen::Matrix<T, 2, 1> Vector2<T>::ToEigen() const {
  return {x, y};
}

template <typename T>
std::string Vector2<T>::ToString() const {
  return strings::Format("({:8f}, {:8f})", x, y);
}

using Vector2d = Vector2<double>;
using Vector2f = Vector2<float>;
using Vector2i = glm::i32vec2;
using Vector2l = glm::i64vec2;

// =================================== Helper Functions ===================================

template <typename T>
bool operator==(const Vector2<T>& p0, const Vector2<T>& p1) {
  return p0.IsNear(p1);
}

template <typename T>
bool operator!=(const Vector2<T>& p0, const Vector2<T>& p1) {
  return !(p0 == p1);
}

template <typename T, typename R>
Vector2<T> operator*(const Vector2<T>& p, R r) {
  return Vector2<T>(p.x * r, p.y * r);
}

template <typename T, typename R>
Vector2<T> operator*(R r, const Vector2<T>& p) {
  return Vector2<T>(p.x * r, p.y * r);
}

template <typename T, typename R>
Vector2<T> operator/(const Vector2<T>& p, R r) {
  DCHECK_NE(r, 0);
  return Vector2<T>(p.x / r, p.y / r);
}

template <typename T>
Vector2<T> operator-(const Vector2<T>& p) {
  return Vector2<T>(-p.x, -p.y);
}

template <typename T>
const Vector2<T>& operator+(const Vector2<T>& p) {
  return p;
}

template <typename T>
std::ostream& operator<<(std::ostream& os, const Vector2<T>& p) {
  return os << p.ToString();
}

template <typename T>
T CrossProd(const Vector2<T>& p0, const Vector2<T>& p1, const Vector2<T>& p2) {
  return (p1 - p0).CrossProd(p2 - p0);
}

template <typename T>
T InnerProd(const Vector2<T>& p0, const Vector2<T>& p1, const Vector2<T>& p2) {
  return (p1 - p0).InnerProd(p2 - p0);
}

template <typename T>
T CrossProd(T x0, T y0, T x1, T y1) {
  return Vector2<T>(x0, y0).CrossProd(Vector2<T>(x1, y1));
}

template <typename T>
T InnerProd(T x0, T y0, T x1, T y1) {
  return Vector2<T>(x0, y0).InnerProd(Vector2<T>(x1, y1));
}

template <typename T, typename R>
Vector2<T> Lerp(const Vector2<T>& p0, const Vector2<T>& p1, R r) {
  return Vector2<T>(math::Lerp<T>(p0.x, p1.x, r), math::Lerp<T>(p0.y, p1.y, r));
}

// TODO(wangnaizheng): deprecate this API, use polygon2d_utils instead.
template <typename T>
void Rotate(const Vector2<T>& center, double angle, std::vector<Vector2<T>>* points) {
  if (!CHECK_NOTNULL(points)->empty()) {
    const auto sin_and_cos = math::SinCos(angle);
    const double sin_angle = sin_and_cos.first;
    const double cos_angle = sin_and_cos.second;
    for (Vector2<T>& p : *points) {
      p = (p - center).Rotate(cos_angle, sin_angle) + center;
    }
  }
}

}  // namespace math
