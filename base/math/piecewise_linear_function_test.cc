// Copyright @2020 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/math/piecewise_linear_function.h"

#include <deque>
#include <memory>

#include "gtest/gtest.h"

namespace math {

namespace {

struct Tuple {
  double data[3];
};

struct TupleLerpOperator {
  Tuple operator()(const Tuple& x, const Tuple& y, double r) const {
    Tuple t;
    for (int i = 0; i < 3; ++i) {
      t.data[i] = math::Lerp(x.data[i], y.data[i], r);
    }
    return t;
  }
};

Tuple LinearTuple(double x) {
  Tuple t;
  t.data[0] = 1.7 * x - 2.3;
  t.data[1] = 3.7 * x + 3.9;
  t.data[2] = 7.1 * x - 2.5;
  return t;
}

double Linear(double x) { return 3.0 * x - 5.0; }

template <typename T>
std::function<T(double)> CastLinear(T y) {
  return [y](double) { return y; };
}

template <typename T, typename LerpFunc = LerpOperator<T, double>>
std::function<T(double)> CastLinear(const PiecewiseLinearFunction<T, LerpFunc>& linear_function,
                                    bool extrapolate) {
  return [&linear_function, extrapolate](double x) { return linear_function(x, extrapolate); };
}

template <typename T, typename LerpFunc = LerpOperator<T, double>>
std::function<T(double)> CastLinear(const PiecewiseLinearFunctionView<T, LerpFunc>& linear_function,
                                    bool extrapolate) {
  return [&linear_function, extrapolate](double x) { return linear_function(x, extrapolate); };
}

void AssertNear(double x, double expected, double actual) {
  ASSERT_NEAR(expected, actual, 1e-6) << "x=" << x;
}

void AssertNear(double x, const Tuple& expected, const Tuple& actual) {
  double delta = 0.0;
  for (int i = 0; i < 3; ++i) {
    double d = expected.data[i] - actual.data[i];
    delta += d * d;
  }
  ASSERT_NEAR(0.0, std::sqrt(delta), 1e-6) << "x=" << x;
}

template <typename T>
void AssertNear(const std::function<T(double)>& expected, const std::function<T(double)>& actual,
                double min_x, double max_x) {
  const int n_samples = 37;
  for (int i = 0; i <= n_samples; ++i) {
    double x = math::Lerp(min_x, max_x, static_cast<double>(i) / n_samples);
    AssertNear(x, expected(x), actual(x));
  }
}

template <typename T>
void AssertNear(const std::function<T(double)>& expected,
                const std::function<T(double)>& actual,
                const std::vector<double>& x_samples) {
  for (double x : x_samples) {
    AssertNear(x, expected(x), actual(x));
  }
}

void AssertEqual(const LerpRatio& expected, const LerpRatio& lerp_ratio) {
  ASSERT_EQ(expected.i, lerp_ratio.i);
  ASSERT_DOUBLE_EQ(expected.r, lerp_ratio.r);
}

template <typename T, typename LerpFunc>
bool IsEqual(const PiecewiseLinearFunction<T, LerpFunc>& this_function,
             const PiecewiseLinearFunction<T, LerpFunc>& other_function,
             double min_x,
             double max_x,
             double x_resolution) {
  for (double x = min_x; x < max_x; x += x_resolution) {
    if (!math::IsNear(this_function(x), other_function(x), math::kEpsilon)) {
      return false;
    }
  }
  return true;
}

}  // namespace

TEST(PiecewiseLinearFunctionTest, GetLowerIndex) {
  {
    const auto run_test = [](const std::vector<double>& x_samples, int hint, double x) {
      ASSERT_GE(x_samples.size(), 1);
      const int i0 = GetLowerIndex(x_samples, x, hint);
      if (x < x_samples.front()) {
        ASSERT_EQ(i0, -1);
      } else if (x >= x_samples.back()) {
        ASSERT_EQ(i0, x_samples.size() - 1);
      } else {
        ASSERT_GE(i0, 0);
        ASSERT_LT(i0, x_samples.size() - 1);
        ASSERT_GE(x, x_samples[i0]);
        ASSERT_LT(x, x_samples[i0 + 1]);
      }
    };
    for (int n_samples : {2, 3, 5, 11}) {
      std::vector<double> x_samples;
      std::generate_n(std::back_inserter(x_samples), n_samples, [&x_samples]() {
        return 1.0 + x_samples.size();
      });
      for (double x : x_samples) {
        for (int hint = -1; hint <= n_samples; ++hint) {
          for (double offset : {-0.2, -math::kEpsilon, 0.0, math::kEpsilon, 0.2}) {
            if (x_samples.front() < x && x < x_samples.back()) {
              run_test(x_samples, hint, x + offset);
            }
          }
        }
      }
    }
  }
  {
    constexpr std::array<int, 5> kHintArray = {-2, -1, 0, 1, 2};
    const std::vector<double> empty_container;
    for (int i : kHintArray) {
      ASSERT_EQ(GetLowerIndex(empty_container, 0.0, i), -1);
    }
    const std::vector<double> single_element_container = {1.0};
    for (int i : kHintArray) {
      ASSERT_EQ(GetLowerIndex(single_element_container, 0.0, i), -1);
      ASSERT_EQ(GetLowerIndex(single_element_container, 0.999, i), -1);
      ASSERT_EQ(GetLowerIndex(single_element_container, 1.0, i), 0);
      ASSERT_EQ(GetLowerIndex(single_element_container, 2.0, i), 0);
    }
    const std::vector<double> two_element_container = {0.0, 1.0};
    for (int i : kHintArray) {
      ASSERT_EQ(GetLowerIndex(two_element_container, -0.1, i), -1);
      ASSERT_EQ(GetLowerIndex(two_element_container, 0.0, i), 0);
      ASSERT_EQ(GetLowerIndex(two_element_container, 0.5, i), 0);
      ASSERT_EQ(GetLowerIndex(two_element_container, 1.0, i), 1);
      ASSERT_EQ(GetLowerIndex(two_element_container, 2.0, i), 1);
    }
  }
}

TEST(PiecewiseLinearFunctionTest, GetLerpRatio) {
  {
    ASSERT_NO_FATAL_FAILURE(
        AssertEqual(LerpRatio(0, 0.0), GetLerpRatio(std::array<double, 1>{0.0}, 1.0)));
    ASSERT_NO_FATAL_FAILURE(
        AssertEqual(LerpRatio(0, 0.0), GetLerpRatio(std::vector<double>{0.0}, 1.0)));
    ASSERT_NO_FATAL_FAILURE(
        AssertEqual(LerpRatio(0, 0.0), GetLerpRatio(std::deque<double>{0.0}, 1.0)));
  }
  {
    const std::vector<double> x_samples = {1.0};
    ASSERT_NO_FATAL_FAILURE(AssertEqual(LerpRatio(0, +0.0), GetLerpRatio(x_samples, 0.0)));
    ASSERT_NO_FATAL_FAILURE(AssertEqual(LerpRatio(0, +0.0), GetLerpRatio(x_samples, 0.0, true)));
    ASSERT_NO_FATAL_FAILURE(AssertEqual(LerpRatio(0, +0.0), GetLerpRatio(x_samples, 1.0)));
    ASSERT_NO_FATAL_FAILURE(AssertEqual(LerpRatio(0, +0.0), GetLerpRatio(x_samples, 1.0, true)));
    ASSERT_NO_FATAL_FAILURE(AssertEqual(LerpRatio(0, +0.0), GetLerpRatio(x_samples, 2.0)));
    int last_index_hint = 3;
    ASSERT_NO_FATAL_FAILURE(
        AssertEqual(LerpRatio(0, +0.0), GetLerpRatio(x_samples, 2.0, &last_index_hint, true)));
  }
  {
    const std::vector<double> x_samples = {1.0, 2.0, 4.0};
    ASSERT_NO_FATAL_FAILURE(AssertEqual(LerpRatio(0, +0.0), GetLerpRatio(x_samples, 1.0)));
    ASSERT_NO_FATAL_FAILURE(AssertEqual(LerpRatio(0, +0.0), GetLerpRatio(x_samples, 1.0, true)));
    ASSERT_NO_FATAL_FAILURE(AssertEqual(LerpRatio(0, +0.3), GetLerpRatio(x_samples, 1.3)));
    ASSERT_NO_FATAL_FAILURE(AssertEqual(LerpRatio(0, +0.0), GetLerpRatio(x_samples, 0.5)));
    ASSERT_NO_FATAL_FAILURE(AssertEqual(LerpRatio(0, -0.5), GetLerpRatio(x_samples, 0.5, true)));
    ASSERT_NO_FATAL_FAILURE(AssertEqual(LerpRatio(0, -1.0), GetLerpRatio(x_samples, 0.0, true)));
    for (int hint = -1; hint <= 5; ++hint) {
      int last_index_hint = hint;
      ASSERT_NO_FATAL_FAILURE(
          AssertEqual(LerpRatio(1, +0.5), GetLerpRatio(x_samples, 3.0, &last_index_hint, false)));
    }
    ASSERT_NO_FATAL_FAILURE(AssertEqual(LerpRatio(2, +0.0), GetLerpRatio(x_samples, 4.0)));
    ASSERT_NO_FATAL_FAILURE(AssertEqual(LerpRatio(2, +0.0), GetLerpRatio(x_samples, 5.0)));
    ASSERT_NO_FATAL_FAILURE(AssertEqual(LerpRatio(2, +0.0), GetLerpRatio(x_samples, 4.0, true)));
    ASSERT_NO_FATAL_FAILURE(AssertEqual(LerpRatio(2, +0.5), GetLerpRatio(x_samples, 5.0, true)));
    ASSERT_NO_FATAL_FAILURE(AssertEqual(LerpRatio(2, +2.0), GetLerpRatio(x_samples, 8.0, true)));
  }
  {
    ASSERT_NO_FATAL_FAILURE(AssertEqual(LerpRatio(0, +0.0), GetLerpRatio(1.0, 1.0, 1, 0.0)));
    ASSERT_NO_FATAL_FAILURE(AssertEqual(LerpRatio(0, +0.0), GetLerpRatio(1.0, 1.0, 1, 0.0, true)));
    ASSERT_NO_FATAL_FAILURE(AssertEqual(LerpRatio(0, +0.0), GetLerpRatio(1.0, 1.0, 1, 1.0)));
    ASSERT_NO_FATAL_FAILURE(AssertEqual(LerpRatio(0, +0.0), GetLerpRatio(1.0, 1.0, 1, 1.0, true)));
    ASSERT_NO_FATAL_FAILURE(AssertEqual(LerpRatio(0, +0.0), GetLerpRatio(1.0, 1.0, 1, 2.0)));
    ASSERT_NO_FATAL_FAILURE(AssertEqual(LerpRatio(0, +0.0), GetLerpRatio(1.0, 1.0, 1, 2.0, true)));
  }
  {
    ASSERT_NO_FATAL_FAILURE(AssertEqual(LerpRatio(0, +0.0), GetLerpRatio(1.0, 2.0, 3, 1.0)));
    ASSERT_NO_FATAL_FAILURE(AssertEqual(LerpRatio(0, +0.0), GetLerpRatio(1.0, 2.0, 3, 1.0, true)));
    ASSERT_NO_FATAL_FAILURE(AssertEqual(LerpRatio(0, +0.2), GetLerpRatio(1.0, 2.0, 3, 1.4)));
    ASSERT_NO_FATAL_FAILURE(AssertEqual(LerpRatio(0, +0.0), GetLerpRatio(1.0, 2.0, 3, 0.5)));
    ASSERT_NO_FATAL_FAILURE(AssertEqual(LerpRatio(0, -0.2), GetLerpRatio(1.0, 2.0, 3, 0.6, true)));
    ASSERT_NO_FATAL_FAILURE(AssertEqual(LerpRatio(0, -0.5), GetLerpRatio(1.0, 2.0, 3, 0.0, true)));
    ASSERT_NO_FATAL_FAILURE(AssertEqual(LerpRatio(1, +0.2), GetLerpRatio(1.0, 2.0, 3, 3.4, false)));
    ASSERT_NO_FATAL_FAILURE(AssertEqual(LerpRatio(1, +0.5), GetLerpRatio(1.0, 2.0, 3, 4.0)));
    ASSERT_NO_FATAL_FAILURE(AssertEqual(LerpRatio(2, +0.0), GetLerpRatio(1.0, 2.0, 3, 5.0)));
    ASSERT_NO_FATAL_FAILURE(AssertEqual(LerpRatio(2, +0.0), GetLerpRatio(1.0, 2.0, 3, 6.0)));
    ASSERT_NO_FATAL_FAILURE(AssertEqual(LerpRatio(2, +0.0), GetLerpRatio(1.0, 2.0, 3, 7.0)));
    ASSERT_NO_FATAL_FAILURE(AssertEqual(LerpRatio(2, +0.0), GetLerpRatio(1.0, 2.0, 3, 8.0)));
    ASSERT_NO_FATAL_FAILURE(AssertEqual(LerpRatio(2, +0.5), GetLerpRatio(1.0, 2.0, 3, 6.0, true)));
    ASSERT_NO_FATAL_FAILURE(AssertEqual(LerpRatio(2, +1.0), GetLerpRatio(1.0, 2.0, 3, 7.0, true)));
    ASSERT_NO_FATAL_FAILURE(AssertEqual(LerpRatio(2, +1.5), GetLerpRatio(1.0, 2.0, 3, 8.0, true)));
    ASSERT_NO_FATAL_FAILURE(AssertEqual(LerpRatio(2, +2.0), GetLerpRatio(1.0, 2.0, 3, 9.0, true)));
  }
}

TEST(PiecewiseLinearFunctionTest, LerpNormalizedAngle) {
  {
    const std::vector<double> angles = {1.0};
    const std::vector<double> y_samples = {0.0};
    ASSERT_NEAR(LerpNormalizedAngle(angles, GetLerpRatio(y_samples, -1.0)), 1.0, math::kEpsilon);
    ASSERT_NEAR(LerpNormalizedAngle(angles, GetLerpRatio(y_samples, 0.0)), 1.0, math::kEpsilon);
  }
  {
    const std::vector<double> angles = {1.0, 2.0};
    const std::vector<double> y_samples = {0.0, 1.0};
    ASSERT_NEAR(LerpNormalizedAngle(angles, GetLerpRatio(y_samples, -1.0)), 1.0, math::kEpsilon);
    ASSERT_NEAR(LerpNormalizedAngle(angles, GetLerpRatio(y_samples, 0.0)), 1.0, math::kEpsilon);
    ASSERT_NEAR(LerpNormalizedAngle(angles, GetLerpRatio(y_samples, 0.5)), 1.5, math::kEpsilon);
    ASSERT_NEAR(LerpNormalizedAngle(angles, GetLerpRatio(y_samples, 2.0)), 2.0, math::kEpsilon);
  }
  {
    const std::vector<double> angles = {0.0, M_PI / 8, M_PI / 4, M_PI / 2, M_PI};
    const std::vector<double> y_samples = {0.0, 1.0, 2.0, 3.0, 4.0};
    ASSERT_NEAR(LerpNormalizedAngle(angles, GetLerpRatio(y_samples, -1.0)), 0.0, math::kEpsilon);
    ASSERT_NEAR(LerpNormalizedAngle(angles, GetLerpRatio(y_samples, 0.0)), 0.0, math::kEpsilon);
    ASSERT_NEAR(
        LerpNormalizedAngle(angles, GetLerpRatio(y_samples, 0.5)), M_PI / 16, math::kEpsilon);
    ASSERT_NEAR(
        LerpNormalizedAngle(angles, GetLerpRatio(y_samples, 2.5)), M_PI * 3 / 8, math::kEpsilon);
    ASSERT_NEAR(LerpNormalizedAngle(angles, GetLerpRatio(y_samples, 4.0)), -M_PI, math::kEpsilon);
    ASSERT_NEAR(LerpNormalizedAngle(angles, GetLerpRatio(y_samples, 5.0)), -M_PI, math::kEpsilon);
  }
}

TEST(PiecewiseLinearFunctionTest, Lerp) {
  {
    const std::vector<double> y_samples = {1.0};
    ASSERT_NEAR(Lerp(y_samples, LerpRatio(0.0, +0.0)), 1.0, 1e-9);
  }
  {
    const std::vector<double> y_samples = {1.0, 2.0};
    ASSERT_NEAR(Lerp(y_samples, LerpRatio(0.0, +0.0)), 1.0, 1e-9);
    ASSERT_NEAR(Lerp(y_samples, LerpRatio(0.0, +0.3)), 1.3, 1e-9);
    ASSERT_NEAR(Lerp(y_samples, LerpRatio(1.0, +0.0)), 2.0, 1e-9);
  }
  {
    const std::vector<double> y_samples = {1.0, 2.0, 6.0};
    ASSERT_NEAR(Lerp(y_samples, LerpRatio(0.0, -0.5)), 0.5, 1e-9);
    ASSERT_NEAR(Lerp(y_samples, LerpRatio(1.0, +0.5)), 4.0, 1e-9);
    ASSERT_NEAR(Lerp(y_samples, LerpRatio(2.0, +0.0)), 6.0, 1e-9);
    ASSERT_NEAR(Lerp(y_samples, LerpRatio(2.0, +0.2)), 6.8, 1e-9);
  }
}

TEST(PiecewiseLinearFunctionTest, Constructor1) {
  {
    const std::vector<double> x_samples = {3.0, 4.0, 5.0, 6.0};
    const std::vector<double> y_samples = {Linear(3.0), Linear(4.0), Linear(5.0), Linear(6.0)};
    const auto linear_function =
        std::make_unique<PiecewiseLinearFunction<double>>(x_samples, y_samples);
    AssertNear<double>(Linear, CastLinear(*linear_function, false), 3.0, 6.0);
    AssertNear<double>(Linear, CastLinear(*linear_function, true), 0.0, 3.0);
    AssertNear<double>(CastLinear(Linear(3.0)), CastLinear(*linear_function, false), 0.0, 3.0);
    AssertNear<double>(Linear, CastLinear(*linear_function, true), 6.0, 9.0);
    AssertNear<double>(CastLinear(Linear(6.0)), CastLinear(*linear_function, false), 6.0, 9.0);
    AssertNear(x_samples.front(), x_samples.front(), linear_function->x_samples().front());
    AssertNear(y_samples.front(), y_samples.front(), linear_function->y_samples().front());
    AssertNear(x_samples.back(), x_samples.back(), linear_function->x_samples().back());
    AssertNear(y_samples.back(), y_samples.back(), linear_function->y_samples().back());
  }
  {
    const std::vector<double> x_samples = {3.0};
    const std::vector<double> y_samples = {Linear(3.0)};
    const auto linear_function =
        std::make_unique<PiecewiseLinearFunction<double>>(x_samples, y_samples);
    AssertNear<double>(
        CastLinear(Linear(3.0)), CastLinear(*linear_function, false), {1.0, 2.0, 3.0, 4.0, 5.0});
    AssertNear<double>(
        CastLinear(Linear(3.0)), CastLinear(*linear_function, true), {1.0, 2.0, 3.0, 4.0, 5.0});
  }
  {
    const std::vector<double> x_samples = {3.0, 4.0, 5.0, 6.0};
    const std::vector<double> y_samples = {Linear(3.0), Linear(4.0), Linear(5.0), Linear(6.0)};
    const auto linear_function =
        std::make_unique<PiecewiseLinearFunctionView<double>>(x_samples, y_samples);
    AssertNear<double>(Linear, CastLinear(*linear_function, false), 3.0, 6.0);
    AssertNear<double>(Linear, CastLinear(*linear_function, true), 0.0, 3.0);
    AssertNear<double>(CastLinear(Linear(3.0)), CastLinear(*linear_function, false), 0.0, 3.0);
    AssertNear<double>(Linear, CastLinear(*linear_function, true), 6.0, 9.0);
    AssertNear<double>(CastLinear(Linear(6.0)), CastLinear(*linear_function, false), 6.0, 9.0);
    const int n_samples = linear_function->n_samples();
    AssertNear(x_samples.front(), x_samples.front(), linear_function->x_sample(0));
    AssertNear(y_samples.front(), y_samples.front(), linear_function->y_sample(0));
    AssertNear(x_samples.back(), x_samples.back(), linear_function->x_sample(n_samples - 1));
    AssertNear(y_samples.back(), y_samples.back(), linear_function->y_sample(n_samples - 1));
  }
  {
    const std::vector<double> x_samples = {3.0};
    const std::vector<double> y_samples = {Linear(3.0)};
    const auto linear_function =
        std::make_unique<PiecewiseLinearFunctionView<double>>(x_samples, y_samples);
    AssertNear<double>(
        CastLinear(Linear(3.0)), CastLinear(*linear_function, false), {1.0, 2.0, 3.0, 4.0, 5.0});
    AssertNear<double>(
        CastLinear(Linear(3.0)), CastLinear(*linear_function, true), {1.0, 2.0, 3.0, 4.0, 5.0});
  }
}

TEST(PiecewiseLinearFunctionTest, Constructor2) {
  {
    const std::vector<double> x_samples = {3.0, 4.0, 5.0, 6.0};
    const auto linear_function =
        std::make_unique<PiecewiseLinearFunction<double>>(x_samples, Linear);
    AssertNear<double>(Linear, CastLinear(*linear_function, false), 3.0, 6.0);
    AssertNear<double>(Linear, CastLinear(*linear_function, true), 0.0, 3.0);
    AssertNear<double>(CastLinear(Linear(3.0)), CastLinear(*linear_function, false), 0.0, 3.0);
    AssertNear<double>(Linear, CastLinear(*linear_function, true), 6.0, 9.0);
    AssertNear<double>(CastLinear(Linear(6.0)), CastLinear(*linear_function, false), 6.0, 9.0);
  }
  {
    const std::vector<double> x_samples = {3.0};
    const auto linear_function =
        std::make_unique<PiecewiseLinearFunction<double>>(x_samples, Linear);
    AssertNear<double>(CastLinear(Linear(3.0)), CastLinear(*linear_function, false),
                       {1.0, 2.0, 3.0, 4.0, 5.0});
    AssertNear<double>(CastLinear(Linear(3.0)), CastLinear(*linear_function, true),
                       {1.0, 2.0, 3.0, 4.0, 5.0});
  }
}

TEST(PiecewiseLinearFunctionTest, Constructor3) {
  for (int n_samples : {2, 3, 5, 7, 11, 13, 23, 30}) {
    double x_step = 3.0 / (n_samples - 1);
    const auto linear_function =
        std::make_unique<PiecewiseLinearFunction<double>>(3.0, x_step, n_samples, Linear);
    ASSERT_TRUE(linear_function->is_uniform_);
    ASSERT_NEAR(linear_function->x_step_inverse_, 1.0 / x_step, math::kEpsilon);
    ASSERT_NEAR(linear_function->max_x(), 6.0, 1e-6);
    AssertNear<double>(Linear, CastLinear(*linear_function, false), 3.0, 6.0);
    AssertNear<double>(Linear, CastLinear(*linear_function, true), 0.0, 3.0);
    AssertNear<double>(CastLinear(Linear(3.0)), CastLinear(*linear_function, false), 0.0, 3.0);
    AssertNear<double>(Linear, CastLinear(*linear_function, true), 6.0, 9.0);
    AssertNear<double>(CastLinear(Linear(6.0)), CastLinear(*linear_function, false), 6.0, 9.0);
  }
  {
    const auto linear_function =
        std::make_unique<PiecewiseLinearFunction<double>>(3.0, 1.0, 1, Linear);
    AssertNear<double>(CastLinear(Linear(3.0)), CastLinear(*linear_function, false),
                       {1.0, 2.0, 3.0, 4.0, 5.0});
    AssertNear<double>(CastLinear(Linear(3.0)), CastLinear(*linear_function, true),
                       {1.0, 2.0, 3.0, 4.0, 5.0});
  }
}

TEST(PiecewiseLinearFunctionTest, Constructor4) {
  for (int n_samples : {2, 3, 5, 7, 11, 13, 23, 30}) {
    double x_step = 3.0 / (n_samples - 1);
    std::vector<double> y_samples;
    y_samples.reserve(n_samples);
    for (int i = 0; i < n_samples; ++i) {
      y_samples.emplace_back(Linear(3.0 + i * x_step));
    }
    {
      const auto linear_function =
          std::make_unique<PiecewiseLinearFunction<double>>(3.0, x_step, y_samples);
      ASSERT_TRUE(linear_function->is_uniform_);
      ASSERT_NEAR(linear_function->x_step_inverse_, 1.0 / x_step, math::kEpsilon);
      ASSERT_NEAR(linear_function->max_x(), 6.0, 1e-6);
      AssertNear<double>(Linear, CastLinear(*linear_function, false), 3.0, 6.0);
      AssertNear<double>(Linear, CastLinear(*linear_function, true), 0.0, 3.0);
      AssertNear<double>(CastLinear(Linear(3.0)), CastLinear(*linear_function, false), 0.0, 3.0);
      AssertNear<double>(Linear, CastLinear(*linear_function, true), 6.0, 9.0);
      AssertNear<double>(CastLinear(Linear(6.0)), CastLinear(*linear_function, false), 6.0, 9.0);
    }
    {
      const auto linear_function =
          std::make_unique<PiecewiseLinearFunctionView<double>>(3.0, x_step, y_samples);
      ASSERT_TRUE(linear_function->is_uniform_);
      ASSERT_NEAR(linear_function->x_step_inverse_, 1.0 / x_step, math::kEpsilon);
      ASSERT_NEAR(linear_function->max_x(), 6.0, 1e-6);
      AssertNear<double>(Linear, CastLinear(*linear_function, false), 3.0, 6.0);
      AssertNear<double>(Linear, CastLinear(*linear_function, true), 0.0, 3.0);
      AssertNear<double>(CastLinear(Linear(3.0)), CastLinear(*linear_function, false), 0.0, 3.0);
      AssertNear<double>(Linear, CastLinear(*linear_function, true), 6.0, 9.0);
      AssertNear<double>(CastLinear(Linear(6.0)), CastLinear(*linear_function, false), 6.0, 9.0);
    }
  }
  {
    const auto linear_function =
        std::make_unique<PiecewiseLinearFunction<double>>(3.0, 1.0, 1, Linear);
    AssertNear<double>(CastLinear(Linear(3.0)), CastLinear(*linear_function, false),
                       {1.0, 2.0, 3.0, 4.0, 5.0});
    AssertNear<double>(CastLinear(Linear(3.0)), CastLinear(*linear_function, true),
                       {1.0, 2.0, 3.0, 4.0, 5.0});
  }
}

TEST(PiecewiseLinearFunctionTest, Constructor5) {
  {
    const std::vector<double> x_samples = {3.0, 4.0, 5.0, 6.0};
    const std::vector<double> y_samples = {Linear(3.0), Linear(4.0), Linear(5.0), Linear(6.0)};
    const auto linear_function =
        std::make_unique<PiecewiseLinearFunction<double>>(y_samples, [&x_samples](int i, double) {
          CHECK_GE(i, 0);
          CHECK_LT(i, x_samples.size());
          return x_samples[i];
        });
    AssertNear<double>(Linear, CastLinear(*linear_function, false), 3.0, 6.0);
    AssertNear<double>(Linear, CastLinear(*linear_function, true), 0.0, 3.0);
    AssertNear<double>(CastLinear(Linear(3.0)), CastLinear(*linear_function, false), 0.0, 3.0);
    AssertNear<double>(Linear, CastLinear(*linear_function, true), 6.0, 9.0);
    AssertNear<double>(CastLinear(Linear(6.0)), CastLinear(*linear_function, false), 6.0, 9.0);
  }
  {
    const std::vector<double> x_samples = {3.0};
    const std::vector<double> y_samples = {Linear(3.0)};
    const auto linear_function =
        std::make_unique<PiecewiseLinearFunction<double>>(y_samples, [&x_samples](int i, double) {
          CHECK_GE(i, 0);
          CHECK_LT(i, x_samples.size());
          return x_samples[i];
        });
    AssertNear<double>(CastLinear(Linear(3.0)), CastLinear(*linear_function, false),
                       {1.0, 2.0, 3.0, 4.0, 5.0});
    AssertNear<double>(CastLinear(Linear(3.0)), CastLinear(*linear_function, true),
                       {1.0, 2.0, 3.0, 4.0, 5.0});
  }
}

TEST(PiecewiseLinearFunctionTest, LerpOperator) {
  {
    const std::vector<double> x_samples = {3.0, 6.0};
    const auto linear_function =
        std::make_unique<PiecewiseLinearFunction<Tuple, TupleLerpOperator>>(x_samples, LinearTuple);
    AssertNear<Tuple>(LinearTuple, CastLinear(*linear_function, false), 3.0, 6.0);
    AssertNear<Tuple>(LinearTuple, CastLinear(*linear_function, true), 0.0, 3.0);
    AssertNear<Tuple>(CastLinear(LinearTuple(3.0)), CastLinear(*linear_function, false), 0.0, 3.0);
    AssertNear<Tuple>(LinearTuple, CastLinear(*linear_function, true), 6.0, 9.0);
    AssertNear<Tuple>(CastLinear(LinearTuple(6.0)), CastLinear(*linear_function, false), 6.0, 9.0);
  }
  {
    const std::vector<double> x_samples = {3.0};
    const auto linear_function =
        std::make_unique<PiecewiseLinearFunction<Tuple, TupleLerpOperator>>(x_samples, LinearTuple);
    AssertNear<Tuple>(CastLinear(LinearTuple(3.0)), CastLinear(*linear_function, false),
                      {1.0, 2.0, 3.0, 4.0, 5.0});
    AssertNear<Tuple>(CastLinear(LinearTuple(3.0)), CastLinear(*linear_function, true),
                      {1.0, 2.0, 3.0, 4.0, 5.0});
  }
}

TEST(PiecewiseLinearFunctionTest, ShiftXAndShiftY) {
  {
    const std::vector<double> x_samples = {3.0, 4.0, 5.0, 6.0};
    const std::vector<double> y_samples = {Linear(2.0), Linear(3.0), Linear(4.0), Linear(5.0)};
    const auto linear_function =
        std::make_unique<PiecewiseLinearFunction<double>>(x_samples, y_samples);
    linear_function->ShiftX(2.0);
    AssertNear<double>(Linear, CastLinear(*linear_function, false), 2.0, 5.0);
  }
  {
    const std::vector<double> y_samples = {Linear(2.0), Linear(3.0), Linear(4.0), Linear(5.0)};
    const auto linear_function =
        std::make_unique<PiecewiseLinearFunction<double>>(3.0, 1.0, y_samples);
    linear_function->ShiftX(2.0);
    AssertNear<double>(Linear, CastLinear(*linear_function, false), 2.0, 5.0);
  }
  {
    const std::vector<double> x_samples = {3.0, 4.0, 5.0, 6.0};
    const std::vector<double> y_samples = {Linear(3.0) - 1.0, Linear(4.0) - 1.0, Linear(5.0) - 1.0,
                                           Linear(6.0) - 1.0};
    const auto linear_function =
        std::make_unique<PiecewiseLinearFunction<double>>(x_samples, y_samples);
    linear_function->ShiftY(4.0);
    AssertNear<double>(Linear, CastLinear(*linear_function, false), 3.0, 6.0);
  }
  {
    const std::vector<double> y_samples = {
        Linear(3.0) - 1.0, Linear(4.0) - 1.0, Linear(5.0) - 1.0, Linear(6.0) - 1.0};
    const auto linear_function =
        std::make_unique<PiecewiseLinearFunction<double>>(3.0, 1.0, y_samples);
    linear_function->ShiftY(4.0);
    AssertNear<double>(Linear, CastLinear(*linear_function, false), 3.0, 6.0);
  }
}

TEST(PiecewiseLinearFunctionTest, MutableY) {
  const std::vector<double> x_samples = {0.0, 1.0, 2.0, 3.0};
  const std::vector<double> y_samples = {0.0, 1.0, 2.0, 3.0};
  PiecewiseLinearFunction<double> linear_function(x_samples, y_samples);
  for (int i = 0; i < static_cast<int>(x_samples.size()); ++i) {
    ASSERT_NEAR(linear_function.x_sample(i), x_samples[i], math::kEpsilon);
    ASSERT_NEAR(linear_function.y_sample(i), y_samples[i], math::kEpsilon);
  }
  *linear_function.mutable_y_sample(1) = 2.0;
  ASSERT_NEAR(linear_function.y_sample(1), 2.0, math::kEpsilon);
  ASSERT_NEAR(linear_function(0.5), 1.0, math::kEpsilon);

  *linear_function.mutable_y_sample(3) = 4.0;
  ASSERT_NEAR(linear_function.y_sample(3), 4.0, math::kEpsilon);
  ASSERT_NEAR(linear_function(2.5), 3.0, math::kEpsilon);
}

TEST(PiecewiseLinearFunctionTest, MakeSubPiecewiseLinearFunction) {
  const std::vector<double> x_samples = {3.0, 4.0, 4.5, 6.0, 10.0};
  const std::vector<double> y_samples = {1.0, 10.0, 12.5, 15.1, 16.0};
  const PiecewiseLinearFunction<double> function(x_samples, y_samples);
  {
    const base::Optional<PiecewiseLinearFunction<double>> sub_function =
        function.MakeSubPiecewiseLinearFunction(-1.0, -0.5);
    ASSERT_TRUE(sub_function == base::none);
  }
  {
    const base::Optional<PiecewiseLinearFunction<double>> sub_function =
        function.MakeSubPiecewiseLinearFunction(-1.0, 3.0);
    ASSERT_TRUE(sub_function == base::none);
  }
  {
    const base::Optional<PiecewiseLinearFunction<double>> sub_function =
        function.MakeSubPiecewiseLinearFunction(-1.0, 3.0 + math::kEpsilon);
    ASSERT_TRUE(sub_function != base::none);
    ASSERT_NEAR(sub_function->min_x(), 3.0, math::kEpsilon);
    ASSERT_NEAR(sub_function->max_x(), 3.0 + math::kEpsilon, math::kEpsilon);
    ASSERT_EQ(sub_function->n_samples(), 2);
    ASSERT_TRUE(
        IsEqual(function, *sub_function, sub_function->min_x(), sub_function->max_x(), 0.02));
  }
  {
    const base::Optional<PiecewiseLinearFunction<double>> sub_function =
        function.MakeSubPiecewiseLinearFunction(4.1, 4.1);
    ASSERT_TRUE(sub_function == base::none);
  }
  {
    const base::Optional<PiecewiseLinearFunction<double>> sub_function =
        function.MakeSubPiecewiseLinearFunction(-1.0, 3.7);
    ASSERT_TRUE(sub_function != base::none);
    ASSERT_NEAR(sub_function->min_x(), 3.0, math::kEpsilon);
    ASSERT_NEAR(sub_function->max_x(), 3.7, math::kEpsilon);
    ASSERT_EQ(sub_function->n_samples(), 2);
    ASSERT_TRUE(
        IsEqual(function, *sub_function, sub_function->min_x(), sub_function->max_x(), 0.02));
  }
  {
    const base::Optional<PiecewiseLinearFunction<double>> sub_function =
        function.MakeSubPiecewiseLinearFunction(3.9, 6.0);
    ASSERT_TRUE(sub_function != base::none);
    ASSERT_NEAR(sub_function->min_x(), 3.9, math::kEpsilon);
    ASSERT_NEAR(sub_function->max_x(), 6.0, math::kEpsilon);
    ASSERT_EQ(sub_function->n_samples(), 4);
    ASSERT_TRUE(
        IsEqual(function, *sub_function, sub_function->min_x(), sub_function->max_x(), 0.02));
  }
  {
    const base::Optional<PiecewiseLinearFunction<double>> sub_function =
        function.MakeSubPiecewiseLinearFunction(4.0, 6.0);
    ASSERT_TRUE(sub_function != base::none);
    ASSERT_NEAR(sub_function->min_x(), 4.0, math::kEpsilon);
    ASSERT_NEAR(sub_function->max_x(), 6.0, math::kEpsilon);
    ASSERT_EQ(sub_function->n_samples(), 3);
    ASSERT_TRUE(
        IsEqual(function, *sub_function, sub_function->min_x(), sub_function->max_x(), 0.02));
  }
  {
    const base::Optional<PiecewiseLinearFunction<double>> sub_function =
        function.MakeSubPiecewiseLinearFunction(5.9, std::numeric_limits<double>::infinity());
    ASSERT_TRUE(sub_function != base::none);
    ASSERT_NEAR(sub_function->min_x(), 5.9, math::kEpsilon);
    ASSERT_NEAR(sub_function->max_x(), 10.0, math::kEpsilon);
    ASSERT_EQ(sub_function->n_samples(), 3);
    ASSERT_TRUE(
        IsEqual(function, *sub_function, sub_function->min_x(), sub_function->max_x(), 0.02));
  }
}

}  // namespace math
