// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>
//          <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)

#pragma once

#include <algorithm>
#include <limits>
#include <string>
#include <vector>

#include "Eigen/Core"

#include "base/math/axis_align_box2d.h"
#include "base/math/line_segment2d.h"
#include "base/math/proto/math.pb.h"
#include "base/math/vector2.h"

namespace math {

class OrientedBox2d final {
 public:
  OrientedBox2d() = default;
  OrientedBox2d(const Vector2d& center, const Vector2d& direction, double length, double width);
  OrientedBox2d(const Vector2d& center, double heading, double length, double width);

  explicit OrientedBox2d(const LineSegment2d& segment, double width = 0.0);
  explicit OrientedBox2d(const AxisAlignBox2d& aabox);
  explicit OrientedBox2d(const math_proto::OrientedBox2d& proto);

  static OrientedBox2d FromAABox(const Vector2d& corner, const Vector2d& opposite_corner);

  const Vector2d& center() const { return center_; }
  const Vector2d& direction() const { return direction_; }

  double heading() const { return heading_; }
  double cos_heading() const { return direction_.x; }
  double sin_heading() const { return direction_.y; }

  double length() const { return 2.0 * half_length_; }
  double width() const { return 2.0 * half_width_; }
  double area() const { return half_length_ * half_width_ * 4.0; }

  double half_length() const { return half_length_; }
  double half_width() const { return half_width_; }

  const AxisAlignBox2d& aabox() const { return aabox_; }
  double min_x() const { return aabox_.min_x(); }
  double max_x() const { return aabox_.max_x(); }
  double min_y() const { return aabox_.min_y(); }
  double max_y() const { return aabox_.max_y(); }

  double GetDiagonal() const { return math::Hypot(half_length_, half_width_) * 2.0; }

  // Get corners in ccw order: front-right, front-left, rear-left, rear-right.
  std::vector<Vector2d> GetCorners() const;
  // TODO(yanlimin03): Improve math by CornerArray
  std::array<Vector2d, 4> GetCornersAsArray() const;
  Vector2d GetFrontLeftCorner() const;
  Vector2d GetFrontRightCorner() const;
  Vector2d GetRearLeftCorner() const;
  Vector2d GetRearRightCorner() const;

  // Get boundary segments in ccw order: front, left, back, right.
  std::vector<LineSegment2d> GetLineSegments() const;
  LineSegment2d GetFrontLineSegment() const;
  LineSegment2d GetLeftLineSegment() const;
  LineSegment2d GetBackLineSegment() const;
  LineSegment2d GetRightLineSegment() const;

  bool IsPointIn(const Vector2d& point) const;
  bool IsPointOnBoundary(const Vector2d& point) const;

  bool HasOverlap(const LineSegment2d& segment) const;
  bool HasOverlap(const Vector2d& segment_start, const Vector2d& segment_end) const;
  bool HasOverlap(const AxisAlignBox2d& other_box) const;
  bool HasOverlap(const OrientedBox2d& other_box) const;

  bool HasOverlapAlongDirection(const AxisAlignBox2d& other_box,
                                const math::Vector2d& direction) const;
  bool HasOverlapAlongDirection(const OrientedBox2d& other_box,
                                const math::Vector2d& direction) const;

  double DistanceTo(const Vector2d& point) const;
  double DistanceTo(const LineSegment2d& segment) const;
  double DistanceTo(const OrientedBox2d& other_box) const;
  double DistanceTo(const AxisAlignBox2d& other_box) const;
  double DistanceTo(const base::ConstSpan<Vector2d>& polyline) const;

  double DistanceTo(const Vector2d& segment_start, const Vector2d& segment_end) const;
  double DistanceSquareTo(const Vector2d& segment_start, const Vector2d& segment_end) const;
  double DistanceSquareTo(const Vector2d& point) const;

  double ComputeOverlapAreaWith(const OrientedBox2d& other_box) const;
  double ComputeIoUWith(const OrientedBox2d& other_box) const;

  OrientedBox2d Shifted(const Vector2d& offset) const;
  OrientedBox2d Rotated(double angle) const;
  OrientedBox2d Rotated(const Vector2d& center, double angle) const;
  OrientedBox2d Extended(double extension_length, double extension_width) const;
  OrientedBox2d DirectionalExtended(double front_extension,
                                    double rear_extension,
                                    double left_extension,
                                    double right_extension) const;

  void Shift(const Vector2d& offset);
  void Rotate(double angle);
  void Rotate(const Vector2d& center, double angle);
  void Extend(double extension_length, double extension_width);
  void DirectionalExtend(double front_extension,
                         double rear_extension,
                         double left_extension,
                         double right_extension);

  void LongitudinalExtend(double extension_length);
  void LateralExtend(double extension_width);

  std::vector<Vector2i> GetTouchedGrids(double grid_resolution,
                                        const Vector2d& base_point = kZeroPoint) const;

  std::vector<Vector2d> GetTouchedGridsCenters(double grid_resolution,
                                               const Vector2d& base_point = kZeroPoint) const;

  // Order: center x, center y, length, width, yaw
  Eigen::Matrix<double, 5, 1> ToEigen() const;

  std::string ToString() const;

  math_proto::OrientedBox2d ToProto() const;

 private:
  void Init();
  bool IsGridIn(const Vector2d& base_point, double grid_resolution, int x_index, int y_index) const;

  static constexpr Vector2d kZeroPoint = Vector2d(0.0, 0.0);
  Vector2d center_;
  double heading_ = 0.0;
  double half_length_ = 0.0;
  double half_width_ = 0.0;

  AxisAlignBox2d aabox_;

  // Used to improve the distance calculation.
  Vector2d direction_{1.0, 0.0};

  // Shallow copy and move are OK.
};

inline OrientedBox2d::OrientedBox2d(const Vector2d& center,
                                    double heading,
                                    double length,
                                    double width)
    : center_(center), heading_(heading), half_length_(length * 0.5), half_width_(width * 0.5) {
  Init();
}

inline OrientedBox2d::OrientedBox2d(const Vector2d& center,
                                    const Vector2d& direction,
                                    double length,
                                    double width)
    : OrientedBox2d(center, direction.Angle(), length, width) {
  CHECK_NEAR(direction.LengthSquare(), 1.0, math::kEpsilon);
}

inline OrientedBox2d::OrientedBox2d(const LineSegment2d& segment, double width)
    : OrientedBox2d(segment.GetCenter(), segment.heading(), segment.length(), width) {}

inline OrientedBox2d::OrientedBox2d(const AxisAlignBox2d& aabox)
    : OrientedBox2d(aabox.center(), 0.0, aabox.x_length(), aabox.y_length()) {}

inline OrientedBox2d::OrientedBox2d(const math_proto::OrientedBox2d& proto)
    : OrientedBox2d(Vector2d(proto.center()), proto.heading(), proto.length(), proto.width()) {}

inline OrientedBox2d OrientedBox2d::FromAABox(const Vector2d& corner,
                                              const Vector2d& opposite_corner) {
  return OrientedBox2d(0.5 * (corner + opposite_corner),
                       0.0,
                       std::abs(corner.x - opposite_corner.x),
                       std::abs(corner.y - opposite_corner.y));
}

inline std::vector<Vector2d> OrientedBox2d::GetCorners() const {
  const Vector2d d0 = direction_ * half_length_;
  const Vector2d d1 = direction_.Rotate90() * half_width_;
  return {
      center_ + d0 - d1,
      center_ + d0 + d1,
      center_ - d0 + d1,
      center_ - d0 - d1,
  };
}

inline std::array<Vector2d, 4> OrientedBox2d::GetCornersAsArray() const {
  const Vector2d d0 = direction_ * half_length_;
  const Vector2d d1 = direction_.Rotate90() * half_width_;
  return {
      center_ + d0 - d1,
      center_ + d0 + d1,
      center_ - d0 + d1,
      center_ - d0 - d1,
  };
}

inline Vector2d OrientedBox2d::GetFrontLeftCorner() const {
  return center_ + direction_ * half_length_ + direction_.Rotate90() * half_width_;
}

inline Vector2d OrientedBox2d::GetFrontRightCorner() const {
  return center_ + direction_ * half_length_ - direction_.Rotate90() * half_width_;
}

inline Vector2d OrientedBox2d::GetRearLeftCorner() const {
  return center_ - direction_ * half_length_ + direction_.Rotate90() * half_width_;
}

inline Vector2d OrientedBox2d::GetRearRightCorner() const {
  return center_ - direction_ * half_length_ - direction_.Rotate90() * half_width_;
}

inline void OrientedBox2d::Shift(const Vector2d& offset) {
  center_ += offset;
  aabox_.Shift(offset);
}

inline void OrientedBox2d::Rotate(double angle) {
  heading_ = math::NormalizeAngle(heading_ + angle);
  Init();
}

inline void OrientedBox2d::Rotate(const Vector2d& center, double angle) {
  heading_ = math::NormalizeAngle(heading_ + angle);
  center_ = (center_ - center).Rotate(angle) + center;
  Init();
}

inline void OrientedBox2d::Extend(double extension_length, double extension_width) {
  half_length_ += extension_length * 0.5;
  half_width_ += extension_width * 0.5;
  Init();
}

inline void OrientedBox2d::DirectionalExtend(double front_extension,
                                             double rear_extension,
                                             double left_extension,
                                             double right_extension) {
  half_length_ += 0.5 * (front_extension + rear_extension);
  half_width_ += 0.5 * (left_extension + right_extension);
  center_ += direction_ * 0.5 * (front_extension - rear_extension) +
             direction_.Rotate90() * 0.5 * (left_extension - right_extension);
  Init();
}

inline OrientedBox2d OrientedBox2d::Shifted(const Vector2d& offset) const {
  OrientedBox2d box = *this;
  box.Shift(offset);
  return box;
}

inline OrientedBox2d OrientedBox2d::Rotated(double angle) const {
  OrientedBox2d box = *this;
  box.Rotate(angle);
  return box;
}

inline OrientedBox2d OrientedBox2d::Rotated(const Vector2d& center, double angle) const {
  OrientedBox2d box = *this;
  box.Rotate(center, angle);
  return box;
}

inline OrientedBox2d OrientedBox2d::Extended(double extension_length,
                                             double extension_width) const {
  OrientedBox2d box = *this;
  box.Extend(extension_length, extension_width);
  return box;
}

inline OrientedBox2d OrientedBox2d::DirectionalExtended(double front_extension,
                                                        double rear_extension,
                                                        double left_extension,
                                                        double right_extension) const {
  OrientedBox2d box = *this;
  box.DirectionalExtend(front_extension, rear_extension, left_extension, right_extension);
  return box;
}

inline bool OrientedBox2d::IsPointIn(const Vector2d& point) const {
  const Vector2d d = point - center_;
  const double dx = std::abs(direction_.InnerProd(d)) - half_length_;
  const double dy = std::abs(direction_.CrossProd(d)) - half_width_;
  return dx <= math::kEpsilon && dy <= math::kEpsilon;
}

inline bool OrientedBox2d::IsPointOnBoundary(const Vector2d& point) const {
  const Vector2d d = point - center_;
  const double dx = std::abs(direction_.InnerProd(d)) - half_length_;
  const double dy = std::abs(direction_.CrossProd(d)) - half_width_;
  return (std::abs(dx) <= math::kEpsilon && dy <= math::kEpsilon) ||
         (std::abs(dy) <= math::kEpsilon && dx <= math::kEpsilon);
}

inline bool OrientedBox2d::HasOverlap(const LineSegment2d& segment) const {
  return HasOverlap(segment.start(), segment.end());
}

inline double OrientedBox2d::DistanceTo(const Vector2d& point) const {
  const Vector2d d = point - center_;
  const double dx = std::abs(direction_.InnerProd(d)) - half_length_;
  const double dy = std::abs(direction_.CrossProd(d)) - half_width_;
  return (dx > 0.0 && dy > 0.0) ? math::Hypot(dx, dy) : std::max(0.0, std::max(dx, dy));
}

inline double OrientedBox2d::DistanceSquareTo(const Vector2d& point) const {
  const Vector2d d = point - center_;
  const double dx = std::abs(direction_.InnerProd(d)) - half_length_;
  const double dy = std::abs(direction_.CrossProd(d)) - half_width_;
  return math::square(std::max(0.0, dx)) + math::square(std::max(0.0, dy));
}

inline double OrientedBox2d::DistanceTo(const Vector2d& segment_start,
                                        const Vector2d& segment_end) const {
  return std::sqrt(DistanceSquareTo(segment_start, segment_end));
}

inline bool OrientedBox2d::IsGridIn(const Vector2d& base_point,
                                    double grid_resolution,
                                    int x_index,
                                    int y_index) const {
  const Vector2d point_center = {(x_index + 0.5) * grid_resolution + base_point.x,
                                 (y_index + 0.5) * grid_resolution + base_point.y};
  if (IsPointIn(point_center)) {
    return true;
  }
  // TODO(liyulong23): using the properties of the square grid here.
  return HasOverlap(AxisAlignBox2d(point_center, grid_resolution, grid_resolution));
}

}  // namespace math
