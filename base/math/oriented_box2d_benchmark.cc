// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>
//          Yanlimin (<EMAIL>)

#include <cmath>
#include <vector>

#include "benchmark/benchmark.h"

#include "base/math/axis_align_box2d.h"
#include "base/math/oriented_box2d.h"
#include "base/math/polygon2d.h"
#include "base/testing/geometry.h"
#include "base/testing/random.h"

namespace math {

namespace {

constexpr int Next(int i, int n) { return i != n - 1 ? i + 1 : 0; }

}  // namespace

void BM_Box2dIsPointIn(benchmark::State& state) { /* NOLINT(runtime/references) */
  constexpr int kNumTests = 1000;
  const auto boxes = testing::RandomBox2ds(kNumTests, 150.0);
  const auto points = testing::RandomPoints(kNumTests, 100.0);
  int i = 0;
  while (state.KeepRunning()) {
    benchmark::DoNotOptimize(boxes[i].IsPointIn(points[i]));
    i = Next(i, boxes.size());
  }
}
BENCHMARK(BM_Box2dIsPointIn);

void BM_Box2dOverlapWithSegment(benchmark::State& state) { /* NOLINT(runtime/references) */
  constexpr int kNumTests = 1000;
  const auto boxes = testing::RandomBox2ds(kNumTests, 150.0);
  const auto segments = testing::RandomSegment2ds(kNumTests, 150.0);
  int i = 0;
  while (state.KeepRunning()) {
    benchmark::DoNotOptimize(boxes[i].HasOverlap(segments[i]));
    i = Next(i, boxes.size());
  }
}
BENCHMARK(BM_Box2dOverlapWithSegment);

void BM_Box2dOverlapWithBox2d(benchmark::State& state) { /* NOLINT(runtime/references) */
  constexpr int kNumTests = 1000;
  const auto boxes = testing::RandomBox2ds(kNumTests, 150.0);
  int i = 0;
  while (state.KeepRunning()) {
    const int j = Next(i, boxes.size());
    benchmark::DoNotOptimize(boxes[i].HasOverlap(boxes[j]));
    i = j;
  }
}
BENCHMARK(BM_Box2dOverlapWithBox2d);

void BM_Box2dDistanceToPoint(benchmark::State& state) { /* NOLINT(runtime/references) */
  constexpr int kNumTests = 1000;
  const auto boxes = testing::RandomAABox2ds(kNumTests, 150.0);
  std::vector<Vector2d> points;
  points.reserve(kNumTests);
  for (int i = 0; i < kNumTests; i++) {
    const Vector2d point = testing::RandomPoint(300) + Vector2d(2000.0, 0.0);
    points.push_back(point);
  }
  int i = 0;
  while (state.KeepRunning()) {
    benchmark::DoNotOptimize(boxes[i].DistanceTo(points[i]));
    i = Next(i, boxes.size());
  }
}
BENCHMARK(BM_Box2dDistanceToPoint);

void BM_Box2dDistanceToSegment(benchmark::State& state) { /* NOLINT(runtime/references) */
  constexpr int kNumTests = 1000;
  const std::vector<OrientedBox2d> boxes = testing::RandomBox2ds(kNumTests, 150.0);
  std::vector<LineSegment2d> segments;
  for (int i = 0; i < kNumTests; ++i) {
    const LineSegment2d segment =
        LineSegment2d(testing::RandomPoint(500.0), testing::RandomPoint(500.0))
            .Shifted(Vector2d(2000.0, 0.0));
    segments.push_back(segment);
  }
  int i = 0;
  while (state.KeepRunning()) {
    benchmark::DoNotOptimize(boxes[i].DistanceTo(segments[i]));
    i = Next(i, boxes.size());
  }
}
BENCHMARK(BM_Box2dDistanceToSegment);

void BM_Box2dDistanceToBox2d(benchmark::State& state) { /* NOLINT(runtime/references) */
  constexpr int kNumTests = 1000;
  std::vector<OrientedBox2d> boxes = {testing::RandomBox2d(150.0)};
  for (int i = 0; i < kNumTests; ++i) {
    const LineSegment2d segment =
        testing::RandomSegment2d(150.0).Shifted(Vector2d(i * 2000.0, 0.0));
    boxes.push_back(OrientedBox2d(segment, 150.0));
  }
  int i = 0;
  while (state.KeepRunning()) {
    const int j = Next(i, boxes.size());
    benchmark::DoNotOptimize(boxes[i].DistanceTo(boxes[j]));
    i = j;
  }
}
BENCHMARK(BM_Box2dDistanceToBox2d);

void BM_Box2dComputeIoUWith(benchmark::State& state) { /* NOLINT(runtime/references) */
  constexpr int kNumTests = 1000;
  const auto boxes = testing::RandomBox2ds(kNumTests, 150.0);
  int i = 0;
  while (state.KeepRunning()) {
    const int j = Next(i, boxes.size());
    benchmark::DoNotOptimize(boxes[i].ComputeIoUWith(boxes[j]));
    i = j;
  }
}
BENCHMARK(BM_Box2dComputeIoUWith);

void BM_Box2dComputeIoUWithUsePolygon(benchmark::State& state) { /* NOLINT(runtime/references) */
  constexpr int kNumTests = 1000;
  const auto boxes = testing::RandomBox2ds(kNumTests, 150.0);
  std::vector<Polygon2d> polygons(boxes.size());
  const auto box_to_polygon = [](const OrientedBox2d& box) { return Polygon2d(box); };
  std::transform(boxes.begin(), boxes.end(), polygons.begin(), box_to_polygon);
  int i = 0;
  while (state.KeepRunning()) {
    const int j = Next(i, polygons.size());
    benchmark::DoNotOptimize(polygons[i].ComputeIoUWith(polygons[j]));
    i = j;
  }
}
BENCHMARK(BM_Box2dComputeIoUWithUsePolygon);

void BM_Box2dComputeIoUWithUseTempPolygon(
    benchmark::State& state) { /* NOLINT(runtime/references) */
  constexpr int kNumTests = 1000;
  const auto boxes = testing::RandomBox2ds(kNumTests, 150.0);
  int i = 0;
  while (state.KeepRunning()) {
    const int j = Next(i, boxes.size());
    benchmark::DoNotOptimize(Polygon2d(boxes[i]).ComputeIoUWith(Polygon2d(boxes[j])));
    i = j;
  }
}
BENCHMARK(BM_Box2dComputeIoUWithUseTempPolygon);

void BM_BOX2dGetTouchedGrids(benchmark::State& state) { /* NOLINT(runtime/references) */
  constexpr int kNumTests = 100;
  int arg = state.range(0);
  const auto boxes = testing::RandomBox2ds(kNumTests, arg * 1.0);
  const auto points = testing::RandomPoints(kNumTests, arg * 1.0);
  while (state.KeepRunning()) {
    for (int i = 0; i < kNumTests; ++i) {
      benchmark::DoNotOptimize(boxes[i].GetTouchedGrids(0.25, points[i]));
    }
  }
}
BENCHMARK(BM_BOX2dGetTouchedGrids)->Arg(1000)->Arg(500)->Arg(100)->Arg(50)->Arg(10)->Arg(5)->Arg(2);

}  // namespace math

BENCHMARK_MAIN();

// clang-format off
/*
Run on (20 X 5200 MHz CPU s)
CPU Caches:
  L1 Data 32K (x10)
  L1 Instruction 32K (x10)
  L2 Unified 256K (x10)
  L3 Unified 20480K (x1)
Load Average: 2.63, 1.53, 1.54
***WARNING*** CPU scaling is enabled, the benchmark real time measurements may be noisy and will incur extra overhead.
-------------------------------------------------------------------------------
Benchmark                                     Time             CPU   Iterations
-------------------------------------------------------------------------------
BM_Box2dIsPointIn                          3.34 ns         3.34 ns    210133908
BM_Box2dOverlapWithSegment                 7.27 ns         7.27 ns     98218212
BM_Box2dOverlapWithBox2d                   11.2 ns         11.2 ns     61334250
BM_Box2dDistanceToPoint                    2.89 ns         2.89 ns    262693408
BM_Box2dDistanceToSegment                  34.7 ns         34.7 ns     20445375
BM_Box2dDistanceToBox2d                    25.0 ns         25.0 ns     28133622
BM_Box2dComputeIoUWith                      277 ns          277 ns      2469477
BM_Box2dComputeIoUWithUsePolygon            243 ns          243 ns      2816205
BM_Box2dComputeIoUWithUseTempPolygon        345 ns          345 ns      2010088
BM_BOX2dGetTouchedGrids/1000         1146974325 ns   1146952742 ns            1
BM_BOX2dGetTouchedGrids/500           129858065 ns    129857589 ns            5
BM_BOX2dGetTouchedGrids/100             3821095 ns      3818848 ns          174
BM_BOX2dGetTouchedGrids/50              1525286 ns      1525278 ns          469
BM_BOX2dGetTouchedGrids/10               272059 ns       272056 ns         2606
BM_BOX2dGetTouchedGrids/5                140501 ns       140498 ns         4910
BM_BOX2dGetTouchedGrids/2                 57092 ns        57091 ns        13295
(1000/500/100/50/10/5/2 mean box's max_width and max_length with same grid_resolution)
*/
