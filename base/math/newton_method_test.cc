// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/math/newton_method.h"

#include "gtest/gtest.h"

#include "base/math/fast_math.h"

namespace math {

TEST(NewtonMethodTest, Line) {
  auto fn = [](double x) { return -3.0 * x + 1.4; };
  auto derivative = [](double x) { return -3.0; };
  for (int i = 0; i <= 100; ++i) {
    const double x = -3.0 + 6.0 * i / 100;
    double x0 = x;
    ASSERT_TRUE(NewtonMethod(-3.0, 3.0, fn(0.0), 10, fn, derivative, &x0));
    ASSERT_NEAR(0.0, x0, 1e-6);
    double x1 = x;
    ASSERT_TRUE(NewtonMethod(-3.0, 3.0, fn(1.0), 10, fn, derivative, &x1));
    ASSERT_NEAR(1.0, x1, 1e-6);
  }
}

TEST(NewtonMethodTest, Curve) {
  auto fn = [](double x) { return std::sin(x); };
  auto derivative = [](double x) { return std::cos(x); };
  for (int i = 0; i <= 100; ++i) {
    const double x = (-1.0 + 2.0 * i / 100) * M_PI / 2;
    double x0 = x;
    ASSERT_TRUE(NewtonMethod(-M_PI, M_PI, fn(x), 10, fn, derivative, &x0));
    ASSERT_NEAR(x0, x, 1e-6);
    double x1 = x;
    ASSERT_FALSE(NewtonMethod(-M_PI / 3, M_PI / 3, 1.0, 10, fn, derivative, &x1));
    ASSERT_NEAR(x1, M_PI / 3, 1e-6);
  }
}

TEST(MultiVariableNewtonMethodTest, Linear) {
  Eigen::Matrix<double, 3, 3> A;
  // clang-format off
  A << 2.0,  0.5,  0.0,
       0.4,  0.0, -1.5,
       0.0,  3.0,  0.0;
  // clang-format on
  const auto fn = [&A](const Eigen::Matrix<double, 3, 1>& x) { return A * x; };
  const auto derivative = [&A](const Eigen::Matrix<double, 3, 1>& x) { return A; };
  Eigen::Matrix<double, 3, 1> min_x;
  min_x << 0.0, -1.0, 1.0;
  Eigen::Matrix<double, 3, 1> max_x;
  max_x << 5.0, 3.0, 1.0;
  const double tolerance = math::kEpsilon;
  for (int i = 0; i <= 100; ++i) {
    Eigen::Matrix<double, 3, 1> expected_x;
    expected_x << 2.0 + 1.0 * i / 100, 2.5 - 3.0 * i / 100, 1.0;
    const Eigen::Matrix<double, 3, 1> target_y = fn(expected_x);
    Eigen::Matrix<double, 3, 1> x = min_x;
    ASSERT_TRUE(NewtonMethod<3>(min_x, max_x, target_y, 10, tolerance, fn, derivative, &x));
    ASSERT_NEAR((x - expected_x).norm(), 0.0, math::kEpsilon);
    ASSERT_NEAR((fn(x) - target_y).norm(), 0.0, tolerance);
  }
}

TEST(MultiVariableNewtonMethodTest, Nonlinear) {
  const auto fn = [](const Eigen::Matrix<double, 2, 1>& x) {
    Eigen::Matrix<double, 2, 1> y;
    y << x(0) + math::Cos(x(1)), -x(0) + 3.0 * x(1);
    return y;
  };
  const auto derivative = [](const Eigen::Matrix<double, 2, 1>& x) {
    Eigen::Matrix<double, 2, 2> jacobians;
    // clang-format off
    jacobians <<  1.0, -math::Sin(x(1)),
                 -1.0,              3.0;
    // clang-format on
    return jacobians;
  };
  Eigen::Matrix<double, 2, 1> min_x;
  min_x << 0.0, 0.0;
  Eigen::Matrix<double, 2, 1> max_x;
  max_x << 10.0, 7.0;
  const double tolerance = math::kEpsilon;
  for (int i = 0; i <= 100; ++i) {
    Eigen::Matrix<double, 2, 1> expected_x;
    expected_x << 3.0 - 2.5 * i / 100, M_PI / 3.0 + 1.0 * i / 100;
    const Eigen::Matrix<double, 2, 1> target_y = fn(expected_x);
    Eigen::Matrix<double, 2, 1> x = max_x;
    ASSERT_TRUE(NewtonMethod<2>(min_x, max_x, target_y, 10, tolerance, fn, derivative, &x));
    ASSERT_NEAR((x - expected_x).norm(), 0.0, 1e-5);
    ASSERT_NEAR((fn(x) - target_y).norm(), 0.0, tolerance);
  }
}

}  // namespace math
