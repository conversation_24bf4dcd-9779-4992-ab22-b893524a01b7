// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>
//          <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)

#include "base/math/oriented_box2d.h"

#include <utility>
#include <vector>

#include "gmock/gmock.h"
#include "gtest/gtest.h"

#include "base/math/line_segment2d.h"
#include "base/math/math_util.h"
#include "base/math/polygon2d.h"
#include "base/testing/boost_geometry.h"
#include "base/testing/geometry.h"
#include "base/testing/random.h"

namespace math {
namespace {

constexpr double kInvalidPolygonArea = -1.0;

double ComputePolygonOverlapArea(const Polygon2d& polygon, const Polygon2d& other_polygon) {
  Polygon2d overlap_polygon;
  if (!polygon.ComputeOverlap(other_polygon, &overlap_polygon)) {
    return kInvalidPolygonArea;
  }
  return overlap_polygon.area();
}

}  // namespace

TEST(OrientedBox2dTest, GetTouchedGrids) {
  const auto get_touched_grids = [](const Vector2d& base_point,
                                    const Vector2d& center_point,
                                    double heading,
                                    double length,
                                    double width,
                                    double grid_resolution) {
    return OrientedBox2d(center_point, heading, length, width)
        .GetTouchedGrids(grid_resolution, base_point);
  };
  const auto get_touched_grids_polygon = [](const Vector2d& base_point,
                                            const Vector2d& center_point,
                                            double heading,
                                            double length,
                                            double width,
                                            double grid_resolution) {
    return Polygon2d(std::move(OrientedBox2d(center_point, heading, length, width)))
        .GetTouchedGrids(grid_resolution, base_point);
  };
  {
    const Vector2d center_point = {0.5, 0.5};
    const double heading = 0.0;
    const double length = 1.0;
    const double width = 1.0;
    ASSERT_THAT(get_touched_grids({0.0, 0.0}, center_point, heading, length, width, 0.5),
                testing::UnorderedElementsAreArray(std::vector<Vector2i>{{0, 0},
                                                                         {1, 0},
                                                                         {0, 1},
                                                                         {1, 1},
                                                                         {-1, 0},
                                                                         {-1, 1},
                                                                         {-1, 2},
                                                                         {-1, -1},
                                                                         {0, 2},
                                                                         {1, 2},
                                                                         {2, 2},
                                                                         {2, 1},
                                                                         {2, 0},
                                                                         {2, -1},
                                                                         {1, -1},
                                                                         {0, -1}}));
    ASSERT_THAT(get_touched_grids({0.0, 0.5}, center_point, heading, length, width, 0.5),
                testing::UnorderedElementsAreArray(std::vector<Vector2i>{{0, -1},
                                                                         {1, -1},
                                                                         {0, 0},
                                                                         {1, 0},
                                                                         {-1, -1},
                                                                         {-1, 0},
                                                                         {-1, 1},
                                                                         {-1, -2},
                                                                         {0, 1},
                                                                         {1, 1},
                                                                         {2, 1},
                                                                         {2, 0},
                                                                         {2, -1},
                                                                         {2, -2},
                                                                         {1, -2},
                                                                         {0, -2}}));
    ASSERT_THAT(get_touched_grids({0.0, 0.1}, center_point, heading, length, width, 0.5),
                testing::UnorderedElementsAreArray(std::vector<Vector2i>{{0, -1},
                                                                         {1, -1},
                                                                         {0, 0},
                                                                         {1, 0},
                                                                         {0, 1},
                                                                         {1, 1},
                                                                         {-1, -1},
                                                                         {-1, 0},
                                                                         {-1, 1},
                                                                         {2, -1},
                                                                         {2, 0},
                                                                         {2, 1}}));
    ASSERT_THAT(get_touched_grids({0.1, 0.1}, center_point, heading, length, width, 0.5),
                testing::UnorderedElementsAreArray(std::vector<Vector2i>{
                    {-1, -1}, {0, -1}, {1, -1}, {-1, 0}, {0, 0}, {1, 0}, {-1, 1}, {0, 1}, {1, 1}}));
    ASSERT_THAT(get_touched_grids({0.5, 0.5}, center_point, heading, length, width, 0.5),
                testing::UnorderedElementsAreArray(std::vector<Vector2i>{{-1, -1},
                                                                         {0, -1},
                                                                         {-1, 0},
                                                                         {0, 0},
                                                                         {-2, -1},
                                                                         {-2, 0},
                                                                         {-2, 1},
                                                                         {-2, -2},
                                                                         {-1, 1},
                                                                         {0, 1},
                                                                         {1, 1},
                                                                         {1, 0},
                                                                         {1, -1},
                                                                         {1, -2},
                                                                         {0, -2},
                                                                         {-1, -2}}));
    ASSERT_THAT(get_touched_grids({-0.5, -0.5}, center_point, heading, length, width, 0.5),
                testing::UnorderedElementsAreArray(std::vector<Vector2i>{{1, 1},
                                                                         {2, 1},
                                                                         {1, 2},
                                                                         {2, 2},
                                                                         {0, 1},
                                                                         {0, 2},
                                                                         {0, 3},
                                                                         {0, 0},
                                                                         {1, 3},
                                                                         {2, 3},
                                                                         {3, 3},
                                                                         {3, 2},
                                                                         {3, 1},
                                                                         {3, 0},
                                                                         {2, 0},
                                                                         {1, 0}}));
    ASSERT_THAT(get_touched_grids({0.0, 0.0}, center_point, heading, length, width, 2.0),
                testing::UnorderedElementsAreArray(
                    std::vector<Vector2i>{{0, 0}, {0, -1}, {-1, -1}, {-1, 0}}));
  }
  {
    const Vector2d center_point = {0.0, 0.0};
    const double heading = M_PI / 4;
    const double length = 3.0;
    const double width = 3.0;
    ASSERT_THAT(get_touched_grids({0.0, 0.0}, center_point, heading, length, width, 1.0),
                testing::UnorderedElementsAreArray(std::vector<Vector2i>{
                    {-1, -3}, {0, -3}, {-2, -2}, {-1, -2}, {0, -2}, {1, -2}, {-3, -1}, {-2, -1},
                    {-1, -1}, {0, -1}, {1, -1},  {2, -1},  {-3, 0}, {-2, 0}, {-1, 0},  {0, 0},
                    {1, 0},   {2, 0},  {-2, 1},  {-1, 1},  {0, 1},  {1, 1},  {-1, 2},  {0, 2}}));
  }
  {
    const Vector2d center_point = {0.5, 1.0};
    const double heading = M_PI / 4;
    const double length = 0.1;
    const double width = 0.1;
    ASSERT_THAT(get_touched_grids({0.0, 0.0}, center_point, heading, length, width, 1.0),
                testing::UnorderedElementsAreArray(std::vector<Vector2i>{{0, 0}, {0, 1}}));
  }
  {
    const Vector2d center_point = {0.15, 0.5};
    const double heading = 0.0;
    const double length = 0.1;
    const double width = 1.0;
    ASSERT_THAT(
        get_touched_grids({0.0, 0.0}, center_point, heading, length, width, 0.5),
        testing::UnorderedElementsAreArray(std::vector<Vector2i>{{0, 0}, {0, 1}, {0, -1}, {0, 2}}));
  }
  {
    const Vector2d center_point = {0.25, 0.5};
    const double heading = 0.0;
    const double length = 0.1;
    const double width = 1.0;
    ASSERT_THAT(
        get_touched_grids({0.0, 0.0}, center_point, heading, length, width, 0.5),
        testing::UnorderedElementsAreArray(std::vector<Vector2i>{{0, 0}, {0, 1}, {0, -1}, {0, 2}}));
  }
  {
    const int Iterations = 10;
    for (int i = 0; i < Iterations; ++i) {
      Vector2d center_point = testing::RandomPoint(10.0);
      double heading = testing::RandomDouble(0.0, M_PI_2);
      double length = testing::RandomDouble(0.0, 10.0);
      double width = testing::RandomDouble(0.0, 10.0);
      ASSERT_THAT(get_touched_grids({0.0, 0.0}, center_point, heading, length, width, 0.25),
                  testing::UnorderedElementsAreArray(get_touched_grids_polygon(
                      {0.0, 0.0}, center_point, heading, length, width, 0.25)));
    }
  }
}

TEST(OrientedBox2dTest, GetTouchedGridsCenters) {
  const auto get_touched_grids = [](const Vector2d& base_point,
                                    const Vector2d& center_point,
                                    double heading,
                                    double length,
                                    double width,
                                    double grid_resolution) {
    auto touched_grids_centers = OrientedBox2d(center_point, heading, length, width)
                                     .GetTouchedGridsCenters(grid_resolution, base_point);
    double grid_resolution_inverse = 1.0 / grid_resolution;
    std::vector<Vector2i> touched_grids;
    for (auto touched_grid_center : touched_grids_centers) {
      int xi =
          std::floor((touched_grid_center.x - 0.5 * grid_resolution) * grid_resolution_inverse);
      int yi =
          std::floor((touched_grid_center.y - 0.5 * grid_resolution) * grid_resolution_inverse);
      touched_grids.emplace_back(xi, yi);
    }
    return touched_grids;
  };
  {
    const Vector2d center_point = {0.5, 0.5};
    const double heading = 0.0;
    const double length = 1.0;
    const double width = 1.0;
    ASSERT_THAT(get_touched_grids({0.0, 0.0}, center_point, heading, length, width, 0.5),
                testing::UnorderedElementsAreArray(std::vector<Vector2i>{{0, 0},
                                                                         {1, 0},
                                                                         {0, 1},
                                                                         {1, 1},
                                                                         {-1, 0},
                                                                         {-1, 1},
                                                                         {-1, 2},
                                                                         {-1, -1},
                                                                         {0, 2},
                                                                         {1, 2},
                                                                         {2, 2},
                                                                         {2, 1},
                                                                         {2, 0},
                                                                         {2, -1},
                                                                         {1, -1},
                                                                         {0, -1}}));
    ASSERT_THAT(get_touched_grids({0.0, 0.5}, center_point, heading, length, width, 0.5),
                testing::UnorderedElementsAreArray(std::vector<Vector2i>{{0, -1},
                                                                         {1, -1},
                                                                         {0, 0},
                                                                         {1, 0},
                                                                         {-1, -1},
                                                                         {-1, 0},
                                                                         {-1, 1},
                                                                         {-1, -2},
                                                                         {0, 1},
                                                                         {1, 1},
                                                                         {2, 1},
                                                                         {2, 0},
                                                                         {2, -1},
                                                                         {2, -2},
                                                                         {1, -2},
                                                                         {0, -2}}));
    ASSERT_THAT(get_touched_grids({0.0, 0.1}, center_point, heading, length, width, 0.5),
                testing::UnorderedElementsAreArray(std::vector<Vector2i>{{0, -1},
                                                                         {1, -1},
                                                                         {0, 0},
                                                                         {1, 0},
                                                                         {0, 1},
                                                                         {1, 1},
                                                                         {-1, -1},
                                                                         {-1, 0},
                                                                         {-1, 1},
                                                                         {2, -1},
                                                                         {2, 0},
                                                                         {2, 1}}));
    ASSERT_THAT(get_touched_grids({0.1, 0.1}, center_point, heading, length, width, 0.5),
                testing::UnorderedElementsAreArray(std::vector<Vector2i>{
                    {-1, -1}, {0, -1}, {1, -1}, {-1, 0}, {0, 0}, {1, 0}, {-1, 1}, {0, 1}, {1, 1}}));
    ASSERT_THAT(get_touched_grids({0.5, 0.5}, center_point, heading, length, width, 0.5),
                testing::UnorderedElementsAreArray(std::vector<Vector2i>{{-1, -1},
                                                                         {0, -1},
                                                                         {-1, 0},
                                                                         {0, 0},
                                                                         {-2, -1},
                                                                         {-2, 0},
                                                                         {-2, 1},
                                                                         {-2, -2},
                                                                         {-1, 1},
                                                                         {0, 1},
                                                                         {1, 1},
                                                                         {1, 0},
                                                                         {1, -1},
                                                                         {1, -2},
                                                                         {0, -2},
                                                                         {-1, -2}}));
    ASSERT_THAT(get_touched_grids({-0.5, -0.5}, center_point, heading, length, width, 0.5),
                testing::UnorderedElementsAreArray(std::vector<Vector2i>{{1, 1},
                                                                         {2, 1},
                                                                         {1, 2},
                                                                         {2, 2},
                                                                         {0, 1},
                                                                         {0, 2},
                                                                         {0, 3},
                                                                         {0, 0},
                                                                         {1, 3},
                                                                         {2, 3},
                                                                         {3, 3},
                                                                         {3, 2},
                                                                         {3, 1},
                                                                         {3, 0},
                                                                         {2, 0},
                                                                         {1, 0}}));
    ASSERT_THAT(get_touched_grids({0.0, 0.0}, center_point, heading, length, width, 2.0),
                testing::UnorderedElementsAreArray(
                    std::vector<Vector2i>{{0, 0}, {0, -1}, {-1, -1}, {-1, 0}}));
  }
  {
    const Vector2d center_point = {0.0, 0.0};
    const double heading = M_PI / 4;
    const double length = 3.0;
    const double width = 3.0;
    ASSERT_THAT(get_touched_grids({0.0, 0.0}, center_point, heading, length, width, 1.0),
                testing::UnorderedElementsAreArray(std::vector<Vector2i>{
                    {-1, -3}, {0, -3}, {-2, -2}, {-1, -2}, {0, -2}, {1, -2}, {-3, -1}, {-2, -1},
                    {-1, -1}, {0, -1}, {1, -1},  {2, -1},  {-3, 0}, {-2, 0}, {-1, 0},  {0, 0},
                    {1, 0},   {2, 0},  {-2, 1},  {-1, 1},  {0, 1},  {1, 1},  {-1, 2},  {0, 2}}));
  }
  {
    const Vector2d center_point = {0.5, 1.0};
    const double heading = M_PI / 4;
    const double length = 0.1;
    const double width = 0.1;
    ASSERT_THAT(get_touched_grids({0.0, 0.0}, center_point, heading, length, width, 1.0),
                testing::UnorderedElementsAreArray(std::vector<Vector2i>{{0, 0}, {0, 1}}));
  }
  {
    const Vector2d center_point = {0.15, 0.5};
    const double heading = 0.0;
    const double length = 0.1;
    const double width = 1.0;
    ASSERT_THAT(
        get_touched_grids({0.0, 0.0}, center_point, heading, length, width, 0.5),
        testing::UnorderedElementsAreArray(std::vector<Vector2i>{{0, 0}, {0, 1}, {0, -1}, {0, 2}}));
  }
  {
    const Vector2d center_point = {0.25, 0.5};
    const double heading = 0.0;
    const double length = 0.1;
    const double width = 1.0;
    ASSERT_THAT(
        get_touched_grids({0.0, 0.0}, center_point, heading, length, width, 0.5),
        testing::UnorderedElementsAreArray(std::vector<Vector2i>{{0, 0}, {0, 1}, {0, -1}, {0, 2}}));
  }
}

TEST(OrientedBox2dTest, Constructors) {
  OrientedBox2d box0({4.0, 5.0}, M_PI_4, 3.0, 4.0);
  ASSERT_TRUE(box0.center().IsNear({4.0, 5.0}));
  ASSERT_TRUE(box0.direction().IsNear(Vector2d::FromAngle(M_PI_4)));
  ASSERT_NEAR(box0.heading(), M_PI_4, 1e-6);
  ASSERT_NEAR(box0.cos_heading(), 1.0 / std::sqrt(2.0), 1e-6);
  ASSERT_NEAR(box0.sin_heading(), 1.0 / std::sqrt(2.0), 1e-6);
  ASSERT_NEAR(box0.length(), 3.0, 1e-6);
  ASSERT_NEAR(box0.width(), 4.0, 1e-6);
  ASSERT_NEAR(box0.area(), 12.0, 1e-6);
  ASSERT_NEAR(box0.half_length(), 1.5, 1e-6);
  ASSERT_NEAR(box0.half_width(), 2.0, 1e-6);
  ASSERT_NEAR(box0.min_x(), 4.0 - 3.5 / std::sqrt(2.0), 1e-6);
  ASSERT_NEAR(box0.max_x(), 4.0 + 3.5 / std::sqrt(2.0), 1e-6);
  ASSERT_NEAR(box0.min_y(), 5.0 - 3.5 / std::sqrt(2.0), 1e-6);
  ASSERT_NEAR(box0.max_y(), 5.0 + 3.5 / std::sqrt(2.0), 1e-6);
  ASSERT_NEAR(box0.GetDiagonal(), 5.0, 1e-6);
  ASSERT_THAT(box0.GetCorners(),
              testing::ElementsAre(box0.GetFrontRightCorner(),
                                   box0.GetFrontLeftCorner(),
                                   box0.GetRearLeftCorner(),
                                   box0.GetRearRightCorner()));
  ASSERT_EQ(box0.ToString(), "OrientedBox2d(c=(4.000000, 5.000000), size(3.000000, 4.000000))");

  OrientedBox2d box1(LineSegment2d({3.0, 6.0}, {7.0, 2.0}), 2.0);
  ASSERT_TRUE(box1.center().IsNear({5.0, 4.0}));
  ASSERT_TRUE(box1.direction().IsNear(Vector2d::FromAngle(-M_PI_4)));
  ASSERT_NEAR(box1.heading(), -M_PI_4, 1e-6);
  ASSERT_NEAR(box1.cos_heading(), +1.0 / std::sqrt(2.0), 1e-6);
  ASSERT_NEAR(box1.sin_heading(), -1.0 / std::sqrt(2.0), 1e-6);
  ASSERT_NEAR(box1.length(), 4.0 * std::sqrt(2.0), 1e-6);
  ASSERT_NEAR(box1.width(), 2.0, 1e-6);
  ASSERT_NEAR(box1.area(), 8.0 * std::sqrt(2.0), 1e-6);
  ASSERT_NEAR(box1.half_length(), 2.0 * std::sqrt(2.0), 1e-6);
  ASSERT_NEAR(box1.half_width(), 1.0, 1e-6);
  ASSERT_NEAR(box1.min_x(), 3.0 - std::sqrt(0.5), 1e-6);
  ASSERT_NEAR(box1.max_x(), 7.0 + std::sqrt(0.5), 1e-6);
  ASSERT_NEAR(box1.min_y(), 2.0 - std::sqrt(0.5), 1e-6);
  ASSERT_NEAR(box1.max_y(), 6.0 + std::sqrt(0.5), 1e-6);
  ASSERT_NEAR(box1.GetDiagonal(), 6.0, 1e-6);
  ASSERT_THAT(box1.GetCorners(),
              testing::ElementsAre(Vector2d(7.0 - std::sqrt(0.5), 2.0 - std::sqrt(0.5)),
                                   Vector2d(7.0 + std::sqrt(0.5), 2.0 + std::sqrt(0.5)),
                                   Vector2d(3.0 + std::sqrt(0.5), 6.0 + std::sqrt(0.5)),
                                   Vector2d(3.0 - std::sqrt(0.5), 6.0 - std::sqrt(0.5))));
  ASSERT_THAT(box1.GetCorners(),
              testing::ElementsAre(box1.GetFrontRightCorner(),
                                   box1.GetFrontLeftCorner(),
                                   box1.GetRearLeftCorner(),
                                   box1.GetRearRightCorner()));
  ASSERT_EQ(box1.ToString(), "OrientedBox2d(c=(5.000000, 4.000000), size(5.656854, 2.000000))");

  OrientedBox2d box2(LineSegment2d({-4.0, 4.0}, {3.0, -3.0}));
  ASSERT_TRUE(box2.center().IsNear({-0.5, 0.5}));
  ASSERT_TRUE(box2.direction().IsNear(Vector2d::FromAngle(-M_PI_4)));
  ASSERT_NEAR(box2.heading(), -M_PI_4, 1e-6);
  ASSERT_NEAR(box2.cos_heading(), +1.0 / std::sqrt(2.0), 1e-6);
  ASSERT_NEAR(box2.sin_heading(), -1.0 / std::sqrt(2.0), 1e-6);
  ASSERT_NEAR(box2.length(), 7.0 * std::sqrt(2.0), 1e-6);
  ASSERT_NEAR(box2.width(), 0.0, 1e-6);
  ASSERT_NEAR(box2.area(), 0.0, 1e-6);
  ASSERT_NEAR(box2.half_length(), 3.5 * std::sqrt(2.0), 1e-6);
  ASSERT_NEAR(box2.half_width(), 0.0, 1e-6);
  ASSERT_NEAR(box2.min_x(), -4.0, 1e-6);
  ASSERT_NEAR(box2.max_x(), +3.0, 1e-6);
  ASSERT_NEAR(box2.min_y(), -3.0, 1e-6);
  ASSERT_NEAR(box2.max_y(), +4.0, 1e-6);
  ASSERT_NEAR(box2.GetDiagonal(), 7.0 * std::sqrt(2.0), 1e-6);
  ASSERT_THAT(
      box2.GetCorners(),
      testing::ElementsAre(
          Vector2d(3.0, -3.0), Vector2d(3.0, -3.0), Vector2d(-4.0, 4.0), Vector2d(-4.0, 4.0)));

  OrientedBox2d box3(AxisAlignBox2d({1.0, 0.0}, 4.0, 2.0));
  ASSERT_TRUE(box3.center().IsNear({1.0, 0.0}));
  ASSERT_TRUE(box3.direction().IsNear({1.0, 0.0}));
  ASSERT_NEAR(box3.heading(), 0.0, 1e-6);
  ASSERT_NEAR(box3.cos_heading(), 1.0, 1e-6);
  ASSERT_NEAR(box3.sin_heading(), 0.0, 1e-6);
  ASSERT_NEAR(box3.length(), 4.0, 1e-6);
  ASSERT_NEAR(box3.width(), 2.0, 1e-6);
  ASSERT_NEAR(box3.area(), 8.0, 1e-6);
  ASSERT_NEAR(box3.half_length(), 2.0, 1e-6);
  ASSERT_NEAR(box3.half_width(), 1.0, 1e-6);
  ASSERT_NEAR(box3.min_x(), -1.0, 1e-6);
  ASSERT_NEAR(box3.max_x(), +3.0, 1e-6);
  ASSERT_NEAR(box3.min_y(), -1.0, 1e-6);
  ASSERT_NEAR(box3.max_y(), +1.0, 1e-6);
  ASSERT_NEAR(box3.GetDiagonal(), std::sqrt(20.0), 1e-6);
  ASSERT_THAT(
      box3.GetCorners(),
      testing::ElementsAre(
          Vector2d(+3.0, -1.0), Vector2d(+3.0, +1.0), Vector2d(-1.0, +1.0), Vector2d(-1.0, -1.0)));

  OrientedBox2d box4 = OrientedBox2d::FromAABox({3.0, 1.0}, {-1.0, -1.0});
  ASSERT_TRUE(box4.center().IsNear({1.0, 0.0}));
  ASSERT_TRUE(box4.direction().IsNear({1.0, 0.0}));
  ASSERT_NEAR(box4.heading(), 0.0, 1e-6);
  ASSERT_NEAR(box4.cos_heading(), 1.0, 1e-6);
  ASSERT_NEAR(box4.sin_heading(), 0.0, 1e-6);
  ASSERT_NEAR(box4.length(), 4.0, 1e-6);
  ASSERT_NEAR(box4.width(), 2.0, 1e-6);
  ASSERT_NEAR(box4.area(), 8.0, 1e-6);
  ASSERT_NEAR(box4.half_length(), 2.0, 1e-6);
  ASSERT_NEAR(box4.half_width(), 1.0, 1e-6);
  ASSERT_NEAR(box4.min_x(), -1.0, 1e-6);
  ASSERT_NEAR(box4.max_x(), +3.0, 1e-6);
  ASSERT_NEAR(box4.min_y(), -1.0, 1e-6);
  ASSERT_NEAR(box4.max_y(), +1.0, 1e-6);
  ASSERT_NEAR(box4.GetDiagonal(), std::sqrt(20.0), 1e-6);
  ASSERT_THAT(
      box4.GetCorners(),
      testing::ElementsAre(
          Vector2d(+3.0, -1.0), Vector2d(+3.0, +1.0), Vector2d(-1.0, +1.0), Vector2d(-1.0, -1.0)));

  OrientedBox2d box5({10.0, 10.0}, 0.0, 2.0, 2.0);
  const auto& aabox5 = box5.aabox();
  ASSERT_NEAR(9.0, aabox5.min_x(), 1e-6);
  ASSERT_NEAR(11.0, aabox5.max_x(), 1e-6);
  ASSERT_NEAR(9.0, aabox5.min_y(), 1e-6);
  ASSERT_NEAR(11.0, aabox5.max_y(), 1e-6);

  OrientedBox2d box6({-10.0, -10.0}, 0.0, 2.0, 2.0);
  const auto& aabox6 = box6.aabox();
  ASSERT_NEAR(-9.0, aabox6.max_x(), 1e-6);
  ASSERT_NEAR(-11.0, aabox6.min_x(), 1e-6);
  ASSERT_NEAR(-9.0, aabox6.max_y(), 1e-6);
  ASSERT_NEAR(-11.0, aabox6.min_y(), 1e-6);
}

TEST(OrientedBox2dTest, GetCorners) {
  const OrientedBox2d box0({0.0, 0.0}, 0.0, 4.0, 2.0);
  ASSERT_THAT(
      box0.GetCorners(),
      testing::ElementsAre(
          Vector2d(+2.0, -1.0), Vector2d(+2.0, +1.0), Vector2d(-2.0, +1.0), Vector2d(-2.0, -1.0)));
  ASSERT_THAT(box0.GetCorners(),
              testing::ElementsAre(box0.GetFrontRightCorner(),
                                   box0.GetFrontLeftCorner(),
                                   box0.GetRearLeftCorner(),
                                   box0.GetRearRightCorner()));

  ASSERT_THAT(box0.GetCornersAsArray(),
              testing::ElementsAre(box0.GetFrontRightCorner(),
                                   box0.GetFrontLeftCorner(),
                                   box0.GetRearLeftCorner(),
                                   box0.GetRearRightCorner()));

  const OrientedBox2d box1(LineSegment2d({2.0, 3.0}, {6.0, 3.0}), 2.0);
  ASSERT_THAT(
      box1.GetCorners(),
      testing::ElementsAre(
          Vector2d(+6.0, +2.0), Vector2d(+6.0, +4.0), Vector2d(+2.0, +4.0), Vector2d(+2.0, +2.0)));
  ASSERT_THAT(box1.GetCorners(),
              testing::ElementsAre(box1.GetFrontRightCorner(),
                                   box1.GetFrontLeftCorner(),
                                   box1.GetRearLeftCorner(),
                                   box1.GetRearRightCorner()));

  ASSERT_THAT(box1.GetCornersAsArray(),
              testing::ElementsAre(box1.GetFrontRightCorner(),
                                   box1.GetFrontLeftCorner(),
                                   box1.GetRearLeftCorner(),
                                   box1.GetRearRightCorner()));
}

TEST(OrientedBox2dTest, GetLineSegments) {
  const OrientedBox2d box0({0.0, 0.0}, 0.0, 4.0, 2.0);
  const std::vector<LineSegment2d> line_segments0 = box0.GetLineSegments();
  ASSERT_EQ(line_segments0.size(), 4);
  ASSERT_THAT(line_segments0[0].endpoints(),
              testing::ElementsAre(Vector2d(2.0, -1.0), Vector2d(2.0, 1.0)));
  ASSERT_THAT(line_segments0[1].endpoints(),
              testing::ElementsAre(Vector2d(2.0, 1.0), Vector2d(-2.0, 1.0)));
  ASSERT_THAT(line_segments0[2].endpoints(),
              testing::ElementsAre(Vector2d(-2.0, 1.0), Vector2d(-2.0, -1.0)));
  ASSERT_THAT(line_segments0[3].endpoints(),
              testing::ElementsAre(Vector2d(-2.0, -1.0), Vector2d(2.0, -1.0)));

  const LineSegment2d front_line_segment = box0.GetFrontLineSegment();
  const LineSegment2d left_line_segment = box0.GetLeftLineSegment();
  const LineSegment2d back_line_segment = box0.GetBackLineSegment();
  const LineSegment2d right_line_segment = box0.GetRightLineSegment();
  ASSERT_THAT(front_line_segment.endpoints(),
              testing::ElementsAre(Vector2d(2.0, -1.0), Vector2d(2.0, 1.0)));
  ASSERT_THAT(left_line_segment.endpoints(),
              testing::ElementsAre(Vector2d(2.0, 1.0), Vector2d(-2.0, 1.0)));
  ASSERT_THAT(back_line_segment.endpoints(),
              testing::ElementsAre(Vector2d(-2.0, 1.0), Vector2d(-2.0, -1.0)));
  ASSERT_THAT(right_line_segment.endpoints(),
              testing::ElementsAre(Vector2d(-2.0, -1.0), Vector2d(2.0, -1.0)));
}

TEST(OrientedBox2dTest, Transform) {
  const Vector2d center0(3.0, 3.0);
  const OrientedBox2d box0(center0, 0.0, 2.0, 3.0);

  const Vector2d shift0(2.0, 1.0);
  const OrientedBox2d box0_a = box0.Shifted(shift0);
  const OrientedBox2d box0_b(center0 + shift0, 0.0, 2.0, 3.0);
  ASSERT_TRUE(box0_a.center().IsNear(box0_b.center()));
  ASSERT_TRUE(box0_a.direction().IsNear(box0_b.direction()));
  ASSERT_NEAR(box0_a.length(), box0_b.length(), 1e-6);
  ASSERT_NEAR(box0_a.width(), box0_b.width(), 1e-6);
  ASSERT_THAT(box0_a.GetCorners(), testing::ElementsAreArray(box0_b.GetCorners()));

  OrientedBox2d box0_c = box0;
  box0_c.Shift(shift0);
  ASSERT_TRUE(box0_c.center().IsNear(box0_b.center()));
  ASSERT_TRUE(box0_c.direction().IsNear(box0_b.direction()));
  ASSERT_NEAR(box0_c.length(), box0_b.length(), 1e-6);
  ASSERT_NEAR(box0_c.width(), box0_b.width(), 1e-6);
  ASSERT_THAT(box0_c.GetCorners(), testing::ElementsAreArray(box0_b.GetCorners()));

  const Vector2d center1(3.0, 3.0);
  const OrientedBox2d box1(center1, 0.1, 2.0, 3.0);
  const OrientedBox2d box1_a = box1.Rotated(4.5);
  const OrientedBox2d box1_b(center1, 4.6, 2.0, 3.0);

  ASSERT_TRUE(box1_a.center().IsNear(box1_b.center()));
  ASSERT_TRUE(box1_a.direction().IsNear(box1_b.direction()));
  ASSERT_NEAR(box1_a.length(), box1_b.length(), 1e-6);
  ASSERT_NEAR(box1_a.width(), box1_b.width(), 1e-6);
  ASSERT_THAT(box1_a.GetCorners(), testing::ElementsAreArray(box1_b.GetCorners()));

  OrientedBox2d box1_c = box1;
  box1_c.Rotate(center1, 4.5);
  ASSERT_TRUE(box1_c.center().IsNear(box1_b.center()));
  ASSERT_TRUE(box1_c.direction().IsNear(box1_b.direction()));
  ASSERT_NEAR(box1_c.length(), box1_b.length(), 1e-6);
  ASSERT_NEAR(box1_c.width(), box1_b.width(), 1e-6);
  ASSERT_THAT(box1_c.GetCorners(), testing::ElementsAreArray(box1_b.GetCorners()));

  const Vector2d center2(2.0, 3.0);
  const OrientedBox2d box2(center2, 3.2, 2.0, 3.0);
  const OrientedBox2d box2_a = box2.Extended(3.0, -1.0);
  const OrientedBox2d box2_b(center2, 3.2, 5.0, 2.0);

  ASSERT_TRUE(box2_a.center().IsNear(box2_b.center()));
  ASSERT_TRUE(box2_a.direction().IsNear(box2_b.direction()));
  ASSERT_NEAR(box2_a.length(), box2_b.length(), 1e-6);
  ASSERT_NEAR(box2_a.width(), box2_b.width(), 1e-6);
  ASSERT_THAT(box2_a.GetCorners(), testing::ElementsAreArray(box2_b.GetCorners()));

  OrientedBox2d box2_c = box2;
  box2_c.LongitudinalExtend(3.0);
  box2_c.LateralExtend(-1.0);
  ASSERT_TRUE(box2_c.center().IsNear(box2_b.center()));
  ASSERT_TRUE(box2_c.direction().IsNear(box2_b.direction()));
  ASSERT_NEAR(box2_c.length(), box2_b.length(), 1e-6);
  ASSERT_NEAR(box2_c.width(), box2_b.width(), 1e-6);
  ASSERT_THAT(box2_c.GetCorners(), testing::ElementsAreArray(box2_b.GetCorners()));
}

TEST(OrientedBox2dTest, IsPointInOrOnBoundary) {
  const OrientedBox2d box0({0.0, 0.0}, 0.0, 4.0, 2.0);
  ASSERT_TRUE(box0.IsPointIn({0.0, 0.0}));
  ASSERT_TRUE(box0.IsPointIn({1.0, 0.5}));
  ASSERT_TRUE(box0.IsPointIn({-0.5, -1.0}));
  ASSERT_TRUE(box0.IsPointIn({2.0, 1.0}));
  ASSERT_FALSE(box0.IsPointIn({-3.0, 0.0}));
  ASSERT_FALSE(box0.IsPointIn({0.0, 2.0}));
  ASSERT_FALSE(box0.IsPointIn({-4.0, -2.0}));

  ASSERT_FALSE(box0.IsPointOnBoundary({0.0, 0.0}));
  ASSERT_FALSE(box0.IsPointOnBoundary({1.0, 0.5}));
  ASSERT_TRUE(box0.IsPointOnBoundary({-0.5, -1.0}));
  ASSERT_TRUE(box0.IsPointOnBoundary({2.0, 0.5}));
  ASSERT_TRUE(box0.IsPointOnBoundary({-2.0, 1.0}));
  ASSERT_FALSE(box0.IsPointOnBoundary({-3.0, 0.0}));
  ASSERT_FALSE(box0.IsPointOnBoundary({0.0, 2.0}));
  ASSERT_FALSE(box0.IsPointOnBoundary({-4.0, -2.0}));
}

TEST(OrientedBox2dTest, HasOverlapWithSegment) {
  const OrientedBox2d box0 = OrientedBox2d::FromAABox({2.0, 2.0}, {4.0, 4.0});
  ASSERT_TRUE(box0.HasOverlap(LineSegment2d({3.0, 3.0}, {3.0, 3.0})));
  ASSERT_FALSE(box0.HasOverlap(LineSegment2d({5.0, 5.0}, {5.0, 5.0})));
  ASSERT_TRUE(box0.HasOverlap(LineSegment2d({3.0, 3.0}, {0.0, 0.0})));
  ASSERT_TRUE(box0.HasOverlap(LineSegment2d({7.0, 2.0}, {3.5, 3.5})));
  ASSERT_TRUE(box0.HasOverlap(LineSegment2d({0.0, 3.0}, {10.0, 3.0})));
  ASSERT_TRUE(box0.HasOverlap(LineSegment2d({3.0, 0.0}, {3.0, 10.0})));
  ASSERT_TRUE(box0.HasOverlap(LineSegment2d({1.1, 3.0}, {3.0, 1.1})));
  ASSERT_FALSE(box0.HasOverlap(LineSegment2d({1.0, 0.0}, {1.0, 10.0})));
  ASSERT_FALSE(box0.HasOverlap(LineSegment2d({0.0, 6.0}, {10.0, 6.0})));
  ASSERT_FALSE(box0.HasOverlap(LineSegment2d({0.9, 3.0}, {3.0, 0.9})));
  ASSERT_TRUE(box0.HasOverlap(LineSegment2d({3.0, 3.0}, {3.0, 3.0})));
  ASSERT_FALSE(box0.HasOverlap(LineSegment2d({1.0, 3.0}, {1.0, 3.0})));
  ASSERT_FALSE(box0.HasOverlap(LineSegment2d({1.0, 1.0}, {1.0, 1.0})));
  ASSERT_FALSE(box0.HasOverlap(LineSegment2d({5.0, 3.0}, {5.0, 3.0})));
  ASSERT_FALSE(box0.HasOverlap(LineSegment2d({1.0, 5.0}, {1.0, 5.0})));

  const OrientedBox2d box1({0.0, 0.0}, 0.0, 4.0, 2.0);
  ASSERT_TRUE(box1.HasOverlap(LineSegment2d({0.0, 0.0}, {1.0, 1.0})));
  ASSERT_TRUE(box1.HasOverlap(LineSegment2d({0.0, 0.0}, {3.0, 3.0})));
  ASSERT_TRUE(box1.HasOverlap(LineSegment2d({0.0, -3.0}, {0.0, 3.0})));
  ASSERT_TRUE(box1.HasOverlap(LineSegment2d({4.0, 0.0}, {-4.0, 0.0})));
  ASSERT_TRUE(box1.HasOverlap(LineSegment2d({-4.0, -4.0}, {4.0, 4.0})));
  ASSERT_TRUE(box1.HasOverlap(LineSegment2d({4.0, -4.0}, {-4.0, 4.0})));
  ASSERT_TRUE(box1.HasOverlap(LineSegment2d({2.0, 0.0}, {2.0, 1.0})));
  ASSERT_TRUE(box1.HasOverlap(LineSegment2d({0.0, 1.0}, {2.0, 1.0})));
  ASSERT_TRUE(box1.HasOverlap(LineSegment2d({4.0, 0.0}, {0.0, 2.0})));
  ASSERT_TRUE(box1.HasOverlap(LineSegment2d({-4.0, 0.0}, {0.0, 2.0})));
  ASSERT_TRUE(box1.HasOverlap(LineSegment2d({4.0, 0.0}, {0.0, -2.0})));
  ASSERT_FALSE(
      box1.HasOverlap(LineSegment2d({-4.0 - math::kEpsilon, 0.0}, {0.0, -2.0 - math::kEpsilon})));
  ASSERT_FALSE(
      box1.HasOverlap(LineSegment2d({4.0 + math::kEpsilon, 0.0}, {0.0, 2.0 + math::kEpsilon})));
  ASSERT_FALSE(
      box1.HasOverlap(LineSegment2d({-4.0 - math::kEpsilon, 0.0}, {0.0, 2.0 + math::kEpsilon})));
  ASSERT_FALSE(
      box1.HasOverlap(LineSegment2d({4.0 + math::kEpsilon, 0.0}, {0.0, -2.0 - math::kEpsilon})));
  ASSERT_FALSE(box1.HasOverlap(LineSegment2d({-4.0, -4.0}, {4.0, -4.0})));
  ASSERT_FALSE(box1.HasOverlap(LineSegment2d({4.0, -4.0}, {4.0, 4.0})));
}

TEST(OrientedBox2dTest, OverlapWithBox1) {
  const OrientedBox2d box1({0.0, 0.0}, 0.0, 4.0, 2.0);
  const OrientedBox2d box2({5.0, 2.0}, 0.0, 4.0, 2.0);
  const OrientedBox2d box3(LineSegment2d({2.0, 3.0}, {6.0, 3.0}), 2.0);
  const OrientedBox2d box4({7.0, 8.0}, M_PI_4, 5.0, 3.0);
  ASSERT_FALSE(box1.HasOverlap(box2));
  ASSERT_FALSE(box1.HasOverlap(box3));
  ASSERT_FALSE(box1.HasOverlap(box4));
  ASSERT_FALSE(box2.HasOverlap(box4));
  ASSERT_FALSE(box3.HasOverlap(box4));
}

TEST(OrientedBox2dTest, OverlapWithBox2) {
  const OrientedBox2d box1 = OrientedBox2d::FromAABox({-2.0, -2.0}, {2.0, 2.0});
  const OrientedBox2d box2 = OrientedBox2d::FromAABox({-4.0, -4.0}, {4.0, 4.0});
  const OrientedBox2d box3 = OrientedBox2d::FromAABox({0.0, 0.0}, {1.0, 1.0});
  const OrientedBox2d box4({4.0, 4.0}, M_PI_4, 0.5, 0.5);
  ASSERT_TRUE(box1.HasOverlap(box2));
  ASSERT_TRUE(box1.HasOverlap(box3));
  ASSERT_FALSE(box1.HasOverlap(box4));
  ASSERT_TRUE(box2.HasOverlap(box3));
  ASSERT_TRUE(box2.HasOverlap(box4));
  ASSERT_FALSE(box3.HasOverlap(box4));
}

TEST(OrientedBox2dTest, ComputeOverlapAreaWithBox) {
  const OrientedBox2d box1 = OrientedBox2d::FromAABox({-2.0, -2.0}, {2.0, 2.0});
  const OrientedBox2d box2 = OrientedBox2d::FromAABox({-4.0, -4.0}, {4.0, 4.0});
  const OrientedBox2d box3 = OrientedBox2d::FromAABox({0.0, 0.0}, {1.0, 1.0});
  const OrientedBox2d box4({4.0, 4.0}, M_PI_4, 0.5, 0.5);
  const OrientedBox2d box5({4.0, 4.0}, M_PI_2, 1.5, 0.5);
  const OrientedBox2d box6({3.0, 5.0}, M_PI, 4.5, 3.5);
  const Polygon2d box1_polygon(box1);
  const Polygon2d box2_polygon(box2);
  const Polygon2d box3_polygon(box3);
  const Polygon2d box4_polygon(box4);
  const Polygon2d box5_polygon(box5);
  const Polygon2d box6_polygon(box6);

  ASSERT_NEAR(box1.ComputeOverlapAreaWith(box1), 16.0, math::kEpsilon);
  ASSERT_NEAR(box1.ComputeOverlapAreaWith(box2), box2.ComputeOverlapAreaWith(box1), math::kEpsilon);
  ASSERT_NEAR(box1.ComputeOverlapAreaWith(box2), ComputePolygonOverlapArea(box1_polygon, box2_polygon), math::kEpsilon);
  ASSERT_NEAR(box1.ComputeOverlapAreaWith(box3), ComputePolygonOverlapArea(box1_polygon, box3_polygon), math::kEpsilon);
  ASSERT_NEAR(box1.ComputeOverlapAreaWith(box4), 0.0, math::kEpsilon);
  ASSERT_NEAR(box3.ComputeOverlapAreaWith(box2), ComputePolygonOverlapArea(box2_polygon, box3_polygon), math::kEpsilon);
  ASSERT_NEAR(box4.ComputeOverlapAreaWith(box2), ComputePolygonOverlapArea(box2_polygon, box4_polygon), math::kEpsilon);
  ASSERT_NEAR(box3.ComputeOverlapAreaWith(box4), 0.0, math::kEpsilon);

  ASSERT_NEAR(box4.ComputeOverlapAreaWith(box5), ComputePolygonOverlapArea(box4_polygon, box5_polygon), math::kEpsilon);
  ASSERT_NEAR(box4.ComputeOverlapAreaWith(box6), ComputePolygonOverlapArea(box4_polygon, box6_polygon), math::kEpsilon);
  ASSERT_NEAR(box6.ComputeOverlapAreaWith(box6), ComputePolygonOverlapArea(box6_polygon, box6_polygon), math::kEpsilon);
}

TEST(OrientedBox2dTest, ComputeIoUWithBox) {
  const OrientedBox2d box1 = OrientedBox2d::FromAABox({-2.0, -2.0}, {2.0, 2.0});
  const OrientedBox2d box2 = OrientedBox2d::FromAABox({-4.0, -4.0}, {4.0, 4.0});
  const OrientedBox2d box3 = OrientedBox2d::FromAABox({0.0, 0.0}, {1.0, 1.0});
  const OrientedBox2d box4({4.0, 4.0}, M_PI_4, 0.5, 0.5);
  const OrientedBox2d box5({4.0, 4.0}, M_PI_2, 1.5, 0.5);
  const OrientedBox2d box6({3.0, 5.0}, M_PI, 4.5, 3.5);
  const Polygon2d box1_polygon(box1);
  const Polygon2d box2_polygon(box2);
  const Polygon2d box3_polygon(box3);
  const Polygon2d box4_polygon(box4);
  const Polygon2d box5_polygon(box5);
  const Polygon2d box6_polygon(box6);
  ASSERT_NEAR(box1.ComputeIoUWith(box1), 1.0, math::kEpsilon);
  ASSERT_NEAR(box1.ComputeIoUWith(box2), box2.ComputeIoUWith(box1), math::kEpsilon);
  ASSERT_NEAR(box1.ComputeIoUWith(box2), box1_polygon.ComputeIoUWith(box2_polygon), math::kEpsilon);
  ASSERT_NEAR(box1.ComputeIoUWith(box3), box1_polygon.ComputeIoUWith(box3_polygon), math::kEpsilon);
  ASSERT_NEAR(box1.ComputeIoUWith(box4), 0.0, math::kEpsilon);
  ASSERT_NEAR(box3.ComputeIoUWith(box2), box2_polygon.ComputeIoUWith(box3_polygon), math::kEpsilon);
  ASSERT_NEAR(box4.ComputeIoUWith(box2), box2_polygon.ComputeIoUWith(box4_polygon), math::kEpsilon);
  ASSERT_NEAR(box3.ComputeIoUWith(box4), 0.0, math::kEpsilon);

  ASSERT_NEAR(box4.ComputeIoUWith(box5), box4_polygon.ComputeIoUWith(box5_polygon), math::kEpsilon);
  ASSERT_NEAR(box4.ComputeIoUWith(box6), box4_polygon.ComputeIoUWith(box6_polygon), math::kEpsilon);
  ASSERT_NEAR(box6.ComputeIoUWith(box6), box6_polygon.ComputeIoUWith(box6_polygon), math::kEpsilon);
}

TEST(OrientedBox2dTest, DistanceToPoint) {
  const OrientedBox2d box0({0.0, 0.0}, 0.0, 4.0, 2.0);
  ASSERT_NEAR(box0.DistanceTo({3.0, 0.0}), 1.0, 1e-6);
  ASSERT_NEAR(box0.DistanceTo({-3.0, 0.0}), 1.0, 1e-6);
  ASSERT_NEAR(box0.DistanceTo({0.0, 2.0}), 1.0, 1e-6);
  ASSERT_NEAR(box0.DistanceTo({0.0, -2.0}), 1.0, 1e-6);
  ASSERT_NEAR(box0.DistanceTo({0.0, 0.0}), 0.0, 1e-6);
  ASSERT_NEAR(box0.DistanceTo({0.0, 1.0}), 0.0, 1e-6);
  ASSERT_NEAR(box0.DistanceTo({1.0, 0.0}), 0.0, 1e-6);
  ASSERT_NEAR(box0.DistanceTo({0.0, -1.0}), 0.0, 1e-6);
  ASSERT_NEAR(box0.DistanceTo({-1.0, 0.0}), 0.0, 1e-6);

  const OrientedBox2d box1 = OrientedBox2d::FromAABox({3.0, -2.0}, {-3.0, 2.0});
  ASSERT_NEAR(0.0, box1.DistanceTo({0.0, 0.0}), 1e-6);
  ASSERT_NEAR(0.0, box1.DistanceSquareTo({0.0, 0.0}), 1e-6);
  ASSERT_NEAR(0.0, box1.DistanceTo({3.0, 1.5}), 1e-6);
  ASSERT_NEAR(0.0, box1.DistanceSquareTo({3.0, 1.5}), 1e-6);
  ASSERT_NEAR(0.0, box1.DistanceTo({-2.0, -0.4}), 1e-6);
  ASSERT_NEAR(0.0, box1.DistanceSquareTo({-2.0, -0.4}), 1e-6);
  ASSERT_NEAR(0.0, box1.DistanceTo({3.0, 2.0}), 1e-6);
  ASSERT_NEAR(0.0, box1.DistanceSquareTo({3.0, 2.0}), 1e-6);
  ASSERT_NEAR(0.7, box1.DistanceTo({3.7, 0.0}), 1e-6);
  ASSERT_NEAR(0.49, box1.DistanceSquareTo({3.7, 0.0}), 1e-6);
  ASSERT_NEAR(2.0, box1.DistanceTo({-5.0, 1.0}), 1e-6);
  ASSERT_NEAR(4.0, box1.DistanceSquareTo({-5.0, 1.0}), 1e-6);
  ASSERT_NEAR(0.60, box1.DistanceTo({0.5, 2.6}), 1e-6);
  ASSERT_NEAR(0.36, box1.DistanceSquareTo({0.5, 2.6}), 1e-6);
  ASSERT_NEAR(0.30, box1.DistanceTo({1.5, -2.3}), 1e-6);
  ASSERT_NEAR(0.09, box1.DistanceSquareTo({1.5, -2.3}), 1e-6);
  ASSERT_NEAR(std::sqrt(5.0), box1.DistanceTo({4.0, 4.0}), 1e-6);
  ASSERT_NEAR(5.0, box1.DistanceSquareTo({4.0, 4.0}), 1e-6);
  ASSERT_NEAR(std::sqrt(5.0), box1.DistanceTo({-4.0, -4.0}), 1e-6);
  ASSERT_NEAR(5.0, box1.DistanceSquareTo({-4.0, -4.0}), 1e-6);
}

TEST(OrientedBox2dTest, DistanceToSegment) {
  const OrientedBox2d box0({0.0, 0.0}, 0.0, 4.0, 2.0);
  ASSERT_NEAR(box0.DistanceTo(LineSegment2d({-4.0, -4.0}, {4.0, 4.0})), 0.0, 1e-6);
  ASSERT_NEAR(box0.DistanceTo(LineSegment2d({4.0, -4.0}, {-4.0, 4.0})), 0.0, 1e-6);
  ASSERT_NEAR(box0.DistanceTo(LineSegment2d({0.0, 2.0}, {4.0, 4.0})), 1.0, 1e-6);
  ASSERT_NEAR(box0.DistanceTo(LineSegment2d({2.0, 2.0}, {3.0, 1.0})), std::sqrt(2.0) / 2.0, 1e-6);
}

TEST(OrientedBox2dTest, TestToEigen) {
  OrientedBox2d box0({0.0, 1.0}, 3.0, 4.0, 2.0);
  Eigen::Matrix<double, 5, 1> vec = box0.ToEigen();
  ASSERT_NEAR(0.0, vec[0], 1e-6);
  ASSERT_NEAR(1.0, vec[1], 1e-6);
  ASSERT_NEAR(4.0, vec[2], 1e-6);
  ASSERT_NEAR(2.0, vec[3], 1e-6);
  ASSERT_NEAR(3.0, vec[4], 1e-6);
}

TEST(OrientedBox2dTest, RandomDistanceTo) {
  const int n = 100;
  std::vector<OrientedBox2d> boxes = testing::RandomBox2ds(n, 150.0);
  for (auto& box : boxes) {
    box.Shift(testing::RandomPoint(50.0));
  }
  std::vector<AxisAlignBox2d> aaboxes = testing::RandomAABox2ds(n, 150.0);
  for (auto& box : boxes) {
    box.Shift(testing::RandomPoint(50.0));
  }
  std::vector<Vector2d> points = testing::RandomPoints(n, 100.0);
  std::vector<LineSegment2d> segments = testing::RandomSegment2ds(n, 150.0);
  for (int i = 0; i < n; ++i) {
    for (int j = 0; j < n; ++j) {
      double d0 = boxes[i].DistanceTo(points[j]);
      double d1 = testing::BoostDistanceTo(testing::ToBoost(math::Polygon2d(boxes[i].GetCorners())),
                                           testing::ToBoost(points[j]));
      ASSERT_NEAR(d0, d1, 1e-6);
      if (boxes[i].IsPointIn(points[j])) {
        ASSERT_NEAR(d0, 0.0, 1e-6);
      }
    }
  }
  for (int i = 0; i < n; ++i) {
    for (int j = 0; j < n; ++j) {
      double d0 = boxes[i].DistanceTo(segments[j]);
      double d1 = testing::BoostDistanceTo(testing::ToBoost(math::Polygon2d(boxes[i].GetCorners())),
                                           testing::ToBoost(segments[j]));
      ASSERT_NEAR(d0, d1, 1e-6);
      if (boxes[i].HasOverlap(segments[j])) {
        ASSERT_NEAR(d0, 0.0, 1e-6);
      }
    }
  }
  for (int i = 0; i < n; ++i) {
    for (int j = 0; j < n; ++j) {
      double d0 = boxes[i].DistanceTo(aaboxes[j]);
      double d1 = testing::BoostDistanceTo(testing::ToBoost(math::Polygon2d(boxes[i].GetCorners())),
                                           testing::ToBoost(aaboxes[j]));
      ASSERT_NEAR(d0, d1, 1e-6);
      if (boxes[i].HasOverlap(aaboxes[j])) {
        ASSERT_NEAR(d0, 0.0, 1e-6);
      }
    }
  }
  for (int i = 0; i < n; ++i) {
    for (int j = 0; j < n; ++j) {
      double d0 = boxes[i].DistanceTo(boxes[j]);
      double d1 =
          testing::BoostDistanceTo(testing::ToBoost(math::Polygon2d(boxes[i].GetCorners())),
                                   testing::ToBoost(math::Polygon2d(boxes[j].GetCorners())));
      ASSERT_NEAR(d0, d1, 1e-6);
      if (boxes[i].HasOverlap(boxes[j])) {
        ASSERT_NEAR(d0, 0.0, 1e-6);
      }
    }
  }
}

TEST(OrientedBox2dTest, ToProtoTest) {
  math_proto::OrientedBox2d proto;
  proto.mutable_center()->set_x(1.0);
  proto.mutable_center()->set_y(2.0);
  proto.set_heading(3.0);
  proto.set_length(4.0);
  proto.set_width(5.0);

  const OrientedBox2d box0(proto);
  ASSERT_NEAR(box0.center().DistanceTo({1.0, 2.0}), 0.0, 1e-6);
  ASSERT_NEAR(box0.heading(), 3.0, 1e-6);
  ASSERT_NEAR(box0.length(), 4.0, 1e-6);
  ASSERT_NEAR(box0.width(), 5.0, 1e-6);

  const OrientedBox2d box1 = box0.Shifted(Vector2d(2.0, 3.0)).Rotated(-1.0);

  const math_proto::OrientedBox2d proto_after = box1.ToProto();
  ASSERT_NEAR(proto_after.center().x(), 3.0, 1e-6);
  ASSERT_NEAR(proto_after.center().y(), 5.0, 1e-6);
  ASSERT_NEAR(proto_after.heading(), 2.0, 1e-6);
  ASSERT_NEAR(proto_after.length(), 4.0, 1e-6);
  ASSERT_NEAR(proto_after.width(), 5.0, 1e-6);
}

TEST(OrientedBox2dTest, HasOverlapAlongDirectionTest) {
  {
    const math::OrientedBox2d box0 =
        math::OrientedBox2d(math::Vector2d(2.0, 3.0), math::DegreeToRadian(90.0), 4.0, 2.0);
    const math::OrientedBox2d box1 =
        math::OrientedBox2d(math::Vector2d(5.0, 2.0), math::DegreeToRadian(90.0), 2.0, 2.0);
    ASSERT_FALSE(box0.HasOverlapAlongDirection(box1, box0.direction()));
    ASSERT_TRUE(box0.HasOverlapAlongDirection(box1, box0.direction().Rotate90()));
    ASSERT_TRUE(box0.HasOverlapAlongDirection(box1, box0.direction().Rotate270()));

    ASSERT_FALSE(box0.HasOverlapAlongDirection(box1, math::Vector2d(1.0, 2.1)));
    ASSERT_TRUE(box0.HasOverlapAlongDirection(box1, math::Vector2d(1.0, 2.0)));
    ASSERT_FALSE(box0.HasOverlapAlongDirection(box1, math::Vector2d(-1.0, -2.1)));
    ASSERT_TRUE(box0.HasOverlapAlongDirection(box1, math::Vector2d(-1.0, -2.0)));
  }

  {
    const math::OrientedBox2d box0 =
        math::OrientedBox2d(math::Vector2d(2.0, 3.0), math::DegreeToRadian(0.0), 2.0, 4.0);
    const math::OrientedBox2d box1 =
        math::OrientedBox2d(math::Vector2d(5.0, 2.0), math::DegreeToRadian(90.0), 2.0, 2.0);
    ASSERT_TRUE(box0.HasOverlapAlongDirection(box1, box0.direction()));
    ASSERT_FALSE(box0.HasOverlapAlongDirection(box1, box0.direction().Rotate90()));
    ASSERT_FALSE(box0.HasOverlapAlongDirection(box1, box0.direction().Rotate270()));

    ASSERT_FALSE(box0.HasOverlapAlongDirection(box1, math::Vector2d(1.0, 2.1)));
    ASSERT_TRUE(box0.HasOverlapAlongDirection(box1, math::Vector2d(1.0, 2.0)));
    ASSERT_FALSE(box0.HasOverlapAlongDirection(box1, math::Vector2d(-1.0, -2.1)));
    ASSERT_TRUE(box0.HasOverlapAlongDirection(box1, math::Vector2d(-1.0, -2.0)));
  }

  {
    const math::OrientedBox2d box0 =
        math::OrientedBox2d(math::Vector2d(2.0, 3.0), math::DegreeToRadian(90.0), 4.0, 2.0);
    const math::AxisAlignBox2d box1 = math::AxisAlignBox2d(math::Vector2d(5.0, 2.0), 2.0, 2.0);
    ASSERT_TRUE(box0.HasOverlapAlongDirection(box1, box0.direction().Rotate90()));
    ASSERT_FALSE(box0.HasOverlapAlongDirection(box1, math::Vector2d(1.0, 2.1)));
    ASSERT_TRUE(box0.HasOverlapAlongDirection(box1, math::Vector2d(1.0, 2.0)));
  }
}

TEST(OrientedBox2dTest, DirectionalExtended) {
  math::OrientedBox2d box({0.0, 0.0}, 0.0, 4.0, 2.0);

  auto box_all = box.DirectionalExtended(1.0, 2.0, 3.0, 4.0);
  ASSERT_NEAR(box_all.length(), 7.0, 1e-6);
  ASSERT_NEAR(box_all.width(), 9.0, 1e-6); 
  ASSERT_TRUE(box_all.center().IsNear({-0.5, -0.5}));

  auto box_shrink = box.DirectionalExtended(-1.0, -1.0, -1.0, -1.0);
  ASSERT_NEAR(box_shrink.length(), 2.0, 1e-6); 
  ASSERT_NEAR(box_shrink.width(), 0.0, 1e-6);  
  ASSERT_TRUE(box_shrink.center().IsNear({0.0, 0.0}));

  ASSERT_NEAR(box_all.heading(), box.heading(), 1e-6);
  ASSERT_NEAR(box_shrink.heading(), box.heading(), 1e-6);
}

}  // namespace math
