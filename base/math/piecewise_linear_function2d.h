// Copyright @2024 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <utility>
#include <vector>

#include "Eigen/Core"

#include "base/math/piecewise_linear_function.h"

namespace math {

template <typename T, int NumY>
class PiecewiseLinearFunction2d {
 public:
  // Input grid need to be a Rectangular form.
  // TODO(liuyacheng): Support uniform x and uniform y.
  PiecewiseLinearFunction2d(std::vector<double> x_samples,
                            std::vector<double> y_samples,
                            const std::vector<std::vector<T>>& z_samples);

  T Interpolate(double x, double y) const;

 private:
  PiecewiseLinearFunction<Eigen::Matrix<T, NumY, 1>> x_to_z_samples_;
  std::vector<double> y_samples_;
};

template <typename T, int NumY>
PiecewiseLinearFunction2d<T, NumY>::PiecewiseLinearFunction2d(
    std::vector<double> x_samples,
    std::vector<double> y_samples,
    const std::vector<std::vector<T>>& z_samples)
    : y_samples_(std::move(y_samples)) {
  CHECK_GT(x_samples.size(), 0);
  CHECK_GT(y_samples_.size(), 0);
  CHECK_EQ(static_cast<int>(y_samples_.size()), NumY);
  CHECK_EQ(x_samples.size(), z_samples.size());

  std::vector<Eigen::Matrix<T, NumY, 1>> z_samples_matrices;
  z_samples_matrices.resize(z_samples.size());
  for (int i = 0; i < static_cast<int>(z_samples.size()); ++i) {
    CHECK_EQ(z_samples[i].size(), NumY);
    for (int j = 0; j < NumY; ++j) {
      z_samples_matrices[i](j, 0) = z_samples[i][j];
    }
  }
  x_to_z_samples_ = PiecewiseLinearFunction<Eigen::Matrix<T, NumY, 1>>(
      std::move(x_samples), std::move(z_samples_matrices));
}

template <typename T, int NumY>
T PiecewiseLinearFunction2d<T, NumY>::Interpolate(double x, double y) const {
  Eigen::Matrix<T, NumY, 1> z_samples_at_x = x_to_z_samples_(x);
  return Lerp(z_samples_at_x, GetLerpRatio(y_samples_, y));
}

}  // namespace math
