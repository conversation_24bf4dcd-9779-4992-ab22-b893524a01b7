// Copyright @2020 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <string>
#include <ostream>

#include "base/math/vector2.h"
#include "base/math/vector4.h"

namespace math {

template <class T>
class QuaternionT {
  static_assert(std::is_floating_point<T>::value, "Vector4 support float, double only.");

 public:
  QuaternionT(T _w, T _x, T _y, T _z) : x(_x), y(_y), z(_z), w(_w) {}
  QuaternionT() : QuaternionT(1.0, 0.0, 0.0, 0.0) {}
  explicit QuaternionT(const Vector4<T>& vec) : QuaternionT(vec.w, vec.x, vec.y, vec.z) {}
  QuaternionT(const QuaternionT<T>& quat) : QuaternionT(quat.w, quat.x, quat.y, quat.z) {}

  QuaternionT<T>& operator=(const QuaternionT<T>& quat) {
    x = quat.x;
    y = quat.y;
    z = quat.z;
    w = quat.w;
    return *this;
  }

  Eigen::Quaternion<T> ToEigen() const {
    return Eigen::Quaternion<T>(w, x, y, z);
  }

  T operator[](int index) const {
    DCHECK(index >= 0 && index < 4);
    return elements[index];
  }
  T& operator[](int index) {
    DCHECK(index >= 0 && index < 4);
    return elements[index];
  }

 public:
  union {
    struct {
      T x;
      T y;
      T z;
      T w;
    };
    T elements[4] = T{(0)};
  };
};

using Quaternion = QuaternionT<double>;

}  // namespace math
