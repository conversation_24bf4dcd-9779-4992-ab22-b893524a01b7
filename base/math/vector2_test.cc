// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>
//          <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)

#include "base/math/vector2.h"

#include <algorithm>
#include <array>
#include <cmath>
#include <vector>

#include "gtest/gtest.h"

#include "base/strings/macros.h"
#include "base/testing/geometry.h"
#include "base/testing/random.h"

namespace math {

TEST(Vector2Test, NormalCases) {
  Vector2d p0(2, 3);
  ASSERT_NEAR(p0.Length(), std::sqrt(13.0), 1e-6);
  ASSERT_NEAR(p0.LengthSquare(), 13.0, 1e-6);
  ASSERT_NEAR(p0.DistanceTo({0, 0}), std::sqrt(13.0), 1e-6);
  ASSERT_NEAR(p0.DistanceSquareTo({0, 0}), 13.0, 1e-6);
  ASSERT_NEAR(p0.DistanceTo({0, 2}), std::sqrt(5.0), 1e-6);
  ASSERT_NEAR(p0.DistanceSquareTo({0, 2}), 5.0, 1e-6);
  ASSERT_NEAR(p0.Angle(), std::atan2(3, 2), 1e-6);
  ASSERT_NEAR(p0.CrossProd({4, 5}), -2, 1e-6);
  ASSERT_NEAR(p0.InnerProd({4, 5}), 23, 1e-6);

  Vector2d p1;
  p1.x = 4;
  p1.y = 5;
  ASSERT_NEAR(p1.Length(), std::sqrt(41.0), 1e-6);
  ASSERT_NEAR(p1.LengthSquare(), 41.0, 1e-6);
  p1.Normalize();
  ASSERT_NEAR(p1.x, 4.0 / std::sqrt(41.0), 1e-6);
  ASSERT_NEAR(p1.y, 5.0 / std::sqrt(41.0), 1e-6);
  ASSERT_NEAR(p1.Length(), 1.0, 1e-6);

  Vector2d p2 = Vector2d(0.5, 1.5) + Vector2d(2.5, 3.5);
  ASSERT_NEAR(p2.x, 3.0, 1e-6);
  ASSERT_NEAR(p2.y, 5.0, 1e-6);

  Vector2d p3 = Vector2d(0.5, 1.5) - Vector2d(2.5, 3.5);
  ASSERT_NEAR(p3.x, -2.0, 1e-6);
  ASSERT_NEAR(p3.y, -2.0, 1e-6);

  Vector2d p4 = p2 / 2.0;
  ASSERT_NEAR(p4.x, 1.5, 1e-6);
  ASSERT_NEAR(p4.y, 2.5, 1e-6);

  Vector2d p5 = p3 * (-3.0);
  ASSERT_NEAR(p5.x, 6.0, 1e-6);
  ASSERT_NEAR(p5.y, 6.0, 1e-6);

  Vector2d p6 = Vector2d::FromAngle(M_PI_4);
  ASSERT_NEAR(p6.x, std::sqrt(2.0) / 2.0, 1e-6);
  ASSERT_NEAR(p6.y, std::sqrt(2.0) / 2.0, 1e-6);
  ASSERT_NEAR(p6.Angle(), M_PI_4, 1e-6);

  Vector2d p7 = Vector2d::FromAngleAndLength(2.5, 1.3);
  ASSERT_NEAR(1.3 * std::cos(2.5), p7.x, 1e-6);
  ASSERT_NEAR(1.3 * std::sin(2.5), p7.y, 1e-6);
  ASSERT_NEAR(2.5, p7.Angle(), 1e-6);
  ASSERT_NEAR(1.3, p7.Length(), 1e-6);
  ASSERT_NEAR(1.3 * 1.3, p7.LengthSquare(), 1e-6);
}

TEST(Vector2Test, Distance) {
  Vector2d p1(1.0, 2.0);
  Vector2d p2(3.0, 4.0);
  Vector2d p3(0.0, 5.0);
  ASSERT_NEAR(std::sqrt(0.0), p1.DistanceTo(p1), 1e-6);
  ASSERT_NEAR(0.0, p1.DistanceSquareTo(p1), 1e-6);
  ASSERT_NEAR(std::sqrt(8.0), p1.DistanceTo(p2), 1e-6);
  ASSERT_NEAR(8.0, p1.DistanceSquareTo(p2), 1e-6);
  ASSERT_NEAR(std::sqrt(10.0), p2.DistanceTo(p3), 1e-6);
  ASSERT_NEAR(10.0, p2.DistanceSquareTo(p3), 1e-6);
  ASSERT_NEAR(std::sqrt(10.0), p3.DistanceTo(p1), 1e-6);
  ASSERT_NEAR(10.0, p3.DistanceSquareTo(p1), 1e-6);
  ASSERT_NEAR(std::sqrt(29.0), p1.DistanceTo(+6.0, +0.0), 1e-6);
  ASSERT_NEAR(29.0, p1.DistanceSquareTo(+6.0, +0.0), 1e-6);
  ASSERT_NEAR(std::sqrt(34.0), p1.DistanceTo(-2.0, -3.0), 1e-6);
  ASSERT_NEAR(34.0, p1.DistanceSquareTo(-2.0, -3.0), 1e-6);
}

TEST(Vector2Test, Operators) {
  Vector2d p1(1.0, 2.0);
  Vector2d p2(3.0, 4.0);
  Vector2d p3(0.0, 5.0);
  ASSERT_NEAR(+4.0, (p1 + p2).x, 1e-6);
  ASSERT_NEAR(+6.0, (p1 + p2).y, 1e-6);
  ASSERT_NEAR(+1.0, (p1 + p3).x, 1e-6);
  ASSERT_NEAR(+7.0, (p1 + p3).y, 1e-6);
  ASSERT_NEAR(+3.0, (p2 + p3).x, 1e-6);
  ASSERT_NEAR(+9.0, (p2 + p3).y, 1e-6);

  ASSERT_NEAR(-2.0, (p1 - p2).x, 1e-6);
  ASSERT_NEAR(-2.0, (p1 - p2).y, 1e-6);
  ASSERT_NEAR(+1.0, (p1 - p3).x, 1e-6);
  ASSERT_NEAR(-3.0, (p1 - p3).y, 1e-6);
  ASSERT_NEAR(+3.0, (p2 - p3).x, 1e-6);
  ASSERT_NEAR(-1.0, (p2 - p3).y, 1e-6);

  ASSERT_NEAR(-5.0, (p1 * -5.0).x, 1e-6);
  ASSERT_NEAR(-6.0, (p1 * -3.0).y, 1e-6);
  ASSERT_NEAR(+1.5, (p2 / +2.0).x, 1e-6);
  ASSERT_NEAR(+2.0, (p2 / +2.0).y, 1e-6);

  ASSERT_NEAR(+8.0, (p2 - p1).CrossProd(p3 - p1), 1e-6);
  ASSERT_NEAR(+8.0, CrossProd(p1, p2, p3), 1e-6);
  ASSERT_NEAR(+8.0, CrossProd(p2, p3, p1), 1e-6);
  ASSERT_NEAR(+8.0, CrossProd(p3, p1, p2), 1e-6);
  ASSERT_NEAR(+4.0, (p2 - p1).InnerProd(p3 - p1), 1e-6);
  ASSERT_NEAR(+4.0, InnerProd(p1, p2, p3), 1e-6);

  ASSERT_NEAR(+4.0, (p1 += p2).x, 1e-6);
  ASSERT_NEAR(+4.0, p1.x, 1e-6);
  ASSERT_NEAR(+6.0, p1.y, 1e-6);

  ASSERT_NEAR(+1.0, (p1 -= p2).x, 1e-6);
  ASSERT_NEAR(+1.0, p1.x, 1e-6);
  ASSERT_NEAR(+2.0, p1.y, 1e-6);

  ASSERT_NEAR(+4.5, (p2 *= 1.5).x, 1e-6);
  ASSERT_NEAR(+6.0, p2.y, 1e-6);

  ASSERT_NEAR(+3.0, (p2 /= 1.5).x, 1e-6);
  ASSERT_NEAR(+4.0, p2.y, 1e-6);
}

TEST(Vector2Test, Rotate1) {
  Vector2d v(1.0, 2.0);
  ASSERT_NEAR(0.0, (v.Rotate(M_PI_2) - v.Rotate90()).Length(), 1e-6);
  ASSERT_NEAR(0.0, (v.Rotate(M_PI) - v.Rotate180()).Length(), 1e-6);
  ASSERT_NEAR(0.0, (v.Rotate(M_PI_2 * 3) - v.Rotate270()).Length(), 1e-6);
  ASSERT_NEAR(std::sqrt(3.0) / 2.0 - 1.0, v.Rotate(M_PI / 6).x, 1e-6);
  ASSERT_NEAR(0.5 + std::sqrt(3.0), v.Rotate(M_PI / 6).y, 1e-6);
  ASSERT_NEAR(std::sqrt(3.0) / 2.0 - 1.0, v.Rotate(std::sqrt(3.0) / 2.0, 0.5).x, 1e-6);
  ASSERT_NEAR(0.5 + std::sqrt(3.0), v.Rotate(std::sqrt(3.0) / 2.0, 0.5).y, 1e-6);
}

TEST(Vector2Test, Rotate2) {
  Vector2d p0(4.0, 0.0);
  Vector2d p1 = p0.Rotate(M_PI_2);
  ASSERT_NEAR(p1.x, 0.0, 1e-6);
  ASSERT_NEAR(p1.y, 4.0, 1e-6);
  Vector2d p2 = p0.Rotate(M_PI);
  ASSERT_NEAR(p2.x, -4.0, 1e-6);
  ASSERT_NEAR(p2.y, 0.0, 1e-6);
  Vector2d p3 = p0.Rotate(-M_PI_2);
  ASSERT_NEAR(p3.x, 0.0, 1e-6);
  ASSERT_NEAR(p3.y, -4.0, 1e-6);
  Vector2d p4 = p0.Rotate(-M_PI);
  ASSERT_NEAR(p4.x, -4.0, 1e-6);
  ASSERT_NEAR(p4.y, 0.0, 1e-6);
}

TEST(Vector2Test, Rotate3) {
  std::vector<Vector2d> points;
  points.push_back(Vector2d(3.0, 7.0));
  for (int i = 0; i < 3; ++i) {
    points.push_back(points.back().Rotate90());
    ASSERT_NEAR(points[0].Length(), points.back().Length(), 1e-6);
  }
  ASSERT_NEAR(points[0].InnerProd(points[1]), 0.0, 1e-6);
  ASSERT_NEAR(points[0].CrossProd(points[1]), points[0].LengthSquare(), 1e-6);
  ASSERT_TRUE(points[2] == points[0] * -1.0);
  ASSERT_TRUE(points[2] == points[0].Rotate180());
  ASSERT_TRUE(points[3] == points[1] * -1.0);
  ASSERT_TRUE(points[3] == points[0].Rotate270());
}

TEST(Vector2Test, CrossAndInnerProd1) {
  Vector2d p1(1.0, 2.0);
  Vector2d p2(3.0, 4.0);
  Vector2d p3(0.0, 5.0);
  ASSERT_NEAR(0.0, p1.CrossProd(p1), 1e-6);
  ASSERT_NEAR(0.0, p2.CrossProd(p2), 1e-6);
  ASSERT_NEAR(0.0, p3.CrossProd(p3), 1e-6);
  ASSERT_NEAR(5.0, p1.InnerProd(p1), 1e-6);
  ASSERT_NEAR(25.0, p2.InnerProd(p2), 1e-6);
  ASSERT_NEAR(25.0, p3.InnerProd(p3), 1e-6);

  ASSERT_NEAR(-2.0, p1.CrossProd(p2), 1e-6);
  ASSERT_NEAR(2.0, p2.CrossProd(p1), 1e-6);
  ASSERT_NEAR(11.0, p1.InnerProd(p2), 1e-6);
  ASSERT_NEAR(11.0, p2.InnerProd(p1), 1e-6);

  ASSERT_NEAR(5.0, p1.CrossProd(p3), 1e-6);
  ASSERT_NEAR(-5.0, p3.CrossProd(p1), 1e-6);
  ASSERT_NEAR(10.0, p1.InnerProd(p3), 1e-6);
  ASSERT_NEAR(10.0, p3.InnerProd(p1), 1e-6);

  ASSERT_NEAR(15.0, p2.CrossProd(p3), 1e-6);
  ASSERT_NEAR(-15.0, p3.CrossProd(p2), 1e-6);
  ASSERT_NEAR(20.0, p2.InnerProd(p3), 1e-6);
  ASSERT_NEAR(20.0, p3.InnerProd(p2), 1e-6);
}

TEST(Vector2Test, CrossAndInnerProd2) {
  Vector2d p0(7.0, -2.34);
  Vector2d p1(9.0, 156.3);
  Vector2d p2(6.2, -37.9);
  ASSERT_NEAR(CrossProd(p0, p1, p2), (p1 - p0).CrossProd(p2 - p0), 1e-6);
  ASSERT_NEAR(InnerProd(p0, p1, p2), (p1 - p0).InnerProd(p2 - p0), 1e-6);
}

TEST(Vector2Test, Lerp) {
  Vector2d p0(0.0, 1.0);
  Vector2d p1(1.0, 2.0);
  ASSERT_NEAR(Lerp(p0, p1, 0.0).DistanceTo(p0), 0.0, 1e-6);
  ASSERT_NEAR(Lerp(p0, p1, 1.0).DistanceTo(p1), 0.0, 1e-6);
  ASSERT_NEAR(Lerp(p0, p1, 0.2).DistanceTo(Vector2d(0.2, 1.2)), 0.0, 1e-6);
}

}  // namespace math
