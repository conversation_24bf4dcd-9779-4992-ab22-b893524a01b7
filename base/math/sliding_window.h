// Copyright @2022 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <algorithm>
#include <deque>
#include <utility>
#include <vector>

#include "gtest/gtest.h"

#include "base/container/circular_queue.h"
#include "base/math/math_util.h"

namespace math {

template <typename T>
class SlidingWindowContainer {
 public:
  explicit SlidingWindowContainer(int capacity) {
    CHECK_GE(capacity, 1);
    elements_.set_capacity(capacity);
  }

  SlidingWindowContainer(base::CircularQueue<T> elements, int index)
      : next_element_index_(index), elements_(std::move(elements)) {
    CHECK_GE(elements_.capacity(), 1);
  }

  int capacity() const { return elements_.capacity(); }
  int size() const { return elements_.size(); }
  int element_index() const { return next_element_index_; }

  void Add(const T& v) {
    AddInternal(v);
    elements_.push_back(v);
    next_element_index_++;
  }

  const base::CircularQueue<T>& elements() const { return elements_; }

 protected:
  virtual void AddInternal(const T& v) = 0;

  int next_element_index_ = 0;
  base::CircularQueue<T> elements_;
};

// 'Add' needs to be called at least once before calling 'GetMax/GetMin/GetAverage'.
// For self-defined structures, 1) operator +/- between T and T, and 2) operator * between T and
// double are required for SlidingWindowAverage and SlidingWindowWeightedAverage; 3) operator<
// between T and T is required for SlidingWindowMax and SlidingWindowMin;
template <typename T>
class SlidingWindowAverage final : public SlidingWindowContainer<T> {
 public:
  explicit SlidingWindowAverage(int capacity) : SlidingWindowContainer<T>(capacity) {}
  SlidingWindowAverage(base::CircularQueue<T> elements, int index, T sum)
      : SlidingWindowContainer<T>(elements, index), sum_(sum) {}

  T GetAverage() const {
    CHECK(!this->elements_.empty());
    return sum_ * (1.0 / this->elements_.size());
  }
  T sum() const { return sum_; }

 private:
  void AddInternal(const T& v) override {
    const int kRecalculateTimesInterval = 1e+8;
    // (v - elememts_.front()) is contantly added to sum_, which may generate accmulative error.
    // To avoid this, for every kRecalculateTimesInterval times, sum_ is recalculated.
    if (this->next_element_index_ % kRecalculateTimesInterval == 0 && this->elements_.full()) {
      sum_ = std::accumulate(this->elements_.begin() + 1, this->elements_.end(), v);
      return;
    }
    if (this->elements_.empty()) {
      sum_ = v;
    } else if (this->elements_.full()) {
      sum_ = sum_ - this->elements_.front();
      sum_ = sum_ + v;
    } else {
      sum_ = sum_ + v;
    }
  }

  T sum_;
};

// Sliding-window maximum/minimum is fulfilled with queue, see
// https://www.geeksforgeeks.org/sliding-window-maximum-maximum-of-all-subarrays-of-size-k/.
template <typename T>
class SlidingWindowMax final : public SlidingWindowContainer<T> {
 public:
  explicit SlidingWindowMax(int capacity) : SlidingWindowContainer<T>(capacity) {}

  const T& GetMax() const {
    CHECK(!this->elements_.empty());
    const int element_index_offset = this->next_element_index_ - this->elements_.size();
    return this->elements_[max_element_indexes_.front() - element_index_offset];
  }

 private:
  void AddInternal(const T& v) override {
    if (!max_element_indexes_.empty() &&
        this->next_element_index_ - this->capacity() == max_element_indexes_.front()) {
      max_element_indexes_.pop_front();
    }
    const int element_index_offset = this->next_element_index_ - this->elements_.size();
    while (!max_element_indexes_.empty() &&
           this->elements_[max_element_indexes_.back() - element_index_offset] < v) {
      max_element_indexes_.pop_back();
    }
    max_element_indexes_.push_back(this->next_element_index_);
  }

  std::deque<int> max_element_indexes_;

  FRIEND_TEST(SlidingWindowTest, OverflowTest);
};

template <typename T>
class SlidingWindowMin final : public SlidingWindowContainer<T> {
 public:
  explicit SlidingWindowMin(int capacity) : SlidingWindowContainer<T>(capacity) {}

  const T& GetMin() const {
    CHECK(!this->elements_.empty());
    const int element_index_offset = this->next_element_index_ - this->elements_.size();
    return this->elements_[min_element_indexes_.front() - element_index_offset];
  }

 private:
  void AddInternal(const T& v) override {
    if (!min_element_indexes_.empty() &&
        this->next_element_index_ - this->capacity() == min_element_indexes_.front()) {
      min_element_indexes_.pop_front();
    }
    const int element_index_offset = this->next_element_index_ - this->elements_.size();
    while (!min_element_indexes_.empty() &&
           v < this->elements_[min_element_indexes_.back() - element_index_offset]) {
      min_element_indexes_.pop_back();
    }
    min_element_indexes_.push_back(this->next_element_index_);
  }

  std::deque<int> min_element_indexes_;
};

template <typename T>
class SlidingWindowWeightedAverage final : public SlidingWindowContainer<T> {
 public:
  SlidingWindowWeightedAverage(int capacity, std::vector<double> weights)
      : SlidingWindowContainer<T>(capacity), weights_(std::move(weights)) {
    CHECK_EQ(capacity, weights_.size());
    accumulated_weights_.reserve(capacity);
    double sum = 0.0;
    for (double weight : weights_) {
      sum += weight;
      accumulated_weights_.emplace_back(sum);
    }
  }

  // Complexity: O(N)
  T GetWeightedAverage() const {
    CHECK(!this->elements_.empty());
    T sum = this->elements_.front() * weights_.front();
    int size = static_cast<int>(this->elements_.size());
    for (int i = 1; i < size; ++i) {
      sum = sum + this->elements_[i] * weights_[i];
    }
    CHECK_GT(accumulated_weights_[size - 1], math::kEpsilon);
    return sum * (1.0 / accumulated_weights_[size - 1]);
  }

 private:
  void AddInternal(const T& v) override {}

  std::vector<double> weights_;
  std::vector<double> accumulated_weights_;
};

}  // namespace math
