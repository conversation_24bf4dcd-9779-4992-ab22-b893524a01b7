// Copyright @2022 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <cmath>
#include <vector>

#include "glog/logging.h"

#include "base/math/math_util.h"

namespace math {

class PolynomialGenerator {
 public:
  explicit PolynomialGenerator(const std::vector<double>& coefs) : coefficients_(coefs) {
    CHECK(!coefficients_.empty());
  }
  virtual ~PolynomialGenerator() = default;

  double Compute(double x) const;
  double ComputeDerivative(double x, int order) const;

 private:
  std::vector<double> coefficients_;

  // Shallow copy is allowed.
};

}  // namespace math
