// Copyright @2020 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <algorithm>
#include <functional>
#include <limits>
#include <utility>
#include <vector>

#include "gtest/gtest.h"

#include "base/common/optional.h"
#include "base/container/span.h"
#include "base/math/math_util.h"
#include "base/strings/macros.h"

namespace math {

template <typename ContainerOfXSamples, typename T, typename LessThan>
int GetLowerIndex(const ContainerOfXSamples& x_samples,
                  const T& value,
                  LessThan less_than,
                  int lower_index_hint) {
  int i0 = -1;
  int i1 = x_samples.size();
  if (lower_index_hint > i0 && lower_index_hint < i1) {
    constexpr int kMaxForwardSteps = 2;
    for (int j = 0; j < kMaxForwardSteps; ++j) {
      const int i = j + lower_index_hint;
      if (i < i1 && !less_than(value, x_samples[i])) {
        i0 = std::max(i0, i);
      } else {
        i1 = std::min(i1, i);
        break;
      }
    }
  }
  if (i0 != i1 - 1) {
    const auto begin_iter = x_samples.begin();
    i1 = std::upper_bound(begin_iter + i0 + 1, begin_iter + i1, value, less_than) - begin_iter;
    i0 = i1 - 1;
  }
  return i0;
}

// Return the max index i0 that satisfies: x_samples[i0] <= x.
// If no such i0 exists, return -1.
// This function is optimized for ordered-access pattern: try the last returned interval first
// (lower_index_hint), then fall back to binary-search.
template <typename ContainerOfXSamples, typename T>
int GetLowerIndex(const ContainerOfXSamples& x_samples, const T& x, int lower_index_hint) {
  return GetLowerIndex(
      x_samples, x, [](const T& a, const T& b) { return a < b; }, lower_index_hint);
}

// The lower-index(i) and lerp-ratio(r) of linear interpolation.
struct LerpRatio {
  constexpr LerpRatio() = default;
  constexpr LerpRatio(int i, double r) : i(i), r(r) {}

  double v() const { return i + r; }

  int i = 0;  // in [0,n-1]
  double r = 0.0;
};

template <typename ContainerOfYSamples,
          typename Y = typename ContainerOfYSamples::value_type,
          typename LerpFunc = LerpOperator<Y, double>>
inline Y Lerp(const ContainerOfYSamples& y_samples, const LerpRatio& lerp_ratio) {
  const int n_samples = y_samples.size();
  const int i = lerp_ratio.i;
  const double r = lerp_ratio.r;
  DCHECK_GE(i, 0);
  DCHECK_LE(i, n_samples - 1);
  DCHECK(std::isfinite(r));
  if (r == 0.0) {
    return y_samples[i];
  }
  DCHECK_GE(n_samples, 2) << DUMP_TO_STREAM(i, r);
  const LerpFunc lerp_func;
  return i != n_samples - 1 ? lerp_func(y_samples[i], y_samples[i + 1], r)
                            : lerp_func(y_samples[i - 1], y_samples[i], r + 1.0);
}

template <typename ContainerOfYSamples, typename Y = typename ContainerOfYSamples::value_type>
inline Y LerpNormalizedAngle(const ContainerOfYSamples& angle_samples,
                             const LerpRatio& lerp_ratio) {
  static_assert(std::is_same<typename ContainerOfYSamples::value_type, double>::value,
                "Only support double floating type.");
  if (lerp_ratio.r == 0.0) {
    return math::NormalizeAngle(angle_samples[lerp_ratio.i]);
  }
  return LerpNormalizedAngle(
      angle_samples[lerp_ratio.i], angle_samples[lerp_ratio.i + 1], lerp_ratio.r);
}

// Uses the linear interpolation when x is inside the range [min_x,max_x].
// Otherwise, uses the nearest neighbor when extrapolate is false.
// Note, i is guaranteed to be inside range [0,n-1].
template <typename ContainerOfXSamples>
inline LerpRatio GetLerpRatio(const ContainerOfXSamples& x_samples,
                              double x,
                              int* lower_index_hint,
                              bool extrapolate = false) {
  static_assert(std::is_same<typename ContainerOfXSamples::value_type, double>::value,
                "Only support double floating type.");
  const int n_samples = x_samples.size();
  if (n_samples <= 1) {
    return LerpRatio(0, 0.0);
  }
  if (x <= x_samples[0]) {
    if (!extrapolate || x == x_samples[0]) {
      return LerpRatio(0, 0.0);
    }
    return LerpRatio(0, InverseLerp(x_samples[0], x_samples[1], x));
  }
  if (x >= x_samples[n_samples - 1]) {
    const int i = n_samples - 1;
    if (!extrapolate || x == x_samples[i]) {
      return LerpRatio(i, 0.0);
    }
    return LerpRatio(i, InverseLerp(x_samples[i - 1], x_samples[i], x) - 1.0);
  } else {
    const int i = GetLowerIndex(x_samples, x, lower_index_hint == nullptr ? -1 : *lower_index_hint);
    if (lower_index_hint != nullptr) {
      *lower_index_hint = i;
    }
    return LerpRatio(i, InverseLerp(x_samples[i], x_samples[i + 1], x));
  }
}

template <typename ContainerOfXSamples>
inline LerpRatio GetLerpRatio(const ContainerOfXSamples& x_samples,
                              double x,
                              bool extrapolate = false) {
  return GetLerpRatio(x_samples, x, nullptr, extrapolate);
}

inline LerpRatio GetLerpRatioWithStepInverse(
    double x0, double x_step_inverse, int n_samples, double x, bool extrapolate = false) {
  if (n_samples <= 1) {
    return LerpRatio(0, 0.0);
  }
  const double x_index = (x - x0) * x_step_inverse;
  if (x_index <= 0.0) {
    return LerpRatio(0, !extrapolate ? 0.0 : x_index);
  }
  if (x_index >= n_samples - 1) {
    const int i = n_samples - 1;
    return LerpRatio(i, !extrapolate ? 0.0 : x_index - i);
  } else {
    const int i = static_cast<int>(x_index);
    return LerpRatio(i, x_index - i);
  }
}

inline LerpRatio GetLerpRatio(
    double x0, double x_step, int n_samples, double x, bool extrapolate = false) {
  return GetLerpRatioWithStepInverse(x0, 1.0 / x_step, n_samples, x, extrapolate);
}

template <typename T, typename LerpFunc = LerpOperator<T, double>>
class PiecewiseLinearFunction final {
 public:
  PiecewiseLinearFunction() = default;

  PiecewiseLinearFunction(std::vector<double> x_samples, std::vector<T> y_samples);

  PiecewiseLinearFunction(std::vector<double> x_samples, const std::function<T(double)>& generator);

  PiecewiseLinearFunction(double x0,
                          double x_step,
                          int n_samples,
                          const std::function<T(double)>& generator);

  PiecewiseLinearFunction(double x0, double x_step, std::vector<T> y_samples);

  PiecewiseLinearFunction(std::vector<T> y_samples,
                          const std::function<double(int, const T&)>& transformer);

  int n_samples() const { return n_samples_; }

  // TODO(wangnaizheng): deprecate this API and make x_samples iterable.
  const std::vector<double>& x_samples() const { return x_samples_; }
  const std::vector<T>& y_samples() const { return y_samples_; }

  double min_x() const { return n_samples_ != 0 ? x_samples_[0] : 0.0; }
  double max_x() const { return n_samples_ != 0 ? x_samples_[n_samples_ - 1] : 0.0; }

  double x_sample(int i) const { return x_samples_[i]; }
  const T& y_sample(int i) const { return y_samples_[i]; }
  T* mutable_y_sample(int i) { return &y_samples_[i]; }

  bool is_uniform() const { return is_uniform_; }

  // Check if x is inside [min_x,max_x] with epsilon.
  bool IsInside(double x, double epsilon = math::kEpsilon) const {
    return n_samples_ != 0 && math::IsInside(x, x_samples_[0], x_samples_[n_samples_ - 1], epsilon);
  }

  // Shift the x axis, s.t.
  //  - new_function.min_x() = x0
  //  - new_function.y(x) = old_function.y(old_function.min_x() + x - new_function.min_x())
  // Complexity is O(N).
  void ShiftX(double x0);
  // Shift the y axis, s.t.
  //  - new_function.min_y() = y0
  // Complexity is O(N).
  void ShiftY(const T& y0);

  // Uses the linear interpolation when x is inside the range [min_x,max_x].
  // Otherwise, uses the nearest neighbor when extrapolate is false.
  T operator()(double x, bool extrapolate = false) const {
    return Lerp<std::vector<T>, T, LerpFunc>(y_samples_, GetLerpRatio(x, extrapolate));
  }

  LerpRatio GetLerpRatio(double x, bool extrapolate = false) const {
    CHECK_NE(n_samples_, 0);
    return is_uniform_ ? math::GetLerpRatioWithStepInverse(
                             x_samples_[0], x_step_inverse_, n_samples_, x, extrapolate)
                       : math::GetLerpRatio(x_samples_, x, &lower_index_hint_, extrapolate);
  }

  // Make a sub function that is defined on the VALID range of [min_x_hint, max_x_hint], s.t.
  //  - for any x, sub_function(x, false) = function(x, false).
  base::Optional<PiecewiseLinearFunction<T, LerpFunc>> MakeSubPiecewiseLinearFunction(
      double min_x_hint, double max_x_hint) const;

 private:
  int n_samples_ = 0;
  std::vector<double> x_samples_;
  std::vector<T> y_samples_;

  // If this function is initialized by uniform x sampling, we can get lower index of x by
  // (x - x0) / x_step fast and easily.
  bool is_uniform_ = false;
  double x_step_inverse_ = 0.0;

  mutable int lower_index_hint_ = -1;

  FRIEND_TEST(PiecewiseLinearFunctionTest, Constructor3);
  FRIEND_TEST(PiecewiseLinearFunctionTest, Constructor4);

  // Shallow copy and move are OK.
};

template <typename T, typename LerpFunc>
PiecewiseLinearFunction<T, LerpFunc>::PiecewiseLinearFunction(std::vector<double> x_samples,
                                                              std::vector<T> y_samples) {
  CHECK_EQ(x_samples.size(), y_samples.size());
  n_samples_ = x_samples.size();
  x_samples_ = std::move(x_samples);
  y_samples_ = std::move(y_samples);
  for (int i = 1; i < n_samples_; ++i) {
    DCHECK_LT(x_samples_[i - 1], x_samples_[i])
        << DUMP_TO_STREAM(i, x_samples_[i - 1], x_samples_[i]);
  }
  if (n_samples_ >= 2) {
    CHECK_GE(x_samples_.back() - x_samples_.front(), math::kEpsilon)
        << DUMP_TO_STREAM(x_samples_.front(), x_samples_.back());
  }
}

template <typename T, typename LerpFunc>
PiecewiseLinearFunction<T, LerpFunc>::PiecewiseLinearFunction(
    std::vector<double> x_samples, const std::function<T(double)>& generator) {
  n_samples_ = x_samples.size();
  x_samples_ = std::move(x_samples);
  y_samples_.reserve(n_samples_);
  for (int i = 1; i < n_samples_; ++i) {
    DCHECK_LT(x_samples_[i - 1], x_samples_[i])
        << DUMP_TO_STREAM(i, x_samples_[i - 1], x_samples_[i]);
  }
  for (double x : x_samples_) {
    y_samples_.push_back(generator(x));
  }
  if (n_samples_ >= 2) {
    CHECK_GE(x_samples_.back() - x_samples_.front(), math::kEpsilon)
        << DUMP_TO_STREAM(x_samples_.front(), x_samples_.back());
  }
}

template <typename T, typename LerpFunc>
PiecewiseLinearFunction<T, LerpFunc>::PiecewiseLinearFunction(
    double x0, double x_step, int n_samples, const std::function<T(double)>& generator) {
  CHECK_GE(x_step, math::kEpsilon);
  n_samples_ = n_samples;
  x_samples_.reserve(n_samples_);
  y_samples_.reserve(n_samples_);
  for (int i = 0; i < n_samples_; ++i) {
    x_samples_.push_back(x0 + i * x_step);
    y_samples_.push_back(generator(x_samples_.back()));
  }
  is_uniform_ = true;
  x_step_inverse_ = 1.0 / x_step;
}

template <typename T, typename LerpFunc>
PiecewiseLinearFunction<T, LerpFunc>::PiecewiseLinearFunction(double x0,
                                                              double x_step,
                                                              std::vector<T> y_samples) {
  CHECK_GE(x_step, math::kEpsilon);
  n_samples_ = y_samples.size();
  x_samples_.reserve(n_samples_);
  y_samples_ = std::move(y_samples);
  for (int i = 0; i < n_samples_; ++i) {
    x_samples_.push_back(x0 + i * x_step);
  }
  is_uniform_ = true;
  x_step_inverse_ = 1.0 / x_step;
}

template <typename T, typename LerpFunc>
PiecewiseLinearFunction<T, LerpFunc>::PiecewiseLinearFunction(
    std::vector<T> y_samples, const std::function<double(int, const T&)>& transformer) {
  n_samples_ = y_samples.size();
  x_samples_.reserve(n_samples_);
  y_samples_ = std::move(y_samples);
  for (int i = 0; i < n_samples_; ++i) {
    x_samples_.push_back(transformer(i, y_samples_[i]));
  }
  if (n_samples_ >= 2) {
    CHECK_GE(x_samples_.back() - x_samples_.front(), math::kEpsilon)
        << DUMP_TO_STREAM(x_samples_.front(), x_samples_.back());
  }
}

template <typename T, typename LerpFunc>
void PiecewiseLinearFunction<T, LerpFunc>::ShiftX(double x0) {
  CHECK_NE(n_samples_, 0);
  const double delta = x0 - x_samples_[0];
  for (int i = 1; i < n_samples_; ++i) {
    x_samples_[i] += delta;
  }
  x_samples_[0] = x0;
}

template <typename T, typename LerpFunc>
void PiecewiseLinearFunction<T, LerpFunc>::ShiftY(const T& y0) {
  CHECK_NE(n_samples_, 0);
  const T delta = y0 - y_samples_[0];
  for (int i = 1; i < n_samples_; ++i) {
    y_samples_[i] += delta;
  }
  y_samples_[0] = y0;
}

template <typename T, typename LerpFunc>
base::Optional<PiecewiseLinearFunction<T, LerpFunc>>
PiecewiseLinearFunction<T, LerpFunc>::MakeSubPiecewiseLinearFunction(double min_x_hint,
                                                                     double max_x_hint) const {
  CHECK_LE(min_x_hint, max_x_hint);
  if (n_samples_ < 2) {
    return base::none;
  }
  const double min_x = std::max(min_x_hint, x_samples_.front());
  const double max_x = std::min(max_x_hint, x_samples_.back());
  if (max_x - min_x < kEpsilon) {
    return base::none;
  }
  const LerpRatio min_lerp_ratio = GetLerpRatio(min_x);
  const LerpRatio max_lerp_ratio = GetLerpRatio(max_x);
  std::vector<double> x_samples = std::vector<double>(x_samples_.begin() + min_lerp_ratio.i,
                                                      x_samples_.begin() + max_lerp_ratio.i + 1);
  std::vector<T> y_samples = std::vector<T>(y_samples_.begin() + min_lerp_ratio.i,
                                            y_samples_.begin() + max_lerp_ratio.i + 1);
  x_samples.front() = Lerp(x_samples_, min_lerp_ratio);
  y_samples.front() = Lerp(y_samples_, min_lerp_ratio);
  if (max_lerp_ratio.r > kEpsilon) {
    x_samples.push_back(Lerp(x_samples_, max_lerp_ratio));
    y_samples.push_back(Lerp(y_samples_, max_lerp_ratio));
  }
  return PiecewiseLinearFunction<T, LerpFunc>(std::move(x_samples), std::move(y_samples));
}

template <typename T, typename LerpFunc = LerpOperator<T, double>>
class PiecewiseLinearFunctionView final {
 public:
  PiecewiseLinearFunctionView() = default;

  PiecewiseLinearFunctionView(const base::ConstSpan<double>& x_samples,
                              const base::ConstSpan<T>& y_samples);

  PiecewiseLinearFunctionView(double x0, double x_step, const base::ConstSpan<T>& y_samples);

  int n_samples() const { return n_samples_; }

  const base::ConstSpan<T>& y_samples() const { return y_samples_; }

  double min_x() const { return x_min_; }
  double max_x() const { return x_max_; }

  double x_sample(int i) const { return is_uniform_ ? x_min_ + i * x_step_ : x_samples_[i]; }
  const T& y_sample(int i) const { return y_samples_[i]; }

  bool is_uniform() const { return is_uniform_; }

  // Check if x is inside [min_x,max_x] with epsilon.
  bool IsInside(double x, double epsilon = math::kEpsilon) const {
    return n_samples_ != 0 && math::IsInside(x, x_min_, x_max_, epsilon);
  }

  // Uses the linear interpolation when x is inside the range [min_x,max_x].
  // Otherwise, uses the nearest neighbor when extrapolate is false.
  T operator()(double x, bool extrapolate = false) const {
    return Lerp<base::ConstSpan<T>, T, LerpFunc>(y_samples_, GetLerpRatio(x, extrapolate));
  }

  LerpRatio GetLerpRatio(double x, bool extrapolate = false) const {
    CHECK_NE(n_samples_, 0);
    return is_uniform_ ? math::GetLerpRatioWithStepInverse(
                             x_min_, x_step_inverse_, n_samples_, x, extrapolate)
                       : math::GetLerpRatio(x_samples_, x, &lower_index_hint_, extrapolate);
  }

 private:
  int n_samples_ = 0;
  base::ConstSpan<double> x_samples_;
  base::ConstSpan<T> y_samples_;

  bool is_uniform_ = false;
  double x_min_ = 0.0;
  double x_max_ = 0.0;
  double x_step_ = 0.0;
  double x_step_inverse_ = 0.0;

  mutable int lower_index_hint_ = -1;

  FRIEND_TEST(PiecewiseLinearFunctionTest, Constructor4);

  // Shallow copy and move are OK.
};

template <typename T, typename LerpFunc>
PiecewiseLinearFunctionView<T, LerpFunc>::PiecewiseLinearFunctionView(
    const base::ConstSpan<double>& x_samples, const base::ConstSpan<T>& y_samples) {
  CHECK_EQ(x_samples.size(), y_samples.size());
  n_samples_ = x_samples.size();
  x_samples_ = x_samples;
  y_samples_ = y_samples;
  for (int i = 1; i < n_samples_; ++i) {
    DCHECK_LT(x_samples_[i - 1], x_samples_[i])
        << DUMP_TO_STREAM(i, x_samples_[i - 1], x_samples_[i]);
  }
  if (n_samples_ != 0) {
    x_min_ = x_samples_.front();
    x_max_ = x_samples_.back();
  }
  if (n_samples_ >= 2) {
    CHECK_GE(x_max_ - x_min_, math::kEpsilon) << DUMP_TO_STREAM(n_samples_, x_min_, x_max_);
  }
}

template <typename T, typename LerpFunc>
PiecewiseLinearFunctionView<T, LerpFunc>::PiecewiseLinearFunctionView(
    double x0, double x_step, const base::ConstSpan<T>& y_samples) {
  CHECK_GE(x_step, math::kEpsilon);
  n_samples_ = y_samples.size();
  y_samples_ = y_samples;
  if (n_samples_ != 0) {
    x_min_ = x0;
    x_max_ = x0 + (n_samples_ - 1) * x_step;
  }
  is_uniform_ = true;
  x_step_ = x_step;
  x_step_inverse_ = 1.0 / x_step;
}

}  // namespace math
