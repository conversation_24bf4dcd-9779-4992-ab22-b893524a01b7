// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>
//          <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)

#include "base/math/vector3.h"

#include "Eigen/Core"
#include "glog/logging.h"
#include "gtest/gtest.h"

namespace math {

TEST(Vector3Test, NormalCases) {
  Vector3d p0;
  ASSERT_NEAR(0.0, p0.x, 1e-6);
  ASSERT_NEAR(0.0, p0.y, 1e-6);
  ASSERT_NEAR(0.0, p0.z, 1e-6);
  ASSERT_NEAR(p0.Length(), 0.0, 1e-6);
  ASSERT_NEAR(p0.LengthSquare(), 0.0, 1e-6);

  Vector3d p1(2.0, 3.0, 4.0);
  ASSERT_NEAR(2.0, p1.x, 1e-6);
  ASSERT_NEAR(3.0, p1.y, 1e-6);
  ASSERT_NEAR(4.0, p1.z, 1e-6);
  ASSERT_NEAR(p1.Length(), std::sqrt(29.0), 1e-6);
  ASSERT_NEAR(p1.LengthSquare(), 29.0, 1e-6);

  Vector3f p2(Vector2f(3.0, 4.0), 5.0);
  ASSERT_NEAR(3.0, p2.xy().x, 1e-6);
  ASSERT_NEAR(4.0, p2.xy().y, 1e-6);
  ASSERT_NEAR(5.0, p2.z, 1e-6);
  ASSERT_NEAR(p2.Length(), std::sqrt(50.0), 1e-6);
  ASSERT_NEAR(p2.LengthSquare(), 50.0, 1e-6);

  Vector3d p3(p2);
  ASSERT_NEAR(3.0, p3[0], 1e-6);
  ASSERT_NEAR(4.0, p3[1], 1e-6);
  ASSERT_NEAR(5.0, p3[2], 1e-6);
  ASSERT_NEAR(p3.Length(), std::sqrt(50.0), 1e-6);
  ASSERT_NEAR(p3.LengthSquare(), 50.0, 1e-6);
}

TEST(Vector3Test, Normalize) {
  Vector3d p1(1.0, 2.0, 3.0);
  Vector3d p2 = p1.Normalized();
  Vector3d p3 = p1;
  p3.Normalize();
  ASSERT_NEAR(p2.DistanceTo(p3), 0.0, 1e-6);
  ASSERT_NEAR(p2.Length(), 1.0, 1e-6);
  ASSERT_NEAR(p3.Length(), 1.0, 1e-6);
  ASSERT_NEAR(1.0, p1.x, 1e-6);
  ASSERT_NEAR(2.0, p1.y, 1e-6);
  ASSERT_NEAR(3.0, p1.z, 1e-6);
}

TEST(Vector3Test, Distance) {
  Vector3d p1(1.0, 2.0, 3.0);
  Vector3d p2(3.0, 4.0, 5.0);
  Vector3d p3(0.0, 5.0, 6.0);
  ASSERT_NEAR(std::sqrt(0.0), p1.DistanceTo(p1), 1e-6);
  ASSERT_NEAR(0.0, p1.DistanceSquareTo(p1), 1e-6);
  ASSERT_NEAR(std::sqrt(12.0), p1.DistanceTo(p2), 1e-6);
  ASSERT_NEAR(12.0, p1.DistanceSquareTo(p2), 1e-6);
  ASSERT_NEAR(std::sqrt(11.0), p2.DistanceTo(p3), 1e-6);
  ASSERT_NEAR(11.0, p2.DistanceSquareTo(p3), 1e-6);
  ASSERT_NEAR(std::sqrt(19.0), p3.DistanceTo(p1), 1e-6);
  ASSERT_NEAR(19.0, p3.DistanceSquareTo(p1), 1e-6);
  ASSERT_NEAR(std::sqrt(33.0), p1.DistanceTo(+6.0, +0.0, +1.0), 1e-6);
  ASSERT_NEAR(33.0, p1.DistanceSquareTo(+6.0, +0.0, +1.0), 1e-6);
  ASSERT_NEAR(std::sqrt(83.0), p1.DistanceTo(-2.0, -3.0, -4.0), 1e-6);
  ASSERT_NEAR(83.0, p1.DistanceSquareTo(-2.0, -3.0, -4.0), 1e-6);
}

TEST(Vector3Test, Operators) {
  const Vector3d p1(1.0, 2.0, 3.0);
  const Vector3d p2(3.0, 4.0, 5.0);
  ASSERT_NEAR(3.0, p2[0], 1e-6);
  ASSERT_NEAR(4.0, p2[1], 1e-6);
  ASSERT_NEAR(5.0, p2[2], 1e-6);

  ASSERT_TRUE((p1 + p2).IsNear(Vector3d(4.0, 6.0, 8.0)));
  ASSERT_TRUE((p2 - p1).IsNear(Vector3d(2.0, 2.0, 2.0)));

  Vector3d p3 = p1 + Vector3d(-1.0, -2.0, -3.0);
  ASSERT_TRUE(p3.IsNear(Vector3d(0.0, 0.0, 0.0)));
  p3 += p1;
  ASSERT_TRUE(p3.IsNear(p1));
  p3 -= p1;
  ASSERT_TRUE(p3.IsNear(Vector3d(0.0, 0.0, 0.0)));

  Vector3d p4 = (p3 += p1) * 2.0;
  ASSERT_TRUE(p3.IsNear(p1));
  ASSERT_TRUE(p4.IsNear(Vector3d(2.0, 4.0, 6.0)));
  p4 /= 2.0;
  ASSERT_TRUE(p4.IsNear(p1));

  Vector3d p5 = 2.0 * p1;
  ASSERT_TRUE(p5.IsNear(Vector3d(2.0, 4.0, 6.0)));
  p5 *= 0.5;
  ASSERT_TRUE(p5.IsNear(p1));

  Vector3d p6 = p1 / 0.5;
  ASSERT_TRUE(p6.IsNear(Vector3d(2.0, 4.0, 6.0)));

  Vector3d p7 = -p1;
  ASSERT_TRUE(p7.IsNear(Vector3d(-1.0, -2.0, -3.0)));

  Vector3d p8 = Vector3d(Eigen::Vector3d(1.0, 2.0, 3.0));
  ASSERT_TRUE(p8.IsNear(p1));
}

TEST(Vector3Test, CrossAndInnerProd) {
  const std::array<Vector3d, 3> points0 = {
      Vector3d(1.0, 0.0, 0.0),
      Vector3d(0.0, 1.0, 0.0),
      Vector3d(0.0, 0.0, 1.0),
  };
  for (int i = 0; i < 3; ++i) {
    const auto& p0 = points0[i];
    const auto& p1 = points0[(i + 1) % 3];
    const auto& p2 = points0[(i + 2) % 3];
    ASSERT_NEAR(p0.CrossProd(p1).DistanceTo(p2), 0.0, 1e-6);
  }

  const std::array<Vector3d, 3> points1 = {
      Vector3d(1.0, 2.0, 3.0),
      Vector3d(2.5, 3.8, 5.0),
      Vector3d(5.6, 7.3, 2.2),
  };
  for (int i = 0; i < 3; ++i) {
    for (int j = 0; j < 3; ++j) {
      const auto& p0 = points1[i];
      const auto& p1 = points1[j];
      const auto t = p0.CrossProd(p1);
      ASSERT_NEAR(t.InnerProd(p0), 0.0, 1e-6);
      ASSERT_NEAR(t.InnerProd(p1), 0.0, 1e-6);
      ASSERT_NEAR(t[0], p0.y * p1.z - p0.z * p1.y, 1e-6);
      ASSERT_NEAR(t[1], p0.z * p1.x - p0.x * p1.z, 1e-6);
      ASSERT_NEAR(t[2], p0.x * p1.y - p0.y * p1.x, 1e-6);
    }
  }
}

TEST(Vector3Test, Lerp) {
  Vector3d p0(0.0, 1.0, 2.0);
  Vector3d p1(1.0, 2.0, 3.0);
  ASSERT_NEAR(Lerp(p0, p1, 0.0).DistanceTo(p0), 0.0, 1e-6);
  ASSERT_NEAR(Lerp(p0, p1, 1.0).DistanceTo(p1), 0.0, 1e-6);
  ASSERT_NEAR(Lerp(p0, p1, 0.2).DistanceTo(Vector3d(0.2, 1.2, 2.2)), 0.0, 1e-6);
}

}  // namespace math
