// Copyright @2022 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>
//          <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)

#pragma once

#include <string>
#include <vector>

#include "gtest/gtest.h"

#include "base/math/axis_align_box2d.h"
#include "base/math/line_segment2d.h"
#include "base/math/oriented_box2d.h"
#include "base/math/vector2.h"

namespace math {

class Polygon2d final {
 public:
  Polygon2d() = default;

  explicit Polygon2d(const AxisAlignBox2d& box);
  explicit Polygon2d(const OrientedBox2d& box);
  explicit Polygon2d(const LineSegment2d& segment);
  explicit Polygon2d(const math_proto::Polygon2d& proto);
  explicit Polygon2d(std::vector<Vector2d> points);

  static Polygon2d FromPoints(const base::ConstSpan<Vector2d>& points);

  const std::vector<Vector2d>& points() const { return points_; }

  int num_points() const { return num_points_; }
  bool is_convex() const { return is_convex_; }
  double area() const { return area_; }

  const AxisAlignBox2d& aabox() const { return aabox_; }
  double min_x() const { return aabox_.min_x(); }
  double max_x() const { return aabox_.max_x(); }
  double min_y() const { return aabox_.min_y(); }
  double max_y() const { return aabox_.max_y(); }

  // Build the polygon, return if the result polygon has non-zero area.
  bool BuildFromPoints(std::vector<Vector2d> points);

  Vector2d GetCentroid() const;
  double GetPerimeter() const;

  std::vector<LineSegment2d> GetLineSegments() const;

  bool IsPointIn(const Vector2d& point) const;
  bool IsPointOnBoundary(const Vector2d& point) const;

  double DistanceTo(const Vector2d& point) const;
  double DistanceSquareTo(const Vector2d& point) const;
  double DistanceTo(const LineSegment2d& segment) const;
  double DistanceTo(const OrientedBox2d& box) const;
  double DistanceTo(const Polygon2d& other_polygon) const;
  double DistanceTo(const Polygon2d& other_polygon, const Vector2d& direction) const;

  double DistanceToBoundary(const Vector2d& point) const;

  bool Contains(const LineSegment2d& segment) const;
  bool Contains(const Polygon2d& other_polygon) const;

  static bool IsConvexHull(const base::ConstSpan<Vector2d>& points);
  static bool ComputeConvexHull(const base::ConstSpan<Vector2d>& points, Polygon2d* result_polygon);

  static std::vector<Vector2d> ComputeConvexHullAsPoints(std::vector<Vector2d> points,
                                                         bool check_convex_first = false);

  bool HasOverlap(const LineSegment2d& segment) const;
  bool HasOverlap(const OrientedBox2d& box) const;
  bool HasOverlap(const Polygon2d& other_polygon) const;
  bool GetOverlap(const LineSegment2d& segment, Vector2d* min_point, Vector2d* max_point) const;
  std::vector<LineSegment2d> GetAllOverlaps(const LineSegment2d& segment) const;
  std::vector<double> GetOverlapProjectS(const LineSegment2d& segment) const;

  // TODO(wangnaizheng): support non-convex polygon.
  bool ComputeOverlap(const Polygon2d& other_polygon, Polygon2d* result_polygon) const;
  std::vector<Vector2d> ComputeOverlapAsPoints(const Polygon2d& other_polygon) const;

  // TODO(wangnaizheng): find some meaningful names for these API.
  // Compute the ratio of the overlapping area to the current polygon.
  double ComputeOverlapAreaWith(const Polygon2d& other_polygon) const;
  double ComputeOverlapRatioWith(const Polygon2d& other_polygon) const;
  double ********************************(const Polygon2d& other_polygon) const;
  double ComputeIoUWith(const Polygon2d& other_polygon) const;

  OrientedBox2d BoundingBoxWithHeading(double heading) const;
  OrientedBox2d BoundingBoxWithDirection(const Vector2d& direction) const;
  OrientedBox2d MinAreaBoundingBox() const;

  static OrientedBox2d BoundingBoxWithHeading(const base::ConstSpan<Vector2d>& points,
                                              double heading);
  static OrientedBox2d BoundingBoxWithDirection(const base::ConstSpan<Vector2d>& points,
                                                const Vector2d& direction);

  void ExtremePoints(const Vector2d& direction, Vector2d* min_point, Vector2d* max_point) const;
  void ExtremeProjections(const Vector2d& direction,
                          double* min_projection,
                          double* max_projection) const;

  static void ExtremePoints(const base::ConstSpan<Vector2d>& points,
                            const Vector2d& direction,
                            Vector2d* min_point,
                            Vector2d* max_point);
  static void ExtremeProjections(const base::ConstSpan<Vector2d>& points,
                                 const Vector2d& direction,
                                 double* min_projection,
                                 double* max_projection);

  // Compute the convex hull, then expand the convex polygon with distance.
  Polygon2d ExpandByDistance(double distance, double angle_step_hint = M_PI / 6.0) const;

  // Compute minkowski-sum of two convex polygons.
  Polygon2d MinkowskiSum(const Polygon2d& other_polygon) const;

  Polygon2d Shifted(const Vector2d& offset) const;
  Polygon2d Rotated(const Vector2d& center, double angle) const;

  void Shift(const Vector2d& offset);
  void Rotate(const Vector2d& center, double angle);

  void ShiftAndRotate(const Vector2d& old_center, const Vector2d& new_center, double angle);

  // Partition polygon into triangles.
  // Vertices 0-1-2 form a triangle, vertices 3-4-5 form a triangle, and so on.
  std::vector<Vector2d> PartitionIntoTriangles() const;
  std::vector<int> PartitionIntoTrianglesAsIndex() const;

  // Divide the 2d plane into grid_resolution x grid_resolution square grid cells.
  // Find all cells which are touched by polygon.
  std::vector<Vector2i> GetTouchedGrids(double grid_resolution,
                                        const Vector2d& base_point = kZeroPoint) const;
  std::vector<Vector2d> GetTouchedGridCenters(double grid_resolution,
                                              const Vector2d& base_point = kZeroPoint) const;
  // Find all cells whose center points are inside or on this polygon.
  std::vector<Vector2i> GetInteriorGrids(double grid_resolution,
                                         const Vector2d& base_point = kZeroPoint) const;
  std::vector<Vector2d> GetInteriorGridCenters(double grid_resolution,
                                               const Vector2d& base_point = kZeroPoint) const;

  std::vector<Vector2d> GetSamplePointsOnPolygonEdge(int max_num_of_points,
                                                     double min_step = math::kEpsilon) const;

  std::string ToString() const;

  math_proto::Polygon2d ToProto() const;

 private:
  static constexpr Vector2d kZeroPoint = Vector2d(0.0, 0.0);

  constexpr int Next(int i) const { return i != num_points_ - 1 ? i + 1 : 0; }
  constexpr int Prev(int i) const { return i != 0 ? i - 1 : num_points_ - 1; }

  LineSegment2d GetSegment(int i) const { return LineSegment2d(points_[i], points_[Next(i)]); }

  std::vector<Vector2i> GetTouchedGrids(const Vector2d& base_point,
                                        double grid_resolution,
                                        bool interior_grids_only) const;
  std::vector<Vector2d> GetTouchedGridCenters(const Vector2d& base_point,
                                              double grid_resolution,
                                              bool interior_grids_only) const;

  std::vector<Vector2d> points_;
  int num_points_ = 0;
  bool is_convex_ = false;
  double area_ = 0.0;

  AxisAlignBox2d aabox_;

  friend class Polygon2dTest;

  // Shallow copy and move are OK.
};

}  // namespace math
