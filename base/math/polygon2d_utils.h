// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>
//          <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)

#pragma once

#include <vector>

#include "base/container/span.h"
#include "base/math/fast_math.h"
#include "base/math/vector2.h"

namespace math {

// Compute polygon area in counter-clockwise order.
double ComputePolygonAreaAsPoints(const base::ConstSpan<Vector2d>& points);

// Compute the centroid or geometric/gravity center of the given polyogn.
// https://en.wikipedia.org/wiki/Centroid#Of_a_polygon
Vector2d ComputePolygonCentroidAsPoints(const base::ConstSpan<Vector2d>& points);

// TODO(wangnaizheng): use base::Span.
// Sort points in counter-clockwise order.
void SortPointsInCCWOrder(std::vector<Vector2d>* points);

inline void ShiftPolygonAsPoints(base::Span<Vector2d> points, const Vector2d& offset) {
  for (Vector2d& p : points) {
    p += offset;
  }
}

inline void RotatePolygonAsPoints(base::Span<Vector2d> points,
                                  const Vector2d& center,
                                  double angle) {
  if (points.empty()) {
    return;
  }
  const auto sin_and_cos = math::SinCos(angle);
  const double sin_angle = sin_and_cos.first;
  const double cos_angle = sin_and_cos.second;
  const Vector2d offset = center - center.Rotate(cos_angle, sin_angle);
  for (Vector2d& p : points) {
    p = p.Rotate(cos_angle, sin_angle) + offset;
  }
}

inline void ShiftAndRotatePolygonAsPoints(base::Span<Vector2d> points,
                                          const Vector2d& old_center,
                                          const Vector2d& new_center,
                                          double angle) {
  if (points.empty()) {
    return;
  }
  const auto sin_and_cos = math::SinCos(angle);
  const double sin_angle = sin_and_cos.first;
  const double cos_angle = sin_and_cos.second;
  const Vector2d offset = new_center - old_center.Rotate(cos_angle, sin_angle);
  for (Vector2d& p : points) {
    p = p.Rotate(cos_angle, sin_angle) + offset;
  }
}

}  // namespace math
