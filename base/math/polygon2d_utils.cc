// Copyright @2021 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>
//          <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)

#include "base/math/polygon2d_utils.h"

#include <algorithm>
#include <numeric>

namespace math {

double ComputePolygonAreaAsPoints(const base::ConstSpan<Vector2d>& points) {
  const int num_points = points.size();
  if (num_points < 3) {
    return 0.0;
  }
  double area = 0.0;
  for (int i = 1; i < num_points; ++i) {
    area += CrossProd(points[0], points[i - 1], points[i]);
  }
  return area * 0.5;
}

Vector2d ComputePolygonCentroidAsPoints(const base::ConstSpan<Vector2d>& points) {
  CHECK(!points.empty());
  const int num_points = points.size();
  double weight = 0.0;
  Vector2d center;
  for (int i = 2; i < num_points; ++i) {
    const Vector2d d0 = points[i - 1] - points[0];
    const Vector2d d1 = points[i] - points[0];
    const double product = d0.CrossProd(d1);
    center += (d0 + d1) * product;
    weight += product;
  }
  if (std::abs(weight) < math::kEpsilon) {
    return std::accumulate(points.begin(), points.end(), Vector2d(0.0, 0.0)) / num_points;
  }
  return center / (3.0 * weight) + points[0];
}

void SortPointsInCCWOrder(std::vector<Vector2d>* points) {
  const int num_points = CHECK_NOTNULL(points)->size();
  if (num_points < 3) {
    return;
  }
  const Vector2d center =
      std::accumulate(points->begin(), points->end(), Vector2d(0.0, 0.0)) / num_points;
  std::vector<double> angles(num_points);
  std::transform(points->begin(), points->end(), angles.begin(), [&center](const Vector2d& point) {
    return (point - center).Angle();
  });
  std::vector<int> sorted_indices(num_points);
  std::iota(sorted_indices.begin(), sorted_indices.end(), 0);
  std::sort(sorted_indices.begin(), sorted_indices.end(), [&angles](int idx1, int idx2) {
    return angles[idx1] < angles[idx2];
  });
  std::vector<Vector2d> sorted_points(num_points);
  std::transform(
      sorted_indices.begin(), sorted_indices.end(), sorted_points.begin(), [&points](int index) {
        return (*points)[index];
      });
  points->swap(sorted_points);
}

}  // namespace math
