// Copyright @2024 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <vector>

#include "base/math/vector2.h"

namespace math {
namespace math_internal {

class TouchedGridsComputer {
 public:
  TouchedGridsComputer(const math::Vector2i& base_point_index,
                       double area_ratio_polygon_aabox,
                       int num_x_grids,
                       int num_y_grids)
      : base_point_index_(base_point_index),
        area_ratio_polygon_aabox_(area_ratio_polygon_aabox),
        num_x_grids_(num_x_grids),
        num_y_grids_(num_y_grids) {
    CHECK_GT(num_y_grids, 0);
    CHECK_GE(std::numeric_limits<int>::max() / num_y_grids_, num_x_grids_);
  }

  void AddGrids(int segment_min_xi, int segment_max_xi, int segment_yi);
  std::vector<math::Vector2i> Build() const;

 private:
  struct SegmentGrids {
    int segment_min_xi = 0;
    int segment_max_xi = 0;
    int segment_yi = 0;
  };

  std::vector<math::Vector2i> TouchedGridsWithHash() const;
  std::vector<math::Vector2i> TouchedGridsWithFlat() const;

  int GridIndex(int grid_xi, int grid_yi) const { return grid_yi * num_x_grids_ + grid_xi; }

  std::vector<SegmentGrids> segments_grids_;
  math::Vector2i base_point_index_;
  double area_ratio_polygon_aabox_ = 0.0;
  int num_x_grids_ = 0;
  int num_y_grids_ = 0;

  friend class TouchedGridsComputerTest;
};

}  // namespace math_internal
}  // namespace math
