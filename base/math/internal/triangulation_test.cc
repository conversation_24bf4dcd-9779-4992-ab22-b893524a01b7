// Copyright @2023 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/math/internal/triangulation.h"

#include <algorithm>
#include <limits>

#include "gmock/gmock.h"
#include "gtest/gtest.h"

#include "base/common/optional.h"
#include "base/testing/boost_geometry.h"
#include "base/testing/geometry.h"
#include "base/testing/random.h"

namespace math {
namespace math_internal {
namespace triangulation {

class TriangulatePolygonTest : public ::testing::Test {
 protected:
  void CheckTriangulatePolygon(const std::vector<Vector2d>& points, bool is_convex) const {
    const std::vector<Vector2d> points_in_ccw_order = SortInCCWOrder(points);
    const int num_points = points_in_ccw_order.size();
    const std::vector<Vector2d> triangles = TriangulatePolygon(points_in_ccw_order, is_convex);
    ASSERT_EQ(0, triangles.size() % 3);
    const int num_triangles = triangles.size() / 3;
    ASSERT_EQ(num_triangles, std::max(1, num_points - 2));
    const double expected_cross_prod = ComputeCrossProd(points_in_ccw_order);
    double cross_prod = 0.0;
    for (int i = 0; i < num_triangles; ++i) {
      cross_prod += ComputeCrossProd(base::MakeConstSpan(triangles, i * 3, (i + 1) * 3));
    }
    ASSERT_NEAR(expected_cross_prod, cross_prod, 1e-5);

    const testing::BoostPolygon2d boost_polygon =
        testing::ToBoostPolygonFromPoints(points_in_ccw_order);
    std::vector<testing::BoostPolygon2d> boost_triangles(num_triangles);
    for (int i = 0; i < num_triangles; ++i) {
      boost_triangles[i] =
          testing::ToBoostPolygonFromPoints(base::MakeConstSpan(triangles, i * 3, (i + 1) * 3));
    }

    const math::AxisAlignBox2d aabox(points_in_ccw_order);
    const int n_steps = 100;
    for (int i = 0; i <= n_steps; ++i) {
      for (int j = 0; j <= n_steps; ++j) {
        const double x = math::Lerp(aabox.min_x(), aabox.max_x(), static_cast<double>(i) / n_steps);
        const double y = math::Lerp(aabox.min_y(), aabox.max_y(), static_cast<double>(j) / n_steps);
        const testing::BoostVector2d boost_point = testing::ToBoost(math::Vector2d(x, y));
        const double distance_to_polygon = testing::BoostDistanceTo(boost_polygon, boost_point);
        double distance_to_triangles = std::numeric_limits<double>::infinity();
        for (int k = 0; k < static_cast<int>(boost_triangles.size()); ++k) {
          distance_to_triangles = std::min(
              distance_to_triangles, testing::BoostDistanceTo(boost_triangles[k], boost_point));
        }
        ASSERT_NEAR(distance_to_polygon, distance_to_triangles, 1e-5);
      }
    }
  }

  std::vector<Vector2d> SortInCCWOrder(std::vector<Vector2d> points) const {
    const int num_points = points.size();
    if (num_points < 3) {
      return points;
    }
    if (ComputeCrossProd(points) < 0.0) {
      std::reverse(points.begin(), points.end());
    }
    return points;
  }

 private:
  double ComputeCrossProd(const base::ConstSpan<Vector2d>& points) const {
    const int num_points = points.size();
    double cross_prod = 0.0;
    for (int i = 1; i < num_points; ++i) {
      cross_prod += CrossProd(points[0], points[i - 1], points[i]);
    }
    return cross_prod;
  }
};

TEST_F(TriangulatePolygonTest, TriangulatePolygon0) {
  {
    const std::vector<Vector2d> points = {{0.0, 0.0}};
    ASSERT_NO_FATAL_FAILURE(CheckTriangulatePolygon(points, false));
  }
  {
    const std::vector<Vector2d> points = {{0.0, 0.0}, {1.0, 1.0}};
    ASSERT_NO_FATAL_FAILURE(CheckTriangulatePolygon(points, false));
  }
  {
    const std::vector<Vector2d> points = {{0.0, 0.0}, {2.0, 0.0}, {2.0, 2.0}};
    ASSERT_NO_FATAL_FAILURE(CheckTriangulatePolygon(points, false));
  }
  {
    const std::vector<Vector2d> points = {{0.0, 0.0}, {2.0, 0.0}, {2.0, 2.0}, {0.0, 2.0}};
    ASSERT_NO_FATAL_FAILURE(CheckTriangulatePolygon(points, false));
  }
  {
    const std::vector<Vector2d> points = {
        {0.0, 0.0}, {2.0, 0.0}, {1.0, 1.0}, {2.0, 2.0}, {0.0, 2.0}};
    ASSERT_NO_FATAL_FAILURE(CheckTriangulatePolygon(points, false));
  }
}

TEST_F(TriangulatePolygonTest, TriangulatePolygon1) {
  {
    const int n = 100;
    const double theta_step = M_PI * 2.0 / n;
    std::vector<Vector2d> points;
    for (int i = 0; i < n; ++i) {
      points.push_back(Vector2d::FromAngleAndLength(i * theta_step, 100.0));
    }
    ASSERT_NO_FATAL_FAILURE(CheckTriangulatePolygon(points, false));
    ASSERT_NO_FATAL_FAILURE(CheckTriangulatePolygon(points, true));
  }
  {
    const int n = 100;
    const double theta_step = M_PI * 2.0 / n;
    std::vector<Vector2d> points;
    for (int i = 0; i < n; ++i) {
      points.push_back(Vector2d::FromAngleAndLength(i * theta_step, 100.0));
      points.push_back(Vector2d::FromAngleAndLength(i * theta_step + theta_step * 0.5, 50.0));
    }
    ASSERT_NO_FATAL_FAILURE(CheckTriangulatePolygon(points, false));
  }
}

TEST_F(TriangulatePolygonTest, TriangulatePolygon2) {
  {
    const std::vector<Vector2d> points = {
        {810.0, 2828.0},
        {818.0, 2828.0},
        {832.0, 2818.0},
        {844.0, 2806.0},
        {855.0, 2808.0},
        {866.0, 2816.0},
        {867.0, 2824.0},
        {876.0, 2827.0},
        {883.0, 2834.0},
        {875.0, 2834.0},
        {867.0, 2840.0},
        {878.0, 2838.0},
        {889.0, 2844.0},
        {880.0, 2847.0},
        {870.0, 2847.0},
        {860.0, 2864.0},
        {852.0, 2879.0},
        {847.0, 2867.0},
    };
    ASSERT_NO_FATAL_FAILURE(CheckTriangulatePolygon(points, false));
  }
  {
    const std::vector<Vector2d> points = {
        {3432.0, 2779.0}, {3432.0, 2794.0}, {3450.0, 2794.0}, {3450.0, 2825.0}, {3413.0, 2825.0},
        {3413.0, 2856.0}, {3395.0, 2856.0}, {3395.0, 2871.0}, {3377.0, 2871.0}, {3377.0, 2856.0},
        {3359.0, 2856.0}, {3359.0, 2840.0}, {3341.0, 2840.0}, {3341.0, 2871.0}, {3322.0, 2871.0},
        {3322.0, 2887.0}, {3249.0, 2887.0}, {3249.0, 2871.0}, {3268.0, 2871.0}, {3268.0, 2840.0},
        {3304.0, 2840.0}, {3304.0, 2825.0}, {3322.0, 2825.0}, {3322.0, 2810.0}, {3304.0, 2810.0},
        {3304.0, 2794.0}, {3322.0, 2794.0}, {3322.0, 2779.0}, {3341.0, 2779.0}, {3341.0, 2733.0},
        {3359.0, 2733.0}, {3359.0, 2687.0}, {3395.0, 2687.0}, {3395.0, 2702.0}, {3432.0, 2702.0},
        {3432.0, 2717.0}, {3450.0, 2717.0}, {3450.0, 2733.0}, {3486.0, 2733.0}, {3486.0, 2748.0},
        {3468.0, 2748.0}, {3468.0, 2763.0}, {3450.0, 2763.0}, {3450.0, 2779.0},
    };
    ASSERT_NO_FATAL_FAILURE(CheckTriangulatePolygon(points, false));
  }
  {
    const std::vector<Vector2d> points = {
        {661.0, 112.0},
        {661.0, 96.0},
        {666.0, 96.0},
        {666.0, 87.0},
        {743.0, 87.0},
        {771.0, 87.0},
        {771.0, 114.0},
        {750.0, 114.0},
        {750.0, 113.0},
        {742.0, 113.0},
        {742.0, 106.0},
        {710.0, 106.0},
        {710.0, 113.0},
        {666.0, 113.0},
    };
    ASSERT_NO_FATAL_FAILURE(CheckTriangulatePolygon(points, false));
  }
  {
    const std::vector<Vector2d> points = {
        {100.0, 100.0},
        {100.0, 100.0},
        {200.0, 100.0},
        {200.0, 200.0},
        {200.0, 100.0},
        {0.0, 100.0},
    };
    ASSERT_NO_FATAL_FAILURE(CheckTriangulatePolygon(points, false));
  }
  {
    const std::vector<Vector2d> points = {
        {280.35714, 648.79075}, {286.78571, 662.8979},  {263.28607, 661.17871},
        {262.31092, 671.41548}, {250.53571, 677.00504}, {250.53571, 683.43361},
        {256.42857, 685.21933}, {297.14286, 669.50504}, {289.28571, 649.50504},
        {285, 631.6479},        {285, 608.79075},       {292.85714, 585.21932},
        {306.42857, 563.79075}, {323.57143, 548.79075}, {339.28571, 545.21932},
        {357.85714, 547.36218}, {375, 550.21932},       {391.42857, 568.07647},
        {404.28571, 588.79075}, {413.57143, 612.36218}, {417.14286, 628.07647},
        {438.57143, 619.1479},  {438.03572, 618.96932}, {437.5, 609.50504},
        {426.96429, 609.86218}, {424.64286, 615.57647}, {419.82143, 615.04075},
        {420.35714, 605.04075}, {428.39286, 598.43361}, {437.85714, 599.68361},
        {443.57143, 613.79075}, {450.71429, 610.21933}, {431.42857, 575.21932},
        {405.71429, 550.21932}, {372.85714, 534.50504}, {349.28571, 531.6479},
        {346.42857, 521.6479},  {346.42857, 511.6479},  {350.71429, 496.6479},
        {367.85714, 476.6479},  {377.14286, 460.93361}, {385.71429, 445.21932},
        {388.57143, 404.50504}, {360, 352.36218},       {337.14286, 325.93361},
        {330.71429, 334.50504}, {347.14286, 354.50504}, {337.85714, 370.21932},
        {333.57143, 359.50504}, {319.28571, 353.07647}, {312.85714, 366.6479},
        {350.71429, 387.36218}, {368.57143, 408.07647}, {375.71429, 431.6479},
        {372.14286, 454.50504}, {366.42857, 462.36218}, {352.85714, 462.36218},
        {336.42857, 456.6479},  {332.85714, 438.79075}, {338.57143, 423.79075},
        {338.57143, 411.6479},  {327.85714, 405.93361}, {320.71429, 407.36218},
        {315.71429, 423.07647}, {314.28571, 440.21932}, {325, 447.71932},
        {324.82143, 460.93361}, {317.85714, 470.57647}, {304.28571, 483.79075},
        {287.14286, 491.29075}, {263.03571, 498.61218}, {251.60714, 503.07647},
        {251.25, 533.61218},    {260.71429, 533.61218}, {272.85714, 528.43361},
        {286.07143, 518.61218}, {297.32143, 508.25504}, {297.85714, 507.36218},
        {298.39286, 506.46932}, {307.14286, 496.6479},  {312.67857, 491.6479},
        {317.32143, 503.07647}, {322.5, 514.1479},      {325.53571, 521.11218},
        {327.14286, 525.75504}, {326.96429, 535.04075}, {311.78571, 540.04075},
        {291.07143, 552.71932}, {274.82143, 568.43361}, {259.10714, 592.8979},
        {254.28571, 604.50504}, {251.07143, 621.11218}, {250.53571, 649.1479},
        {268.1955, 654.36208},
    };
    ASSERT_NO_FATAL_FAILURE(CheckTriangulatePolygon(points, false));
  }
  {
    const std::vector<Vector2d> points = {
        {2328.0, 2408.0}, {2328.0, 2472.0}, {2344.0, 2472.0}, {2344.0, 2432.0}, {2384.0, 2448.0},
        {2384.0, 2536.0}, {2408.0, 2552.0}, {2448.0, 2544.0}, {2456.0, 2560.0}, {2496.0, 2544.0},
        {2480.0, 2624.0}, {2456.0, 2664.0}, {2424.0, 2680.0}, {2400.0, 2768.0}, {2376.0, 2768.0},
        {2368.0, 2704.0}, {2336.0, 2704.0}, {2264.0, 2784.0}, {2216.0, 2784.0}, {2200.0, 2760.0},
        {2168.0, 2760.0}, {2152.0, 2744.0}, {2128.0, 2744.0}, {2128.0, 2784.0}, {2072.0, 2768.0},
        {2032.0, 2720.0}, {2000.0, 2720.0}, {2000.0, 2688.0}, {1936.0, 2696.0}, {1920.0, 2736.0},
        {1888.0, 2728.0}, {1896.0, 2696.0}, {1928.0, 2688.0}, {1928.0, 2664.0}, {1896.0, 2664.0},
        {1896.0, 2640.0}, {1912.0, 2632.0}, {1872.0, 2608.0}, {1888.0, 2576.0}, {2056.0, 2576.0},
        {2088.0, 2600.0}, {2184.0, 2608.0}, {2216.0, 2632.0}, {2256.0, 2624.0}, {2248.0, 2600.0},
        {2216.0, 2592.0}, {2192.0, 2560.0}, {2120.0, 2576.0}, {2072.0, 2544.0}, {2096.0, 2544.0},
        {2080.0, 2520.0}, {2080.0, 2488.0}, {2096.0, 2480.0}, {2080.0, 2448.0}, {2096.0, 2432.0},
        {2176.0, 2496.0}, {2200.0, 2488.0}, {2224.0, 2528.0}, {2248.0, 2528.0}, {2240.0, 2488.0},
        {2256.0, 2472.0}, {2280.0, 2480.0}, {2264.0, 2416.0}, {2272.0, 2392.0},
    };
    ASSERT_NO_FATAL_FAILURE(CheckTriangulatePolygon(points, false));
  }
  {
    const std::vector<Vector2d> points = {
        {2181.0, 1228.0}, {2182.0, 1231.0}, {2178.0, 1231.0}, {2180.0, 1228.0}, {2175.0, 1225.0},
        {2174.0, 1212.0}, {2182.0, 1210.0}, {2182.0, 1193.0}, {2190.0, 1187.0}, {2187.0, 1166.0},
        {2194.0, 1158.0}, {2186.0, 1149.0}, {2186.0, 1103.0}, {2195.0, 1091.0}, {2207.0, 1092.0},
        {2209.0, 1080.0}, {2203.0, 1077.0}, {2213.0, 1057.0}, {2213.0, 1035.0}, {2224.0, 1031.0},
        {2238.0, 983.0},  {2251.0, 982.0},  {2254.0, 965.0},  {2275.0, 970.0},  {2277.0, 948.0},
        {2317.0, 982.0},  {2317.0, 1030.0}, {2323.0, 1044.0}, {2306.0, 1041.0}, {2303.0, 1051.0},
        {2290.0, 1057.0}, {2294.0, 1062.0}, {2287.0, 1071.0}, {2294.0, 1081.0}, {2255.0, 1123.0},
        {2249.0, 1118.0}, {2253.0, 1128.0}, {2245.0, 1131.0}, {2249.0, 1137.0}, {2243.0, 1168.0},
        {2265.0, 1195.0}, {2253.0, 1203.0}, {2260.0, 1204.0}, {2252.0, 1215.0}, {2249.0, 1208.0},
        {2245.0, 1217.0}, {2232.0, 1220.0}, {2241.0, 1223.0}, {2235.0, 1223.0}, {2238.0, 1245.0},
        {2229.0, 1274.0}, {2215.0, 1272.0}, {2209.0, 1288.0}, {2196.0, 1288.0}, {2190.0, 1269.0},
        {2194.0, 1271.0}, {2195.0, 1262.0}, {2181.0, 1240.0}, {2182.0, 1233.0}, {2183.0, 1229.0},
    };
    ASSERT_NO_FATAL_FAILURE(CheckTriangulatePolygon(points, false));
  }
}

}  // namespace triangulation
}  // namespace math_internal
}  // namespace math
