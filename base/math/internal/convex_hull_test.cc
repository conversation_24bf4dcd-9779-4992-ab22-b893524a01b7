// Copyright @2022 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/math/internal/convex_hull.h"

#include "gmock/gmock.h"
#include "gtest/gtest.h"

#include "base/common/optional.h"
#include "base/math/internal/convex_utils.h"
#include "base/testing/boost_geometry.h"
#include "base/testing/geometry.h"
#include "base/testing/random.h"

namespace math {
namespace math_internal {
namespace convex {

using convex_internal::Next;

class ConvexHullTest : public ::testing::Test {
 protected:
  void CheckComputeConvexHull(
      const std::vector<Vector2d>& points,
      base::Optional<std::vector<Vector2d>> expected_convex_points = base::none) const {
    const std::vector<Vector2d> convex_points = ComputeConvexHull(points);
    const testing::BoostPolygon2d p0 =
        testing::BoostConvexHull(testing::ToBoostPolygonFromPoints(points));
    const testing::BoostPolygon2d p1 = testing::ToBoostPolygonFromPoints(convex_points);
    if (convex_points.size() >= 3) {
      ASSERT_TRUE(IsConvexHull(convex_points));
      ASSERT_NEAR(testing::BoostPolygonArea(p0), testing::BoostPolygonArea(p1), math::kEpsilon);
      ASSERT_NEAR(testing::BoostPolygonIntersectionOverUnion(p0, p1), 1.0, 1e-4);
    } else {
      ASSERT_NEAR(testing::BoostPolygonArea(p0), 0.0, math::kEpsilon);
      ASSERT_NEAR(testing::BoostPolygonArea(p1), 0.0, math::kEpsilon);
    }
    if (expected_convex_points) {
      ASSERT_THAT(convex_points, testing::UnorderedElementsAreArray(*expected_convex_points));
      ASSERT_EQ(convex_points.size(), expected_convex_points->size());
      if (!convex_points.empty()) {
        const auto iter = std::min_element(
            expected_convex_points->begin(), expected_convex_points->end(),
            [&convex_points](const auto& p0, const auto& p1) {
              return convex_points[0].DistanceSquareTo(p0) < convex_points[0].DistanceSquareTo(p1);
            });
        std::rotate(expected_convex_points->begin(), iter, expected_convex_points->end());
        ASSERT_THAT(convex_points, testing::ElementsAreArray(*expected_convex_points));
      }
    }
  }
};

TEST_F(ConvexHullTest, IsConvexHull) {
  ASSERT_FALSE(IsConvexHull({}));
  ASSERT_FALSE(IsConvexHull({{0.0, 0.0}}));
  ASSERT_FALSE(IsConvexHull({{0.0, 0.0}, {0.0, 1.0}}));
  ASSERT_FALSE(IsConvexHull({{0.0, 0.0}, {1.0, 0.0}, {2.0, 0.0}}));
  ASSERT_FALSE(IsConvexHull({{0.0, 0.0}, {1.0, 1.0}, {2.0, 0.0}}));
  {
    //
    //     +-----------+
    //     |           |
    //     |   +---+   |
    //     |   |   |   |
    //     |   +-------+
    //     |       |
    //     +-------+
    //
    const std::vector<Vector2d> points = {{0.0, 0.0}, {2.0, 0.0}, {2.0, 2.0}, {1.0, 2.0},
                                          {1.0, 1.0}, {3.0, 1.0}, {3.0, 3.0}, {0.0, 3.0}};
    const std::vector<Vector2d> convex_points = {
        {0.0, 0.0}, {2.0, 0.0}, {3.0, 1.0}, {3.0, 3.0}, {0.0, 3.0}};
    ASSERT_FALSE(IsConvexHull(points));
    ASSERT_NO_FATAL_FAILURE(CheckComputeConvexHull(points, convex_points));
  }
}

TEST_F(ConvexHullTest, ComputeConvexHull0) {
  const std::vector<Vector2d> points0 = {{1.0, 2.0}};
  ASSERT_NO_FATAL_FAILURE(CheckComputeConvexHull(points0, points0));
  const std::vector<Vector2d> points1 = {{3.0, 4.0}, {5.0, 6.0}};
  ASSERT_NO_FATAL_FAILURE(CheckComputeConvexHull(points1, points1));
  ASSERT_NO_FATAL_FAILURE(
      CheckComputeConvexHull({points1[0], points1[0], points1[1], points1[1]}, points1));
  const std::vector<Vector2d> points2 = {{0.0, 0.0}, {3.0, 0.0}, {0.0, 4.0}};
  ASSERT_NO_FATAL_FAILURE(CheckComputeConvexHull(points2, points2));
  ASSERT_NO_FATAL_FAILURE(
      CheckComputeConvexHull({points2[0], points2[1], points2[1], points2[2]}, points2));
  ASSERT_NO_FATAL_FAILURE(
      CheckComputeConvexHull({points2[0], points2[2], points2[1], points2[0]}, points2));
  const std::vector<Vector2d> points3 = {{0.0, 0.0}, {3.0, 0.0}, {3.0, 4.0}, {0.0, 4.0}};
  ASSERT_NO_FATAL_FAILURE(CheckComputeConvexHull(points3, points3));
  ASSERT_NO_FATAL_FAILURE(
      CheckComputeConvexHull({points3[0], points3[3], points3[1], points3[2]}, points3));
  ASSERT_NO_FATAL_FAILURE(
      CheckComputeConvexHull({points3[0], points3[3], points3[2], points3[1]}, points3));
  const std::vector<Vector2d> points4 = {{0.0, 0.0}, {6.0, 0.0}};
  ASSERT_NO_FATAL_FAILURE(
      CheckComputeConvexHull({points4[0], math::Lerp(points4[0], points4[1], 0.4),
                              math::Lerp(points4[0], points4[1], 0.6), points4[1]},
                             points4));
  ASSERT_NO_FATAL_FAILURE(
      CheckComputeConvexHull({points4[0], math::Lerp(points4[0], points4[1], 0.8),
                              math::Lerp(points4[0], points4[1], 0.2), points4[1]},
                             points4));
  const std::vector<Vector2d> points5 = {};
  ASSERT_NO_FATAL_FAILURE(CheckComputeConvexHull({}, points5));
  const std::vector<Vector2d> points6 = {{0.0, 0.0}};
  ASSERT_NO_FATAL_FAILURE(CheckComputeConvexHull({{0.0, 0.0}, {1e-10, 1e-10}}, points6));
}

TEST_F(ConvexHullTest, ComputeConvexHull1) {
  {
    const std::vector<Vector2d> convex_points = {{0.0, 0.0}, {10.0, 0.0}, {0.0, 10.0}};
    std::vector<Vector2d> points = convex_points;
    for (int i = 0; i < 3; ++i) {
      const Vector2d& p0 = convex_points[i];
      const Vector2d& p1 = convex_points[Next(i, convex_points.size())];
      for (int j = 0; j <= 10; ++j) {
        points.push_back(math::Lerp(p1, p0, 0.1 * j));
      }
    }
    ASSERT_NO_FATAL_FAILURE(CheckComputeConvexHull(points, convex_points));
  }
  {
    const std::vector<Vector2d> convex_points = {{0.0, 0.0}, {5.0, 0.0}, {5.0, 5.0}, {0.0, 5.0}};
    std::vector<Vector2d> points = convex_points;
    for (int i = 0; i < 4; ++i) {
      const Vector2d& p0 = convex_points[i];
      const Vector2d& p1 = convex_points[(i + testing::RandomInt(1, 3)) % 4];
      for (int j = 0; j <= 10; ++j) {
        points.push_back(math::Lerp(p0, p1, testing::RandomDouble(0.0, 1.0)));
      }
    }
    ASSERT_NO_FATAL_FAILURE(CheckComputeConvexHull(points, convex_points));
  }
  {
    const std::vector<Vector2d> convex_points = {
        {-1.0, -1.0}, {+2.0, -1.0}, {+2.0, +2.0}, {-1.0, +2.0}};
    const std::vector<Vector2d> points = {{+0.0, +0.0}, {+1.0, +0.0}, {+1.0, +1.0}, {-1.0, +1.0},
                                          {-1.0, -1.0}, {+2.0, -1.0}, {+2.0, +2.0}, {-1.0, +2.0}};
    ASSERT_NO_FATAL_FAILURE(CheckComputeConvexHull(points, convex_points));
  }
}

TEST_F(ConvexHullTest, ComputeConvexHull2) {
  {
    const std::vector<Vector2d> points = {
        {0.373181, 1.202527}, {0.624960, 5.939989}, {0.626321, 0.174774}, {1.372463, 0.330171},
        {2.625010, 7.437686}, {2.625008, 7.437693}, {2.625017, 7.313057}, {2.750006, 7.688055},
        {2.875004, 6.936947}, {3.125012, 6.561941}, {3.625011, 7.688081}, {3.750010, 7.688082},
        {3.875010, 7.686945}, {4.137332, 1.400244}, {4.363017, 2.224317}, {4.387551, 1.399961},
        {4.487467, 1.475056}, {4.487468, 1.850063}, {4.487614, 1.599868}, {6.249452, 3.445823},
        {7.999997, 6.938132}, {8.249006, 2.448656}, {9.123709, 1.950165}};
    ASSERT_NO_FATAL_FAILURE(CheckComputeConvexHull(points));
  }
  {
    const std::vector<Vector2d> points = {
        {0.048913, 3.095128}, {0.048912, 3.095116}, {0.077091, 2.100278}, {0.077093, 2.100277},
        {0.208276, 1.565525}, {0.527118, 0.788605}, {0.546892, 0.785558}, {0.616516, 0.784233},
        {1.454000, 0.790744}, {1.605257, 0.868854}, {1.676683, 0.928534}, {1.696861, 0.948142},
        {1.727749, 0.997596}, {1.783072, 1.198710}, {1.786115, 1.218476}, {1.799834, 1.359367},
        {1.827048, 2.351195}, {1.602496, 4.372728}, {1.523819, 4.885554}, {1.514873, 4.935751},
        {1.478024, 5.086519}, {1.402355, 5.346335}, {1.382581, 5.349389}, {0.337188, 4.021473},
        {0.054627, 3.281344}, {0.051583, 3.261586}, {0.051683, 3.261786}};
    ASSERT_NO_FATAL_FAILURE(CheckComputeConvexHull(points));
  }
  {
    const std::vector<Vector2d> convex_points = {{0.0, 0.0}, {1.0, 0.0}, {2.0, 1.0}};
    const std::vector<Vector2d> points = {{0.0, 0.0}, {1.0, 0.0}, {1.0, 1e-18},      {1.0, 1e-9},
                                          {1.0, 0.1}, {1.0, 0.1}, {1.0 - 1e-9, 0.1}, {2.0, 1.0}};
    ASSERT_NO_FATAL_FAILURE(CheckComputeConvexHull(points, convex_points));
  }
  {
    const int n = 10;
    std::vector<Vector2d> convex_points;
    for (int i = 0; i < n; ++i) {
      const double theta = M_PI * 2.0 * (static_cast<double>(i) / n);
      convex_points.push_back(Vector2d::FromAngle(theta));
    }
    std::vector<Vector2d> points = convex_points;
    for (int i = 0; i < n; ++i) {
      const Vector2d& p0 = convex_points[i];
      const Vector2d& p1 = convex_points[Next(i, n)];
      for (int j = 0; j < 20; ++j) {
        points.push_back(math::Lerp(p0, p1, testing::RandomDouble(0.0, 1.0)));
      }
      points.push_back(math::Lerp(p0, p1, 1e-9));
      points.push_back(math::Lerp(p0, p1, 1.0 - 1e-9));
    }
    ASSERT_NO_FATAL_FAILURE(CheckComputeConvexHull(points, convex_points));
  }
  {
    for (int i = 0; i < 10; ++i) {
      const int num_points = testing::RandomInt(2, 50);
      const std::vector<Vector2d> points = testing::RandomPoints(num_points, 10.0);
      ASSERT_NO_FATAL_FAILURE(CheckComputeConvexHull(points));
    }
  }
}

TEST_F(ConvexHullTest, ComputeConvexHull3) {
  {
    const std::vector<Vector2d> convex_points = {{0.0, 0.0}, {1.0, 0.0}};
    const std::vector<Vector2d> points = {{0.0, 0.0}, {0.2, +1e-20}, {0.8, +1e-20}, {1.0, 0.0}};
    ASSERT_NO_FATAL_FAILURE(CheckComputeConvexHull(points, convex_points));
  }
  {
    const std::vector<Vector2d> convex_points = {{0.0, 0.0}, {1.0, 0.0}};
    const std::vector<Vector2d> points = {{0.0, 0.0}, {0.2, -1e-20}, {0.8, -1e-20}, {1.0, 0.0}};
    ASSERT_NO_FATAL_FAILURE(CheckComputeConvexHull(points, convex_points));
  }
  {
    std::vector<Vector2d> points = {{0.0, 0.0}, {5.0 - 1e-9, -1e-9}, {1e-9, +1e-9},
                                    {5.0, 0.0}, {5.0 - 1e-9, +1e-9}, {1e-9, -1e-9}};
    ASSERT_NO_FATAL_FAILURE(CheckComputeConvexHull(points));
  }
}

}  // namespace convex
}  // namespace math_internal
}  // namespace math
