// Copyright @2022 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <vector>

#include "base/container/span.h"
#include "base/math/line_segment2d.h"
#include "base/math/vector2.h"

namespace math {
namespace math_internal {
namespace convex {

// Get the (sorted) project_s of the intersection points.
std::vector<double> GetOverlapConvex(const base::ConstSpan<Vector2d>& convex_points,
                                     const LineSegment2d& segment,
                                     bool force_binary_search = false);
std::vector<double> GetOverlapBruteForce(const base::ConstSpan<Vector2d>& polygon_points,
                                         const LineSegment2d& segment);

bool HasOverlapConvex(const base::ConstSpan<Vector2d>& convex_points,
                      const LineSegment2d& segment,
                      bool force_binary_search = false);
bool HasOverlapBruteForce(const base::ConstSpan<Vector2d>& polygon_points,
                          const LineSegment2d& segment);

// http://programmerart.weebly.com/separating-axis-theorem.html
// http://dyn4j.org/2010/01/sat/
// Check if convex polygons intersect by Separating Axis Theorem.
bool HasOverlapConvex(const base::ConstSpan<Vector2d>& convex_points0,
                      const base::ConstSpan<Vector2d>& convex_points1);
bool HasOverlapBruteForce(const base::ConstSpan<Vector2d>& polygon_points0,
                          const base::ConstSpan<Vector2d>& polygon_points1);

// Check if convex_points1 is entirely inside convex_points0.
bool ContainsConvex(const base::ConstSpan<Vector2d>& convex_points0,
                    const base::ConstSpan<Vector2d>& convex_points1);

// http://www.cs.jhu.edu/~misha/Spring16/ORourke82.pdf
// http://tildesites.bowdoin.edu/~ltoma/teaching/cs3250-CompGeom/spring17/Lectures/cg-convexintersection.pdf
// See, Computational Geometry In C 2nd ed. - J. O'Rourke (1997) WW, Page252.
// Compute the overlap between convex polygons.
std::vector<Vector2d> ComputeOverlapConvex(const base::ConstSpan<Vector2d>& convex_points0,
                                           const base::ConstSpan<Vector2d>& convex_points1);

}  // namespace convex
}  // namespace math_internal
}  // namespace math
