// Copyright @2022 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <algorithm>
#include <vector>

#include "base/container/span.h"
#include "base/math/internal/convex_utils.h"

namespace math {
namespace math_internal {
namespace convex {
namespace convex_internal {

class Iterator {
 public:
  explicit Iterator(const base::ConstSpan<Vector2d>& points) : points_(points) {
    CHECK_GE(points_.size(), 2);
    i0_ = 0;
    i1_ = 1;
    segment_ = points_[i1_] - points_[i0_];
  }

  Iterator(const base::ConstSpan<Vector2d>& points, int i0) : points_(points) {
    CHECK_GE(points_.size(), 2);
    CHECK_GE(i0, 0);
    CHECK_LT(i0, points_.size());
    i0_ = i0;
    i1_ = convex_internal::Next(i0_, points_.size());
    segment_ = points_[i1_] - points_[i0_];
  }

  template <typename CompareFunction>
  Iterator(const base::ConstSpan<Vector2d>& points, const CompareFunction& compare)
      : points_(points) {
    CHECK_GE(points_.size(), 2);
    i0_ = std::min_element(points_.begin(), points_.end(), compare) - points_.begin();
    i1_ = convex_internal::Next(i0_, points_.size());
    segment_ = points_[i1_] - points_[i0_];
  }

  int num_points() const { return points_.size(); }
  int steps() const { return steps_; }

  const Vector2d& start() const { return points_[i0_]; }
  const Vector2d& end() const { return points_[i1_]; }
  const Vector2d& segment() const { return segment_; }

  void operator++() {
    i0_ = i1_;
    i1_ = convex_internal::Next(i0_, points_.size());
    steps_++;
    segment_ = points_[i1_] - points_[i0_];
  }

  void operator++(int) { ++(*this); }

  void ResetSteps() { steps_ = 0; }

 private:
  const base::ConstSpan<Vector2d> points_;

  int i0_ = 0;
  int i1_ = 0;
  int steps_ = 0;
  Vector2d segment_;
};

}  // namespace convex_internal
}  // namespace convex
}  // namespace math_internal
}  // namespace math
