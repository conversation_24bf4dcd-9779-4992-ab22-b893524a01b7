// Copyright @2022 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/math/internal/convex_hull.h"

#include <algorithm>
#include <utility>

#include "base/math/internal/convex_utils.h"

namespace math {
namespace math_internal {
namespace convex {

using convex_internal::Next;

namespace {

void AddPoint(const Vector2d& point, int start_index, std::vector<Vector2d>* convex_points) {
  DCHECK_GE(start_index, 0);
  DCHECK(convex_points != nullptr);
  int i0 = static_cast<int>(convex_points->size()) - 2;
  while (i0 >= start_index &&
         CrossProd((*convex_points)[i0], (*convex_points)[i0 + 1], point) <= 0.0) {
    i0--;
    convex_points->pop_back();
  }
  convex_points->push_back(point);
}

int GetWeaklyConvexPointIndex(const std::vector<Vector2d>& convex_points, int i0, int i1, int i2) {
  const Vector2d& p0 = convex_points[i0];
  const Vector2d& p1 = convex_points[i1];
  const Vector2d& p2 = convex_points[i2];
  const double cross_prod = CrossProd(p0, p1, p2);
  if (cross_prod < 0.0) {
    return i1;
  }
  if (cross_prod < math::kEpsilon) {
    const double d10 = p1.DistanceSquareTo(p0);
    const double d12 = p1.DistanceSquareTo(p2);
    const double d02 = p0.DistanceSquareTo(p2);
    if (d02 < std::max(d10, d12)) {
      return d10 < d12 ? i0 : i2;
    }
    return i1;
  }
  return -1;
}

void ClearWeaklyConvexPoints(std::vector<Vector2d>* convex_points) {
  DCHECK(convex_points != nullptr);
  while (true) {
    const int num_points = convex_points->size();
    if (num_points <= 1) {
      return;
    }
    if (num_points == 2) {
      if ((*convex_points)[0].DistanceSquareTo((*convex_points)[1]) < math::kEpsilonSquare) {
        convex_points->pop_back();
      }
      return;
    }
    int remaining_points = num_points;
    for (int i1 = 0, pos = num_points - 1; i1 < num_points; ++i1) {
      if (remaining_points >= 3) {
        const int p = GetWeaklyConvexPointIndex(*convex_points, pos, i1, Next(i1, num_points));
        if (p != -1) {
          (*convex_points)[p] = (*convex_points)[i1];
          remaining_points--;
          continue;
        }
      }
      pos = Next(pos, num_points);
      if (pos != i1) {
        (*convex_points)[pos] = (*convex_points)[i1];
      }
    }
    if (remaining_points == num_points) {
      return;
    }
    convex_points->resize(remaining_points);
  }
}

}  // namespace

bool IsConvexHull(const base::ConstSpan<Vector2d>& points) {
  const int num_points = points.size();
  if (num_points <= 2) {
    return false;
  }
  for (int i = 0, j = num_points - 1; i < num_points; ++i) {
    if (CrossProd(points[j], points[i], points[Next(i, num_points)]) < math::kEpsilon) {
      return false;
    }
    j = i;
  }
  for (int i = 3; i < num_points; ++i) {
    const int p = i - 1;
    if (CrossProd(points[0], points[p], points[i]) < -math::kEpsilon ||
        CrossProd(points[0], points[1], points[i]) < -math::kEpsilon) {
      return false;
    }
  }
  return true;
}

std::vector<Vector2d> ComputeConvexHull(std::vector<Vector2d> points, bool check_convex_first) {
  const int num_points = points.size();
  if (num_points <= 1) {
    return points;
  }
  if (num_points == 2) {
    if (points[0].DistanceSquareTo(points[1]) < math::kEpsilonSquare) {
      points.pop_back();
    }
    return points;
  }
  if (check_convex_first && IsConvexHull(points)) {
    return points;
  }

  // Sort points by x-axis, then by y-axis.
  std::sort(points.begin(), points.end(), convex_internal::LessThan);

  std::vector<Vector2d> convex_points;
  convex_points.reserve(num_points + 1);

  // Build the lower half of the convex-hull.
  for (int i = 0; i <= num_points - 1; ++i) {
    AddPoint(points[i], 0, &convex_points);
  }
  // Build the upper half of the convex-hull.
  const int start_index = convex_points.size() - 1;
  for (int i = num_points - 2; i >= 0; --i) {
    AddPoint(points[i], start_index, &convex_points);
  }
  convex_points.pop_back();

  ClearWeaklyConvexPoints(&convex_points);
  return convex_points;
}

}  // namespace convex
}  // namespace math_internal
}  // namespace math
