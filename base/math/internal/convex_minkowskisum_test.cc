// Copyright @2022 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/math/internal/convex_minkowskisum.h"

#include <algorithm>
#include <utility>

#include "gmock/gmock.h"
#include "gtest/gtest.h"

#include "base/common/optional.h"
#include "base/math/internal/convex_hull.h"
#include "base/testing/boost_geometry.h"
#include "base/testing/geometry.h"
#include "base/testing/random.h"

namespace math {
namespace math_internal {
namespace convex {

class ConvexMinkowskiSumTest : public ::testing::Test {
 protected:
  void CheckMinkowskiSum(base::Optional<double> expected_area = base::none) const {
    for (int i = 0; i < 2; ++i) {
      if (polygon_points_[i].size() >= 3) {
        ASSERT_TRUE(IsConvexHull(polygon_points_[i]));
      }
    }
    for (int i = 0; i < 2; ++i) {
      const int j = 1 - i;
      const std::vector<Vector2d> result_points0 =
          MinkowskiSumConvex(polygon_points_[i], polygon_points_[j]);
      const std::vector<Vector2d> result_points1 = MinkowskiSumConvexBruteForce();
      const testing::BoostPolygon2d p0 = testing::ToBoostPolygonFromPoints(result_points0);
      const testing::BoostPolygon2d p1 = testing::ToBoostPolygonFromPoints(result_points1);
      if (result_points0.size() >= 3) {
        ASSERT_TRUE(IsConvexHull(result_points0));
        ASSERT_TRUE(IsConvexHull(result_points1));
        ASSERT_NEAR(testing::BoostPolygonArea(p0), testing::BoostPolygonArea(p1), math::kEpsilon);
        ASSERT_NEAR(testing::BoostPolygonIntersectionOverUnion(p0, p1), 1.0, 1e-4);
      } else {
        ASSERT_NEAR(testing::BoostPolygonArea(p0), 0.0, 1e-6);
        ASSERT_NEAR(testing::BoostPolygonArea(p1), 0.0, 1e-6);
      }
      if (expected_area) {
        ASSERT_NEAR(testing::BoostPolygonArea(p0), *expected_area, 1e-6);
      }
    }
  }

  void CheckExpandByDistanceConvex(const std::vector<Vector2d>& convex_points,
                                   double distance,
                                   double angle_step_hint,
                                   std::vector<Vector2d> expected_result_points) const {
    ASSERT_GE(convex_points.size(), 1);
    const std::vector<Vector2d> result_points =
        ExpandByDistanceConvex(convex_points, distance, angle_step_hint);
    for (const Vector2d& p0 : convex_points) {
      for (const Vector2d& p1 : result_points) {
        ASSERT_GE(p0.DistanceTo(p1), distance - math::kEpsilon);
      }
    }
    ASSERT_THAT(result_points, testing::UnorderedElementsAreArray(expected_result_points));
    const auto iter = std::min_element(expected_result_points.begin(),
                                       expected_result_points.end(),
                                       [&result_points](const auto& p0, const auto& p1) {
                                         return result_points[0].DistanceSquareTo(p0) <
                                                result_points[0].DistanceSquareTo(p1);
                                       });
    std::rotate(expected_result_points.begin(), iter, expected_result_points.end());
    ASSERT_THAT(result_points, testing::ElementsAreArray(expected_result_points));
  }

 protected:
  std::vector<Vector2d> MinkowskiSumConvexBruteForce() const {
    std::vector<Vector2d> result_points;
    for (const Vector2d& p0 : polygon_points_[0]) {
      for (const Vector2d& p1 : polygon_points_[1]) {
        result_points.push_back(p0 + p1);
      }
    }
    return ComputeConvexHull(std::move(result_points));
  }

  std::array<std::vector<Vector2d>, 2> polygon_points_;
};

TEST_F(ConvexMinkowskiSumTest, MinkowskiSum0) {
  {
    polygon_points_[0] = {{0.0, 0.0}, {1.0, 0.0}, {1.0, 1.0}, {0.0, 1.0}};
    polygon_points_[1] = {{1.0, 0.0}, {0.0, 1.0}, {0.0, 0.0}};
    ASSERT_NO_FATAL_FAILURE(CheckMinkowskiSum(3.5));

    polygon_points_[0] = {{0.0, 1.0}, {0.0, 0.0}, {1.0, 0.0}};
    polygon_points_[1] = {{1.0, 1.0}, {0.0, 0.0}, {1.0, 0.0}};
    ASSERT_NO_FATAL_FAILURE(CheckMinkowskiSum(3.0));

    polygon_points_[0] = {{-1.0, 0.0}, {1.0, 0.0}, {0.0, 1.0}};
    polygon_points_[1] = {{-1.0, 0.0}, {1.0, 0.0}, {1.0, 2.0}, {-1.0, 2.0}};
    ASSERT_NO_FATAL_FAILURE(CheckMinkowskiSum(11.0));
  }
  {
    polygon_points_[0] = {{0.0, 0.0}, {1.0, 0.0}};
    polygon_points_[1] = {{0.0, 0.0}, {1.0, 0.0}};
    ASSERT_NO_FATAL_FAILURE(CheckMinkowskiSum(0.0));

    polygon_points_[0] = {{0.0, 1.0}, {0.0, 0.0}, {1.0, 0.0}};
    polygon_points_[1] = {{0.0, 0.0}, {1.0, 0.0}};
    ASSERT_NO_FATAL_FAILURE(CheckMinkowskiSum(1.5));
  }
}

TEST_F(ConvexMinkowskiSumTest, MinkowskiSum1) {
  {
    for (int k = 0; k < 200; ++k) {
      polygon_points_[0] = testing::MakeRandomConvexPolygon(20, testing::RandomDouble(1.0, 10.0),
                                                            testing::RandomPoint(10.0));
      polygon_points_[1] = testing::MakeRandomConvexPolygon(20, testing::RandomDouble(1.0, 10.0),
                                                            testing::RandomPoint(10.0));
      ASSERT_NO_FATAL_FAILURE(CheckMinkowskiSum());
    }
  }
}

TEST_F(ConvexMinkowskiSumTest, ExpandByDistanceConvex) {
  const auto make_points = [](const Vector2d& point,
                              double distance,
                              double start_angle,
                              double angle_range,
                              double angle_step_hint,
                              std::vector<Vector2d>* result_points) {
    const int num_segments =
        std::max(1, static_cast<int>(std::ceil(angle_range / angle_step_hint - math::kEpsilon)));
    const double angle_step = angle_range / num_segments;
    for (int i = 0; i <= num_segments; ++i) {
      const double angle = start_angle + i * angle_step;
      result_points->push_back(point + Vector2d::FromAngleAndLength(angle, distance));
    }
    return num_segments + 1;
  };
  {
    const Vector2d p0 = {0.0, 0.0};
    const double angle_step = M_PI / 2.0;
    std::vector<Vector2d> expected_points;
    ASSERT_EQ(5, make_points(p0, 1.0, 0.0, M_PI * 2.0, angle_step, &expected_points));
    expected_points = ComputeConvexHull(std::move(expected_points));
    ASSERT_EQ(4, expected_points.size());
    ASSERT_NO_FATAL_FAILURE(CheckExpandByDistanceConvex({p0}, 1.0, angle_step, expected_points));
  }
  {
    const Vector2d p0 = {0.0, 0.0};
    const double angle_step = M_PI / 6.0;
    std::vector<Vector2d> expected_points;
    ASSERT_EQ(13, make_points(p0, 1.0, 0.0, M_PI * 2.0, angle_step, &expected_points));
    expected_points = ComputeConvexHull(std::move(expected_points));
    ASSERT_EQ(12, expected_points.size());
    ASSERT_NO_FATAL_FAILURE(CheckExpandByDistanceConvex({p0}, 1.0, angle_step, expected_points));
  }
  {
    const Vector2d p0 = {0.0, 0.0};
    const Vector2d p1 = {1.0, 0.0};
    const double angle_step = M_PI / 6.0;
    std::vector<Vector2d> expected_points;
    ASSERT_EQ(7, make_points(p1, 1.0, -M_PI_2, M_PI, angle_step, &expected_points));
    ASSERT_EQ(7, make_points(p0, 1.0, +M_PI_2, M_PI, angle_step, &expected_points));
    expected_points = ComputeConvexHull(std::move(expected_points));
    ASSERT_EQ(14, expected_points.size());
    ASSERT_NO_FATAL_FAILURE(
        CheckExpandByDistanceConvex({p0, p1}, 1.0, angle_step, expected_points));
    ASSERT_NO_FATAL_FAILURE(
        CheckExpandByDistanceConvex({p1, p0}, 1.0, angle_step, expected_points));
  }
  {
    const std::vector<Vector2d> points = {
        {0.0, 0.0},
        {2.0, 0.0},
        {2.0, 1.0},
        {0.0, 1.0},
    };
    const std::vector<double> start_angle = {-M_PI, -M_PI_2, 0.0, M_PI_2};
    const double angle_step = M_PI / 6.0;
    std::vector<Vector2d> expected_points;
    for (int i = 0; i < 4; ++i) {
      ASSERT_EQ(4,
                make_points(points[i], 1.0, start_angle[i], M_PI_2, angle_step, &expected_points));
    }
    expected_points = ComputeConvexHull(std::move(expected_points));
    ASSERT_EQ(16, expected_points.size());
    ASSERT_NO_FATAL_FAILURE(CheckExpandByDistanceConvex(points, 1.0, angle_step, expected_points));
  }
  {
    const std::vector<Vector2d> points = {
        {0.0, 0.0},
        {2.0, 0.0},
        {2.0, 1.0},
        {0.0, 1.0},
    };
    const std::vector<double> start_angle = {-M_PI, -M_PI_2, 0.0, M_PI_2};
    const double angle_step = 0.037;
    std::vector<Vector2d> expected_points;
    for (int i = 0; i < 4; ++i) {
      ASSERT_EQ(44,
                make_points(points[i], 1.0, start_angle[i], M_PI_2, angle_step, &expected_points));
    }
    expected_points = ComputeConvexHull(std::move(expected_points));
    ASSERT_EQ(176, expected_points.size());
    ASSERT_NO_FATAL_FAILURE(CheckExpandByDistanceConvex(points, 1.0, angle_step, expected_points));
  }
}

}  // namespace convex
}  // namespace math_internal
}  // namespace math
