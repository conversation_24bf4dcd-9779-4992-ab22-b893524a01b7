// Copyright @2022 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/math/internal/convex_distance.h"

#include <algorithm>
#include <utility>

#include "gmock/gmock.h"
#include "gtest/gtest.h"

#include "base/common/optional.h"
#include "base/math/internal/convex_hull.h"
#include "base/math/line_segment2d.h"
#include "base/testing/boost_geometry.h"
#include "base/testing/geometry.h"
#include "base/testing/random.h"

namespace math {
namespace math_internal {
namespace convex {

class ConvexDistanceTest : public ::testing::Test {
 protected:
  void CheckGetLowerIndexBinarySearch(const Vector2d& point, int expected_index) const {
    ASSERT_TRUE(IsConvexHull(polygon_points_));
    ASSERT_EQ(expected_index, convex_internal::GetLowerIndexBinarySearch(polygon_points_, point));
  }

  void CheckGetLowerIndexBinarySearch(double angle, int expected_index) const {
    ASSERT_TRUE(IsConvexHull(polygon_points_));
    ASSERT_EQ(expected_index, convex_internal::GetLowerIndexBinarySearch(
                                  polygon_points_, Vector2d::FromAngle(angle)));
  }

  void CheckIsPointInOrOnBoundaryConvex(
      const Vector2d& point, base::Optional<bool> expected_is_point_in = base::none,
      base::Optional<bool> expected_is_point_on_boundary = base::none) const {
    ASSERT_TRUE(IsConvexHull(polygon_points_));
    const testing::BoostPolygon2d boost_polygon =
        testing::ToBoostPolygonFromPoints(polygon_points_);
    const testing::BoostVector2d boost_point = testing::ToBoost(point);
    const bool is_point_in = testing::BoostPolygonIsPointIn(boost_polygon, boost_point, 1e-6);
    if (expected_is_point_in) {
      ASSERT_EQ(is_point_in, *expected_is_point_in);
    }
    ASSERT_EQ(is_point_in, IsPointInBruteForce(polygon_points_, point));
    ASSERT_EQ(is_point_in, IsPointInConvex(polygon_points_, point));
    const bool is_point_on_boundary =
        testing::BoostPolygonIsPointOnBoundary(boost_polygon, boost_point, 1e-6);
    if (expected_is_point_on_boundary) {
      ASSERT_EQ(is_point_on_boundary, *expected_is_point_on_boundary);
    }
    ASSERT_EQ(is_point_on_boundary, IsPointOnBoundaryBruteForce(polygon_points_, point));
    ASSERT_EQ(is_point_on_boundary, IsPointOnBoundaryConvex(polygon_points_, point));
  }

  void CheckIsPointInOrOnBoundaryBruteForce(
      const Vector2d& point, base::Optional<bool> expected_is_point_in = base::none,
      base::Optional<bool> expected_is_point_on_boundary = base::none) const {
    const testing::BoostPolygon2d boost_polygon =
        testing::ToBoostPolygonFromPoints(polygon_points_);
    const testing::BoostVector2d boost_point = testing::ToBoost(point);
    const bool is_point_in = testing::BoostPolygonIsPointIn(boost_polygon, boost_point, 1e-6);
    if (expected_is_point_in) {
      ASSERT_EQ(is_point_in, *expected_is_point_in);
    }
    ASSERT_EQ(is_point_in, IsPointInBruteForce(polygon_points_, point));
    const bool is_point_on_boundary =
        testing::BoostPolygonIsPointOnBoundary(boost_polygon, boost_point, 1e-6);
    if (expected_is_point_on_boundary) {
      ASSERT_EQ(is_point_on_boundary, *expected_is_point_on_boundary);
    }
    ASSERT_EQ(is_point_on_boundary, IsPointOnBoundaryBruteForce(polygon_points_, point));
  }

  void CheckDistanceToConvex(const Vector2d& point,
                             base::Optional<double> expected_distance = base::none) const {
    ASSERT_TRUE(IsConvexHull(polygon_points_));
    const testing::BoostPolygon2d boost_polygon =
        testing::ToBoostPolygonFromPoints(polygon_points_);
    const double boost_distance = testing::BoostDistanceTo(boost_polygon, testing::ToBoost(point));
    if (expected_distance) {
      ASSERT_NEAR(boost_distance, *expected_distance, 1e-6);
    }
    Vector2d nearest_point;
    ASSERT_NEAR(boost_distance, DistanceToConvex(polygon_points_, point, &nearest_point), 1e-6);
    ASSERT_NEAR(boost_distance, point.DistanceTo(nearest_point), 1e-6);
  }

  void CheckDistanceToBruteForce(const Vector2d& point,
                                 base::Optional<double> expected_distance = base::none) const {
    const testing::BoostPolygon2d boost_polygon =
        testing::ToBoostPolygonFromPoints(polygon_points_);
    const double boost_distance = testing::BoostDistanceTo(boost_polygon, testing::ToBoost(point));
    if (expected_distance) {
      ASSERT_NEAR(boost_distance, *expected_distance, 1e-6);
    }
    Vector2d nearest_point;
    ASSERT_NEAR(boost_distance, DistanceToBruteForce(polygon_points_, point, &nearest_point), 1e-6);
    ASSERT_NEAR(boost_distance, point.DistanceTo(nearest_point), 1e-6);
  }

 protected:
  std::vector<Vector2d> polygon_points_;
};

TEST_F(ConvexDistanceTest, GetLowerIndexBinarySearch) {
  for (int n = 3; n <= 100; ++n) {
    std::vector<double> angles = {0.0};
    std::vector<Vector2d> points = {{0.0, 0.0}};
    for (int i = 1; i < n; ++i) {
      angles.push_back(M_PI_2 / (n - 1) * (i - 1));
      points.push_back(Vector2d::FromAngle(angles.back()));
    }
    polygon_points_ = std::move(points);
    ASSERT_NO_FATAL_FAILURE(CheckGetLowerIndexBinarySearch(polygon_points_[0], 0));
    ASSERT_NO_FATAL_FAILURE(CheckGetLowerIndexBinarySearch(polygon_points_[1], 0));
    for (int i = 2; i <= n - 1; ++i) {
      ASSERT_NO_FATAL_FAILURE(CheckGetLowerIndexBinarySearch(polygon_points_[i], i));
    }
    for (int i = 1; i <= n - 1; ++i) {
      ASSERT_NO_FATAL_FAILURE(CheckGetLowerIndexBinarySearch(angles[i] - 1e-3, i - 1));
    }
    for (int i = 1; i <= n - 1; ++i) {
      ASSERT_NO_FATAL_FAILURE(CheckGetLowerIndexBinarySearch(angles[i] + 1e-3, i));
    }
    ASSERT_NO_FATAL_FAILURE(
        CheckGetLowerIndexBinarySearch(math::Lerp(polygon_points_[0], polygon_points_[1], 0.5), 0));
    ASSERT_NO_FATAL_FAILURE(CheckGetLowerIndexBinarySearch(
        math::Lerp(polygon_points_[0], polygon_points_[n - 1], 0.5), n - 1));
  }
}

TEST_F(ConvexDistanceTest, IsPointInOrOnBoundaryConvex) {
  {
    polygon_points_ = {
        {0.0, 0.0},
        {5.0, 0.0},
        {0.0, 5.0},
    };
    ASSERT_NO_FATAL_FAILURE(CheckIsPointInOrOnBoundaryConvex({+0.0, +2.0}, true, true));
    ASSERT_NO_FATAL_FAILURE(CheckIsPointInOrOnBoundaryConvex({+2.0, +2.0}, true, false));
    ASSERT_NO_FATAL_FAILURE(CheckIsPointInOrOnBoundaryConvex({-1.0, +0.0}, false, false));
    ASSERT_NO_FATAL_FAILURE(CheckIsPointInOrOnBoundaryConvex({+0.0, -1.0}, false, false));
    ASSERT_NO_FATAL_FAILURE(CheckIsPointInOrOnBoundaryConvex({-1.0, +6.0}, false, false));
    ASSERT_NO_FATAL_FAILURE(CheckIsPointInOrOnBoundaryConvex({+6.0, -1.0}, false, false));
    const int num_points = polygon_points_.size();
    for (int i = 0, j = num_points - 1; i < num_points; ++i) {
      const Vector2d& p0 = polygon_points_[i];
      const Vector2d& p1 = polygon_points_[j];
      for (int r = 0; r <= 100; ++r) {
        const Vector2d point = math::Lerp(p0, p1, r / 100.0);
        ASSERT_NO_FATAL_FAILURE(CheckIsPointInOrOnBoundaryConvex(point, true, true));
      }
      j = i;
    }
  }
  {
    polygon_points_ = {
        {0.0, 0.0},
        {2.0, 0.0},
        {2.0, 2.0},
        {0.0, 2.0},
    };
    ASSERT_NO_FATAL_FAILURE(CheckIsPointInOrOnBoundaryConvex({-0.5, +0.0}, false, false));
    ASSERT_NO_FATAL_FAILURE(CheckIsPointInOrOnBoundaryConvex({+0.5, +1.0}, true, false));
    ASSERT_NO_FATAL_FAILURE(CheckIsPointInOrOnBoundaryConvex({+1.5, +1.0}, true, false));
    ASSERT_NO_FATAL_FAILURE(CheckIsPointInOrOnBoundaryConvex({+2.5, +1.0}, false, false));
    ASSERT_NO_FATAL_FAILURE(CheckIsPointInOrOnBoundaryConvex({+0.0, +1.0}, true, true));
    ASSERT_NO_FATAL_FAILURE(CheckIsPointInOrOnBoundaryConvex({+1.0, +1.0}, true, false));
    ASSERT_NO_FATAL_FAILURE(CheckIsPointInOrOnBoundaryConvex({+2.0, +1.0}, true, true));

    ASSERT_NO_FATAL_FAILURE(CheckIsPointInOrOnBoundaryConvex({-1e-3, -1e-3}, false, false));
    ASSERT_NO_FATAL_FAILURE(CheckIsPointInOrOnBoundaryConvex({-1e-9, -1e-9}, true, true));
    ASSERT_NO_FATAL_FAILURE(CheckIsPointInOrOnBoundaryConvex({+1.0, -1e-3}, false, false));
    ASSERT_NO_FATAL_FAILURE(CheckIsPointInOrOnBoundaryConvex({+1.0, -1e-9}, true, true));

    const std::vector<Vector2d> testcases = {{0.5, 0.5},  {0.2, 0.8},  {0.8, 0.2},  {1.5, 1.5},
                                             {0.0, 0.0},  {1.0, 0.0},  {2.0, 1.0},  {0.0, 1.0},
                                             {-0.4, 0.6}, {1.2, 1.0},  {1.0, -0.5}, {0.0, -0.2},
                                             {-0.2, 0.1}, {1.0, -0.2}, {2.1, 0.0},  {2.0, 2.1}};
    for (const Vector2d& point : testcases) {
      ASSERT_NO_FATAL_FAILURE(CheckIsPointInOrOnBoundaryConvex(point));
    }
  }
  {
    polygon_points_ = {
        {0.0, 0.0},
        {2.0, 2.0},
        {1.0, 2.0},
    };
    const std::vector<Vector2d> testcases = {
        {1.0, 0.5}, {1.0, 1.0}, {1.0, 1.5}, {1.0, 2.0}, {1.0, 2.5}};
    for (const Vector2d& point : testcases) {
      ASSERT_NO_FATAL_FAILURE(CheckIsPointInOrOnBoundaryConvex(point));
    }
  }
  {
    for (int k = 0; k < 300; ++k) {
      polygon_points_ = ComputeConvexHull(testing::RandomPoints(testing::RandomInt(3, 20), 10.0));
      if (!IsConvexHull(polygon_points_)) {
        continue;
      }
      for (int i = 0; i < 500; ++i) {
        const Vector2d point = testing::RandomPoint(15.0);
        ASSERT_NO_FATAL_FAILURE(CheckIsPointInOrOnBoundaryConvex(point));
      }
      for (const Vector2d& point : polygon_points_) {
        ASSERT_NO_FATAL_FAILURE(CheckIsPointInOrOnBoundaryConvex(point, true, true));
      }
      const int num_points = polygon_points_.size();
      for (int i = 0, j = num_points - 1; i < num_points; ++i) {
        const Vector2d point = math::Lerp(polygon_points_[i], polygon_points_[j], 0.5);
        ASSERT_NO_FATAL_FAILURE(CheckIsPointInOrOnBoundaryConvex(point, true, true));
        j = i;
      }
    }
  }
}

TEST_F(ConvexDistanceTest, IsPointInOrOnBoundaryBruteForce) {
  {
    polygon_points_ = {
        {0.0, 0.0}, {2.0, 0.0}, {2.0, 2.0}, {1.0, 1.0}, {0.0, 2.0},
    };
    ASSERT_NO_FATAL_FAILURE(CheckIsPointInOrOnBoundaryBruteForce({+0.5, +1.5}, true, true));
    ASSERT_NO_FATAL_FAILURE(CheckIsPointInOrOnBoundaryBruteForce({+1.5, +1.5}, true, true));
    ASSERT_NO_FATAL_FAILURE(CheckIsPointInOrOnBoundaryBruteForce({+1.0, +1.5}, false, false));
  }
  {
    polygon_points_ = {
        {0.0, 0.0}, {4.0, 0.0}, {4.0, 2.0}, {3.0, 2.0}, {2.0, 1.0}, {1.0, 2.0}, {0.0, 2.0},
    };
    ASSERT_NO_FATAL_FAILURE(CheckIsPointInOrOnBoundaryBruteForce({-0.5, +2.0}, false, false));
    ASSERT_NO_FATAL_FAILURE(CheckIsPointInOrOnBoundaryBruteForce({+0.5, +2.0}, true, true));
    ASSERT_NO_FATAL_FAILURE(CheckIsPointInOrOnBoundaryBruteForce({+1.5, +2.0}, false, false));
    ASSERT_NO_FATAL_FAILURE(CheckIsPointInOrOnBoundaryBruteForce({+2.0, +2.0}, false, false));
    ASSERT_NO_FATAL_FAILURE(CheckIsPointInOrOnBoundaryBruteForce({+2.5, +2.0}, false, false));
    ASSERT_NO_FATAL_FAILURE(CheckIsPointInOrOnBoundaryBruteForce({+3.5, +2.0}, true, true));
    ASSERT_NO_FATAL_FAILURE(CheckIsPointInOrOnBoundaryBruteForce({+4.5, +2.0}, false, false));
  }
  {
    polygon_points_ = {
        {11.0, 2.0},
        {10.0, 0.0},
        {11.0, 1.0},
    };
    ASSERT_NO_FATAL_FAILURE(CheckIsPointInOrOnBoundaryBruteForce({+11.0, +0.0}, false, false));
  }
  {
    polygon_points_ = {
        {2.0, 2.0},
        {3.0, 3.0},
    };
    ASSERT_NO_FATAL_FAILURE(CheckIsPointInOrOnBoundaryBruteForce({+2.0, +2.0}, true, true));
    ASSERT_NO_FATAL_FAILURE(CheckIsPointInOrOnBoundaryBruteForce({+2.5, +2.5}, true, true));
    ASSERT_NO_FATAL_FAILURE(CheckIsPointInOrOnBoundaryBruteForce({+3.0, +3.0}, true, true));
    ASSERT_NO_FATAL_FAILURE(CheckIsPointInOrOnBoundaryBruteForce({+1.0, +1.0}, false, false));
    ASSERT_NO_FATAL_FAILURE(CheckIsPointInOrOnBoundaryBruteForce({+2.6, +2.4}, false, false));
    ASSERT_NO_FATAL_FAILURE(CheckIsPointInOrOnBoundaryBruteForce({+4.0, +4.0}, false, false));
  }
  {
    polygon_points_ = {{0.0, 0.0}};
    ASSERT_NO_FATAL_FAILURE(CheckIsPointInOrOnBoundaryBruteForce({+0.0, +0.0}, true, true));
    ASSERT_NO_FATAL_FAILURE(CheckIsPointInOrOnBoundaryBruteForce({+1.0, +1.0}, false, false));
  }
  {
    for (int k = 0; k < 300; ++k) {
      polygon_points_ = testing::MakeRandomPolygon(testing::RandomInt(3, 20), 10.0);
      for (int i = 0; i < 500; ++i) {
        const Vector2d point = testing::RandomPoint(15.0);
        ASSERT_NO_FATAL_FAILURE(CheckIsPointInOrOnBoundaryBruteForce(point));
      }
      for (const Vector2d& point : polygon_points_) {
        ASSERT_NO_FATAL_FAILURE(CheckIsPointInOrOnBoundaryBruteForce(point, true, true));
      }
      const int num_points = polygon_points_.size();
      for (int i = 0, j = num_points - 1; i < num_points; ++i) {
        const Vector2d point = math::Lerp(polygon_points_[i], polygon_points_[j], 0.5);
        ASSERT_NO_FATAL_FAILURE(CheckIsPointInOrOnBoundaryBruteForce(point, true, true));
        j = i;
      }
    }
  }
}

TEST_F(ConvexDistanceTest, DistanceToConvex) {
  {
    polygon_points_ = {
        {0.0, 0.0},
        {1.0, 0.0},
        {1.0, 1.0},
        {0.0, 1.0},
    };
    ASSERT_NO_FATAL_FAILURE(CheckDistanceToConvex({+0.5, +0.5}, 0.0));
    ASSERT_NO_FATAL_FAILURE(CheckDistanceToConvex({-0.1, +0.5}, 0.1));
    ASSERT_NO_FATAL_FAILURE(CheckDistanceToConvex({+1.0, +0.5}, 0.0));
    ASSERT_NO_FATAL_FAILURE(CheckDistanceToConvex({+1.3, +0.5}, 0.3));
    ASSERT_NO_FATAL_FAILURE(CheckDistanceToConvex({+1.0, -0.3}, 0.3));
    ASSERT_NO_FATAL_FAILURE(CheckDistanceToConvex({+1.0, +1.2}, 0.2));
    for (int i = 0; i < 1000; ++i) {
      ASSERT_NO_FATAL_FAILURE(CheckDistanceToConvex(testing::RandomPoint(10.0)));
    }
  }
  {
    polygon_points_ = {
        {+1.0, +0.0},
        {+0.0, +1.0},
        {-1.0, +0.0},
        {+0.0, -1.0},
    };
    ASSERT_NO_FATAL_FAILURE(CheckDistanceToConvex({+0.0, +0.0}, 0.0));
    ASSERT_NO_FATAL_FAILURE(CheckDistanceToConvex({+0.0, +1.1}, 0.1));
    ASSERT_NO_FATAL_FAILURE(CheckDistanceToConvex({+0.0, -1.1}, 0.1));
    ASSERT_NO_FATAL_FAILURE(CheckDistanceToConvex({+1.1, +0.0}, 0.1));
    for (int i = 0; i < 1000; ++i) {
      ASSERT_NO_FATAL_FAILURE(CheckDistanceToConvex(testing::RandomPoint(10.0)));
    }
  }
  {
    for (int k = 0; k < 100; ++k) {
      const double heading = testing::RandomDouble();
      const OrientedBox2d box({0.0, 0.0}, heading, 10.0, 4.0);
      polygon_points_ = box.GetCorners();
      for (int i = 0; i < 1000; ++i) {
        ASSERT_NO_FATAL_FAILURE(CheckDistanceToConvex(testing::RandomPoint(15.0)));
      }
      for (const Vector2d& point : polygon_points_) {
        ASSERT_NO_FATAL_FAILURE(CheckDistanceToConvex(point, 0.0));
      }
      ASSERT_NO_FATAL_FAILURE(
          CheckDistanceToConvex(math::Lerp(polygon_points_[0], polygon_points_[1], 0.5), 0.0));
    }
  }
  {
    for (int k = 0; k < 300; ++k) {
      polygon_points_ = testing::MakeRandomConvexPolygon(testing::RandomInt(3, 20),
                                                         testing::RandomDouble(1.0, 10.0));
      for (int i = 0; i < 500; ++i) {
        ASSERT_NO_FATAL_FAILURE(CheckDistanceToConvex(testing::RandomPoint(15.0)));
      }
      for (const Vector2d& point : polygon_points_) {
        ASSERT_NO_FATAL_FAILURE(CheckDistanceToConvex(point, 0.0));
      }
      ASSERT_NO_FATAL_FAILURE(
          CheckDistanceToConvex(math::Lerp(polygon_points_[0], polygon_points_[1], 0.5), 0.0));
    }
  }
}

TEST_F(ConvexDistanceTest, DistanceToBruteForce) {
  {
    polygon_points_ = {{1.0, 2.0}};
    ASSERT_NO_FATAL_FAILURE(CheckDistanceToBruteForce({2.0, 2.0}, 1.0));

    polygon_points_ = {{1.0, 1.0}, {2.0, 1.0}};
    ASSERT_NO_FATAL_FAILURE(CheckDistanceToBruteForce({2.0, 3.0}, 2.0));
    ASSERT_NO_FATAL_FAILURE(CheckDistanceToBruteForce({1.0, 2.0}, 1.0));

    polygon_points_ = {{0.0, 0.0}, {1.0, 0.0}, {1.0, 1.0}};
    ASSERT_NO_FATAL_FAILURE(CheckDistanceToBruteForce({1.0, 2.0}, 1.0));
    ASSERT_NO_FATAL_FAILURE(CheckDistanceToBruteForce({2.0, 1.0}, 1.0));

    polygon_points_ = {{0.0, 0.0}, {0.0, 0.0}, {1.0, 1.0}, {1.0, 1.0}};
    ASSERT_NO_FATAL_FAILURE(CheckDistanceToBruteForce({0.0, 0.0}, 0.0));
    ASSERT_NO_FATAL_FAILURE(CheckDistanceToBruteForce({2.0, 1.0}, 1.0));
  }
  {
    for (int k = 0; k < 100; ++k) {
      polygon_points_ = testing::MakeRandomPolygon(testing::RandomInt(3, 20), 10.0);
      for (int i = 0; i < 500; ++i) {
        ASSERT_NO_FATAL_FAILURE(CheckDistanceToBruteForce(testing::RandomPoint(15.0)));
      }
    }
  }
}

}  // namespace convex
}  // namespace math_internal
}  // namespace math
