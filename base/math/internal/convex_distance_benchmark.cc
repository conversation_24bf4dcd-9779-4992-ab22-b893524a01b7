// Copyright @2022 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "benchmark/benchmark.h"

#include "base/math/internal/convex_distance.h"
#include "base/math/internal/convex_hull.h"
#include "base/testing/geometry.h"
#include "base/testing/random.h"

namespace math {
namespace math_internal {
namespace convex {

namespace {

void CustomArguments(benchmark::internal::Benchmark* b) {
  CHECK(b != nullptr);
  for (int n = 3; n <= 10; ++n) {
    b->Args({n});
  }
  for (int n = 16; n <= 1024; n *= 2) {
    b->Args({n});
  }
}

}  // namespace

void BM_IsPointIn(benchmark::State& state, bool is_convex) { /* NOLINT(runtime/references) */
  const int num_points = state.range(0);
  const double radius = std::max(10.0, num_points / 10.0);
  const std::vector<Vector2d> convex_points = testing::MakeRegularPolygon(num_points, radius);
  const std::vector<Vector2d> points = testing::RandomPoints(num_points, radius * 3.0);
  int i = 0;
  while (state.KeepRunning()) {
    if (is_convex) {
      benchmark::DoNotOptimize(IsPointInConvex(convex_points, points[i]));
    } else {
      benchmark::DoNotOptimize(IsPointInBruteForce(convex_points, points[i]));
    }
    i = (i + 1) % points.size();
  }
  state.SetComplexityN(num_points);
}
BENCHMARK_CAPTURE(BM_IsPointIn, Convex, true)->Apply(CustomArguments)->Complexity();
BENCHMARK_CAPTURE(BM_IsPointIn, BruteForce, false)->Apply(CustomArguments)->Complexity();

void BM_IsPointOnBoundary(benchmark::State& state, /* NOLINT(runtime/references) */
                          bool is_convex) {
  const int num_points = state.range(0);
  const double radius = std::max(10.0, num_points / 10.0);
  const std::vector<Vector2d> convex_points = testing::MakeRegularPolygon(num_points, radius);
  const std::vector<Vector2d> points = testing::RandomPoints(num_points, radius * 3.0);
  int i = 0;
  while (state.KeepRunning()) {
    if (is_convex) {
      benchmark::DoNotOptimize(IsPointOnBoundaryConvex(convex_points, points[i]));
    } else {
      benchmark::DoNotOptimize(IsPointOnBoundaryBruteForce(convex_points, points[i]));
    }
    i = (i + 1) % points.size();
  }
  state.SetComplexityN(num_points);
}
BENCHMARK_CAPTURE(BM_IsPointOnBoundary, Convex, true)->Apply(CustomArguments)->Complexity();
BENCHMARK_CAPTURE(BM_IsPointOnBoundary, BruteForce, false)->Apply(CustomArguments)->Complexity();

void BM_DistanceToPoint(benchmark::State& state, bool is_convex) { /* NOLINT(runtime/references) */
  const int num_points = state.range(0);
  const double radius = std::max(10.0, num_points / 10.0);
  const std::vector<Vector2d> convex_points = testing::MakeRegularPolygon(num_points, radius);
  const std::vector<Vector2d> points = testing::RandomPoints(num_points, radius * 3.0);
  int i = 0;
  while (state.KeepRunning()) {
    if (is_convex) {
      benchmark::DoNotOptimize(DistanceToConvex(convex_points, points[i]));
    } else {
      benchmark::DoNotOptimize(DistanceToBruteForce(convex_points, points[i]));
    }
    i = (i + 1) % points.size();
  }
  state.SetComplexityN(num_points);
}
BENCHMARK_CAPTURE(BM_DistanceToPoint, Convex, true)->Apply(CustomArguments)->Complexity();
BENCHMARK_CAPTURE(BM_DistanceToPoint, BruteForce, false)->Apply(CustomArguments)->Complexity();

}  // namespace convex
}  // namespace math_internal
}  // namespace math

BENCHMARK_MAIN();

// clang-format off
/*
Run on (16 X 5000 MHz CPU s)
CPU Caches:
  L1 Data 32K (x8)
  L1 Instruction 32K (x8)
  L2 Unified 256K (x8)
  L3 Unified 16384K (x1)
Load Average: 2.31, 2.22, 2.38
***WARNING*** CPU scaling is enabled, the benchmark real time measurements may be noisy and will incur extra overhead.
-------------------------------------------------------------------------------
Benchmark                                     Time             CPU   Iterations
-------------------------------------------------------------------------------
BM_IsPointIn/Convex/3                      12.4 ns         12.4 ns     58561902
BM_IsPointIn/Convex/4                      13.5 ns         13.5 ns     48128370
BM_IsPointIn/Convex/5                      13.7 ns         13.7 ns     51938523
BM_IsPointIn/Convex/6                      14.0 ns         14.0 ns     49557271
BM_IsPointIn/Convex/7                      13.6 ns         13.6 ns     44740067
BM_IsPointIn/Convex/8                      13.5 ns         13.5 ns     48405654
BM_IsPointIn/Convex/9                      14.4 ns         14.4 ns     54335019
BM_IsPointIn/Convex/10                     13.6 ns         13.6 ns     46977475
BM_IsPointIn/Convex/16                     14.3 ns         14.3 ns     44844646
BM_IsPointIn/Convex/32                     16.3 ns         16.3 ns     42356530
BM_IsPointIn/Convex/64                     18.0 ns         18.0 ns     41015952
BM_IsPointIn/Convex/128                    18.3 ns         18.3 ns     33796444
BM_IsPointIn/Convex/256                    20.4 ns         20.4 ns     34767584
BM_IsPointIn/Convex/512                    31.8 ns         31.8 ns     20730613
BM_IsPointIn/Convex/1024                   47.1 ns         47.1 ns     14138131
BM_IsPointIn/Convex_BigO                   3.70 lgN        3.70 lgN
BM_IsPointIn/Convex_RMS                      29 %            29 %
BM_IsPointIn/BruteForce/3                  19.8 ns         19.8 ns     35731049
BM_IsPointIn/BruteForce/4                  22.6 ns         22.6 ns     31820708
BM_IsPointIn/BruteForce/5                  25.9 ns         25.9 ns     26370215
BM_IsPointIn/BruteForce/6                  29.7 ns         29.7 ns     23373756
BM_IsPointIn/BruteForce/7                  35.7 ns         35.7 ns     21487601
BM_IsPointIn/BruteForce/8                  38.7 ns         38.7 ns     18229240
BM_IsPointIn/BruteForce/9                  46.1 ns         46.1 ns     15099775
BM_IsPointIn/BruteForce/10                 43.3 ns         43.3 ns     16461896
BM_IsPointIn/BruteForce/16                 65.7 ns         65.7 ns     10982910
BM_IsPointIn/BruteForce/32                  125 ns          125 ns      5645872
BM_IsPointIn/BruteForce/64                  228 ns          228 ns      3057130
BM_IsPointIn/BruteForce/128                 448 ns          448 ns      1367994
BM_IsPointIn/BruteForce/256                 887 ns          887 ns       803006
BM_IsPointIn/BruteForce/512                1736 ns         1736 ns       409531
BM_IsPointIn/BruteForce/1024               3479 ns         3479 ns       204226
BM_IsPointIn/BruteForce_BigO               3.40 N          3.40 N
BM_IsPointIn/BruteForce_RMS                   2 %             2 %
BM_IsPointOnBoundary/Convex/3              13.2 ns         13.3 ns     45389079
BM_IsPointOnBoundary/Convex/4              14.9 ns         14.9 ns     51286005
BM_IsPointOnBoundary/Convex/5              13.5 ns         13.5 ns     51769122
BM_IsPointOnBoundary/Convex/6              13.3 ns         13.3 ns     44054494
BM_IsPointOnBoundary/Convex/7              13.3 ns         13.3 ns     49769747
BM_IsPointOnBoundary/Convex/8              16.5 ns         16.5 ns     43024197
BM_IsPointOnBoundary/Convex/9              13.8 ns         13.8 ns     48615258
BM_IsPointOnBoundary/Convex/10             14.2 ns         14.2 ns     50358680
BM_IsPointOnBoundary/Convex/16             14.2 ns         14.2 ns     43203695
BM_IsPointOnBoundary/Convex/32             15.6 ns         15.6 ns     44709879
BM_IsPointOnBoundary/Convex/64             17.0 ns         17.0 ns     39805060
BM_IsPointOnBoundary/Convex/128            17.9 ns         17.9 ns     38484985
BM_IsPointOnBoundary/Convex/256            20.1 ns         20.1 ns     35068201
BM_IsPointOnBoundary/Convex/512            30.0 ns         30.0 ns     22868482
BM_IsPointOnBoundary/Convex/1024           45.1 ns         45.1 ns     15074554
BM_IsPointOnBoundary/Convex_BigO           3.60 lgN        3.60 lgN
BM_IsPointOnBoundary/Convex_RMS              30 %            30 %
BM_IsPointOnBoundary/BruteForce/3          14.6 ns         14.6 ns     46983263
BM_IsPointOnBoundary/BruteForce/4          17.2 ns         17.2 ns     41771804
BM_IsPointOnBoundary/BruteForce/5          19.0 ns         19.0 ns     37497548
BM_IsPointOnBoundary/BruteForce/6          21.1 ns         21.1 ns     33452134
BM_IsPointOnBoundary/BruteForce/7          22.7 ns         22.7 ns     30311182
BM_IsPointOnBoundary/BruteForce/8          25.6 ns         25.6 ns     26523544
BM_IsPointOnBoundary/BruteForce/9          29.8 ns         29.8 ns     23503931
BM_IsPointOnBoundary/BruteForce/10         29.0 ns         29.0 ns     24348987
BM_IsPointOnBoundary/BruteForce/16         42.3 ns         42.3 ns     16184001
BM_IsPointOnBoundary/BruteForce/32         74.7 ns         74.7 ns      9525502
BM_IsPointOnBoundary/BruteForce/64          145 ns          145 ns      4801431
BM_IsPointOnBoundary/BruteForce/128         283 ns          283 ns      2499943
BM_IsPointOnBoundary/BruteForce/256         550 ns          550 ns      1281361
BM_IsPointOnBoundary/BruteForce/512        1068 ns         1068 ns       638755
BM_IsPointOnBoundary/BruteForce/1024       2161 ns         2161 ns       331917
BM_IsPointOnBoundary/BruteForce_BigO       2.11 N          2.11 N
BM_IsPointOnBoundary/BruteForce_RMS           3 %             3 %
BM_DistanceToPoint/Convex/3                17.1 ns         17.1 ns     42997621
BM_DistanceToPoint/Convex/4                15.8 ns         15.8 ns     42828653
BM_DistanceToPoint/Convex/5                18.3 ns         18.3 ns     32958657
BM_DistanceToPoint/Convex/6                16.9 ns         16.9 ns     38149536
BM_DistanceToPoint/Convex/7                23.4 ns         23.4 ns     34910262
BM_DistanceToPoint/Convex/8                23.6 ns         23.6 ns     27628697
BM_DistanceToPoint/Convex/9                28.8 ns         28.8 ns     25835324
BM_DistanceToPoint/Convex/10               22.6 ns         22.6 ns     27010306
BM_DistanceToPoint/Convex/16               33.0 ns         33.0 ns     25766975
BM_DistanceToPoint/Convex/32               40.5 ns         40.5 ns     16997380
BM_DistanceToPoint/Convex/64               46.7 ns         46.7 ns     14889843
BM_DistanceToPoint/Convex/128              52.9 ns         52.9 ns     12628482
BM_DistanceToPoint/Convex/256              61.0 ns         61.0 ns     11179127
BM_DistanceToPoint/Convex/512               110 ns          110 ns      6382685
BM_DistanceToPoint/Convex/1024              146 ns          146 ns      4848552
BM_DistanceToPoint/Convex_BigO            10.24 lgN       10.24 lgN
BM_DistanceToPoint/Convex_RMS                36 %            36 %
BM_DistanceToPoint/BruteForce/3            33.8 ns         33.8 ns     21535464
BM_DistanceToPoint/BruteForce/4            38.3 ns         38.3 ns     18098182
BM_DistanceToPoint/BruteForce/5            45.2 ns         45.2 ns     15877104
BM_DistanceToPoint/BruteForce/6            45.0 ns         45.0 ns     13569612
BM_DistanceToPoint/BruteForce/7            54.2 ns         54.2 ns     13206389
BM_DistanceToPoint/BruteForce/8            65.0 ns         65.0 ns     11023520
BM_DistanceToPoint/BruteForce/9            76.9 ns         76.9 ns      9564495
BM_DistanceToPoint/BruteForce/10           69.7 ns         69.7 ns      9311630
BM_DistanceToPoint/BruteForce/16            109 ns          109 ns      5918753
BM_DistanceToPoint/BruteForce/32            209 ns          209 ns      3065087
BM_DistanceToPoint/BruteForce/64            419 ns          419 ns      1648334
BM_DistanceToPoint/BruteForce/128           789 ns          789 ns       882696
BM_DistanceToPoint/BruteForce/256          1516 ns         1516 ns       460643
BM_DistanceToPoint/BruteForce/512          2996 ns         2996 ns       235498
BM_DistanceToPoint/BruteForce/1024         5951 ns         5951 ns       118003
BM_DistanceToPoint/BruteForce_BigO         5.83 N          5.83 N
BM_DistanceToPoint/BruteForce_RMS             3 %             3 %
*/
