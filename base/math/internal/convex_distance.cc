// Copyright @2022 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/math/internal/convex_distance.h"

#include <limits>

#include "glog/logging.h"

#include "base/math/internal/convex_utils.h"
#include "base/math/line_segment2d.h"
#include "base/strings/macros.h"

namespace math {
namespace math_internal {
namespace convex {

using convex_internal::Next;
using convex_internal::Prev;

namespace convex_internal {

// Get the index of the local closest segment's start_index in forward or backward direction.
// Return -1 if not found.
int DistanceToConvexForward(const base::ConstSpan<Vector2d>& convex_points,
                            const Vector2d& point,
                            int forward_start) {
  DCHECK_GE(forward_start, 0);
  DCHECK_LT(forward_start, static_cast<int>(convex_points.size()));
  const int num_points = convex_points.size();
  if (num_points < 2) {
    return -1;
  }
  const auto is_closer = [&convex_points, &point](int start, int end) {
    const Vector2d d0 = point - convex_points[start];
    const Vector2d d1 = convex_points[end] - convex_points[start];
    return d0.InnerProd(d1) > 0.0 && d0.CrossProd(d1) > 0.0;
  };
  const int forward_end = Next(forward_start, num_points);
  if (!is_closer(forward_start, forward_end)) {
    return -1;
  }
  const Vector2d direction = convex_points[forward_end] - convex_points[forward_start];
  const int offset = convex_internal::BinarySearch(
      1,
      num_points - 1,
      [&convex_points, &direction, &is_closer, num_points, forward_start](int offset) {
        const int start = (forward_start + offset) % num_points;
        return direction.InnerProd(convex_points[start] - convex_points[forward_start]) > 0.0 &&
               is_closer(start, Next(start, num_points));
      });
  return (forward_start + offset) % num_points;
}

int DistanceToConvexBackward(const base::ConstSpan<Vector2d>& convex_points,
                             const Vector2d& point,
                             int backward_start) {
  DCHECK_GE(backward_start, 0);
  DCHECK_LT(backward_start, static_cast<int>(convex_points.size()));
  const int num_points = convex_points.size();
  if (num_points < 2) {
    return -1;
  }
  const auto is_closer = [&convex_points, &point](int start, int end) {
    const Vector2d d0 = convex_points[end] - convex_points[start];
    const Vector2d d1 = point - convex_points[start];
    return d0.InnerProd(d1) > 0.0 && d0.CrossProd(d1) > 0.0;
  };
  const int backward_end = Prev(backward_start, num_points);
  if (!is_closer(backward_start, backward_end)) {
    return -1;
  }
  const Vector2d direction = convex_points[backward_end] - convex_points[backward_start];
  const int offset = convex_internal::BinarySearch(
      1,
      num_points - 1,
      [&convex_points, &direction, &is_closer, num_points, backward_start](int offset) {
        const int start = (backward_start + num_points - offset) % num_points;
        return direction.InnerProd(convex_points[start] - convex_points[backward_start]) > 0.0 &&
               is_closer(start, Prev(start, num_points));
      });
  return (backward_start + num_points - offset) % num_points;
}

int GetLowerIndexBinarySearch(const base::ConstSpan<Vector2d>& convex_points,
                              const Vector2d& point) {
  CHECK_GE(convex_points.size(), 3);
  const int num_points = convex_points.size();
  if (CrossProd(convex_points[0], convex_points[1], point) <= 0.0) {
    return 0;
  }
  if (CrossProd(convex_points[0], convex_points[num_points - 1], point) >= 0.0) {
    return num_points - 1;
  }
  const Vector2d segment = point - convex_points[0];
  return BinarySearch(2, num_points - 2, [&convex_points, &segment](int index) {
    return (convex_points[index] - convex_points[0]).CrossProd(segment) >= 0.0;
  });
}

}  // namespace convex_internal

bool IsPointInConvex(const base::ConstSpan<Vector2d>& convex_points, const Vector2d& point) {
  const int num_points = convex_points.size();
  const int p = convex_internal::GetLowerIndexBinarySearch(convex_points, point);
  if (p >= 1 && p <= num_points - 2) {
    if (CrossProd(convex_points[p], convex_points[p + 1], point) >= 0.0 ||
        LineSegment2d::IsPointIn(point, convex_points[p], convex_points[p + 1])) {
      return true;
    }
  }
  if (p <= 1) {
    if (LineSegment2d::IsPointIn(point, convex_points[0], convex_points[1])) {
      return true;
    }
  }
  if (p >= num_points - 2) {
    if (LineSegment2d::IsPointIn(point, convex_points[0], convex_points[num_points - 1])) {
      return true;
    }
  }
  return false;
}

bool IsPointOnBoundaryConvex(const base::ConstSpan<Vector2d>& convex_points,
                             const Vector2d& point) {
  const int num_points = convex_points.size();
  const int p = convex_internal::GetLowerIndexBinarySearch(convex_points, point);
  if (p >= 1 && p <= num_points - 2) {
    if (LineSegment2d::IsPointIn(point, convex_points[p], convex_points[p + 1])) {
      return true;
    }
  }
  if (p <= 1) {
    if (LineSegment2d::IsPointIn(point, convex_points[0], convex_points[1])) {
      return true;
    }
  }
  if (p >= num_points - 2) {
    if (LineSegment2d::IsPointIn(point, convex_points[0], convex_points[num_points - 1])) {
      return true;
    }
  }
  return false;
}

double DistanceSquareToConvex(const base::ConstSpan<Vector2d>& convex_points,
                              const Vector2d& point,
                              Vector2d* nearest_point) {
  const int num_points = convex_points.size();
  const int p = convex_internal::GetLowerIndexBinarySearch(convex_points, point);
  if (p >= 1 && p <= num_points - 2) {
    if (CrossProd(convex_points[p], convex_points[p + 1], point) >= 0.0 ||
        LineSegment2d::IsPointIn(point, convex_points[p], convex_points[p + 1])) {
      if (nearest_point != nullptr) {
        *nearest_point = point;
      }
      return 0.0;
    }
  }
  const int i0 =
      convex_internal::DistanceToConvexForward(convex_points, point, Next(p, num_points));
  if (i0 >= 0) {
    const Vector2d& p0 = convex_points[i0];
    const Vector2d& p1 = convex_points[Next(i0, num_points)];
    return LineSegment2d::DistanceSquareTo(point, p0, p1, nearest_point);
  }
  const int i1 = convex_internal::DistanceToConvexBackward(convex_points, point, p);
  if (i1 >= 0) {
    const Vector2d& p0 = convex_points[i1];
    const Vector2d& p1 = convex_points[Prev(i1, num_points)];
    return LineSegment2d::DistanceSquareTo(point, p0, p1, nearest_point);
  }
  return LineSegment2d::DistanceSquareTo(
      point, convex_points[p], convex_points[Next(p, num_points)], nearest_point);
}

double DistanceToConvex(const base::ConstSpan<Vector2d>& convex_points,
                        const Vector2d& point,
                        Vector2d* nearest_point) {
  return std::sqrt(DistanceSquareToConvex(convex_points, point, nearest_point));
}

bool IsPointInBruteForce(const base::ConstSpan<Vector2d>& polygon_points, const Vector2d& point) {
  CHECK_GE(polygon_points.size(), 1);
  const int num_points = polygon_points.size();
  if (num_points == 1) {
    return point.IsNear(polygon_points[0], math::kEpsilon);
  }
  if (num_points == 2) {
    return LineSegment2d::IsPointIn(point, polygon_points[0], polygon_points[1]);
  }
  int intersections = 0;
  for (int i = 0, j = num_points - 1; i < num_points; ++i) {
    const Vector2d& p0 = polygon_points[j];
    const Vector2d& p1 = polygon_points[i];
    if (LineSegment2d::IsPointIn(point, p0, p1)) {
      return true;
    }
    if ((p0.y > point.y) != (p1.y > point.y)) {
      const double cross = CrossProd(p0, p1, point);
      if ((p1.y > p0.y) ? (cross > 0.0) : (cross < 0.0)) {
        intersections++;
      }
    }
    j = i;
  }
  return intersections % 2 != 0;
}

bool IsPointOnBoundaryBruteForce(const base::ConstSpan<Vector2d>& polygon_points,
                                 const Vector2d& point) {
  CHECK_GE(polygon_points.size(), 1);
  const int num_points = polygon_points.size();
  if (num_points == 1) {
    return point.IsNear(polygon_points[0], math::kEpsilon);
  }
  if (num_points == 2) {
    return LineSegment2d::IsPointIn(point, polygon_points[0], polygon_points[1]);
  }
  for (int i = 0, j = num_points - 1; i < num_points; ++i) {
    const Vector2d& p0 = polygon_points[j];
    const Vector2d& p1 = polygon_points[i];
    if (LineSegment2d::IsPointIn(point, p0, p1)) {
      return true;
    }
    j = i;
  }
  return false;
}

double DistanceSquareToBruteForce(const base::ConstSpan<Vector2d>& polygon_points,
                                  const Vector2d& point,
                                  Vector2d* nearest_point) {
  CHECK_GE(polygon_points.size(), 1);
  const int num_points = polygon_points.size();
  if (num_points == 1) {
    if (nearest_point != nullptr) {
      *nearest_point = polygon_points[0];
    }
    return point.DistanceSquareTo(polygon_points[0]);
  }
  if (num_points == 2) {
    return LineSegment2d::DistanceSquareTo(
        point, polygon_points[0], polygon_points[1], nearest_point);
  }
  if (IsPointInBruteForce(polygon_points, point)) {
    if (nearest_point != nullptr) {
      *nearest_point = point;
    }
    return 0.0;
  }
  double min_dist2 = std::numeric_limits<double>::infinity();
  for (int i = 0, j = num_points - 1; i < num_points; ++i) {
    Vector2d candidate_point;
    const Vector2d& p0 = polygon_points[j];
    const Vector2d& p1 = polygon_points[i];
    const double dist2 = LineSegment2d::DistanceSquareTo(point, p0, p1, &candidate_point);
    if (dist2 < min_dist2) {
      if (nearest_point != nullptr) {
        *nearest_point = candidate_point;
      }
      min_dist2 = dist2;
    }
    j = i;
  }
  return min_dist2;
}

double DistanceToBruteForce(const base::ConstSpan<Vector2d>& polygon_points,
                            const Vector2d& point,
                            Vector2d* nearest_point) {
  return std::sqrt(DistanceSquareToBruteForce(polygon_points, point, nearest_point));
}

}  // namespace convex
}  // namespace math_internal
}  // namespace math
