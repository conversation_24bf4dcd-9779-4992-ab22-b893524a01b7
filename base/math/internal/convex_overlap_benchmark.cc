// Copyright @2022 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "benchmark/benchmark.h"

#include "base/math/internal/convex_overlap.h"
#include "base/math/internal/convex_utils.h"
#include "base/testing/geometry.h"
#include "base/testing/random.h"

namespace math {
namespace math_internal {
namespace convex {

using convex_internal::Next;

namespace {

void CustomArguments(benchmark::internal::Benchmark* b) {
  CHECK(b != nullptr);
  for (int n = 3; n <= 10; ++n) {
    b->Args({n});
  }
  for (int n = 16; n <= 1024; n *= 2) {
    b->Args({n});
  }
}

}  // namespace

void BM_GetOverlapWithSegment(benchmark::State& state, /* NOLINT(runtime/references) */
                              bool is_convex) {
  const int num_points = state.range(0);
  const double radius = std::max(10.0, num_points / 10.0);
  std::vector<std::vector<Vector2d>> convex_points(100);
  for (int i = 0; i < static_cast<int>(convex_points.size()); ++i) {
    convex_points[i] =
        testing::MakeRegularPolygon(num_points, radius, testing::RandomPoint(radius));
  }
  std::vector<LineSegment2d> segments(200);
  for (int i = 0; i < static_cast<int>(segments.size()); ++i) {
    segments[i] = LineSegment2d(Vector2d(0.0, 0.0), testing::RandomPoint(radius * 10.0));
  }
  int i = 0;
  int j = 0;
  while (state.KeepRunning()) {
    if (is_convex) {
      benchmark::DoNotOptimize(GetOverlapConvex(convex_points[i], segments[j], true));
    } else {
      benchmark::DoNotOptimize(GetOverlapBruteForce(convex_points[i], segments[j]));
    }
    i = Next(i, convex_points.size());
    if (i != 0) {
      continue;
    }
    j = Next(j, convex_points.size());
  }
  state.SetComplexityN(num_points);
}
BENCHMARK_CAPTURE(BM_GetOverlapWithSegment, Convex, true)->Apply(CustomArguments)->Complexity();
BENCHMARK_CAPTURE(BM_GetOverlapWithSegment, BruteForce, false)
    ->Apply(CustomArguments)
    ->Complexity();

void BM_HasOverlapWithSegment(benchmark::State& state, /* NOLINT(runtime/references) */
                              bool is_convex) {
  const int num_points = state.range(0);
  const double radius = std::max(10.0, num_points / 10.0);
  std::vector<std::vector<Vector2d>> convex_points(100);
  for (int i = 0; i < static_cast<int>(convex_points.size()); ++i) {
    convex_points[i] =
        testing::MakeRegularPolygon(num_points, radius, testing::RandomPoint(radius));
  }
  std::vector<LineSegment2d> segments(200);
  for (int i = 0; i < static_cast<int>(segments.size()); ++i) {
    segments[i] = LineSegment2d(Vector2d(0.0, 0.0), testing::RandomPoint(radius * 10.0));
  }
  int i = 0;
  int j = 0;
  while (state.KeepRunning()) {
    if (is_convex) {
      benchmark::DoNotOptimize(HasOverlapConvex(convex_points[i], segments[j], true));
    } else {
      benchmark::DoNotOptimize(HasOverlapBruteForce(convex_points[i], segments[j]));
    }
    i = Next(i, convex_points.size());
    if (i != 0) {
      continue;
    }
    j = Next(j, convex_points.size());
  }
  state.SetComplexityN(num_points);
}
BENCHMARK_CAPTURE(BM_HasOverlapWithSegment, Convex, true)->Apply(CustomArguments)->Complexity();
BENCHMARK_CAPTURE(BM_HasOverlapWithSegment, BruteForce, false)
    ->Apply(CustomArguments)
    ->Complexity();

void BM_HasOverlapWithPolygon(benchmark::State& state, /* NOLINT(runtime/references) */
                              bool is_convex) {
  const int num_points = state.range(0);
  const double radius = std::max(10.0, num_points / 10.0);
  std::vector<std::vector<Vector2d>> convex_points(100);
  for (int i = 0; i < static_cast<int>(convex_points.size()); ++i) {
    convex_points[i] =
        testing::MakeRegularPolygon(num_points, radius, testing::RandomPoint(radius * 5.0));
  }
  int i = 0;
  int j = 0;
  while (state.KeepRunning()) {
    if (is_convex) {
      benchmark::DoNotOptimize(HasOverlapConvex(convex_points[i], convex_points[j]));
    } else {
      benchmark::DoNotOptimize(HasOverlapBruteForce(convex_points[i], convex_points[j]));
    }
    i = Next(i, convex_points.size());
    if (i != 0) {
      continue;
    }
    j = Next(j, convex_points.size());
  }
  state.SetComplexityN(num_points);
}
BENCHMARK_CAPTURE(BM_HasOverlapWithPolygon, Convex, true)->Apply(CustomArguments)->Complexity();
BENCHMARK_CAPTURE(BM_HasOverlapWithPolygon, BruteForce, false)
    ->Apply(CustomArguments)
    ->Complexity();

void BM_ContainsConvex(benchmark::State& state) { /* NOLINT(runtime/references) */
  const int num_points = state.range(0);
  const double radius = std::max(10.0, num_points / 10.0);
  std::vector<std::vector<Vector2d>> convex_points(100);
  for (int i = 0; i < static_cast<int>(convex_points.size()); ++i) {
    convex_points[i] =
        testing::MakeRegularPolygon(num_points, radius, testing::RandomPoint(radius * 5.0));
  }
  int i = 0;
  int j = 0;
  while (state.KeepRunning()) {
    benchmark::DoNotOptimize(ContainsConvex(convex_points[i], convex_points[j]));
    i = Next(i, convex_points.size());
    if (i != 0) {
      continue;
    }
    j = Next(j, convex_points.size());
  }
  state.SetComplexityN(num_points);
}
BENCHMARK(BM_ContainsConvex)->Apply(CustomArguments)->Complexity();

void BM_ComputeOverlapConvex(benchmark::State& state) { /* NOLINT(runtime/references) */
  const int num_points = state.range(0);
  const double radius = std::max(10.0, num_points / 10.0);
  std::vector<std::vector<Vector2d>> convex_points(100);
  for (int i = 0; i < static_cast<int>(convex_points.size()); ++i) {
    convex_points[i] =
        testing::MakeRegularPolygon(num_points, radius, testing::RandomPoint(radius * 2.0));
  }
  int i = 0;
  int j = 0;
  while (state.KeepRunning()) {
    benchmark::DoNotOptimize(ComputeOverlapConvex(convex_points[i], convex_points[j]));
    i = Next(i, convex_points.size());
    if (i != 0) {
      continue;
    }
    j = Next(j, convex_points.size());
  }
  state.SetComplexityN(num_points);
}
BENCHMARK(BM_ComputeOverlapConvex)->Apply(CustomArguments)->Complexity();

}  // namespace convex
}  // namespace math_internal
}  // namespace math

BENCHMARK_MAIN();

// clang-format off
/*
Run on (16 X 5000 MHz CPU s)
CPU Caches:
  L1 Data 32K (x8)
  L1 Instruction 32K (x8)
  L2 Unified 256K (x8)
  L3 Unified 16384K (x1)
Load Average: 0.22, 0.43, 0.42
***WARNING*** CPU scaling is enabled, the benchmark real time measurements may be noisy and will incur extra overhead.
-----------------------------------------------------------------------------------
Benchmark                                         Time             CPU   Iterations
-----------------------------------------------------------------------------------
BM_GetOverlapWithSegment/Convex/3               122 ns          122 ns      5685512
BM_GetOverlapWithSegment/Convex/4               129 ns          129 ns      5408749
BM_GetOverlapWithSegment/Convex/5               140 ns          140 ns      5100216
BM_GetOverlapWithSegment/Convex/6               142 ns          142 ns      4847585
BM_GetOverlapWithSegment/Convex/7               145 ns          145 ns      4742158
BM_GetOverlapWithSegment/Convex/8               154 ns          154 ns      4602422
BM_GetOverlapWithSegment/Convex/9               152 ns          152 ns      4583845
BM_GetOverlapWithSegment/Convex/10              157 ns          157 ns      4476601
BM_GetOverlapWithSegment/Convex/16              182 ns          182 ns      3995765
BM_GetOverlapWithSegment/Convex/32              210 ns          210 ns      3274686
BM_GetOverlapWithSegment/Convex/64              229 ns          229 ns      3097599
BM_GetOverlapWithSegment/Convex/128             266 ns          266 ns      2704484
BM_GetOverlapWithSegment/Convex/256             317 ns          317 ns      2219441
BM_GetOverlapWithSegment/Convex/512             390 ns          390 ns      1860916
BM_GetOverlapWithSegment/Convex/1024            430 ns          430 ns      1587056
BM_GetOverlapWithSegment/Convex_BigO          42.99 lgN       42.99 lgN
BM_GetOverlapWithSegment/Convex_RMS              13 %            13 %
BM_GetOverlapWithSegment/BruteForce/3          87.7 ns         87.7 ns      8288559
BM_GetOverlapWithSegment/BruteForce/4          99.5 ns         99.5 ns      6903360
BM_GetOverlapWithSegment/BruteForce/5           113 ns          113 ns      6269193
BM_GetOverlapWithSegment/BruteForce/6           119 ns          119 ns      5823612
BM_GetOverlapWithSegment/BruteForce/7           135 ns          135 ns      5130805
BM_GetOverlapWithSegment/BruteForce/8           144 ns          144 ns      4949957
BM_GetOverlapWithSegment/BruteForce/9           156 ns          156 ns      4494443
BM_GetOverlapWithSegment/BruteForce/10          162 ns          162 ns      4245541
BM_GetOverlapWithSegment/BruteForce/16          223 ns          223 ns      3184701
BM_GetOverlapWithSegment/BruteForce/32          380 ns          380 ns      1852039
BM_GetOverlapWithSegment/BruteForce/64          657 ns          657 ns      1068102
BM_GetOverlapWithSegment/BruteForce/128        1197 ns         1197 ns       583978
BM_GetOverlapWithSegment/BruteForce/256        2288 ns         2288 ns       307997
BM_GetOverlapWithSegment/BruteForce/512        4409 ns         4409 ns       158506
BM_GetOverlapWithSegment/BruteForce/1024       8740 ns         8740 ns        80012
BM_GetOverlapWithSegment/BruteForce_BigO       8.59 N          8.59 N
BM_GetOverlapWithSegment/BruteForce_RMS           6 %             6 %
BM_HasOverlapWithSegment/Convex/3              45.1 ns         45.1 ns     12462123
BM_HasOverlapWithSegment/Convex/4              49.5 ns         49.5 ns     13265649
BM_HasOverlapWithSegment/Convex/5              48.6 ns         48.6 ns     19872567
BM_HasOverlapWithSegment/Convex/6              48.8 ns         48.8 ns     19017595
BM_HasOverlapWithSegment/Convex/7              40.0 ns         40.0 ns     17283119
BM_HasOverlapWithSegment/Convex/8              48.6 ns         48.6 ns     18150184
BM_HasOverlapWithSegment/Convex/9              40.5 ns         40.5 ns     19152595
BM_HasOverlapWithSegment/Convex/10             37.2 ns         37.2 ns     17327927
BM_HasOverlapWithSegment/Convex/16             36.8 ns         36.8 ns     16834138
BM_HasOverlapWithSegment/Convex/32             56.0 ns         56.0 ns     10000000
BM_HasOverlapWithSegment/Convex/64             53.8 ns         53.8 ns     12401427
BM_HasOverlapWithSegment/Convex/128            59.0 ns         59.0 ns     10000000
BM_HasOverlapWithSegment/Convex/256            65.4 ns         65.4 ns      7963773
BM_HasOverlapWithSegment/Convex/512            80.4 ns         80.4 ns     11115258
BM_HasOverlapWithSegment/Convex/1024            113 ns          113 ns      6556904
BM_HasOverlapWithSegment/Convex_BigO          10.44 lgN       10.44 lgN
BM_HasOverlapWithSegment/Convex_RMS              30 %            30 %
BM_HasOverlapWithSegment/BruteForce/3          43.3 ns         43.3 ns     15029631
BM_HasOverlapWithSegment/BruteForce/4          45.9 ns         45.9 ns     16007598
BM_HasOverlapWithSegment/BruteForce/5          45.8 ns         45.8 ns     13593914
BM_HasOverlapWithSegment/BruteForce/6          54.8 ns         54.8 ns     10000000
BM_HasOverlapWithSegment/BruteForce/7          58.4 ns         58.4 ns     12148176
BM_HasOverlapWithSegment/BruteForce/8          55.8 ns         55.8 ns     11898260
BM_HasOverlapWithSegment/BruteForce/9          57.8 ns         57.8 ns     11066355
BM_HasOverlapWithSegment/BruteForce/10         66.0 ns         66.0 ns     10013358
BM_HasOverlapWithSegment/BruteForce/16         91.1 ns         91.1 ns      7838834
BM_HasOverlapWithSegment/BruteForce/32          163 ns          163 ns      4239110
BM_HasOverlapWithSegment/BruteForce/64          302 ns          302 ns      2543205
BM_HasOverlapWithSegment/BruteForce/128         545 ns          545 ns      1275589
BM_HasOverlapWithSegment/BruteForce/256        1130 ns         1130 ns       610940
BM_HasOverlapWithSegment/BruteForce/512        2283 ns         2283 ns       325912
BM_HasOverlapWithSegment/BruteForce/1024       4760 ns         4760 ns       162502
BM_HasOverlapWithSegment/BruteForce_BigO       4.60 N          4.60 N
BM_HasOverlapWithSegment/BruteForce_RMS           5 %             5 %
BM_HasOverlapWithPolygon/Convex/3              26.5 ns         26.5 ns     26720743
BM_HasOverlapWithPolygon/Convex/4              28.4 ns         28.4 ns     25707567
BM_HasOverlapWithPolygon/Convex/5              31.6 ns         31.6 ns     22231684
BM_HasOverlapWithPolygon/Convex/6              38.6 ns         38.6 ns     18112924
BM_HasOverlapWithPolygon/Convex/7              36.5 ns         36.5 ns     19874842
BM_HasOverlapWithPolygon/Convex/8              42.2 ns         42.2 ns     16702665
BM_HasOverlapWithPolygon/Convex/9              41.5 ns         41.5 ns     17353035
BM_HasOverlapWithPolygon/Convex/10             53.0 ns         53.0 ns     13295854
BM_HasOverlapWithPolygon/Convex/16             76.3 ns         76.3 ns      9476977
BM_HasOverlapWithPolygon/Convex/32              129 ns          129 ns      5381464
BM_HasOverlapWithPolygon/Convex/64              244 ns          244 ns      2931375
BM_HasOverlapWithPolygon/Convex/128             431 ns          431 ns      1593951
BM_HasOverlapWithPolygon/Convex/256             841 ns          841 ns       813958
BM_HasOverlapWithPolygon/Convex/512            1791 ns         1791 ns       410901
BM_HasOverlapWithPolygon/Convex/1024           3397 ns         3397 ns       212181
BM_HasOverlapWithPolygon/Convex_BigO           3.35 N          3.35 N
BM_HasOverlapWithPolygon/Convex_RMS               6 %             6 %
BM_HasOverlapWithPolygon/BruteForce/3           111 ns          111 ns      6285081
BM_HasOverlapWithPolygon/BruteForce/4           171 ns          171 ns      4057132
BM_HasOverlapWithPolygon/BruteForce/5           217 ns          217 ns      3259846
BM_HasOverlapWithPolygon/BruteForce/6           306 ns          306 ns      2290611
BM_HasOverlapWithPolygon/BruteForce/7           383 ns          383 ns      1852884
BM_HasOverlapWithPolygon/BruteForce/8           507 ns          507 ns      1357801
BM_HasOverlapWithPolygon/BruteForce/9           593 ns          593 ns      1185348
BM_HasOverlapWithPolygon/BruteForce/10          763 ns          763 ns       921599
BM_HasOverlapWithPolygon/BruteForce/16         1894 ns         1894 ns       369979
BM_HasOverlapWithPolygon/BruteForce/32         6140 ns         6140 ns       113090
BM_HasOverlapWithPolygon/BruteForce/64        21750 ns        21750 ns        31788
BM_HasOverlapWithPolygon/BruteForce/128       79604 ns        79603 ns         8440
BM_HasOverlapWithPolygon/BruteForce/256      307449 ns       307450 ns         2344
BM_HasOverlapWithPolygon/BruteForce/512     1187621 ns      1187618 ns          631
BM_HasOverlapWithPolygon/BruteForce/1024    4710181 ns      4710231 ns          147
BM_HasOverlapWithPolygon/BruteForce_BigO       4.50 N^2        4.50 N^2
BM_HasOverlapWithPolygon/BruteForce_RMS           1 %             1 %
BM_ContainsConvex/3                            22.3 ns         22.3 ns     31308493
BM_ContainsConvex/4                            20.9 ns         20.9 ns     33062965
BM_ContainsConvex/5                            24.3 ns         24.3 ns     28952114
BM_ContainsConvex/6                            25.3 ns         25.3 ns     27512866
BM_ContainsConvex/7                            27.8 ns         27.8 ns     24604907
BM_ContainsConvex/8                            25.8 ns         25.8 ns     27748756
BM_ContainsConvex/9                            29.9 ns         29.9 ns     23534931
BM_ContainsConvex/10                           29.1 ns         29.1 ns     23958488
BM_ContainsConvex/16                           35.5 ns         35.5 ns     19458258
BM_ContainsConvex/32                           49.6 ns         49.6 ns     13541231
BM_ContainsConvex/64                           78.3 ns         78.3 ns      9052954
BM_ContainsConvex/128                           129 ns          129 ns      5221467
BM_ContainsConvex/256                           232 ns          232 ns      2961605
BM_ContainsConvex/512                           443 ns          443 ns      1537955
BM_ContainsConvex/1024                          921 ns          921 ns       791455
BM_ContainsConvex_BigO                         0.90 N          0.90 N
BM_ContainsConvex_RMS                            13 %            13 %
BM_ComputeOverlapConvex/3                       134 ns          134 ns      5230610
BM_ComputeOverlapConvex/4                       120 ns          120 ns      5755132
BM_ComputeOverlapConvex/5                       216 ns          216 ns      3144727
BM_ComputeOverlapConvex/6                       176 ns          176 ns      3927778
BM_ComputeOverlapConvex/7                       294 ns          294 ns      2385935
BM_ComputeOverlapConvex/8                       235 ns          235 ns      2916161
BM_ComputeOverlapConvex/9                       369 ns          369 ns      1889547
BM_ComputeOverlapConvex/10                      282 ns          282 ns      2348870
BM_ComputeOverlapConvex/16                      413 ns          413 ns      1762454
BM_ComputeOverlapConvex/32                      768 ns          768 ns       943697
BM_ComputeOverlapConvex/64                     1334 ns         1334 ns       525695
BM_ComputeOverlapConvex/128                    2683 ns         2683 ns       267731
BM_ComputeOverlapConvex/256                    5133 ns         5133 ns       138402
BM_ComputeOverlapConvex/512                   10686 ns        10686 ns        66035
BM_ComputeOverlapConvex/1024                  20292 ns        20292 ns        33842
BM_ComputeOverlapConvex_BigO                  20.05 N         20.05 N
BM_ComputeOverlapConvex_RMS                       5 %             5 %
*/
