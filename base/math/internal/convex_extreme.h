// Copyright @2022 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <vector>

#include "base/container/span.h"
#include "base/math/vector2.h"

namespace math {
namespace math_internal {
namespace convex {

// http://programmer.group/extremum-of-convex-polygon.html
// Get the index of the min/max value point relative to direction.
void ExtremePointsConvex(const base::ConstSpan<Vector2d>& convex_points,
                         const Vector2d& direction,
                         int* min_index,
                         int* max_index,
                         bool force_binary_search = false);
void ExtremePointsConvex(const base::ConstSpan<Vector2d>& convex_points,
                         const Vector2d& direction,
                         Vector2d* min_point,
                         Vector2d* max_point,
                         bool force_binary_search = false);

void ExtremePointsBruteForce(const base::ConstSpan<Vector2d>& polygon_points,
                             const Vector2d& direction,
                             int* min_index,
                             int* max_index);
void ExtremePointsBruteForce(const base::ConstSpan<Vector2d>& polygon_points,
                             const Vector2d& direction,
                             Vector2d* min_point,
                             Vector2d* max_point);

double GetMinAreaBoundingBoxConvex(const base::ConstSpan<Vector2d>& convex_points);

}  // namespace convex
}  // namespace math_internal
}  // namespace math
