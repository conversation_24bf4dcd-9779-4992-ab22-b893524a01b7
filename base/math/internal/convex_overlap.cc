// Copyright @2022 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/math/internal/convex_overlap.h"

#include <algorithm>
#include <utility>

#include "glog/logging.h"

#include "base/math/internal/convex_distance.h"
#include "base/math/internal/convex_extreme.h"
#include "base/math/internal/convex_hull.h"
#include "base/math/internal/convex_iterator.h"
#include "base/math/line_segment2d.h"
#include "base/math/math_util.h"

namespace math {
namespace math_internal {
namespace convex {

using convex_internal::Iterator;

namespace {

bool SortAndUniqueOverlap(std::vector<double>* overlap_project_s) {
  if (overlap_project_s == nullptr || overlap_project_s->empty()) {
    return false;
  }
  std::sort(overlap_project_s->begin(), overlap_project_s->end());
  int num_overlaps = 1;
  for (int i = 1; i < static_cast<int>(overlap_project_s->size()); ++i) {
    if ((*overlap_project_s)[i] - (*overlap_project_s)[num_overlaps - 1] > math::kEpsilon) {
      (*overlap_project_s)[num_overlaps++] = (*overlap_project_s)[i];
    }
  }
  overlap_project_s->resize(num_overlaps);
  return true;
}

bool GetOverlapWithBoundaryConvexBinarySearch(const LineSegment2d& segment,
                                              const base::ConstSpan<Vector2d>& convex_points,
                                              std::vector<double>* overlap_project_s) {
  int min_index = 0;
  int max_index = 0;
  ExtremePointsConvex(convex_points, segment.direction().Rotate90(), &min_index, &max_index);
  if (segment.CrossProdWithUnit(convex_points[min_index]) > +math::kEpsilon ||
      segment.CrossProdWithUnit(convex_points[max_index]) < -math::kEpsilon) {
    return SortAndUniqueOverlap(overlap_project_s);
  }

  // Get the lower-index of the closest point along the given direction.
  const auto get_lower_index = [&convex_points, &segment](int start_index, int end_index,
                                                          const Vector2d& direction) {
    const double min_project_s = direction.InnerProd(segment.start());
    const int num_points = convex_points.size();
    const int index = convex_internal::BinarySearch(
        start_index, start_index < end_index ? end_index : end_index + num_points,
        [&convex_points, num_points, min_project_s, &direction](int i) {
          return direction.InnerProd(convex_points[i % num_points]) < min_project_s;
        });
    return std::max(index, start_index) % num_points;
  };

  const std::array<int, 2> cursor_index = {
      get_lower_index(min_index, max_index, segment.direction().Rotate90()),
      get_lower_index(max_index, min_index, segment.direction().Rotate270()),
  };
  for (int index : cursor_index) {
    Iterator iter(convex_points, index);
    for (int i = 0; i < 2; ++i, ++iter) {
      Vector2d intersection_point;
      if (LineSegment2d::GetIntersect(iter.start(), iter.end(), segment.start(), segment.end(),
                                      &intersection_point)) {
        if (overlap_project_s == nullptr) {
          return true;
        }
        const double project_s = segment.InnerProdWithUnit(intersection_point);
        overlap_project_s->push_back(math::Clamp(project_s, 0.0, segment.length()));
      }
    }
  }
  return SortAndUniqueOverlap(overlap_project_s);
}

bool GetOverlapWithBoundaryBruteForce(const LineSegment2d& segment,
                                      const base::ConstSpan<Vector2d>& polygon_points,
                                      std::vector<double>* overlap_project_s) {
  const int num_points = polygon_points.size();
  int j = num_points - 1;
  int sign_j = math::Sign(segment.CrossProdWithUnit(polygon_points[j]));
  for (int i = 0; i < num_points; ++i) {
    const int sign_i = math::Sign(segment.CrossProdWithUnit(polygon_points[i]));
    double project_s = 0.0;
    if (sign_i == 0 && segment.IsPointIn(polygon_points[i], &project_s)) {
      if (overlap_project_s == nullptr) {
        return true;
      }
      overlap_project_s->push_back(math::Clamp(project_s, 0.0, segment.length()));
    }
    Vector2d intersection_point;
    if (sign_i * sign_j < 0 &&
        LineSegment2d::GetIntersect(polygon_points[i], polygon_points[j], segment.start(),
                                    segment.end(), &intersection_point)) {
      if (overlap_project_s == nullptr) {
        return true;
      }
      project_s = segment.InnerProdWithUnit(intersection_point);
      overlap_project_s->push_back(math::Clamp(project_s, 0.0, segment.length()));
    }
    j = i;
    sign_j = sign_i;
  }
  return SortAndUniqueOverlap(overlap_project_s);
}

bool GetOverlapConvex(const base::ConstSpan<Vector2d>& convex_points,
                      const LineSegment2d& segment,
                      std::vector<double>* overlap_project_s,
                      bool force_binary_search) {
  CHECK_GE(convex_points.size(), 3);
  if (segment.length() < math::kEpsilon) {
    if (IsPointInConvex(convex_points, segment.endpoints()[0])) {
      if (overlap_project_s == nullptr) {
        return true;
      }
      overlap_project_s->push_back(0.0);
    }
    return SortAndUniqueOverlap(overlap_project_s);
  }
  int num_overlaps = 0;
  for (int i = 0; i < 2; ++i) {
    if (IsPointInConvex(convex_points, segment.endpoints()[i])) {
      if (overlap_project_s == nullptr) {
        return true;
      }
      num_overlaps++;
      overlap_project_s->push_back(i * segment.length());
    }
  }
  if (num_overlaps == 2) {
    return SortAndUniqueOverlap(overlap_project_s);
  }
  if (convex_points.size() <= 32 && !force_binary_search) {
    return GetOverlapWithBoundaryBruteForce(segment, convex_points, overlap_project_s);
  } else {
    return GetOverlapWithBoundaryConvexBinarySearch(segment, convex_points, overlap_project_s);
  }
}

bool GetOverlapBruteForce(const base::ConstSpan<Vector2d>& polygon_points,
                          const LineSegment2d& segment,
                          std::vector<double>* overlap_project_s) {
  CHECK_GE(polygon_points.size(), 1);
  if (polygon_points.size() == 1) {
    double project_s = 0.0;
    if (segment.IsPointIn(polygon_points[0], &project_s)) {
      if (overlap_project_s == nullptr) {
        return true;
      }
      overlap_project_s->push_back(math::Clamp(project_s, 0.0, segment.length()));
    }
    return SortAndUniqueOverlap(overlap_project_s);
  }
  if (segment.length() < math::kEpsilon) {
    if (IsPointInBruteForce(polygon_points, segment.endpoints()[0])) {
      if (overlap_project_s == nullptr) {
        return true;
      }
      overlap_project_s->push_back(0.0);
    }
    return SortAndUniqueOverlap(overlap_project_s);
  }
  for (int i = 0; i < 2; ++i) {
    if (IsPointInBruteForce(polygon_points, segment.endpoints()[i])) {
      if (overlap_project_s == nullptr) {
        return true;
      }
      overlap_project_s->push_back(i * segment.length());
    }
  }
  return GetOverlapWithBoundaryBruteForce(segment, polygon_points, overlap_project_s);
}

}  // namespace

std::vector<double> GetOverlapConvex(const base::ConstSpan<Vector2d>& convex_points,
                                     const LineSegment2d& segment,
                                     bool force_binary_search) {
  std::vector<double> overlap_project_s;
  GetOverlapConvex(convex_points, segment, &overlap_project_s, force_binary_search);
  return overlap_project_s;
}

std::vector<double> GetOverlapBruteForce(const base::ConstSpan<Vector2d>& polygon_points,
                                         const LineSegment2d& segment) {
  std::vector<double> overlap_project_s;
  GetOverlapBruteForce(polygon_points, segment, &overlap_project_s);
  return overlap_project_s;
}

bool HasOverlapConvex(const base::ConstSpan<Vector2d>& convex_points,
                      const LineSegment2d& segment,
                      bool force_binary_search) {
  return GetOverlapConvex(convex_points, segment, nullptr, force_binary_search);
}

bool HasOverlapBruteForce(const base::ConstSpan<Vector2d>& polygon_points,
                          const LineSegment2d& segment) {
  return GetOverlapBruteForce(polygon_points, segment, nullptr);
}

bool HasOverlapConvex(const base::ConstSpan<Vector2d>& convex_points0,
                      const base::ConstSpan<Vector2d>& convex_points1) {
  CHECK_GE(convex_points0.size(), 3);
  CHECK_GE(convex_points1.size(), 3);
  const auto maximize_cross = [](const base::ConstSpan<Vector2d>& convex_points,
                                 bool ccw_order_only,
                                 const Iterator& iter,
                                 int* max_index) {
    double cross = 0.0;
    std::tie(*max_index, cross) = convex_internal::MaximizeFunctionLocally(
        [&iter, &convex_points](int i) {
          return iter.segment().CrossProd(convex_points[i] - iter.start());
        },
        convex_points.size(), *max_index, ccw_order_only);
    return cross;
  };
  Iterator iter0(convex_points0);
  for (int i = 0, j = 0; i < iter0.num_points(); ++i, ++iter0) {
    const double cross = maximize_cross(convex_points1, i != 0, iter0, &j);
    if (cross < -math::kEpsilon) {
      return false;
    }
  }
  Iterator iter1(convex_points1);
  for (int i = 0, j = 0; i < iter1.num_points(); ++i, ++iter1) {
    const double cross = maximize_cross(convex_points0, i != 0, iter1, &j);
    if (cross < -math::kEpsilon) {
      return false;
    }
  }
  return true;
}

bool HasOverlapBruteForce(const base::ConstSpan<Vector2d>& polygon_points0,
                          const base::ConstSpan<Vector2d>& polygon_points1) {
  CHECK_GE(polygon_points0.size(), 1);
  CHECK_GE(polygon_points1.size(), 1);
  if (polygon_points0.size() == 1) {
    return IsPointInBruteForce(polygon_points1, polygon_points0[0]);
  }
  if (polygon_points1.size() == 1) {
    return IsPointInBruteForce(polygon_points0, polygon_points1[0]);
  }
  if (IsPointInBruteForce(polygon_points0, polygon_points1[0])) {
    return true;
  }
  if (IsPointInBruteForce(polygon_points1, polygon_points0[0])) {
    return true;
  }
  Iterator iter0(polygon_points0);
  Iterator iter1(polygon_points1);
  for (int i = 0; i < iter0.num_points(); ++i, ++iter0) {
    for (int j = 0; j < iter1.num_points(); ++j, ++iter1) {
      if (LineSegment2d::HasIntersect(iter0.start(), iter0.end(), iter1.start(), iter1.end())) {
        return true;
      }
    }
  }
  return false;
}

bool ContainsConvex(const base::ConstSpan<Vector2d>& convex_points0,
                    const base::ConstSpan<Vector2d>& convex_points1) {
  CHECK_GE(convex_points0.size(), 3);
  CHECK_GE(convex_points1.size(), 3);
  const auto minimize_cross = [](const base::ConstSpan<Vector2d>& convex_points,
                                 bool ccw_order_only,
                                 const Iterator& iter,
                                 int* min_index) {
    double cross = 0.0;
    std::tie(*min_index, cross) = convex_internal::MinimizeFunctionLocally(
        [&iter, &convex_points](int i) {
          return iter.segment().CrossProd(convex_points[i] - iter.start());
        },
        convex_points.size(), *min_index, ccw_order_only);
    return cross;
  };
  Iterator iter0(convex_points0);
  for (int i = 0, j = 0; i < iter0.num_points(); ++i, ++iter0) {
    const double cross = minimize_cross(convex_points1, i != 0, iter0, &j);
    if (cross < -math::kEpsilon) {
      return false;
    }
  }
  return true;
}

std::vector<Vector2d> ComputeOverlapConvex(const base::ConstSpan<Vector2d>& convex_points0,
                                           const base::ConstSpan<Vector2d>& convex_points1) {
  CHECK_GE(convex_points0.size(), 3);
  CHECK_GE(convex_points1.size(), 3);
  std::vector<Vector2d> result_points;
  const auto advance_iter = [&result_points](Iterator& iter, bool add_point = false) {
    if (add_point) {
      result_points.push_back(iter.end());
    }
    iter++;
  };

  bool outside_polygon0 = false;
  bool outside_polygon1 = false;

  enum class InFlag {
    kNone,
    kConvex0,
    kConvex1,
  };
  InFlag inflag = InFlag::kNone;

  Iterator iter0(convex_points0);
  Iterator iter1(convex_points1);
  do {
    const Vector2d& segment0 = iter0.segment();
    const Vector2d& segment1 = iter1.segment();

    const double cross = segment0.CrossProd(segment1);
    // > 0.0 means: iter1.end is in left half-plane of segment iter0.start~iter0.end.
    const double cross_to_segment0 = segment0.CrossProd(iter1.end() - iter0.start());
    if (cross_to_segment0 < -math::kEpsilon) {
      outside_polygon0 = true;
    }
    // > 0.0 means: iter0.end is in left half-plane of segment iter1.start~iter1.end.
    const double cross_to_segment1 = segment1.CrossProd(iter0.end() - iter1.start());
    if (cross_to_segment1 < -math::kEpsilon) {
      outside_polygon1 = true;
    }

    const bool is_parallel = (std::abs(cross) <= math::kEpsilon);
    const bool is_collinear = (is_parallel && (std::abs(cross_to_segment0) <= math::kEpsilon &&
                                               std::abs(cross_to_segment1) <= math::kEpsilon));
    if (!is_collinear) {
      Vector2d intersection;
      if (LineSegment2d::GetIntersect(iter0.start(), iter0.end(), iter1.start(), iter1.end(),
                                      &intersection)) {
        if (result_points.empty()) {
          iter0.ResetSteps();
          iter1.ResetSteps();
        }
        result_points.push_back(intersection);
      }
    }

    // Special Case 0: segments are parallel and separated.
    if (is_parallel && cross_to_segment0 < -math::kEpsilon && cross_to_segment1 < -math::kEpsilon) {
      return ComputeConvexHull(std::move(result_points), true);
    }
    // Special Case 1: segments are collinear and oppositely oriented.
    if (is_collinear && segment0.InnerProd(segment1) < -math::kEpsilon) {
      return ComputeConvexHull(std::move(result_points), true);
    }
    // Special Case 2: segments are collinear, then advance but don't add point.
    if (is_collinear) {
      advance_iter(inflag == InFlag::kConvex0 ? iter1 : iter0, false);
      continue;
    }

    // See https://blog.csdn.net/sinat_41104353/article/details/84594835
    //
    // P/Q - segment0/1
    // s/e - start/end point
    // </> - on left/right half-plane of segment
    //
    // Case (0~3), cross>=0.0
    //
    //   Case0:                Case1:                Case2:                Case3:
    //       P(e)<Q & Q(e)>P       P(e)<Q & Q(e)<P       P(e)>Q & Q(e)>P       P(e)>Q & Q(e)<P
    //                e              e------s                                    e
    //              e  \           (Q)    e                     e                 \   e
    //              |   \ (Q)             |                     |    (Q)       (Q) \  |
    //              |    \                |                     | e----s            s |
    //          (P) |     s           (P) |                 (P) |                     | (P)
    //              s                     s                     s                     s
    //      inside-P/advance-Q    inside-P/advance-P    inside-P/advance-Q    inside-Q/advance-P
    //
    //
    // Case (4~7), cross<0.0
    //
    //   Case4:                Case5:                Case6:                Case7:
    //       P(e)>Q & Q(e)<P       P(e)<Q & Q(e)<P       P(e)>Q & Q(e)>P       P(e)<Q & Q(e)>P
    //                e              e------s                                    e
    //              e  \           (P)    e                     e                 \   e
    //              |   \ (P)             |                     |    (P)       (P) \  |
    //              |    \                |                     | e----s            s |
    //          (Q) |     s           (Q) |                 (Q) |                     | (Q)
    //              s                     s                     s                     s
    //      inside-Q/advance-P    inside-Q/advance-Q    inside-Q/advance-P    inside-P/advance-Q
    //

    if (!result_points.empty()) {
      if (cross >= 0.0) {
        inflag = (cross_to_segment0 > 0.0 && cross_to_segment1 < 0.0) ? InFlag::kConvex1
                                                                      : InFlag::kConvex0;
      } else {
        inflag = (cross_to_segment0 < 0.0 && cross_to_segment1 > 0.0) ? InFlag::kConvex0
                                                                      : InFlag::kConvex1;
      }
    }

    // General cases.
    if (cross >= 0.0) {
      if (cross_to_segment0 > 0.0) {
        advance_iter(iter0, inflag == InFlag::kConvex0);
      } else {
        advance_iter(iter1, inflag == InFlag::kConvex1);
      }
    } else {
      if (cross_to_segment1 > 0.0) {
        advance_iter(iter1, inflag == InFlag::kConvex1);
      } else {
        advance_iter(iter0, inflag == InFlag::kConvex0);
      }
    }
  } while ((iter0.steps() < iter0.num_points() || iter1.steps() < iter1.num_points()) &&
           (iter0.steps() < iter0.num_points() * 2) && (iter1.steps() < iter1.num_points() * 2));

  if (!result_points.empty()) {
    return ComputeConvexHull(std::move(result_points), true);
  }

  // Check if convex_points0 contains convex_points1.
  if (!outside_polygon0 && ContainsConvex(convex_points0, convex_points1)) {
    return std::vector<Vector2d>(convex_points1.begin(), convex_points1.end());
  }
  // Check if convex_points1 contains convex_points0.
  if (!outside_polygon1 && ContainsConvex(convex_points1, convex_points0)) {
    return std::vector<Vector2d>(convex_points0.begin(), convex_points0.end());
  }
  return {};
}

}  // namespace convex
}  // namespace math_internal
}  // namespace math
