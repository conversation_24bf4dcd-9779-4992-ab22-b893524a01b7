// Copyright @2024 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/math/internal/touched_grids_computer.h"

#include <limits>
#include <unordered_set>
#include <vector>

#include "base/math/packed_index.h"

namespace math {
namespace math_internal {
namespace {

// https://km.sankuai.com/collabpage/2485347463
bool ShouldUseHash(double area_ratio_polygon_aabox, int num_grids) {
  constexpr int kMaxGridNum = 256 * 1024;
  constexpr int kMinGridNum = 8192;
  constexpr double kSparsePolygonThreshold = 0.1;
  if (num_grids >= kMaxGridNum) {
    return true;
  }
  return num_grids >= kMinGridNum && area_ratio_polygon_aabox <= kSparsePolygonThreshold;
}

}  // namespace

void TouchedGridsComputer::AddGrids(int segment_min_xi, int segment_max_xi, int segment_yi) {
  DCHECK_GE(segment_max_xi, segment_min_xi);
  DCHECK_GE(segment_min_xi, base_point_index_.x);
  DCHECK_GE(base_point_index_.x + num_x_grids_ - 1, segment_max_xi);
  DCHECK_GE(segment_yi, base_point_index_.y);
  DCHECK_GE(base_point_index_.y + num_y_grids_ - 1, segment_yi);
  segments_grids_.emplace_back(SegmentGrids{
      .segment_min_xi = segment_min_xi,
      .segment_max_xi = segment_max_xi,
      .segment_yi = segment_yi,
  });
}

std::vector<math::Vector2i> TouchedGridsComputer::Build() const {
  const int num_grids = num_x_grids_ * num_y_grids_;
  return ShouldUseHash(area_ratio_polygon_aabox_, num_grids) ? TouchedGridsWithHash()
                                                             : TouchedGridsWithFlat();
}

std::vector<math::Vector2i> TouchedGridsComputer::TouchedGridsWithHash() const {
  const int num_grids = num_x_grids_ * num_y_grids_;
  std::unordered_set<int64_t> touched_grids;
  touched_grids.reserve(num_grids);
  for (const SegmentGrids& segment_grid : segments_grids_) {
    for (int xi = segment_grid.segment_min_xi; xi <= segment_grid.segment_max_xi; ++xi) {
      const int yi = segment_grid.segment_yi;
      touched_grids.insert(math::PackedIndex(xi, yi));
    }
  }
  std::vector<math::Vector2i> result_grids;
  result_grids.reserve(result_grids.size());
  for (const int64_t grid : touched_grids) {
    result_grids.push_back(math::UnpackIndex(grid));
  }
  return result_grids;
}

std::vector<math::Vector2i> TouchedGridsComputer::TouchedGridsWithFlat() const {
  const int num_grids = num_x_grids_ * num_y_grids_;
  std::vector<math::Vector2i> touched_grids;
  touched_grids.reserve(num_grids);
  std::vector<uint8_t> is_occupied(num_grids, 0);
  for (const SegmentGrids& segment_grid : segments_grids_) {
    for (int xi = segment_grid.segment_min_xi; xi <= segment_grid.segment_max_xi; ++xi) {
      const int yi = segment_grid.segment_yi;
      const int offset = GridIndex(xi - base_point_index_.x, yi - base_point_index_.y);
      if (is_occupied[offset] == 0) {
        touched_grids.emplace_back(xi, yi);
        is_occupied[offset] = 1;
      }
    }
  }
  return touched_grids;
}

}  // namespace math_internal
}  // namespace math
