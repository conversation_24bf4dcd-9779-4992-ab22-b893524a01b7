// Copyright @2024 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

package main

import (
	"bytes"
	"fmt"
	"math"
)

type FastMathTable struct {
	NumSamples int
	Name       string
	Func       func(x float64) float64
}

func (p *FastMathTable) GenTable() []float64 {
	var table0 = p.GenTable0()
	var table1 = p.GenTable1()
	var table2 = p.GenTable2()
	var n = p.NumSamples
	table := make([]float64, (n+1)*3)
	for i := 0; i <= n; i++ {
		table[i*3+0] = table0[i]
		table[i*3+1] = table1[i]
		table[i*3+2] = table2[i]
	}
	return table
}

func (p *FastMathTable) HeadString() string {
	var b bytes.Buffer
	fmt.Fprintf(&b, "constexpr int k%sTableSamples = %d;", p.Name, p.NumSamples)
	return b.String()
}

func (p *FastMathTable) BodyString() string {
	var b bytes.Buffer
	fmt.Fprintf(&b, "constexpr double k%sTable[] = {", p.Name)
	var table = p.GenTable()
	for i := 0; i < len(table); i++ {
		switch i % 3 {
		case 0:
			fmt.Fprintf(&b, "\n    ")
		default:
			fmt.Fprintf(&b, " ")
		}
		fmt.Fprintf(&b, "%+.16e,", table[i])
	}
	fmt.Fprintf(&b, "\n};")
	return b.String()
}

func (p *FastMathTable) GenTable0() []float64 {
	var n = p.NumSamples
	var f = p.Func
	table := make([]float64, n+1)
	for i := 0; i <= n; i++ {
		x := float64(i) / float64(n)
		table[i] = f(x)
	}
	return table
}

func (p *FastMathTable) GenTable1() []float64 {
	var n = p.NumSamples
	var f = p.Func
	var d = 1.0 / float64(n)
	table := make([]float64, n+1)
	for i := 0; i <= n; i++ {
		x := float64(i) / float64(n)
		v := -3.0*f(x) + 4.0*f(x+d*0.5) - f(x+d)
		table[i] = v
	}
	return table
}

func (p *FastMathTable) GenTable2() []float64 {
	var n = p.NumSamples
	var f = p.Func
	var d = 1.0 / float64(n)
	table := make([]float64, n+1)
	for i := 0; i <= n; i++ {
		x := float64(i) / float64(n)
		v := 2.0*f(x) - 4.0*f(x+d*0.5) + 2.0*f(x+d)
		table[i] = v
	}
	return table
}

func Min(x, y float64) float64 {
	if x < y {
		return x
	} else {
		return y
	}
}

func Max(x, y float64) float64 {
	if x < y {
		return y
	} else {
		return x
	}
}

func Clamp(value, min_value, max_value float64) float64 {
	return Max(min_value, Min(max_value, value))
}

func main() {
	var tables = []*FastMathTable{
		{
			NumSamples: 512,
			Name:       "Atan",
			Func:       func(x float64) float64 { return math.Atan(2.0 * (x - 0.5)) },
		},
		{
			NumSamples: 512,
			Name:       "Asin",
			Func:       func(x float64) float64 { return math.Asin(Clamp(2.0*(x-0.5), -1.0, +1.0)) },
		},
		{
			NumSamples: 1024,
			Name:       "Tan",
			Func:       func(x float64) float64 { return math.Tan(math.Pi * 0.5 * (x - 0.5)) },
		},
		{
			NumSamples: 1024,
			Name:       "Sin",
			Func:       func(x float64) float64 { return math.Sin(math.Pi * 2.0 * x) },
		},
		{
			NumSamples: 1024,
			Name:       "Cos",
			Func:       func(x float64) float64 { return math.Cos(math.Pi * 2.0 * x) },
		},
	}
	for i := 0; i < len(tables); i++ {
		fmt.Println(tables[i].HeadString())
	}

	for i := 0; i < len(tables); i++ {
		fmt.Println()
		fmt.Println(tables[i].BodyString())
	}
}
