// Copyright @2022 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "benchmark/benchmark.h"

#include "base/math/internal/convex_hull.h"
#include "base/testing/geometry.h"

namespace math {
namespace math_internal {
namespace convex {

namespace {

void CustomArguments(benchmark::internal::Benchmark* b) {
  CHECK(b != nullptr);
  for (int num_points : {1, 2, 3, 5, 10, 20, 50, 100, 200, 500, 1000}) {
    b->Args({num_points});
  }
}

}  // namespace

void BM_IsConvexHull(benchmark::State& state) { /* NOLINT(runtime/references) */
  std::vector<Vector2d> points(state.range(0));
  for (int i = 0; i < static_cast<int>(points.size()); ++i) {
    const double theta = M_PI * 2.0 * (static_cast<double>(i) / points.size());
    points[i] = Vector2d::FromAngleAndLength(theta, 100.0);
  }
  while (state.KeepRunning()) {
    benchmark::DoNotOptimize(IsConvexHull(points));
  }
}
BENCHMARK(BM_IsConvexHull)->Apply(CustomArguments);

void BM_ComputeConvexHull(benchmark::State& state) { /* NOLINT(runtime/references) */
  const std::vector<Vector2d> points = testing::RandomPoints(state.range(0), 100.0);
  while (state.KeepRunning()) {
    benchmark::DoNotOptimize(ComputeConvexHull(points));
  }
}
BENCHMARK(BM_ComputeConvexHull)->Apply(CustomArguments);

}  // namespace convex
}  // namespace math_internal
}  // namespace math

BENCHMARK_MAIN();

// clang-format off
/*
Run on (16 X 5000 MHz CPU s)
CPU Caches:
  L1 Data 32K (x8)
  L1 Instruction 32K (x8)
  L2 Unified 256K (x8)
  L3 Unified 16384K (x1)
Load Average: 0.85, 0.56, 0.44
***WARNING*** CPU scaling is enabled, the benchmark real time measurements may be noisy and will incur extra overhead.
--------------------------------------------------------------------
Benchmark                          Time             CPU   Iterations
--------------------------------------------------------------------
BM_IsConvexHull/1               1.08 ns         1.08 ns    642838693
BM_IsConvexHull/2               1.08 ns         1.08 ns    650081611
BM_IsConvexHull/3               5.71 ns         5.71 ns    122226948
BM_IsConvexHull/5               10.9 ns         10.9 ns     64441665
BM_IsConvexHull/10              23.5 ns         23.5 ns     29978925
BM_IsConvexHull/20              48.9 ns         48.9 ns     14204577
BM_IsConvexHull/50               126 ns          126 ns      5569324
BM_IsConvexHull/100              263 ns          263 ns      2665352
BM_IsConvexHull/200              520 ns          520 ns      1352794
BM_IsConvexHull/500             1290 ns         1290 ns       544116
BM_IsConvexHull/1000            2654 ns         2654 ns       271546
BM_ComputeConvexHull/1          11.7 ns         11.7 ns     60308388
BM_ComputeConvexHull/2          12.3 ns         12.3 ns     57143883
BM_ComputeConvexHull/3          42.5 ns         42.5 ns     17105560
BM_ComputeConvexHull/5          64.1 ns         64.1 ns     11699617
BM_ComputeConvexHull/10          118 ns          118 ns      5977652
BM_ComputeConvexHull/20          265 ns          265 ns      2855813
BM_ComputeConvexHull/50          668 ns          668 ns      1030311
BM_ComputeConvexHull/100        1547 ns         1547 ns       448445
BM_ComputeConvexHull/200        3400 ns         3400 ns       197775
BM_ComputeConvexHull/500       14622 ns        14622 ns        47496
BM_ComputeConvexHull/1000      44489 ns        44489 ns        16117
*/
