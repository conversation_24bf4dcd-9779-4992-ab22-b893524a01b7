// Copyright @2022 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "benchmark/benchmark.h"

#include "base/math/internal/convex_minkowskisum.h"
#include "base/testing/geometry.h"

namespace math {
namespace math_internal {
namespace convex {

namespace {

void CustomArguments(benchmark::internal::Benchmark* b) {
  CHECK(b != nullptr);
  for (int n = 3; n <= 10; ++n) {
    b->Args({n});
  }
  for (int n = 16; n <= 1024; n *= 2) {
    b->Args({n});
  }
}

}  // namespace

void BM_MinkowskiSumConvex(benchmark::State& state) { /* NOLINT(runtime/references) */
  const int n = 100;
  const int num_points = state.range(0);
  const double radius = std::max(10.0, num_points / 10.0);
  std::vector<std::vector<Vector2d>> convex_points(n);
  for (int i = 0; i < n; ++i) {
    convex_points[i] =
        testing::MakeRegularPolygon(num_points, radius, testing::RandomPoint(radius * 3.0));
  }
  while (state.KeepRunning()) {
    const int i = testing::RandomInt(0, n - 1);
    const int j = testing::RandomInt(0, n - 1);
    benchmark::DoNotOptimize(MinkowskiSumConvex(convex_points[i], convex_points[j]));
  }
  state.SetComplexityN(num_points);
}
BENCHMARK(BM_MinkowskiSumConvex)->Apply(CustomArguments)->Complexity();

}  // namespace convex
}  // namespace math_internal
}  // namespace math

BENCHMARK_MAIN();

// clang-format off
/*
Run on (16 X 5000 MHz CPU s)
CPU Caches:
  L1 Data 32K (x8)
  L1 Instruction 32K (x8)
  L2 Unified 256K (x8)
  L3 Unified 16384K (x1)
Load Average: 0.84, 0.97, 0.94
***WARNING*** CPU scaling is enabled, the benchmark real time measurements may be noisy and will incur extra overhead.
---------------------------------------------------------------------
Benchmark                           Time             CPU   Iterations
---------------------------------------------------------------------
BM_MinkowskiSumConvex/3           137 ns          137 ns      5068720
BM_MinkowskiSumConvex/4           140 ns          140 ns      4975454
BM_MinkowskiSumConvex/5           183 ns          183 ns      3946985
BM_MinkowskiSumConvex/6           255 ns          255 ns      2936423
BM_MinkowskiSumConvex/7           350 ns          350 ns      2035096
BM_MinkowskiSumConvex/8           395 ns          395 ns      1862285
BM_MinkowskiSumConvex/9           489 ns          489 ns      1421471
BM_MinkowskiSumConvex/10          451 ns          451 ns      1457850
BM_MinkowskiSumConvex/16          893 ns          893 ns       795948
BM_MinkowskiSumConvex/32         1977 ns         1977 ns       374229
BM_MinkowskiSumConvex/64         4229 ns         4229 ns       166818
BM_MinkowskiSumConvex/128        9467 ns         9466 ns        74603
BM_MinkowskiSumConvex/256       20119 ns        20119 ns        35013
BM_MinkowskiSumConvex/512       44587 ns        44587 ns        16148
BM_MinkowskiSumConvex/1024      95456 ns        95456 ns         7343
BM_MinkowskiSumConvex_BigO       9.41 NlgN       9.41 NlgN
BM_MinkowskiSumConvex_RMS           5 %             5 %
*/
