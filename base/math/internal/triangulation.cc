// Copyright @2023 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/math/internal/triangulation.h"

#include <algorithm>
#include <utility>

#include "glog/logging.h"

namespace math {
namespace math_internal {
namespace triangulation {
namespace {

struct VertexState {
  int index = 0;
  Vector2d point;

  VertexState* prev = nullptr;
  VertexState* next = nullptr;

  // -1 for convex point.
  int concave_index = -1;

  // Used to speed up the calculation.
  std::array<Vector2d, 3> triangle_in_ccw_order;
  double abs_cross_prod = 0.0;
};

inline bool IsPointIn(const std::array<Vector2d, 3>& triangle_in_ccw_order, const Vector2d& point) {
  const Vector2d d0 = triangle_in_ccw_order[0] - point;
  const Vector2d d1 = triangle_in_ccw_order[1] - point;
  const Vector2d d2 = triangle_in_ccw_order[2] - point;
  return std::min({d0.<PERSON><PERSON><PERSON>(d1), d1.<PERSON><PERSON><PERSON>(d2), d2.<PERSON><PERSON>rod(d0)}) >= -math::kEpsilon;
}

inline void ClearConcaveIndex(VertexState* v, std::vector<VertexState*>* concave_states) {
  DCHECK(v != nullptr);
  DCHECK(concave_states != nullptr);
  if (v->concave_index == -1) {
    return;
  }
  const int concave_index = v->concave_index;
  DCHECK_LT(concave_index, concave_states->size());
  DCHECK_EQ((*concave_states)[concave_index], v);
  concave_states->back()->concave_index = concave_index;
  std::swap((*concave_states)[concave_index], concave_states->back());
  concave_states->pop_back();
  v->concave_index = -1;
}

void UpdateVertexState(VertexState* v, std::vector<VertexState*>* concave_states) {
  DCHECK(v != nullptr);
  DCHECK(v->prev != nullptr);
  DCHECK(v->next != nullptr);
  DCHECK(concave_states != nullptr);
  const Vector2d& p0 = v->prev->point;
  const Vector2d& p1 = v->point;
  const Vector2d& p2 = v->next->point;
  const double cross_prod = CrossProd(p0, p1, p2);

  ClearConcaveIndex(v, concave_states);

  if (cross_prod < -math::kEpsilon) {
    concave_states->push_back(v);
    v->concave_index = static_cast<int>(concave_states->size()) - 1;
  }
  v->triangle_in_ccw_order = {p0, p1, p2};
  if (cross_prod < 0.0) {
    std::swap(v->triangle_in_ccw_order[1], v->triangle_in_ccw_order[2]);
  }
  v->abs_cross_prod = std::abs(cross_prod);
}

bool IsEar(const VertexState& v, const std::vector<VertexState*>& concave_states) {
  DCHECK(v.next != nullptr);
  DCHECK(v.prev != nullptr);
  if (v.concave_index != -1) {
    return false;
  }
  for (const VertexState* other : concave_states) {
    if (other == v.next || other == v.prev) {
      continue;
    }
    if (IsPointIn(v.triangle_in_ccw_order, other->point)) {
      return false;
    }
  }
  return true;
}

template <typename IndexToResultFunction, typename ContainerOfResult>
inline void AddTriangle(const base::ConstSpan<Vector2d>& points,
                        const IndexToResultFunction& index_to_result,
                        int i0,
                        int i1,
                        int i2,
                        ContainerOfResult* triangles) {
  DCHECK(triangles != nullptr);
  triangles->push_back(index_to_result(i0));
  triangles->push_back(index_to_result(i1));
  triangles->push_back(index_to_result(i2));
}

template <typename IndexToResultFunction, typename ContainerOfResult>
inline void AddTriangle(const base::ConstSpan<Vector2d>& points,
                        const IndexToResultFunction& index_to_result,
                        const VertexState& v,
                        ContainerOfResult* triangles) {
  DCHECK(v.next != nullptr);
  DCHECK(v.prev != nullptr);
  AddTriangle(points, index_to_result, v.prev->index, v.index, v.next->index, triangles);
}

}  // namespace

template <typename IndexToResultFunction, typename ContainerOfResult>
void TriangulatePolygon(const base::ConstSpan<Vector2d>& points_in_ccw_order,
                        const IndexToResultFunction& index_to_result,
                        bool is_convex,
                        ContainerOfResult* triangles) {
  CHECK_NE(points_in_ccw_order.size(), 0);
  CHECK_NOTNULL(triangles)->clear();

  const int num_points = points_in_ccw_order.size();
  if (num_points <= 3) {
    const int i0 = 0;
    const int i1 = std::max(0, num_points - 2);
    const int i2 = std::max(0, num_points - 1);
    AddTriangle(points_in_ccw_order, index_to_result, i0, i1, i2, triangles);
    return;
  }
  triangles->reserve((num_points - 2) * 3);

  if (is_convex) {
    for (int i = 2; i < num_points; ++i) {
      AddTriangle(points_in_ccw_order, index_to_result, 0, i - 1, i, triangles);
    }
    return;
  }

  std::vector<VertexState> states(num_points);
  for (int i = 0, j = num_points - 1; i < num_points; ++i) {
    states[i].index = i;
    states[i].point = points_in_ccw_order[i];
    states[i].prev = &states[j];
    states[j].next = &states[i];
    j = i;
  }

  std::vector<VertexState*> concave_states;
  concave_states.reserve(num_points);

  for (int i = 0; i < num_points; ++i) {
    UpdateVertexState(&states[i], &concave_states);
  }

  VertexState* current = &states[0];
  for (int remaining_points = num_points; remaining_points != 3; --remaining_points) {
    const VertexState* last = current;
    VertexState* smallest = nullptr;
    for (int i = 0; i < remaining_points && !IsEar(*current, concave_states); ++i) {
      if (smallest == nullptr || current->abs_cross_prod < smallest->abs_cross_prod) {
        smallest = current;
      }
      current = current->next;
    }
    if (last == current && smallest != nullptr) {
      current = smallest;
    }
    AddTriangle(points_in_ccw_order, index_to_result, *current, triangles);
    current->prev->next = current->next;
    current->next->prev = current->prev;
    ClearConcaveIndex(current, &concave_states);
    UpdateVertexState(current->prev, &concave_states);
    UpdateVertexState(current->next, &concave_states);
    current = current->next;
  }
  AddTriangle(points_in_ccw_order, index_to_result, *current, triangles);
}

std::vector<Vector2d> TriangulatePolygon(const base::ConstSpan<Vector2d>& points_in_ccw_order,
                                         bool is_convex) {
  const auto index_to_result = [&points_in_ccw_order](int i) { return points_in_ccw_order[i]; };
  std::vector<Vector2d> triangles;
  TriangulatePolygon(points_in_ccw_order, index_to_result, is_convex, &triangles);
  return triangles;
}

std::vector<int> TriangulatePolygonAsIndex(const base::ConstSpan<Vector2d>& points_in_ccw_order,
                                           bool is_convex) {
  const auto index_to_result = [](int i) { return i; };
  std::vector<int> triangles;
  TriangulatePolygon(points_in_ccw_order, index_to_result, is_convex, &triangles);
  return triangles;
}

}  // namespace triangulation
}  // namespace math_internal
}  // namespace math
