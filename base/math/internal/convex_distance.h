// Copyright @2022 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <vector>

#include "base/container/span.h"
#include "base/math/vector2.h"

namespace math {
namespace math_internal {
namespace convex {
namespace convex_internal {

// Get the i where angle(p[i]) <= angle(point) < angle(p[i+1]). If segment p[0]~point is not
// inside or on the boundary of sector p[n-1]~p[0]~p[1], 0 or n-1 is returned.
int GetLowerIndexBinarySearch(const base::ConstSpan<Vector2d>& convex_points,
                              const Vector2d& point);

int DistanceToConvexForward(const base::ConstSpan<Vector2d>& convex_points,
                            const Vector2d& point,
                            int forward_start);

int DistanceToConvexBackward(const base::ConstSpan<Vector2d>& convex_points,
                             const Vector2d& point,
                             int backward_start);

}  // namespace convex_internal

// Check if point is inside or on the boundary of convex polygon.
bool IsPointInConvex(const base::ConstSpan<Vector2d>& convex_points, const Vector2d& point);
bool IsPointOnBoundaryConvex(const base::ConstSpan<Vector2d>& convex_points, const Vector2d& point);

double DistanceToConvex(const base::ConstSpan<Vector2d>& convex_points,
                        const Vector2d& point,
                        Vector2d* nearest_point = nullptr);
double DistanceSquareToConvex(const base::ConstSpan<Vector2d>& convex_points,
                              const Vector2d& point,
                              Vector2d* nearest_point = nullptr);

// Check if point is inside or on the boundary of normal polygon.
bool IsPointInBruteForce(const base::ConstSpan<Vector2d>& polygon_points, const Vector2d& point);
bool IsPointOnBoundaryBruteForce(const base::ConstSpan<Vector2d>& polygon_points,
                                 const Vector2d& point);

double DistanceToBruteForce(const base::ConstSpan<Vector2d>& polygon_points,
                            const Vector2d& point,
                            Vector2d* nearest_point = nullptr);
double DistanceSquareToBruteForce(const base::ConstSpan<Vector2d>& polygon_points,
                                  const Vector2d& point,
                                  Vector2d* nearest_point = nullptr);

}  // namespace convex
}  // namespace math_internal
}  // namespace math
