// Copyright @2022 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <vector>

#include "base/container/span.h"
#include "base/math/vector2.h"

namespace math {
namespace math_internal {
namespace convex {

// https://en.wikipedia.org/wiki/Minkowski_addition
// https://cp-algorithms.com/geometry/minkowski.html
// Compute minkowski-sum of two convex polygons (each contains at least 2 points, or is convex).
std::vector<Vector2d> MinkowskiSumConvex(const base::ConstSpan<Vector2d>& convex_points0,
                                         const base::ConstSpan<Vector2d>& convex_points1);

// TODO(wangnaizheng): deprecate this API.
std::vector<Vector2d> ExpandByDistanceConvex(const base::ConstSpan<Vector2d>& convex_points,
                                             double distance,
                                             double angle_step_hint);

}  // namespace convex
}  // namespace math_internal
}  // namespace math
