// Copyright @2024 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/math/internal/touched_grids_computer.h"

#include <utility>

#include "gmock/gmock.h"
#include "gtest/gtest.h"

namespace math {
namespace math_internal {

class TouchedGridsComputerTest : public ::testing::Test {
 protected:
  std::vector<math::Vector2i> GetTouchedGridsWithHash(
      const TouchedGridsComputer& touched_grids_computer) {
    return touched_grids_computer.TouchedGridsWithHash();
  }

  std::vector<math::Vector2i> GetTouchedGridsWithFlat(
      const TouchedGridsComputer& touched_grids_computer) {
    return touched_grids_computer.TouchedGridsWithFlat();
  }
  
  math::Vector2i base_point_index_;
  double area_ratio_polygon_aabox_ = 0.0;
  int num_x_grids_ = 0;
  int num_y_grids_ = 0;
};

TEST_F(TouchedGridsComputerTest, TestBuild) {
  const auto touched_grids = [](const math::Vector2i& base_point_index,
                                double area_ratio_polygon_aabox,
                                int num_x_grids,
                                int num_y_grids) {
    TouchedGridsComputer touched_grids_computer(
        base_point_index, area_ratio_polygon_aabox, num_x_grids, num_y_grids);
    const int min_xi = base_point_index.x;
    const int min_yi = base_point_index.y;
    const int max_xi = min_xi + num_x_grids - 1;
    const int max_yi = min_yi + num_y_grids - 1;
    for (int yi = min_yi; yi <= max_yi; ++yi) {
      touched_grids_computer.AddGrids(min_xi, max_xi, yi);
    }
    return touched_grids_computer.Build();
  };
  {
    base_point_index_ = {1, 2};
    area_ratio_polygon_aabox_ = 0.1;
    num_x_grids_ = 3;
    num_y_grids_ = 4;
    ASSERT_THAT(
        touched_grids(base_point_index_, area_ratio_polygon_aabox_, num_x_grids_, num_y_grids_),
        testing::UnorderedElementsAreArray(std::vector<math::Vector2i>{{1, 2},
                                                                       {1, 3},
                                                                       {1, 4},
                                                                       {1, 5},
                                                                       {2, 2},
                                                                       {2, 3},
                                                                       {2, 4},
                                                                       {2, 5},
                                                                       {3, 2},
                                                                       {3, 3},
                                                                       {3, 4},
                                                                       {3, 5}}));
  };
  {
    base_point_index_ = {-1, -2};
    area_ratio_polygon_aabox_ = 0.4;
    num_x_grids_ = 2;
    num_y_grids_ = 2;
    ASSERT_THAT(
        touched_grids(base_point_index_, area_ratio_polygon_aabox_, num_x_grids_, num_y_grids_),
        testing::UnorderedElementsAreArray(
            std::vector<math::Vector2i>{{-1, -2}, {-1, -1}, {0, -2}, {0, -1}}));
  };
  {
    base_point_index_ = {-1, -2};
    area_ratio_polygon_aabox_ = 0.1;
    num_x_grids_ = 1;
    num_y_grids_ = 2;
    ASSERT_THAT(
        touched_grids(base_point_index_, area_ratio_polygon_aabox_, num_x_grids_, num_y_grids_),
        testing::UnorderedElementsAreArray(std::vector<math::Vector2i>{{-1, -2}, {-1, -1}}));
  };
  {
    base_point_index_ = {-1, -2};
    area_ratio_polygon_aabox_ = 0.1;
    num_x_grids_ = 2;
    num_y_grids_ = 1;
    ASSERT_THAT(
        touched_grids(base_point_index_, area_ratio_polygon_aabox_, num_x_grids_, num_y_grids_),
        testing::UnorderedElementsAreArray(std::vector<math::Vector2i>{{-1, -2}, {0, -2}}));
  }
}

TEST_F(TouchedGridsComputerTest, TestTouchedGridsWithHash) {
  const auto touched_grids = [this](const math::Vector2i& base_point_index,
                                    double area_ratio_polygon_aabox,
                                    int num_x_grids,
                                    int num_y_grids) {
    TouchedGridsComputer touched_grids_computer(
        base_point_index, area_ratio_polygon_aabox, num_x_grids, num_y_grids);
    const int min_xi = base_point_index.x;
    const int min_yi = base_point_index.y;
    const int max_xi = min_xi + num_x_grids - 1;
    const int max_yi = min_yi + num_y_grids - 1;
    for (int yi = min_yi; yi <= max_yi; ++yi) {
      touched_grids_computer.AddGrids(min_xi, max_xi, yi);
    }
    return GetTouchedGridsWithHash(touched_grids_computer);
  };
  {
    base_point_index_ = {1, 2};
    area_ratio_polygon_aabox_ = 0.1;
    num_x_grids_ = 3;
    num_y_grids_ = 4;
    ASSERT_THAT(
        touched_grids(base_point_index_, area_ratio_polygon_aabox_, num_x_grids_, num_y_grids_),
        testing::UnorderedElementsAreArray(std::vector<math::Vector2i>{{1, 2},
                                                                       {1, 3},
                                                                       {1, 4},
                                                                       {1, 5},
                                                                       {2, 2},
                                                                       {2, 3},
                                                                       {2, 4},
                                                                       {2, 5},
                                                                       {3, 2},
                                                                       {3, 3},
                                                                       {3, 4},
                                                                       {3, 5}}));
  };
  {
    base_point_index_ = {-1, -2};
    area_ratio_polygon_aabox_ = 0.4;
    num_x_grids_ = 2;
    num_y_grids_ = 2;
    ASSERT_THAT(
        touched_grids(base_point_index_, area_ratio_polygon_aabox_, num_x_grids_, num_y_grids_),
        testing::UnorderedElementsAreArray(
            std::vector<math::Vector2i>{{-1, -2}, {-1, -1}, {0, -2}, {0, -1}}));
  };
  {
    base_point_index_ = {-1, -2};
    area_ratio_polygon_aabox_ = 0.1;
    num_x_grids_ = 1;
    num_y_grids_ = 2;
    ASSERT_THAT(
        touched_grids(base_point_index_, area_ratio_polygon_aabox_, num_x_grids_, num_y_grids_),
        testing::UnorderedElementsAreArray(std::vector<math::Vector2i>{{-1, -2}, {-1, -1}}));
  };
  {
    base_point_index_ = {-1, -2};
    area_ratio_polygon_aabox_ = 0.1;
    num_x_grids_ = 2;
    num_y_grids_ = 1;
    ASSERT_THAT(
        touched_grids(base_point_index_, area_ratio_polygon_aabox_, num_x_grids_, num_y_grids_),
        testing::UnorderedElementsAreArray(std::vector<math::Vector2i>{{-1, -2}, {0, -2}}));
  }
}

TEST_F(TouchedGridsComputerTest, TestTouchedGridsWithFlat) {
  const auto touched_grids = [this](const math::Vector2i& base_point_index,
                                    double area_ratio_polygon_aabox,
                                    int num_x_grids,
                                    int num_y_grids) {
    TouchedGridsComputer touched_grids_computer(
        base_point_index, area_ratio_polygon_aabox, num_x_grids, num_y_grids);
    const int min_xi = base_point_index.x;
    const int min_yi = base_point_index.y;
    const int max_xi = min_xi + num_x_grids - 1;
    const int max_yi = min_yi + num_y_grids - 1;
    for (int yi = min_yi; yi <= max_yi; ++yi) {
      touched_grids_computer.AddGrids(min_xi, max_xi, yi);
    }
    return GetTouchedGridsWithFlat(touched_grids_computer);
  };
  {
    base_point_index_ = {1, 2};
    area_ratio_polygon_aabox_ = 0.1;
    num_x_grids_ = 3;
    num_y_grids_ = 4;
    ASSERT_THAT(
        touched_grids(base_point_index_, area_ratio_polygon_aabox_, num_x_grids_, num_y_grids_),
        testing::UnorderedElementsAreArray(std::vector<math::Vector2i>{{1, 2},
                                                                       {1, 3},
                                                                       {1, 4},
                                                                       {1, 5},
                                                                       {2, 2},
                                                                       {2, 3},
                                                                       {2, 4},
                                                                       {2, 5},
                                                                       {3, 2},
                                                                       {3, 3},
                                                                       {3, 4},
                                                                       {3, 5}}));
  };
  {
    base_point_index_ = {-1, -2};
    area_ratio_polygon_aabox_ = 0.4;
    num_x_grids_ = 2;
    num_y_grids_ = 2;
    ASSERT_THAT(
        touched_grids(base_point_index_, area_ratio_polygon_aabox_, num_x_grids_, num_y_grids_),
        testing::UnorderedElementsAreArray(
            std::vector<math::Vector2i>{{-1, -2}, {-1, -1}, {0, -2}, {0, -1}}));
  };
  {
    base_point_index_ = {-1, -2};
    area_ratio_polygon_aabox_ = 0.1;
    num_x_grids_ = 1;
    num_y_grids_ = 2;
    ASSERT_THAT(
        touched_grids(base_point_index_, area_ratio_polygon_aabox_, num_x_grids_, num_y_grids_),
        testing::UnorderedElementsAreArray(std::vector<math::Vector2i>{{-1, -2}, {-1, -1}}));
  };
  {
    base_point_index_ = {-1, -2};
    area_ratio_polygon_aabox_ = 0.1;
    num_x_grids_ = 2;
    num_y_grids_ = 1;
    ASSERT_THAT(
        touched_grids(base_point_index_, area_ratio_polygon_aabox_, num_x_grids_, num_y_grids_),
        testing::UnorderedElementsAreArray(std::vector<math::Vector2i>{{-1, -2}, {0, -2}}));
  }
}

}  // namespace math_internal
}  // namespace math
