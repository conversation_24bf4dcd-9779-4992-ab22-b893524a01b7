package(default_visibility = ["//visibility:public"])

cc_library(
    name = "convex_utils",
    hdrs = [
        "convex_utils.h",
    ],
    deps = [
        "//base/math:vector",
    ],
)

cc_test(
    name = "convex_utils_test",
    size = "small",
    srcs = [
        "convex_utils_test.cc",
    ],
    tags = [
        "ci",
        "ci_cpu",
    ],
    deps = [
        ":convex_utils",
        "//base/testing:test_main",
    ],
)

cc_library(
    name = "convex_hull",
    srcs = [
        "convex_hull.cc",
    ],
    hdrs = [
        "convex_hull.h",
    ],
    deps = [
        ":convex_utils",
        "//base/container:span",
        "//base/math:vector",
    ],
)

cc_test(
    name = "convex_hull_test",
    size = "small",
    srcs = [
        "convex_hull_test.cc",
    ],
    tags = [
        "ci",
        "ci_cpu",
    ],
    deps = [
        ":convex_hull",
        ":convex_utils",
        "//base/testing:geometry",
        "//base/testing:random",
        "//base/testing:test_main",
    ],
)

cc_binary(
    name = "convex_hull_benchmark",
    srcs = [
        "convex_hull_benchmark.cc",
    ],
    deps = [
        ":convex_hull",
        "//base/testing:geometry",
        "@benchmark",
    ],
)

cc_library(
    name = "convex_distance",
    srcs = [
        "convex_distance.cc",
    ],
    hdrs = [
        "convex_distance.h",
    ],
    deps = [
        ":convex_hull",
        ":convex_utils",
        "//base/container:span",
        "//base/math:line_segment2d",
        "//base/math:vector",
    ],
)

cc_test(
    name = "convex_distance_test",
    size = "small",
    srcs = [
        "convex_distance_test.cc",
    ],
    tags = [
        "ci",
        "ci_cpu",
    ],
    deps = [
        ":convex_distance",
        ":convex_hull",
        "//base/testing:geometry",
        "//base/testing:random",
        "//base/testing:test_main",
    ],
)

cc_binary(
    name = "convex_distance_benchmark",
    srcs = [
        "convex_distance_benchmark.cc",
    ],
    deps = [
        ":convex_distance",
        ":convex_hull",
        "//base/testing:geometry",
        "//base/testing:random",
        "@benchmark",
    ],
)

cc_library(
    name = "convex_iterator",
    hdrs = [
        "convex_iterator.h",
    ],
    deps = [
        ":convex_utils",
        "//base/container:span",
    ],
)

cc_library(
    name = "convex_minkowskisum",
    srcs = [
        "convex_minkowskisum.cc",
    ],
    hdrs = [
        "convex_minkowskisum.h",
    ],
    deps = [
        ":convex_hull",
        ":convex_iterator",
        ":convex_utils",
        "//base/container:span",
        "//base/math:vector",
    ],
)

cc_test(
    name = "convex_minkowskisum_test",
    size = "small",
    srcs = [
        "convex_minkowskisum_test.cc",
    ],
    tags = [
        "ci",
        "ci_cpu",
    ],
    deps = [
        ":convex_minkowskisum",
        "//base/testing:geometry",
        "//base/testing:random",
        "//base/testing:test_main",
    ],
)

cc_binary(
    name = "convex_minkowskisum_benchmark",
    srcs = [
        "convex_minkowskisum_benchmark.cc",
    ],
    deps = [
        ":convex_minkowskisum",
        "//base/testing:geometry",
        "//base/testing:random",
        "@benchmark",
    ],
)

cc_library(
    name = "convex_overlap",
    srcs = [
        "convex_overlap.cc",
    ],
    hdrs = [
        "convex_overlap.h",
    ],
    deps = [
        ":convex_distance",
        ":convex_extreme",
        ":convex_iterator",
        ":convex_utils",
        "//base/container:span",
        "//base/math:line_segment2d",
        "//base/math:vector",
    ],
)

cc_test(
    name = "convex_overlap_test",
    size = "small",
    srcs = [
        "convex_overlap_test.cc",
    ],
    tags = [
        "ci",
        "ci_cpu",
    ],
    deps = [
        ":convex_distance",
        ":convex_overlap",
        "//base/testing:geometry",
        "//base/testing:random",
        "//base/testing:test_main",
    ],
)

cc_binary(
    name = "convex_overlap_benchmark",
    srcs = [
        "convex_overlap_benchmark.cc",
    ],
    deps = [
        ":convex_overlap",
        "//base/testing:geometry",
        "//base/testing:random",
        "@benchmark",
    ],
)

cc_library(
    name = "touched_grids_computer",
    srcs = [
        "touched_grids_computer.cc",
    ],
    hdrs = [
        "touched_grids_computer.h",
    ],
    deps = [
        "//base/math:packed_index",
        "//base/math:vector",
    ],
)

cc_test(
    name = "touched_grids_computer_test",
    size = "small",
    srcs = [
        "touched_grids_computer_test.cc",
    ],
    tags = [
        "ci",
        "ci_cpu",
    ],
    deps = [
        ":touched_grids_computer",
        "//base/testing:test_main",
    ],
)

cc_library(
    name = "convex_extreme",
    srcs = [
        "convex_extreme.cc",
    ],
    hdrs = [
        "convex_extreme.h",
    ],
    deps = [
        ":convex_iterator",
        ":convex_utils",
        "//base/container:span",
        "//base/math:vector",
    ],
)

cc_test(
    name = "convex_extreme_test",
    size = "small",
    srcs = [
        "convex_extreme_test.cc",
    ],
    tags = [
        "ci",
        "ci_cpu",
    ],
    deps = [
        ":convex_extreme",
        "//base/testing:geometry",
        "//base/testing:random",
        "//base/testing:test_main",
    ],
)

cc_binary(
    name = "convex_extreme_benchmark",
    srcs = [
        "convex_extreme_benchmark.cc",
    ],
    deps = [
        ":convex_extreme",
        "//base/testing:geometry",
        "//base/testing:random",
        "@benchmark",
    ],
)

cc_library(
    name = "triangulation",
    srcs = [
        "triangulation.cc",
    ],
    hdrs = [
        "triangulation.h",
    ],
    deps = [
        "//base/container:span",
        "//base/math:vector",
    ],
)

cc_test(
    name = "triangulation_test",
    size = "small",
    srcs = [
        "triangulation_test.cc",
    ],
    tags = [
        "ci",
        "ci_cpu",
    ],
    deps = [
        ":triangulation",
        "//base/math:axis_align_box2d",
        "//base/testing:geometry",
        "//base/testing:random",
        "//base/testing:test_main",
    ],
)

cc_binary(
    name = "triangulation_benchmark",
    srcs = [
        "triangulation_benchmark.cc",
    ],
    deps = [
        ":triangulation",
        "//base/testing:geometry",
        "//base/testing:random",
        "@benchmark",
    ],
)
