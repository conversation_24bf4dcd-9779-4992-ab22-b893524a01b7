// Copyright @2022 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/math/internal/convex_minkowskisum.h"

#include <algorithm>
#include <utility>

#include "base/math/internal/convex_hull.h"
#include "base/math/internal/convex_iterator.h"
#include "base/math/internal/convex_utils.h"

namespace math {
namespace math_internal {
namespace convex {

using convex_internal::Iterator;
using convex_internal::Next;

std::vector<Vector2d> MinkowskiSumConvex(const base::ConstSpan<Vector2d>& convex_points0,
                                         const base::ConstSpan<Vector2d>& convex_points1) {
  CHECK_GE(convex_points0.size(), 2);
  CHECK_GE(convex_points1.size(), 2);
  const auto has_next = [](const Iterator& iter) { return iter.steps() < iter.num_points(); };
  std::vector<Vector2d> result_points;
  result_points.reserve(convex_points0.size() + convex_points1.size());
  Iterator iter0(convex_points0, convex_internal::LessThan);
  Iterator iter1(convex_points1, convex_internal::LessThan);
  while (has_next(iter0) || has_next(iter1)) {
    result_points.push_back(iter0.start() + iter1.start());
    if (!has_next(iter0)) {
      iter1++;
      continue;
    }
    if (!has_next(iter1)) {
      iter0++;
      continue;
    }
    const double cross = iter0.segment().CrossProd(iter1.segment());
    if (cross == 0.0) {
      iter0++;
      iter1++;
    } else if (cross > 0.0) {
      iter0++;
    } else {
      iter1++;
    }
  }
  return ComputeConvexHull(std::move(result_points), true);
}

std::vector<Vector2d> ExpandByDistanceConvex(const base::ConstSpan<Vector2d>& convex_points,
                                             double distance,
                                             double angle_step_hint) {
  CHECK_GE(convex_points.size(), 1);
  CHECK_GE(distance, 0.0);
  CHECK_GE(angle_step_hint, math::kEpsilon);
  CHECK_LE(angle_step_hint, M_PI_2);
  if (distance < math::kEpsilon) {
    return std::vector<Vector2d>(convex_points.begin(), convex_points.end());
  }
  std::vector<Vector2d> result_points;
  result_points.reserve(convex_points.size() * 2 +
                        static_cast<int>(std::ceil(M_PI * 2.0 / angle_step_hint - math::kEpsilon)));
  const auto add_points = [&result_points, distance, angle_step_hint](const Vector2d& point,
                                                                      double start_angle,
                                                                      double delta_angle,
                                                                      bool is_loop = false) {
    const int num_segments =
        std::max(1, static_cast<int>(std::ceil(delta_angle / angle_step_hint - math::kEpsilon)));
    const int num_points = (is_loop ? num_segments : num_segments + 1);
    const double angle_step = delta_angle / num_segments;
    for (int i = 0; i < num_points; ++i) {
      const double angle = (start_angle - M_PI_2) + i * angle_step;
      result_points.push_back(point + Vector2d::FromAngleAndLength(angle, distance));
    }
  };
  const int num_points = convex_points.size();
  if (num_points == 1) {
    add_points(convex_points[0], 0.0, M_PI * 2.0, true);
  } else {
    double last_angle = (convex_points[0] - convex_points[num_points - 1]).Angle();
    for (int i = 0; i < num_points; ++i) {
      const double next_angle = (convex_points[Next(i, num_points)] - convex_points[i]).Angle();
      add_points(
          convex_points[i], last_angle, math::NormalizeAngleZeroTo2PI(next_angle - last_angle));
      last_angle = next_angle;
    }
  }
  return ComputeConvexHull(std::move(result_points), true);
}

}  // namespace convex
}  // namespace math_internal
}  // namespace math
