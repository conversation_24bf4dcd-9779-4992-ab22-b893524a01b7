// Copyright @2022 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/math/internal/convex_extreme.h"

#include <algorithm>
#include <limits>

#include "gmock/gmock.h"
#include "gtest/gtest.h"

#include "base/math/internal/convex_hull.h"
#include "base/math/internal/convex_iterator.h"
#include "base/math/internal/convex_utils.h"
#include "base/testing/boost_geometry.h"
#include "base/testing/geometry.h"
#include "base/testing/random.h"

namespace math {
namespace math_internal {
namespace convex {

using convex_internal::Iterator;
using convex_internal::Next;

class ConvexExtremeTest : public ::testing::Test {
 protected:
  void CheckExtremePointsConvex(const Vector2d& direction) const {
    ASSERT_TRUE(IsConvexHull(polygon_points_));
    int min_index = -1;
    int max_index = -1;
    ExtremePointsConvex(polygon_points_, direction, &min_index, &max_index, true);
    ASSERT_GE(min_index, 0);
    ASSERT_GE(max_index, 0);
    ASSERT_TRUE(polygon_points_[min_index].IsNear(GetExtremePoint(direction.Rotate180())));
    ASSERT_TRUE(polygon_points_[max_index].IsNear(GetExtremePoint(direction)));

    ExtremePointsConvex(polygon_points_, direction, &min_index, &max_index, true);
    ASSERT_TRUE(polygon_points_[min_index].IsNear(GetExtremePoint(direction.Rotate180())));
    ASSERT_TRUE(polygon_points_[max_index].IsNear(GetExtremePoint(direction)));

    Vector2d min_point;
    Vector2d max_point;
    ExtremePointsConvex(polygon_points_, direction, &min_point, &max_point, true);
    ASSERT_TRUE(min_point.IsNear(GetExtremePoint(direction.Rotate180())));
    ASSERT_TRUE(max_point.IsNear(GetExtremePoint(direction)));
  }

  void CheckExtremePointsBruteForce(const Vector2d& direction) const {
    int min_index = -1;
    int max_index = -1;
    ExtremePointsBruteForce(polygon_points_, direction, &min_index, &max_index);
    ASSERT_TRUE(polygon_points_[min_index].IsNear(GetExtremePoint(direction.Rotate180())));
    ASSERT_TRUE(polygon_points_[max_index].IsNear(GetExtremePoint(direction)));

    Vector2d min_point;
    Vector2d max_point;
    ExtremePointsBruteForce(polygon_points_, direction, &min_point, &max_point);
    ASSERT_TRUE(min_point.IsNear(GetExtremePoint(direction.Rotate180())));
    ASSERT_TRUE(max_point.IsNear(GetExtremePoint(direction)));
  }

  void CheckGetMinAreaBoundingBoxConvex(base::Optional<double> expected_min_area = base::none) {
    ASSERT_TRUE(IsConvexHull(polygon_points_));
    const auto compute_bounding_box_area = [this](double heading) {
      const Vector2d d0 = Vector2d::FromAngle(heading);
      const Vector2d d1 = d0.Rotate90();
      Vector2d min_x;
      Vector2d max_x;
      ExtremePointsConvex(polygon_points_, d0, &min_x, &max_x);
      Vector2d min_y;
      Vector2d max_y;
      ExtremePointsConvex(polygon_points_, d1, &min_y, &max_y);
      return d0.InnerProd(max_x - min_x) * d1.InnerProd(max_y - min_y);
    };
    const double min_area_heading = GetMinAreaBoundingBoxConvex(polygon_points_);
    const double min_area = compute_bounding_box_area(min_area_heading);
    if (expected_min_area) {
      ASSERT_NEAR(*expected_min_area, min_area, 1e-3);
    }
    Iterator iter(polygon_points_);
    double min_area_brute_force = std::numeric_limits<double>::infinity();
    for (int i = 0; i < iter.num_points(); ++i, ++iter) {
      const double area = compute_bounding_box_area(iter.segment().Angle());
      min_area_brute_force = std::min(area, min_area_brute_force);
      ASSERT_LE(min_area, area + 1e-3);
    }
    ASSERT_NEAR(min_area_brute_force, min_area, 1e-3);
  }

 protected:
  const Vector2d& GetExtremePoint(const Vector2d& direction) const {
    CHECK(!polygon_points_.empty());
    return *std::max_element(polygon_points_.begin(), polygon_points_.end(),
                             [&direction](const Vector2d& p0, const Vector2d& p1) {
                               return direction.InnerProd(p0) < direction.InnerProd(p1);
                             });
  }

  std::vector<Vector2d> polygon_points_;
};

TEST_F(ConvexExtremeTest, ExtremePoints) {
  polygon_points_ = {
      {1.0, 1.0},
      {2.0, 1.0},
      {2.0, 2.0},
      {1.0, 2.0},
  };
  for (double angle : {0.0, M_PI / 4, M_PI / 3, M_PI / 2, M_PI, -M_PI, 1.0, 2.0}) {
    const Vector2d direction = Vector2d::FromAngle(angle);
    ASSERT_NO_FATAL_FAILURE(CheckExtremePointsConvex(direction));
    ASSERT_NO_FATAL_FAILURE(CheckExtremePointsBruteForce(direction));
  }
  for (int k = 0; k < 100; ++k) {
    const int num_points = testing::RandomInt(3, 100);
    polygon_points_ =
        testing::MakeRandomConvexPolygon(num_points, testing::RandomDouble(1.0, 10.0));
    for (int i = 0; i < num_points; ++i) {
      const Vector2d direction0 =
          (polygon_points_[i] - polygon_points_[Next(i, num_points)]).Normalized();
      const Vector2d direction1 = -direction0;
      ASSERT_NO_FATAL_FAILURE(CheckExtremePointsConvex(direction0));
      ASSERT_NO_FATAL_FAILURE(CheckExtremePointsBruteForce(direction0));
      ASSERT_NO_FATAL_FAILURE(CheckExtremePointsConvex(direction1));
      ASSERT_NO_FATAL_FAILURE(CheckExtremePointsBruteForce(direction1));
    }
    for (int i = 0; i < 100; ++i) {
      const Vector2d direction0 = Vector2d::FromAngle(testing::RandomDouble(0.0, M_PI * 2.0));
      const Vector2d direction1 = -direction0;
      ASSERT_NO_FATAL_FAILURE(CheckExtremePointsConvex(direction0));
      ASSERT_NO_FATAL_FAILURE(CheckExtremePointsBruteForce(direction0));
      ASSERT_NO_FATAL_FAILURE(CheckExtremePointsConvex(direction1));
      ASSERT_NO_FATAL_FAILURE(CheckExtremePointsBruteForce(direction1));
    }
  }
}

TEST_F(ConvexExtremeTest, ExtremePointsBruteForce) {
  for (int k = 0; k < 100; ++k) {
    const int num_points = testing::RandomInt(3, 100);
    polygon_points_ = testing::MakeRandomPolygon(num_points, testing::RandomDouble(1.0, 10.0));
    for (int i = 0; i < num_points; ++i) {
      const Vector2d direction0 =
          (polygon_points_[i] - polygon_points_[Next(i, num_points)]).Normalized();
      const Vector2d direction1 = -direction0;
      ASSERT_NO_FATAL_FAILURE(CheckExtremePointsBruteForce(direction0));
      ASSERT_NO_FATAL_FAILURE(CheckExtremePointsBruteForce(direction1));
    }
    for (int i = 0; i < 100; ++i) {
      const Vector2d direction0 = Vector2d::FromAngle(testing::RandomDouble(0.0, M_PI * 2.0));
      const Vector2d direction1 = -direction0;
      ASSERT_NO_FATAL_FAILURE(CheckExtremePointsBruteForce(direction0));
      ASSERT_NO_FATAL_FAILURE(CheckExtremePointsBruteForce(direction1));
    }
  }
}

TEST_F(ConvexExtremeTest, GetMinAreaBoundingBoxConvex) {
  {
    polygon_points_ = {
        {+0.0, +0.0},
        {+2.0, +0.0},
        {+2.0, +2.0},
        {+0.0, +2.0},
    };
    ASSERT_NO_FATAL_FAILURE(CheckGetMinAreaBoundingBoxConvex(4.0));

    polygon_points_ = {
        {+1.0, +0.0},
        {+0.0, +1.0},
        {-1.0, +0.0},
        {+0.0, -1.0},
    };
    ASSERT_NO_FATAL_FAILURE(CheckGetMinAreaBoundingBoxConvex(2.0));

    polygon_points_ = {{50.0, -1e-8}, {100.0, 0.0}, {100.0, 100.0}, {0.0, 100.0}, {0.0, 0.0}};
    ASSERT_NO_FATAL_FAILURE(CheckGetMinAreaBoundingBoxConvex(10000.0));

    polygon_points_ = {{0.0, 0.0}, {10.0, 0.0}, {0.0, 10.0}};
    ASSERT_NO_FATAL_FAILURE(CheckGetMinAreaBoundingBoxConvex(100.0));
  }
  {
    for (int i = 0; i < 1000; ++i) {
      polygon_points_ = testing::MakeRandomConvexPolygon(testing::RandomInt(3, 20),
                                                         testing::RandomDouble(1.0, 10.0));
      ASSERT_NO_FATAL_FAILURE(CheckGetMinAreaBoundingBoxConvex());
    }
  }
}

}  // namespace convex
}  // namespace math_internal
}  // namespace math
