// Copyright @2022 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "benchmark/benchmark.h"

#include "base/math/internal/convex_extreme.h"
#include "base/math/internal/convex_utils.h"
#include "base/testing/geometry.h"
#include "base/testing/random.h"

namespace math {
namespace math_internal {
namespace convex {

using convex_internal::Next;

namespace {

void CustomArguments(benchmark::internal::Benchmark* b) {
  CHECK(b != nullptr);
  for (int n = 3; n <= 10; ++n) {
    b->Args({n});
  }
  for (int n = 16; n <= 1024; n *= 2) {
    b->Args({n});
  }
}

}  // namespace

void BM_ExtremePoints(benchmark::State& state, bool is_convex) { /* NOLINT(runtime/references) */
  const int num_points = state.range(0);
  const double radius = std::max(10.0, num_points / 10.0);
  std::vector<std::vector<Vector2d>> convex_points(100);
  std::vector<Vector2d> directions(convex_points.size());
  for (int i = 0; i < static_cast<int>(convex_points.size()); ++i) {
    convex_points[i] =
        testing::MakeRegularPolygon(num_points, radius, testing::RandomPoint(radius * 5.0));
    directions[i] = Vector2d::FromAngle(testing::RandomDouble(0.0, M_PI * 2.0));
  }
  int i = 0;
  int j = 0;
  while (state.KeepRunning()) {
    int min_index = 0;
    int max_index = 0;
    if (is_convex) {
      ExtremePointsConvex(convex_points[i], directions[j], &min_index, &max_index, true);
    } else {
      ExtremePointsBruteForce(convex_points[i], directions[j], &min_index, &max_index);
    }
    i = Next(i, convex_points.size());
    if (i != 0) {
      continue;
    }
    j = Next(j, convex_points.size());
  }
  state.SetComplexityN(num_points);
}
BENCHMARK_CAPTURE(BM_ExtremePoints, Convex, true)->Apply(CustomArguments)->Complexity();
BENCHMARK_CAPTURE(BM_ExtremePoints, BruteForce, false)->Apply(CustomArguments)->Complexity();

}  // namespace convex
}  // namespace math_internal
}  // namespace math

BENCHMARK_MAIN();

// clang-format off
/*
Run on (16 X 5000 MHz CPU s)
CPU Caches:
  L1 Data 32K (x8)
  L1 Instruction 32K (x8)
  L2 Unified 256K (x8)
  L3 Unified 16384K (x1)
Load Average: 1.10, 1.41, 1.72
***WARNING*** CPU scaling is enabled, the benchmark real time measurements may be noisy and will incur extra overhead.
---------------------------------------------------------------------------
Benchmark                                 Time             CPU   Iterations
---------------------------------------------------------------------------
BM_ExtremePoints/Convex/3              9.76 ns         9.76 ns     71447883
BM_ExtremePoints/Convex/4              10.2 ns         10.2 ns     68441769
BM_ExtremePoints/Convex/5              13.4 ns         13.4 ns     52111441
BM_ExtremePoints/Convex/6              14.1 ns         14.1 ns     49858057
BM_ExtremePoints/Convex/7              14.7 ns         14.7 ns     46996109
BM_ExtremePoints/Convex/8              16.3 ns         16.3 ns     44083786
BM_ExtremePoints/Convex/9              16.9 ns         16.9 ns     42171296
BM_ExtremePoints/Convex/10             17.4 ns         17.4 ns     39363657
BM_ExtremePoints/Convex/16             20.9 ns         20.9 ns     32804024
BM_ExtremePoints/Convex/32             26.4 ns         26.4 ns     26642617
BM_ExtremePoints/Convex/64             31.3 ns         31.3 ns     22561237
BM_ExtremePoints/Convex/128            37.8 ns         37.8 ns     18071433
BM_ExtremePoints/Convex/256            51.1 ns         51.1 ns     12785811
BM_ExtremePoints/Convex/512            65.1 ns         65.1 ns     10374711
BM_ExtremePoints/Convex/1024           76.9 ns         76.9 ns      8672429
BM_ExtremePoints/Convex_BigO           6.40 lgN        6.40 lgN
BM_ExtremePoints/Convex_RMS              19 %            19 %
BM_ExtremePoints/BruteForce/3          5.88 ns         5.88 ns    121215041
BM_ExtremePoints/BruteForce/4          6.83 ns         6.83 ns    101546737
BM_ExtremePoints/BruteForce/5          7.50 ns         7.50 ns     91302274
BM_ExtremePoints/BruteForce/6          8.20 ns         8.20 ns     86199235
BM_ExtremePoints/BruteForce/7          8.86 ns         8.86 ns     79293128
BM_ExtremePoints/BruteForce/8          9.51 ns         9.51 ns     75249233
BM_ExtremePoints/BruteForce/9          10.3 ns         10.3 ns     67585976
BM_ExtremePoints/BruteForce/10         10.8 ns         10.8 ns     65345622
BM_ExtremePoints/BruteForce/16         15.0 ns         15.0 ns     45547972
BM_ExtremePoints/BruteForce/32         27.4 ns         27.4 ns     26206823
BM_ExtremePoints/BruteForce/64         52.2 ns         52.2 ns     13500828
BM_ExtremePoints/BruteForce/128         114 ns          114 ns      6138784
BM_ExtremePoints/BruteForce/256         217 ns          217 ns      3195059
BM_ExtremePoints/BruteForce/512         415 ns          415 ns      1724917
BM_ExtremePoints/BruteForce/1024        801 ns          801 ns       856933
BM_ExtremePoints/BruteForce_BigO       0.79 N          0.79 N
BM_ExtremePoints/BruteForce_RMS           6 %             6 %
*/
