// Copyright @2022 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <utility>

#include "base/math/vector2.h"

namespace math {
namespace math_internal {
namespace convex {
namespace convex_internal {

constexpr int Next(int i, int n) { return i != n - 1 ? i + 1 : 0; }
constexpr int Prev(int i, int n) { return i != 0 ? i - 1 : n - 1; }

// Compare points by x-axis, then by y-axis.
constexpr bool <PERSON><PERSON>(const Vector2d& p0, const Vector2d& p1) {
  return p0.x != p1.x ? p0.x < p1.x : p0.y < p1.y;
}

// Find the last index that satisfied the condition.
// Return lower-1 if not found.
template <typename Condition>
int BinarySearch(int lower, int upper, const Condition& condition) {
  while (upper - lower >= 4) {
    const int index = lower + (upper - lower) / 2;
    if (condition(index)) {
      lower = index + 1;
    } else {
      upper = index - 1;
    }
  }
  while (lower <= upper && condition(lower)) {
    lower++;
  }
  return lower - 1;
}

template <typename Function>
std::pair<int, double> MaximizeFunctionLocally(const Function& func,
                                               int num_points,
                                               int index,
                                               bool ccw_order_only) {
  int max_index = index;
  double max_value = func(index);
  while (true) {
    const int i = Next(max_index, num_points);
    const double v = func(i);
    if (v <= max_value) {
      break;
    }
    max_index = i;
    max_value = v;
  }
  if (ccw_order_only || index != max_index) {
    return std::make_pair(max_index, max_value);
  }
  while (true) {
    const int i = Prev(max_index, num_points);
    const double v = func(i);
    if (v <= max_value) {
      break;
    }
    max_index = i;
    max_value = v;
  }
  return std::make_pair(max_index, max_value);
}

template <typename Function>
std::pair<int, double> MinimizeFunctionLocally(const Function& func,
                                               int num_points,
                                               int index,
                                               bool ccw_order_only) {
  int min_index = 0;
  double max_value = 0.0;
  std::tie(min_index, max_value) = MaximizeFunctionLocally(
      [&func](int i) { return -func(i); }, num_points, index, ccw_order_only);
  return std::make_pair(min_index, -max_value);
}

}  // namespace convex_internal
}  // namespace convex
}  // namespace math_internal
}  // namespace math
