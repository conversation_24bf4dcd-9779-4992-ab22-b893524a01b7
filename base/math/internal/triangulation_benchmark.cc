// Copyright @2023 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "benchmark/benchmark.h"

#include <vector>

#include "base/math/internal/triangulation.h"
#include "base/testing/geometry.h"

namespace math {
namespace math_internal {
namespace triangulation {
namespace {

void CustomArguments(benchmark::internal::Benchmark* b) {
  CHECK(b != nullptr);
  for (int n = 3; n <= 10; ++n) {
    b->Args({n});
  }
  for (int n = 16; n <= 1024; n *= 2) {
    b->Args({n});
  }
}

}  // namespace

void BM_TriangulatePolygon(benchmark::State& state, /* NOLINT(runtime/references) */
                           bool is_concave) {
  const int num_points = state.range(0);
  const double theta_step = M_PI * 2.0 / num_points;
  std::vector<Vector2d> points(num_points);
  for (int i = 0; i < num_points; ++i) {
    const double radius_ratio = (is_concave && (i % 2 != 0)) ? 0.5 : 1.0;
    points[i] = Vector2d::FromAngleAndLength(i * theta_step, 500.0 * radius_ratio);
  }
  while (state.KeepRunning()) {
    benchmark::DoNotOptimize(TriangulatePolygon(points, false));
  }
  state.SetComplexityN(num_points);
}
BENCHMARK_CAPTURE(BM_TriangulatePolygon, Concave, true)->Apply(CustomArguments)->Complexity();
BENCHMARK_CAPTURE(BM_TriangulatePolygon, Convex, false)->Apply(CustomArguments)->Complexity();

}  // namespace triangulation
}  // namespace math_internal
}  // namespace math

BENCHMARK_MAIN();

// clang-format off
/*
Run on (16 X 5000 MHz CPU s)
CPU Caches:
  L1 Data 32K (x8)
  L1 Instruction 32K (x8)
  L2 Unified 256K (x8)
  L3 Unified 16384K (x1)
Load Average: 0.87, 0.51, 0.39
***WARNING*** CPU scaling is enabled, the benchmark real time measurements may be noisy and will incur extra overhead.
-----------------------------------------------------------------------------
Benchmark                                   Time             CPU   Iterations
-----------------------------------------------------------------------------
BM_TriangulatePolygon/Concave/3          17.7 ns         17.7 ns     40031209
BM_TriangulatePolygon/Concave/4          69.7 ns         69.7 ns     10124394
BM_TriangulatePolygon/Concave/5          88.2 ns         88.2 ns      7890278
BM_TriangulatePolygon/Concave/6           112 ns          112 ns      6366519
BM_TriangulatePolygon/Concave/7           155 ns          155 ns      4555958
BM_TriangulatePolygon/Concave/8           165 ns          165 ns      4269973
BM_TriangulatePolygon/Concave/9           193 ns          193 ns      3650969
BM_TriangulatePolygon/Concave/10          223 ns          223 ns      3097529
BM_TriangulatePolygon/Concave/16          417 ns          417 ns      1681031
BM_TriangulatePolygon/Concave/32          999 ns          999 ns       702799
BM_TriangulatePolygon/Concave/64         2533 ns         2533 ns       285466
BM_TriangulatePolygon/Concave/128        6976 ns         6976 ns       101108
BM_TriangulatePolygon/Concave/256       22677 ns        22676 ns        30928
BM_TriangulatePolygon/Concave/512       78194 ns        78194 ns         8901
BM_TriangulatePolygon/Concave/1024     302145 ns       302143 ns         2308
BM_TriangulatePolygon/Concave_BigO       0.29 N^2        0.29 N^2
BM_TriangulatePolygon/Concave_RMS           5 %             5 %
BM_TriangulatePolygon/Convex/3           17.9 ns         17.9 ns     38216419
BM_TriangulatePolygon/Convex/4           70.8 ns         70.8 ns      9844536
BM_TriangulatePolygon/Convex/5           89.9 ns         89.9 ns      7918508
BM_TriangulatePolygon/Convex/6            111 ns          111 ns      6429800
BM_TriangulatePolygon/Convex/7            139 ns          139 ns      4965654
BM_TriangulatePolygon/Convex/8            147 ns          147 ns      4877435
BM_TriangulatePolygon/Convex/9            164 ns          164 ns      4246146
BM_TriangulatePolygon/Convex/10           268 ns          268 ns      2602124
BM_TriangulatePolygon/Convex/16           345 ns          345 ns      2025086
BM_TriangulatePolygon/Convex/32           669 ns          669 ns      1050572
BM_TriangulatePolygon/Convex/64          1317 ns         1317 ns       536207
BM_TriangulatePolygon/Convex/128         2571 ns         2571 ns       272486
BM_TriangulatePolygon/Convex/256         5648 ns         5648 ns       125003
BM_TriangulatePolygon/Convex/512        11897 ns        11897 ns        58762
BM_TriangulatePolygon/Convex/1024       30137 ns        30136 ns        23222
BM_TriangulatePolygon/Convex_BigO        2.88 NlgN       2.88 NlgN
BM_TriangulatePolygon/Convex_RMS           12 %            12 %
*/
