// Copyright @2022 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/math/internal/convex_extreme.h"

#include <limits>

#include "base/math/internal/convex_iterator.h"
#include "base/math/internal/convex_utils.h"

namespace math {
namespace math_internal {
namespace convex {

using convex_internal::Iterator;

namespace {

int GetExtremePointIndex(const base::ConstSpan<Vector2d>& convex_points,
                         const Vector2d& direction) {
  const auto is_up = [&convex_points, &direction](int i0, int i1) {
    return direction.InnerProd(convex_points[i1] - convex_points[i0]) > math::kEpsilon;
  };
  const int num_points = convex_points.size();
  if (is_up(1, 0) && is_up(num_points - 1, 0)) {
    return 0;
  }
  int lower = 0;
  int upper = num_points - 1;
  while (upper - lower >= 4) {
    const int index = lower + (upper - lower) / 2;
    const bool a_up = is_up(lower, lower + 1);
    const bool c_up = is_up(index, index + 1);
    if (a_up) {
      if (c_up && is_up(lower, index)) {
        lower = index;
      } else {
        upper = index;
      }
    } else {
      if (c_up || is_up(index, lower)) {
        lower = index;
      } else {
        upper = index;
      }
    }
  }
  std::tie(lower, std::ignore) = convex_internal::MaximizeFunctionLocally(
      [&convex_points, &direction](int i) { return direction.InnerProd(convex_points[i]); },
      num_points,
      lower,
      false);
  return lower;
}

void ExtremePointsConvexBinarySearch(const base::ConstSpan<Vector2d>& convex_points,
                                     const Vector2d& direction,
                                     int* min_index,
                                     int* max_index) {
  CHECK_GE(convex_points.size(), 3);
  CHECK(min_index != nullptr);
  CHECK(max_index != nullptr);
  *min_index = GetExtremePointIndex(convex_points, direction.Rotate180());
  *max_index = GetExtremePointIndex(convex_points, direction);
}

}  // namespace

void ExtremePointsConvex(const base::ConstSpan<Vector2d>& convex_points,
                         const Vector2d& direction,
                         int* min_index,
                         int* max_index,
                         bool force_binary_search) {
  if (convex_points.size() <= 32 && !force_binary_search) {
    ExtremePointsBruteForce(convex_points, direction, min_index, max_index);
  } else {
    ExtremePointsConvexBinarySearch(convex_points, direction, min_index, max_index);
  }
}

void ExtremePointsConvex(const base::ConstSpan<Vector2d>& convex_points,
                         const Vector2d& direction,
                         Vector2d* min_point,
                         Vector2d* max_point,
                         bool force_binary_search) {
  CHECK(min_point != nullptr);
  CHECK(max_point != nullptr);
  int min_index = 0;
  int max_index = 0;
  ExtremePointsConvex(convex_points, direction, &min_index, &max_index, force_binary_search);
  *min_point = convex_points[min_index];
  *max_point = convex_points[max_index];
}

void ExtremePointsBruteForce(const base::ConstSpan<Vector2d>& polygon_points,
                             const Vector2d& direction,
                             int* min_index,
                             int* max_index) {
  CHECK_GE(polygon_points.size(), 1);
  CHECK(min_index != nullptr);
  CHECK(max_index != nullptr);
  double min_projection = +std::numeric_limits<double>::infinity();
  double max_projection = -std::numeric_limits<double>::infinity();
  for (int i = 0; i < static_cast<int>(polygon_points.size()); ++i) {
    const double projection = direction.InnerProd(polygon_points[i]);
    if (projection < min_projection) {
      min_projection = projection;
      *min_index = i;
    }
    if (projection > max_projection) {
      max_projection = projection;
      *max_index = i;
    }
  }
}

void ExtremePointsBruteForce(const base::ConstSpan<Vector2d>& polygon_points,
                             const Vector2d& direction,
                             Vector2d* min_point,
                             Vector2d* max_point) {
  CHECK(min_point != nullptr);
  CHECK(max_point != nullptr);
  int min_index = 0;
  int max_index = 0;
  ExtremePointsBruteForce(polygon_points, direction, &min_index, &max_index);
  *min_point = polygon_points[min_index];
  *max_point = polygon_points[max_index];
}

double GetMinAreaBoundingBoxConvex(const base::ConstSpan<Vector2d>& convex_points) {
  CHECK_GE(convex_points.size(), 3);
  const auto maximize_inner = [&convex_points](
                                  bool ccw_order_only, const Vector2d& direction, int* max_index) {
    double inner = 0.0;
    std::tie(*max_index, inner) = convex_internal::MaximizeFunctionLocally(
        [&direction, &convex_points](int i) { return direction.InnerProd(convex_points[i]); },
        convex_points.size(),
        *max_index,
        ccw_order_only);
    return inner;
  };
  double min_area = std::numeric_limits<double>::infinity();
  Vector2d min_area_direction;
  Iterator iter(convex_points);
  for (int i0 = 0, i1 = 0, i2 = 0, i3 = 0; i0 < iter.num_points(); ++i0, ++iter) {
    const bool ccw_order_only = (i0 != 0);
    const Vector2d direction = iter.segment().Normalized();
    const double dx = maximize_inner(ccw_order_only, direction.Rotate90(), &i2) -
                      direction.CrossProd(iter.start());
    const double dy = maximize_inner(ccw_order_only, direction, &i1) +
                      maximize_inner(ccw_order_only, direction.Rotate180(), &i3);
    const double area = dx * dy;
    if (area < min_area) {
      min_area = area;
      min_area_direction = direction;
    }
  }
  return min_area_direction.Angle();
}

}  // namespace convex
}  // namespace math_internal
}  // namespace math
