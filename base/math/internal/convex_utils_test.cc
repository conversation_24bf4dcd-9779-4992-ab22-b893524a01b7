// Copyright @2022 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/math/internal/convex_utils.h"

#include "gmock/gmock.h"
#include "gtest/gtest.h"

namespace math {
namespace math_internal {
namespace convex {

TEST(ConvexUtilsTest, BinarySearch) {
  int pivot = 0;
  const auto condition = [&pivot](int index) { return index <= pivot; };
  while (pivot <= 100) {
    for (int lower = 0; lower <= 100; ++lower) {
      for (int upper = 0; upper <= 100; ++upper) {
        if (lower > upper) {
          ASSERT_EQ(lower - 1, convex_internal::BinarySearch(lower, upper, condition));
        } else if (pivot < lower) {
          ASSERT_EQ(lower - 1, convex_internal::BinarySearch(lower, upper, condition));
        } else if (pivot >= upper) {
          ASSERT_EQ(upper, convex_internal::BinarySearch(lower, upper, condition));
        } else if (pivot >= lower) {
          ASSERT_EQ(pivot, convex_internal::BinarySearch(lower, upper, condition));
        } else {
          ASSERT_EQ(lower - 1, convex_internal::BinarySearch(lower, upper, condition));
        }
      }
    }
    pivot++;
  }
}

}  // namespace convex
}  // namespace math_internal
}  // namespace math
