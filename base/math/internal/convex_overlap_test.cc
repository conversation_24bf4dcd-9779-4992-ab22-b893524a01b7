// Copyright @2022 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/math/internal/convex_overlap.h"

#include <algorithm>
#include <limits>
#include <utility>

#include "gmock/gmock.h"
#include "gtest/gtest.h"

#include "base/common/optional.h"
#include "base/math/internal/convex_distance.h"
#include "base/math/internal/convex_hull.h"
#include "base/math/internal/convex_utils.h"
#include "base/strings/macros.h"
#include "base/testing/boost_geometry.h"
#include "base/testing/geometry.h"
#include "base/testing/random.h"

namespace math {
namespace math_internal {
namespace convex {

class ConvexOverlapTest : public ::testing::Test {
 protected:
  void CheckGetOverlapWithSegment(
      bool is_convex,
      base::Optional<const std::vector<double>> expected_overlap_ratios = base::none) const {
    ASSERT_TRUE(!is_convex || IsConvexHull(polygon_points0_));
    ASSERT_TRUE(segment_);
    const auto interpolate_by_s = [this](const std::vector<double>& project_s) {
      std::vector<Vector2d> points;
      for (double s : project_s) {
        points.push_back(segment_->InterpolateByS(s));
      }
      return points;
    };
    std::vector<Vector2d> overlap_points_convex;
    std::vector<Vector2d> overlap_points_brute_force;
    if (is_convex) {
      overlap_points_convex = interpolate_by_s(GetOverlapConvex(polygon_points0_, *segment_, true));
    }
    {
      overlap_points_brute_force =
          interpolate_by_s(GetOverlapBruteForce(polygon_points0_, *segment_));
    }
    if (!overlap_points_convex.empty()) {
      ASSERT_TRUE(HasOverlapConvex(polygon_points0_, *segment_));
    }
    if (!overlap_points_brute_force.empty()) {
      ASSERT_TRUE(HasOverlapBruteForce(polygon_points0_, *segment_));
    }

    const auto distance_to = [](const std::vector<Vector2d>& points, const Vector2d& point) {
      double min_dist = std::numeric_limits<double>::infinity();
      for (const Vector2d& p : points) {
        min_dist = std::min(min_dist, p.DistanceTo(point));
      }
      return min_dist;
    };
    if (expected_overlap_ratios) {
      std::vector<Vector2d> expected_overlap_points;
      for (double r : *expected_overlap_ratios) {
        expected_overlap_points.push_back(segment_->Interpolate(r));
      }
      ASSERT_EQ(expected_overlap_points.size(), overlap_points_brute_force.size());
      for (const Vector2d& point : expected_overlap_points) {
        ASSERT_NEAR(distance_to(overlap_points_brute_force, point), 0.0, 1e-5);
      }
      if (is_convex) {
        ASSERT_EQ(expected_overlap_points.size(), overlap_points_convex.size());
        for (const Vector2d& point : expected_overlap_points) {
          ASSERT_NEAR(distance_to(overlap_points_convex, point), 0.0, 1e-5);
        }
      }
    }
    for (const Vector2d& point : overlap_points_convex) {
      ASSERT_NEAR(DistanceToBruteForce(polygon_points0_, point), 0.0, 1e-5);
    }
    for (const Vector2d& point : overlap_points_brute_force) {
      ASSERT_NEAR(DistanceToBruteForce(polygon_points0_, point), 0.0, 1e-5);
    }

    const int num_points0 = polygon_points0_.size();
    if (num_points0 >= 2) {
      for (int i = 0, j = num_points0 - 1; i < num_points0; ++i) {
        Vector2d point;
        if (LineSegment2d::GetIntersect(polygon_points0_[i], polygon_points0_[j], segment_->start(),
                                        segment_->end(), &point)) {
          ASSERT_NEAR(distance_to(overlap_points_brute_force, point), 0.0, 1e-5);
          if (is_convex) {
            ASSERT_NEAR(distance_to(overlap_points_convex, point), 0.0, 1e-5);
          }
        }
        j = i;
      }
      for (const Vector2d& point : segment_->endpoints()) {
        if (IsPointInBruteForce(polygon_points0_, point)) {
          ASSERT_NEAR(distance_to(overlap_points_brute_force, point), 0.0, 1e-5);
          if (is_convex) {
            ASSERT_NEAR(distance_to(overlap_points_convex, point), 0.0, 1e-5);
          }
        }
      }
    }
  }

  void CheckHasOverlapWithPolygon(bool is_convex) const {
    ASSERT_TRUE(!is_convex || IsConvexHull(polygon_points0_));
    ASSERT_TRUE(!is_convex || IsConvexHull(polygon_points1_));
    double extreme_dist = 0.0;
    for (const Vector2d& p0 : polygon_points0_) {
      for (const Vector2d& p1 : polygon_points0_) {
        extreme_dist = std::max(extreme_dist, p0.DistanceTo(p1) * 2.0);
      }
    }
    ASSERT_GE(extreme_dist, 1e-3);
    for (const Vector2d& p0 : polygon_points0_) {
      for (const Vector2d& p1 : polygon_points0_) {
        if (p0.IsNear(p1)) {
          continue;
        }
        const Vector2d segment = p1 - p0;
        const Vector2d direction = segment.Normalized();
        const double min_dist = segment.Length();
        const double max_dist = min_dist + extreme_dist;
        const double boost_distance = GetDistanceAlongDirection(
            direction, min_dist, max_dist,
            [](const std::vector<Vector2d>& polygon0, const std::vector<Vector2d>& polygon1) {
              const testing::BoostPolygon2d boost_polygon0 =
                  testing::ToBoostPolygonFromPoints(polygon0);
              const testing::BoostPolygon2d boost_polygon1 =
                  testing::ToBoostPolygonFromPoints(polygon1);
              return testing::BoostHasOverlap(boost_polygon0, boost_polygon1);
            });
        const double d0 = GetDistanceAlongDirection(
            direction, min_dist, max_dist,
            [](const std::vector<Vector2d>& polygon0, const std::vector<Vector2d>& polygon1) {
              return HasOverlapBruteForce(polygon0, polygon1);
            });
        ASSERT_NEAR(d0, boost_distance, 1e-3) << DUMP_TO_STREAM(p0, p1, boost_distance, d0);
        if (!is_convex) {
          continue;
        }
        const double d1 = GetDistanceAlongDirection(
            direction, min_dist, max_dist,
            [](const std::vector<Vector2d>& polygon0, const std::vector<Vector2d>& polygon1) {
              CHECK(IsConvexHull(polygon0));
              CHECK(IsConvexHull(polygon1));
              return HasOverlapConvex(polygon0, polygon1);
            });
        ASSERT_NEAR(d1, boost_distance, 1e-3) << DUMP_TO_STREAM(p0, p1, boost_distance, d1);
      }
    }
  }

  void CheckComputeOverlapConvex(
      base::Optional<double> expected_area = base::none,
      base::Optional<std::vector<Vector2d>> expected_overlap_points = base::none) const {
    ASSERT_TRUE(IsConvexHull(polygon_points0_));
    ASSERT_TRUE(IsConvexHull(polygon_points1_));
    const testing::BoostPolygon2d p0 = testing::ToBoostPolygonFromPoints(polygon_points0_);
    const testing::BoostPolygon2d p1 = testing::ToBoostPolygonFromPoints(polygon_points1_);
    const std::vector<testing::BoostPolygon2d> boost_overlaps =
        testing::BoostPolygonOverlap(p0, p1);
    ASSERT_LE(boost_overlaps.size(), 1);
    double boost_area = 0.0;
    for (const auto& p : boost_overlaps) {
      boost_area += testing::BoostPolygonArea(p);
    }
    const std::vector<Vector2d> overlap_points =
        ComputeOverlapConvex(polygon_points0_, polygon_points1_);
    for (const Vector2d& p : overlap_points) {
      ASSERT_TRUE(IsPointInConvex(polygon_points0_, p));
      ASSERT_TRUE(IsPointInConvex(polygon_points1_, p));
    }
    const testing::BoostPolygon2d p3 = testing::ToBoostPolygonFromPoints(overlap_points);
    if (overlap_points.size() >= 3) {
      ASSERT_TRUE(IsConvexHull(overlap_points));
      ASSERT_EQ(boost_overlaps.size(), 1);
      const AxisAlignBox2d overlap_bounding_box(overlap_points);
      const AxisAlignBox2d boost_bounding_box(testing::FromBoostAsPoints(boost_overlaps[0]));
      ASSERT_NEAR(overlap_bounding_box.GetMinCorner().DistanceTo(boost_bounding_box.GetMinCorner()),
                  0.0, 1e-3);
      ASSERT_NEAR(overlap_bounding_box.GetMaxCorner().DistanceTo(boost_bounding_box.GetMaxCorner()),
                  0.0, 1e-3);
      if (boost_area > 1e-3) {
        ASSERT_NEAR(testing::BoostPolygonIntersectionOverUnion(p3, boost_overlaps[0]), 1.0, 1e-3);
      }
      ASSERT_NEAR(testing::BoostPolygonArea(p3), boost_area, 1e-3);
    } else {
      ASSERT_NEAR(testing::BoostPolygonArea(p3), 0.0, 1e-3);
    }
    if (expected_area) {
      ASSERT_NEAR(boost_area, *expected_area, 1e-3);
    }
    if (expected_overlap_points) {
      ASSERT_THAT(overlap_points, testing::UnorderedElementsAreArray(*expected_overlap_points));
      ASSERT_EQ(overlap_points.size(), expected_overlap_points->size());
      if (!overlap_points.empty()) {
        const auto iter =
            std::min_element(expected_overlap_points->begin(), expected_overlap_points->end(),
                             [&overlap_points](const auto& p0, const auto& p1) {
                               return overlap_points[0].DistanceSquareTo(p0) <
                                      overlap_points[0].DistanceSquareTo(p1);
                             });
        std::rotate(expected_overlap_points->begin(), iter, expected_overlap_points->end());
        ASSERT_THAT(overlap_points, testing::ElementsAreArray(*expected_overlap_points));
      }
    }
  }

 protected:
  std::vector<Vector2d> polygon_points0_;
  std::vector<Vector2d> polygon_points1_;
  base::Optional<LineSegment2d> segment_;

 private:
  template <typename HasOverlapFunc>
  double GetDistanceAlongDirection(const Vector2d& direction, double min_dist, double max_dist,
                                   const HasOverlapFunc& has_overlap) const {
    while (max_dist - min_dist > 1e-6) {
      const double dist = min_dist + (max_dist - min_dist) * 0.5;
      if (has_overlap(polygon_points0_, testing::ShiftPoints(polygon_points1_, dist * direction))) {
        min_dist = dist;
      } else {
        max_dist = dist;
      }
    }
    return min_dist;
  }
};

TEST_F(ConvexOverlapTest, GetOverlapWithSegment) {
  struct TestCase {
    Vector2d p0;
    Vector2d p1;
    std::vector<double> expected_overlap_ratios;
  };
  {
    const std::vector<TestCase> testcases = {
        {{0.0, 0.0}, {2.0, 2.0}, {0.5}},
        {{0.0, 0.0}, {1.0, 1.0}, {1.0}},
        {{1.0, 1.0}, {2.0, 2.0}, {0.0}},
        {{1.0, 1.0}, {0.0, 0.0}, {0.0}},
    };
    polygon_points0_ = {{1.0, 1.0}};
    for (const auto& t : testcases) {
      segment_ = LineSegment2d(t.p0, t.p1);
      ASSERT_NO_FATAL_FAILURE(CheckGetOverlapWithSegment(false, t.expected_overlap_ratios));
    }
  }
  {
    const std::vector<TestCase> testcases = {
        {{1.0, 5.0}, {1.0, 5.0}, {0.0}},
        {{1.0, 5.0}, {1.0, 7.0}, {0.0, 0.5}},
        {{1.0, 3.0}, {1.0, 8.0}, {0.2, 0.6}},
        {{1.0, 7.0}, {1.0, 8.0}, {}},
    };
    polygon_points0_ = {{1.0, 4.0}, {1.0, 6.0}};
    for (const auto& t : testcases) {
      segment_ = LineSegment2d(t.p0, t.p1);
      ASSERT_NO_FATAL_FAILURE(CheckGetOverlapWithSegment(false, t.expected_overlap_ratios));
    }
  }
  {
    const std::vector<TestCase> testcases = {
        {{4.0, 4.0}, {4.0, 4.0}, {0.0}},      {{8.0, 4.0}, {8.0, 4.0}, {0.0}},
        {{9.0, 4.0}, {9.0, 4.0}, {}},         {{7.0, 4.0}, {9.0, 4.0}, {0.0, 0.5}},
        {{7.0, 7.0}, {9.0, 9.0}, {0.0, 0.5}}, {{9.0, 9.0}, {7.0, 7.0}, {0.5, 1.0}},
        {{3.0, 3.0}, {6.0, 6.0}, {0.0, 1.0}}, {{0.0, 0.0}, {3.0, 0.0}, {0.0, 1.0}},
        {{7.0, 0.0}, {9.0, 0.0}, {0.0, 0.5}}, {{4.0, 9.0}, {8.0, 7.0}, {0.5, 1.0}},
    };
    polygon_points0_ = {
        {0.0, 0.0},
        {8.0, 0.0},
        {8.0, 8.0},
        {0.0, 8.0},
    };
    for (const auto& t : testcases) {
      segment_ = LineSegment2d(t.p0, t.p1);
      ASSERT_NO_FATAL_FAILURE(CheckGetOverlapWithSegment(true, t.expected_overlap_ratios));
    }
  }
  {
    for (int k = 0; k < 100; ++k) {
      polygon_points0_ = testing::MakeRandomConvexPolygon(testing::RandomInt(3, 20),
                                                          testing::RandomDouble(1.0, 10.0));
      for (int i = 0; i < 100; ++i) {
        segment_ = LineSegment2d(Vector2d(0.0, 0.0), testing::RandomPoint(10.0));
        ASSERT_NO_FATAL_FAILURE(CheckGetOverlapWithSegment(true));
      }
      const int num_points = polygon_points0_.size();
      for (int i = 0; i < num_points; ++i) {
        segment_ = LineSegment2d(polygon_points0_[i],
                                 polygon_points0_[convex_internal::Next(i, num_points)]);
        ASSERT_NO_FATAL_FAILURE(CheckGetOverlapWithSegment(true));
      }
    }
  }
  {
    for (int k = 0; k < 100; ++k) {
      polygon_points0_ =
          testing::MakeRandomPolygon(testing::RandomInt(3, 20), testing::RandomDouble(1.0, 10.0));
      for (int i = 0; i < 100; ++i) {
        segment_ = LineSegment2d(Vector2d(0.0, 0.0), testing::RandomPoint(10.0));
        ASSERT_NO_FATAL_FAILURE(CheckGetOverlapWithSegment(false));
      }
      const int num_points = polygon_points0_.size();
      for (int i = 0; i < num_points; ++i) {
        segment_ = LineSegment2d(polygon_points0_[i],
                                 polygon_points0_[convex_internal::Next(i, num_points)]);
        ASSERT_NO_FATAL_FAILURE(CheckGetOverlapWithSegment(false));
      }
    }
  }
}

TEST_F(ConvexOverlapTest, HasOverlapWithPolygon) {
  polygon_points0_ = {
      {+2.0, -0.5},
      {+2.0, +0.5},
      {+0.0, +0.5},
      {+0.0, -0.5},
  };
  polygon_points1_ = {
      {5.0, -0.5},
      {10.0, -1.0},
      {10.0, 3.0},
      {5.0, 0.5},
  };
  ASSERT_NO_FATAL_FAILURE(CheckHasOverlapWithPolygon(true));

  polygon_points1_ = testing::ShiftPoints(polygon_points1_, {-4.0, 0.0});
  ASSERT_NO_FATAL_FAILURE(CheckHasOverlapWithPolygon(true));
}

TEST_F(ConvexOverlapTest, HasOverlapRandom0) {
  for (int n = 0; n < 100; ++n) {
    polygon_points0_ = testing::MakeRandomConvexPolygon(testing::RandomInt(3, 20),
                                                        testing::RandomDouble(1.0, 10.0));
    polygon_points1_ = testing::MakeRandomConvexPolygon(testing::RandomInt(3, 20),
                                                        testing::RandomDouble(1.0, 10.0));
    ASSERT_NO_FATAL_FAILURE(CheckHasOverlapWithPolygon(true));
  }
}

TEST_F(ConvexOverlapTest, HasOverlapRandom1) {
  for (int n = 0; n < 100; ++n) {
    polygon_points0_ =
        testing::MakeRandomPolygon(testing::RandomInt(3, 20), testing::RandomDouble(1.0, 10.0));
    polygon_points1_ =
        testing::MakeRandomPolygon(testing::RandomInt(3, 20), testing::RandomDouble(1.0, 10.0));
    ASSERT_NO_FATAL_FAILURE(CheckHasOverlapWithPolygon(false));
  }
}

TEST_F(ConvexOverlapTest, ComputeOverlapConvex0) {
  {
    const std::vector<Vector2d> points0 = {
        {0.0, 0.0},
        {2.0, 0.0},
        {2.0, 2.0},
        {0.0, 2.0},
    };
    const std::vector<Vector2d> points1 = {
        {1.0, 1.0},
        {3.0, 1.0},
        {3.0, 3.0},
        {1.0, 3.0},
    };
    const std::vector<Vector2d> points2 = {
        {2.0, 0.0},
        {4.0, 0.0},
        {4.0, 2.0},
        {2.0, 2.0},
    };
    const std::vector<Vector2d> points3 = {
        {1.0, 2.0},
        {2.0, 1.0},
        {3.0, 2.0},
        {2.0, 3.0},
    };

    polygon_points0_ = points0;
    polygon_points1_ = points1;
    ASSERT_NO_FATAL_FAILURE(CheckComputeOverlapConvex(1.0));

    polygon_points0_ = points1;
    polygon_points1_ = points2;
    ASSERT_NO_FATAL_FAILURE(CheckComputeOverlapConvex(1.0));

    polygon_points0_ = points0;
    polygon_points1_ = points2;
    ASSERT_NO_FATAL_FAILURE(CheckComputeOverlapConvex(0.0));

    polygon_points0_ = points0;
    polygon_points1_ = points3;
    ASSERT_NO_FATAL_FAILURE(CheckComputeOverlapConvex(0.5));

    polygon_points0_ = points1;
    polygon_points1_ = points3;
    ASSERT_NO_FATAL_FAILURE(CheckComputeOverlapConvex(2.0));

    polygon_points0_ = points2;
    polygon_points1_ = points3;
    ASSERT_NO_FATAL_FAILURE(CheckComputeOverlapConvex(0.5));
  }
  {
    polygon_points0_ = {{0.0, 0.0}, {4.0, 0.0}, {0.0, 4.0}};
    polygon_points1_ = {{1.0, 1.0}, {3.0, 1.0}, {1.0, 3.0}};
    ASSERT_NO_FATAL_FAILURE(CheckComputeOverlapConvex(2.0));
    polygon_points1_ = {{1.0, 1.0}, {1.0, 3.0}, {-1.0, -1.0}};
    ASSERT_NO_FATAL_FAILURE(CheckComputeOverlapConvex(1.5));
    polygon_points1_ = {{2.0, 1.0}, {2.0, 4.0}, {-1.0, +1.0}};
    ASSERT_NO_FATAL_FAILURE(CheckComputeOverlapConvex(3.0));
    polygon_points1_ = {{3.0, 1.0}, {3.0, 5.0}, {-1.0, +1.0}};
    ASSERT_NO_FATAL_FAILURE(CheckComputeOverlapConvex(3.5));
    polygon_points1_ = {{4.0, 1.0}, {4.0, 6.0}, {-1.0, +1.0}};
    ASSERT_NO_FATAL_FAILURE(CheckComputeOverlapConvex(3.5));
  }
}

TEST_F(ConvexOverlapTest, ComputeOverlapConvex1) {
  {
    // +------------+
    // |            |
    // |    +-------+
    // |    |       |
    // |    +-------+
    // |            |
    // +------------+
    polygon_points0_ = {
        {0.0, 0.0},
        {4.0, 0.0},
        {4.0, 4.0},
        {0.0, 4.0},
    };
    polygon_points1_ = {
        {1.0, 1.0},
        {4.0, 1.0},
        {4.0, 2.0},
        {1.0, 2.0},
    };
    ASSERT_NO_FATAL_FAILURE(CheckComputeOverlapConvex(3.0, polygon_points1_));

    polygon_points1_ = {
        {1.0, 1.0},
        {4.0, 1.0},
        {1.0, 2.0},
    };
    ASSERT_NO_FATAL_FAILURE(CheckComputeOverlapConvex(1.5, polygon_points1_));
  }
  {
    // +------------+
    // |            |
    // |    +-------+
    // |    |       |
    // |    |       |
    // |    |       |
    // +----|-------|
    //      |       |
    //      +-------+
    polygon_points0_ = {
        {0.0, 1.0},
        {4.0, 1.0},
        {4.0, 4.0},
        {0.0, 4.0},
    };
    polygon_points1_ = {
        {1.0, 0.0},
        {4.0, 0.0},
        {4.0, 3.0},
        {1.0, 3.0},
    };
    const std::vector<Vector2d> expected_overlap_points = {
        {1.0, 1.0},
        {4.0, 1.0},
        {4.0, 3.0},
        {1.0, 3.0},
    };
    ASSERT_NO_FATAL_FAILURE(CheckComputeOverlapConvex(6.0, expected_overlap_points));
  }
  {
    // +------------+
    // |            |
    // +----+-------------+
    //      |             |
    //      +-------------+
    polygon_points0_ = {
        {0.0, 1.0},
        {3.0, 1.0},
        {3.0, 2.0},
        {0.0, 2.0},
    };
    polygon_points1_ = {
        {1.0, 0.0},
        {4.0, 0.0},
        {4.0, 1.0},
        {1.0, 1.0},
    };
    ASSERT_NO_FATAL_FAILURE(CheckComputeOverlapConvex(0.0));

    polygon_points1_ = {
        {1.0, 2.0},
        {2.0, 2.0},
        {2.0, 4.0},
        {1.0, 4.0},
    };
    ASSERT_NO_FATAL_FAILURE(CheckComputeOverlapConvex(0.0));
  }
  {
    // +--+--+
    // | / \ |
    // |/   \|
    // +     +
    // |\   /|
    // | \ / |
    // +--+--+
    polygon_points0_ = {
        {0.0, 0.0},
        {4.0, 0.0},
        {4.0, 4.0},
        {0.0, 4.0},
    };
    polygon_points1_ = {
        {0.0, 2.0},
        {2.0, 0.0},
        {4.0, 2.0},
        {2.0, 4.0},
    };
    ASSERT_NO_FATAL_FAILURE(CheckComputeOverlapConvex(8.0, polygon_points1_));
  }
  {
    //    +------+
    //   /|\     |
    //  / | \    |
    // +  +------+
    // |     |
    // +-----+
    polygon_points0_ = {
        {0.0, 0.0}, {4.0, 0.0}, {4.0, 2.0}, {2.0, 4.0}, {0.0, 2.0},
    };
    polygon_points1_ = {
        {2.0, 2.0},
        {6.0, 2.0},
        {6.0, 4.0},
        {2.0, 4.0},
    };
    const std::vector<Vector2d> expected_overlap_points = {
        {2.0, 2.0},
        {4.0, 2.0},
        {2.0, 4.0},
    };
    ASSERT_NO_FATAL_FAILURE(CheckComputeOverlapConvex(2.0, expected_overlap_points));
  }
}

TEST_F(ConvexOverlapTest, ComputeOverlapConvex2) {
  {
    // https://km.sankuai.com/page/1268318046
    polygon_points0_ = {
        {470116.23196699674, 4446645.1316624591}, {470116.46756207233, 4446645.1215051413},
        {470117.94728714513, 4446645.1121197594}, {470118.1194837332, 4446645.1117906198},
        {470118.4095155024, 4446645.1115087317},  {470120.99200583599, 4446645.1122494712},
        {470124.35673073324, 4446645.1139550544}, {470129.46379679797, 4446645.122752226},
        {470133.88352547481, 4446645.1393845323}, {470134.12939485093, 4446645.1456219601},
        {470134.21576075931, 4446645.1985338256}, {470133.86114417948, 4446645.1997741433},
        {470133.42673201842, 4446645.1999929259}, {470125.93288966041, 4446645.1999996714},
        {470118.20710028568, 4446645.1999350898}, {470117.71189158538, 4446645.1999023296},
        {470116.32617260632, 4446645.1939878734}, {470116.24942267139, 4446645.1796367439},
    };
    polygon_points1_ = {
        {470116.23999270465, 4446645.1245851424}, {470116.46184425172, 4446645.1225926401},
        {470118.32851924101, 4446645.1088085258}, {470124.33782804274, 4446645.1081435075},
        {470134.08299967513, 4446645.1351171043}, {470134.13427028968, 4446645.1469630105},
        {470134.20433789433, 4446645.1885089651}, {470134.20243395655, 4446645.1996946791},
        {470132.95133537642, 4446645.199924659},  {470132.02277769527, 4446645.1999873035},
        {470131.08339568507, 4446645.1999971299}, {470130.83369482827, 4446645.1999973496},
        {470118.34847433912, 4446645.1999994162}, {470118.32564757485, 4446645.1999963783},
        {470116.29912126123, 4446645.1991082979}, {470116.24377285206, 4446645.1807353981},
    };
    ASSERT_NO_FATAL_FAILURE(CheckComputeOverlapConvex());

    std::swap(polygon_points0_, polygon_points1_);
    ASSERT_NO_FATAL_FAILURE(CheckComputeOverlapConvex());
  }
  {
    polygon_points0_ = {
        {588784.50875117, 4074495.98043629},
        {588784.95639476, 4074496.20318101},
        {588784.73365004, 4074496.65082461},
        {588784.28600644, 4074496.42807988},
    };
    polygon_points1_ = {
        {588784.00121380, 4074496.58240088},
        {588784.23839054, 4074495.81836914},
        {588785.00225303, 4074496.05545588},
        {588784.76507629, 4074496.81948762},
    };
    ASSERT_NO_FATAL_FAILURE(CheckComputeOverlapConvex(0.25, polygon_points0_));

    std::swap(polygon_points0_, polygon_points1_);
    ASSERT_NO_FATAL_FAILURE(CheckComputeOverlapConvex(0.25, polygon_points1_));
  }
  {
    // +-------+--+
    // |      / \ |
    // |     /   \|
    // |    +     +
    // |     \   /|
    // |      \ / |
    // +-------+--+
    const std::vector<Vector2d> points0 = {
        {0.0, 0.0},
        {4.0, 0.0},
        {4.0, 4.0},
        {0.0, 4.0},
    };
    const std::vector<Vector2d> points1 = {
        {0.0, 2.0},
        {2.0, 0.0},
        {4.0, 2.0},
        {2.0, 4.0},
    };
    // +----------+
    // |          |
    // |          |
    // +----+     |
    // |    |     |
    // |    |     |
    // +----+-----+
    const std::vector<Vector2d> points2 = {
        {0.0, 0.0},
        {2.0, 0.0},
        {2.0, 2.0},
        {0.0, 2.0},
    };
    // +----------+
    // |          |
    // |          |
    // |          +----+
    // |          |    |
    // |          |    |
    // +----------+----+
    const std::vector<Vector2d> points3 = {
        {4.0, 0.0},
        {6.0, 0.0},
        {6.0, 2.0},
        {4.0, 2.0},
    };
    polygon_points0_ = points0;
    polygon_points1_ = points1;
    ASSERT_NO_FATAL_FAILURE(CheckComputeOverlapConvex(8.0, points1));
    polygon_points1_ = points2;
    ASSERT_NO_FATAL_FAILURE(CheckComputeOverlapConvex(4.0, points2));
    polygon_points1_ = points3;
    ASSERT_NO_FATAL_FAILURE(CheckComputeOverlapConvex(0.0));

    const std::vector<Vector2d> directions = {
        {+1.0, +0.0},
        {+0.0, +1.0},
        {-1.0, +0.0},
        {+0.0, -1.0},
    };
    for (const std::vector<Vector2d>& points_i : {points1, points2, points3}) {
      for (const Vector2d& d : directions) {
        for (int k = 10; k >= 1; --k) {
          const double offset = std::pow(0.1, k);
          polygon_points0_ = points0;
          polygon_points1_ = testing::ShiftPoints(points_i, d * offset);
          ASSERT_NO_FATAL_FAILURE(CheckComputeOverlapConvex());
          std::swap(polygon_points0_, polygon_points1_);
          ASSERT_NO_FATAL_FAILURE(CheckComputeOverlapConvex());
        }
      }
    }
  }
}

TEST_F(ConvexOverlapTest, ComputeOverlapConvexRandom0) {
  for (int n = 0; n < 5000; ++n) {
    polygon_points0_ = testing::MakeRandomConvexPolygon(testing::RandomInt(3, 20),
                                                        testing::RandomDouble(1.0, 10.0));
    polygon_points1_ = testing::MakeRandomConvexPolygon(testing::RandomInt(3, 20),
                                                        testing::RandomDouble(1.0, 10.0));
    ASSERT_NO_FATAL_FAILURE(CheckComputeOverlapConvex());
    polygon_points1_ = testing::ShiftPoints(polygon_points1_, testing::RandomPoint(50.0));
    ASSERT_NO_FATAL_FAILURE(CheckComputeOverlapConvex());
  }
}

TEST_F(ConvexOverlapTest, ComputeOverlapConvexRandom1) {
  for (int n = 0; n < 5000; ++n) {
    const std::vector<Vector2d> convex_points0 = testing::MakeRandomConvexPolygon(
        testing::RandomInt(3, 20), testing::RandomDouble(1.0, 10.0));
    const std::vector<Vector2d> convex_points1 = testing::MakeRandomConvexPolygon(
        testing::RandomInt(3, 20), testing::RandomDouble(1.0, 10.0));
    for (int i = 0; i < 10; ++i) {
      polygon_points0_ = testing::ShiftPoints(convex_points0, testing::RandomPoint(10.0));
      polygon_points1_ = testing::ShiftPoints(convex_points1, testing::RandomPoint(10.0));
      ASSERT_NO_FATAL_FAILURE(CheckComputeOverlapConvex());
    }
  }
}

}  // namespace convex
}  // namespace math_internal
}  // namespace math
