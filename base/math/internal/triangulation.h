// Copyright @2023 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <vector>

#include "base/container/span.h"
#include "base/math/vector2.h"

namespace math {
namespace math_internal {
namespace triangulation {

// https://en.wikipedia.org/wiki/Polygon_triangulation
// https://web.ntnu.edu.tw/~algo/Triangulation.html
// https://www.geometrictools.com/Documentation/TriangulationByEarClipping.pdf
// Triangulate simple polygon with ear-clipping method.
//  - simple polygon: a polygon that does not intersect itself and has no holes.
// The input points is required to be sorted in counter-clockwise order.
// Complexity is O(N^3).
std::vector<Vector2d> TriangulatePolygon(const base::ConstSpan<Vector2d>& points_in_ccw_order,
                                         bool is_convex);
std::vector<int> TriangulatePolygonAsIndex(const base::ConstSpan<Vector2d>& points_in_ccw_order,
                                           bool is_convex);

}  // namespace triangulation
}  // namespace math_internal
}  // namespace math
