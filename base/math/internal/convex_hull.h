// Copyright @2022 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <vector>

#include "base/container/span.h"
#include "base/math/vector2.h"

namespace math {
namespace math_internal {
namespace convex {

// Check if the given polygon is strictly convex and not self-intersecting.
// The input points is required to be sorted in counter-clockwise order.
bool IsConvexHull(const base::ConstSpan<Vector2d>& points);

// http://web.ntnu.edu.tw/~algo/ConvexHull.html
// Build strictly convex-hull with <PERSON>'s Monotone Chain algorithm.
std::vector<Vector2d> ComputeConvexHull(std::vector<Vector2d> points,
                                        bool check_convex_first = false);

}  // namespace convex
}  // namespace math_internal
}  // namespace math
