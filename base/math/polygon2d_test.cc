// Copyright @2022 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>
//          <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)

#include "base/math/polygon2d.h"

#include <algorithm>
#include <chrono>
#include <limits>
#include <random>
#include <string>
#include <utility>

#include "gmock/gmock.h"
#include "gtest/gtest.h"

#include "base/common/optional.h"
#include "base/math/line_segment2d.h"
#include "base/math/math_util.h"
#include "base/math/oriented_box2d.h"
#include "base/math/vector2.h"
#include "base/testing/boost_geometry.h"
#include "base/testing/geometry.h"
#include "base/testing/random.h"

// TODO(wangnaizheng): refactor this file.
namespace math {
namespace {

constexpr int Next(int i, int n) { return i != n - 1 ? i + 1 : 0; }

bool ProjectByXSlow(const std::vector<Vector2d>& points, double x, double* const min_y,
                    double* const max_y) {
  *min_y = std::numeric_limits<double>::infinity();
  *max_y = -std::numeric_limits<double>::infinity();
  for (const Vector2d& p1 : points) {
    if (p1.x < x) {
      for (const Vector2d& p2 : points) {
        if (p2.x > x) {
          const double y = ((p2.x - x) * p1.y + (x - p1.x) * p2.y) / (p2.x - p1.x);
          *min_y = std::min(*min_y, y);
          *max_y = std::max(*max_y, y);
        }
      }
    }
  }
  return *min_y <= *max_y;
}

Polygon2d GetRandomConvexPolygon(double radius) {
  double angle = 0.0;
  std::vector<math::Vector2d> points;
  while (true) {
    unsigned seed = 500;
    std::mt19937 random_gen(seed);
    std::uniform_real_distribution<double> angle_distribution(M_PI * 2.0 / 50.0, M_PI * 2.0 / 10.0);
    angle += angle_distribution(random_gen);
    if (angle > M_PI * 2 - kEpsilon) {
      break;
    }
    points.emplace_back(radius * std::cos(angle), radius * std::sin(angle));
  }
  return Polygon2d(points);
}

}  // namespace

class Polygon2dTest : public ::testing::Test {
 protected:
  void CheckDistanceToSegment(const LineSegment2d& segment,
                              base::Optional<double> expected_distance = base::none) const {
    const testing::BoostPolygon2d boost_polygon =
        testing::ToBoostPolygonFromPoints(polygon_points0_);
    const testing::BoostLineSegment2d boost_segment = testing::ToBoost(segment);
    const double boost_distance = testing::BoostDistanceTo(boost_polygon, boost_segment);
    if (expected_distance) {
      ASSERT_NEAR(boost_distance, *expected_distance, 1e-6);
    }
    const Polygon2d polygon(polygon_points0_);
    ASSERT_NEAR(boost_distance, polygon.DistanceTo(segment), 1e-6);
  }

  void CheckComputeOverlapAreaWith(base::Optional<double> expected_area = base::none) const {
    const testing::BoostPolygon2d boost_polygon0 =
        testing::ToBoostPolygonFromPoints(polygon_points0_);
    const testing::BoostPolygon2d boost_polygon1 =
        testing::ToBoostPolygonFromPoints(polygon_points1_);
    const std::vector<testing::BoostPolygon2d> boost_overlaps =
        testing::BoostPolygonOverlap(boost_polygon0, boost_polygon1);
    double boost_area = 0.0;
    for (const auto& p : boost_overlaps) {
      boost_area += testing::BoostPolygonArea(p);
    }
    if (expected_area) {
      ASSERT_NEAR(boost_area, *expected_area, 1e-3);
    }
    Polygon2d polygon0(polygon_points0_);
    Polygon2d polygon1(polygon_points1_);
    ASSERT_NEAR(boost_area, polygon0.ComputeOverlapAreaWith(polygon1), 1e-3);
    ASSERT_NEAR(boost_area, polygon1.ComputeOverlapAreaWith(polygon0), 1e-3);
    SetConvex(false, &polygon0);
    ASSERT_NEAR(boost_area, polygon0.ComputeOverlapAreaWith(polygon1), 1e-3);
    ASSERT_NEAR(boost_area, polygon1.ComputeOverlapAreaWith(polygon0), 1e-3);
    SetConvex(false, &polygon1);
    ASSERT_NEAR(boost_area, polygon0.ComputeOverlapAreaWith(polygon1), 1e-3);
    ASSERT_NEAR(boost_area, polygon1.ComputeOverlapAreaWith(polygon0), 1e-3);
  }

  void SetConvex(bool is_convex, Polygon2d* polygon) const {
    CHECK_NOTNULL(polygon)->is_convex_ = is_convex;
  }

  std::vector<Vector2d> polygon_points0_;
  std::vector<Vector2d> polygon_points1_;
};

TEST_F(Polygon2dTest, DistanceToSegment) {
  struct TestCase {
    Vector2d segment_start;
    Vector2d segment_end;
    double expected_distance = 0.0;
  };
  {
    polygon_points0_ = {
        {0.0, 0.0},
        {1.0, 0.0},
        {1.0, 1.0},
        {0.0, 1.0},
    };
    const std::vector<TestCase> testcases = {
        {{+0.5, +0.5}, {+1.0, +1.0}, 0.0},
        {{-0.2, +0.5}, {+1.2, +0.5}, 0.0},
        {{-2.0, -2.0}, {+2.0, +2.0}, 0.0},
        {{-0.2, +0.5}, {-0.2, +0.8}, 0.2},
        {{+1.2, +0.5}, {+1.2, +0.3}, 0.2},
        {{+0.5, -0.2}, {+0.8, -0.2}, 0.2},
        {{+0.5, +1.2}, {+0.3, +1.2}, 0.2},
        {{-0.3, +0.5}, {-0.2, +0.8}, 0.2},
        {{+1.2, +0.5}, {+1.3, +0.3}, 0.2},
        {{+0.5, -0.3}, {+0.8, -0.2}, 0.2},
        {{+0.5, +1.2}, {+0.3, +1.3}, 0.2},
        {{+0.0, -0.1}, {-0.1, +0.0}, 0.1 / std::sqrt(2.0)},
        {{+1.0, +1.1}, {+1.1, +1.0}, 0.1 / std::sqrt(2.0)},
        {{-1.0, +2.0}, {-1.0, +2.0}, std::sqrt(2.0)},
        {{+0.5, +0.5}, {+0.5, +0.5}, 0.0},
        {{-10.0, +0.5}, {+2.0, +0.5}, 0.0},
        {{-1.0, +0.5}, {+10.0, +0.5}, 0.0},
    };
    for (const auto& t : testcases) {
      ASSERT_NO_FATAL_FAILURE(
          CheckDistanceToSegment({t.segment_start, t.segment_end}, t.expected_distance));
    }
  }
  {
    polygon_points0_ = {
        {+0.0, +1.0},
        {+1.0, +0.0},
        {+0.0, -1.0},
        {-1.0, +0.0},
    };
    const std::vector<TestCase> testcases = {
        {{-2.0, +0.0}, {+2.0, +0.0}, 0.0},
        {{+0.0, -2.0}, {+0.0, +2.0}, 0.0},
        {{+0.0, +1.1}, {+1.1, +0.0}, 0.1 / std::sqrt(2.0)},
        {{+0.0, +1.1}, {-1.1, +0.0}, 0.1 / std::sqrt(2.0)},
        {{+0.0, -1.1}, {+1.1, +0.0}, 0.1 / std::sqrt(2.0)},
        {{+0.0, -1.1}, {-1.1, +0.0}, 0.1 / std::sqrt(2.0)},
        {{+0.6, +0.6}, {+0.7, +0.7}, 0.1 * std::sqrt(2.0)},
        {{-0.6, -0.6}, {-0.7, -0.7}, 0.1 * std::sqrt(2.0)},
        {{-0.6, -0.6}, {+0.7, +0.7}, 0.0},
    };
    for (const auto& t : testcases) {
      ASSERT_NO_FATAL_FAILURE(
          CheckDistanceToSegment({t.segment_start, t.segment_end}, t.expected_distance));
    }
  }
  {
    polygon_points0_ = {
        {+0.0, +0.0}, {+2.0, +0.0}, {+2.0, +2.0}, {+1.0, +1.0}, {+0.0, +2.0},
    };
    const std::vector<TestCase> testcases = {
        {{-2.0, +0.0}, {+2.0, +0.0}, 0.0},
        {{+0.7, +2.0}, {+1.2, +2.0}, 0.7 / sqrt(2.0)},
        {{+0.7, +2.0}, {+1.4, +2.0}, 0.6 / sqrt(2.0)},
        {{+0.7, +1.5}, {+1.6, +1.5}, 0.0},
    };
    for (const auto& t : testcases) {
      ASSERT_NO_FATAL_FAILURE(
          CheckDistanceToSegment({t.segment_start, t.segment_end}, t.expected_distance));
    }
  }
  {
    polygon_points0_ = {{0.0, 0.0}, {10.0, 0.0}, {0.0, 10.0}};
    const std::vector<TestCase> testcases = {
        {{+1.0, +2.0}, {+2.0, +1.0}, 0.0},
        {{+3.0, +3.0}, {+10.0, +10.0}, 0.0},
        {{-2.0, +3.0}, {+10.0, +3.0}, 0.0},
        {{+3.0, +20.0}, {+3.0, +3.0}, 0.0},
        {{-2.0, +3.0}, {-3.0, +5.0}, 2.0},
        {{-3.0, +2.0}, {+2.0, -3.0}, 1.0 / std::sqrt(2.0)},
        {{+7.0, +7.0}, {+6.0, +6.0}, std::sqrt(2.0)},
        {{-2.0, +12.0}, {-1.0, +11.0}, std::sqrt(2.0)},
    };
    for (const auto& t : testcases) {
      ASSERT_NO_FATAL_FAILURE(
          CheckDistanceToSegment({t.segment_start, t.segment_end}, t.expected_distance));
    }
  }
  {
    for (int k = 0; k < 100; ++k) {
      polygon_points0_ = testing::MakeRandomConvexPolygon(testing::RandomInt(3, 20),
                                                          testing::RandomDouble(1.0, 10.0));
      for (int i = 0; i < 500; ++i) {
        ASSERT_NO_FATAL_FAILURE(CheckDistanceToSegment(testing::RandomSegment2d(20.0)));
      }
    }
  }
  {
    for (int k = 0; k < 100; ++k) {
      polygon_points0_ = testing::MakeRandomPolygon(testing::RandomInt(3, 20), 10.0);
      for (int i = 0; i < 500; ++i) {
        ASSERT_NO_FATAL_FAILURE(CheckDistanceToSegment(testing::RandomSegment2d(20.0)));
      }
    }
  }
}

TEST_F(Polygon2dTest, OverlapWithSegment) {
  {
    struct TestCase {
      Vector2d segment_start;
      Vector2d segment_end;
      std::vector<Vector2d> intersections;
    };
    const Polygon2d polygon({
        {0.0, 0.0},
        {2.0, 0.0},
        {2.0, 2.0},
        {0.0, 2.0},
    });
    const std::vector<TestCase> testcases = {
        {{-1.0, +0.0}, {-1.0, +2.0}, {}},
        {{-1.0, +1.0}, {-3.0, +1.0}, {}},
        {{+1.0, +3.0}, {+1.0, +5.0}, {}},
        {{+1.0, -1.0}, {+1.0, +3.0}, {{+1.0, +0.0}, {+1.0, +2.0}}},
        {{+1.0, +1.0}, {+1.0, +3.0}, {{+1.0, +1.0}, {+1.0, +2.0}}},
        {{+1.0, -1.0}, {+1.0, +1.0}, {{+1.0, +0.0}, {+1.0, +1.0}}},
        {{+1.0, +3.0}, {+3.0, +1.0}, {{+2.0, +2.0}}},
        {{+4.0, +3.0}, {+4.0, +3.0}, {}},
        {{+1.0, +1.0}, {+1.0, +1.0}, {{+1.0, +1.0}}},
        {{+2.0, -1.0}, {+2.0, +3.0}, {{+2.0, +0.0}, {+2.0, +2.0}}},
        {{+2.0, -1.0}, {+2.0, +1.0}, {{+2.0, +0.0}, {+2.0, +1.0}}},
        {{+2.0, -1.0}, {+2.0, +0.0}, {{+2.0, +0.0}}},
    };
    for (const auto& t : testcases) {
      Vector2d min_point;
      Vector2d max_point;
      if (t.intersections.empty()) {
        ASSERT_FALSE(polygon.GetOverlap(LineSegment2d(t.segment_start, t.segment_end), &min_point,
                                        &max_point));
      } else {
        ASSERT_TRUE(polygon.GetOverlap(LineSegment2d(t.segment_start, t.segment_end), &min_point,
                                       &max_point));
        ASSERT_TRUE(min_point.IsNear(t.intersections.front()));
        ASSERT_TRUE(max_point.IsNear(t.intersections.back()));
        ASSERT_TRUE(polygon.GetOverlap(LineSegment2d(t.segment_end, t.segment_start), &min_point,
                                       &max_point));
        ASSERT_TRUE(min_point.IsNear(t.intersections.back()));
        ASSERT_TRUE(max_point.IsNear(t.intersections.front()));
      }
    }
  }
  {
    struct TestCase {
      Vector2d segment_start;
      Vector2d segment_end;
      std::vector<std::vector<Vector2d>> segments;
    };
    //  +----+     +----+
    //  |     \   /     |
    //  |      \ /      |
    //  |       +       |
    //  |               |
    //  |               |
    //  +---------------+
    const Polygon2d polygon({
        {0.0, 0.0},
        {4.0, 0.0},
        {4.0, 2.0},
        {3.0, 2.0},
        {2.0, 1.0},
        {1.0, 2.0},
        {0.0, 2.0},
    });
    const std::vector<TestCase> testcases = {
        {{-10.0, +1.5},
         {+10.0, +1.5},
         {
             {{0.0, 1.5}, {1.5, 1.5}},
             {{2.5, 1.5}, {4.0, 1.5}},
         }},
        {{-10.0, +1.0},
         {+10.0, +1.0},
         {
             {{0.0, 1.0}, {4.0, 1.0}},
         }},
        {{-10.0, +0.5},
         {+10.0, +0.5},
         {
             {{0.0, 0.5}, {4.0, 0.5}},
         }},
        {{-10.0, -0.5}, {+10.0, -0.5}, {}},
        {{-10.0, +2.5}, {+10.0, +2.5}, {}},
        {{+2.0, +0.5},
         {+2.0, +0.5},
         {
             {{2.0, 0.5}, {2.0, 0.5}},
         }},
        {{+5.0, +0.5}, {+5.0, +0.5}, {}},
        {{+0.0, +0.0},
         {+1.0, +0.0},
         {
             {{0.0, 0.0}, {1.0, 0.0}},
         }},
        {{+0.0, +0.0},
         {+6.0, +0.0},
         {
             {{0.0, 0.0}, {4.0, 0.0}},
         }},
        {{+2.0, +1.0},
         {+3.0, +2.0},
         {
             {{2.0, 1.0}, {3.0, 2.0}},
         }},
        {{+1.0, +0.0},
         {+3.0, +2.0},
         {
             {{1.0, 0.0}, {3.0, 2.0}},
         }},
        {{+0.0, -1.0},
         {+4.0, +3.0},
         {
             {{1.0, 0.0}, {3.0, 2.0}},
         }},
        {{+0.0, +2.0},
         {+1.0, +2.0},
         {
             {{0.0, 2.0}, {1.0, 2.0}},
         }},
        {{+0.0, +2.0},
         {+3.0, +2.0},
         {
             {{0.0, 2.0}, {1.0, 2.0}},
             {{3.0, 2.0}, {3.0, 2.0}},
         }},
        {{+1.0, +2.0},
         {+3.0, +2.0},
         {
             {{1.0, 2.0}, {1.0, 2.0}},
             {{3.0, 2.0}, {3.0, 2.0}},
         }},
    };
    for (const auto& t : testcases) {
      const LineSegment2d segment(t.segment_start, t.segment_end);
      const std::vector<LineSegment2d> overlaps = polygon.GetAllOverlaps(segment);
      Vector2d min_point;
      Vector2d max_point;
      if (t.segments.empty()) {
        ASSERT_TRUE(overlaps.empty());
        ASSERT_FALSE(polygon.GetOverlap(segment, &min_point, &max_point));
      } else {
        ASSERT_EQ(overlaps.size(), t.segments.size());
        for (int i = 0; i < static_cast<int>(overlaps.size()); ++i) {
          for (int j = 0; j < 2; ++j) {
            ASSERT_TRUE(overlaps[i].endpoints()[j].IsNear(t.segments[i][j]));
          }
        }
        ASSERT_TRUE(polygon.GetOverlap(segment, &min_point, &max_point));
        ASSERT_TRUE(min_point.IsNear(t.segments.front().front()));
        ASSERT_TRUE(max_point.IsNear(t.segments.back().back()));
      }
    }
  }
  // TODO(wangnaizheng): add more testcases.
}

TEST_F(Polygon2dTest, ContainsWithSegment) {
  {
    const Polygon2d polygon0({
        {+0.0, +0.0},
        {-1.0, +1.0},
        {+0.0, -1.0},
        {+1.0, +1.0},
    });
    ASSERT_FALSE(polygon0.Contains({{0.0, -0.5}, {0.0, 0.75}}));
    ASSERT_FALSE(polygon0.Contains({{1.0, 0.0}, {-5.0, 1.0}}));
    ASSERT_TRUE(polygon0.Contains({{0.0, -1.0}, {0.5, 0.5}}));
  }
  {
    const Polygon2d polygon1({
        {+0.0, +0.0},
        {+1.0, +1.0},
        {-1.0, +1.0},
        {-1.0, -1.0},
        {+1.0, -1.0},
    });
    ASSERT_TRUE(polygon1.Contains({{-0.5, -0.5}, {-0.5, 0.5}}));
    ASSERT_FALSE(polygon1.Contains({{3.0, 2.0}, {-2.0, 3.0}}));
  }
  {
    const Polygon2d polygon2({
        {+1.0, +0.0},
        {+2.0, +1.0},
        {-2.0, +1.0},
        {-1.0, +0.0},
        {-2.0, -1.0},
        {+2.0, -1.0},
    });
    ASSERT_TRUE(polygon2.Contains({{0.0, 0.0}, {1.0, 0.0}}));
  }
  {
    const Polygon2d polygon3({
        {+0.0, +0.0},
        {+0.0, -1.0},
        {+1.0, -1.0},
        {+1.0, +1.0},
        {-2.0, +1.0},
        {-2.0, -1.0},
        {-1.0, -1.0},
        {-1.0, +0.0},
    });
    ASSERT_FALSE(polygon3.Contains({{10.0, 8.5}, {1.2, 3.4}}));
    ASSERT_FALSE(polygon3.Contains({{2.0, 5.5}, {-2.0, -1.0}}));
  }
}

TEST_F(Polygon2dTest, ContainsWithPolygon) {
  {
    const Polygon2d polygon0({{0.0, 0.0}, {10.0, 0.0}, {0.0, 10.0}});
    const Polygon2d polygon1({
        {+0.0, +0.0},
        {+2.0, +0.0},
        {+2.0, +2.0},
        {+1.0, +1.0},
        {+0.0, +2.0},
    });
    const Polygon2d polygon2({
        {-5.0, -5.0},
        {-5.0, +5.0},
        {+5.0, +5.0},
        {+5.0, -5.0},
    });
    ASSERT_TRUE(polygon0.Contains(polygon1));
    ASSERT_FALSE(polygon0.Contains(polygon2));
    ASSERT_FALSE(polygon1.Contains(polygon0));
    ASSERT_FALSE(polygon1.Contains(polygon2));
    ASSERT_TRUE(polygon2.Contains(polygon1));
    ASSERT_FALSE(polygon2.Contains(polygon0));
  }
  {
    const Polygon2d polygon0({
        {+3.0, +0.0},
        {+3.0, +3.0},
        {+0.0, +3.0},
        {+0.0, +0.0},
    });
    const Polygon2d polygon1({
        {+2.0, +1.0},
        {+2.0, +2.0},
        {+1.0, +2.0},
        {+1.0, +1.0},
    });
    const Polygon2d polygon2({
        {+4.0, +1.5},
        {+4.0, +4.0},
        {+1.5, +4.0},
        {+1.5, +1.5},
    });
    const Polygon2d polygon3({
        {+10.0, -10.0},
        {+10.0, +10.0},
        {-10.0, +10.0},
        {-10.0, -10.0},
    });
    ASSERT_TRUE(polygon0.Contains(polygon1));
    ASSERT_FALSE(polygon0.Contains(polygon2));
    ASSERT_FALSE(polygon1.Contains(polygon0));
    ASSERT_FALSE(polygon1.Contains(polygon2));
    ASSERT_FALSE(polygon2.Contains(polygon0));
    ASSERT_FALSE(polygon2.Contains(polygon1));
    ASSERT_TRUE(polygon3.Contains(polygon0));
    ASSERT_TRUE(polygon3.Contains(polygon1));
    ASSERT_TRUE(polygon3.Contains(polygon2));
  }
  {
    const Polygon2d polygon0({
        {+0.0, +0.0},
        {+4.0, +0.0},
        {+4.0, +2.0},
        {+3.0, +2.0},
        {+2.0, +1.0},
        {+1.0, +2.0},
        {+0.0, +2.0},
    });
    const Polygon2d polygon1({
        {+0.0, +1.0},
        {+4.0, +1.0},
        {+4.0, +2.0},
        {+0.0, +2.0},
    });
    const Polygon2d polygon2({
        {+0.0, +1.0},
        {+1.0, +1.0},
        {+1.0, +2.0},
        {+0.0, +2.0},
    });
    const Polygon2d polygon3({
        {+3.0, +1.0},
        {+4.0, +1.0},
        {+4.0, +2.0},
        {+3.0, +2.0},
    });
    const Polygon2d polygon4({
        {+0.0, +0.0},
        {+4.0, +0.0},
        {+4.0, +1.0},
        {+0.0, +1.0},
    });
    ASSERT_FALSE(polygon0.Contains(polygon1));
    ASSERT_TRUE(polygon0.Contains(polygon2));
    ASSERT_TRUE(polygon0.Contains(polygon3));
    ASSERT_TRUE(polygon0.Contains(polygon4));
  }
}

TEST_F(Polygon2dTest, ComputeOverlapAreaWith0) {
  {
    const std::vector<Vector2d> points0 = {
        {0.0, 0.0},
        {2.0, 0.0},
        {2.0, 2.0},
        {0.0, 2.0},
    };
    const std::vector<Vector2d> points1 = {
        {1.0, 1.0},
        {3.0, 1.0},
        {3.0, 3.0},
        {1.0, 3.0},
    };
    const std::vector<Vector2d> points2 = {
        {2.0, 0.0},
        {4.0, 0.0},
        {4.0, 2.0},
        {2.0, 2.0},
    };
    const std::vector<Vector2d> points3 = {
        {1.0, 2.0},
        {2.0, 1.0},
        {3.0, 2.0},
        {2.0, 3.0},
    };

    polygon_points0_ = points0;
    polygon_points1_ = points1;
    ASSERT_NO_FATAL_FAILURE(CheckComputeOverlapAreaWith(1.0));

    polygon_points0_ = points1;
    polygon_points1_ = points2;
    ASSERT_NO_FATAL_FAILURE(CheckComputeOverlapAreaWith(1.0));

    polygon_points0_ = points0;
    polygon_points1_ = points2;
    ASSERT_NO_FATAL_FAILURE(CheckComputeOverlapAreaWith(0.0));

    polygon_points0_ = points0;
    polygon_points1_ = points3;
    ASSERT_NO_FATAL_FAILURE(CheckComputeOverlapAreaWith(0.5));

    polygon_points0_ = points1;
    polygon_points1_ = points3;
    ASSERT_NO_FATAL_FAILURE(CheckComputeOverlapAreaWith(2.0));

    polygon_points0_ = points2;
    polygon_points1_ = points3;
    ASSERT_NO_FATAL_FAILURE(CheckComputeOverlapAreaWith(0.5));
  }
  {
    polygon_points0_ = {{0.0, 0.0}, {4.0, 0.0}, {0.0, 4.0}};
    polygon_points1_ = {{1.0, 1.0}, {3.0, 1.0}, {1.0, 3.0}};
    ASSERT_NO_FATAL_FAILURE(CheckComputeOverlapAreaWith(2.0));
    polygon_points1_ = {{1.0, 1.0}, {1.0, 3.0}, {-1.0, -1.0}};
    ASSERT_NO_FATAL_FAILURE(CheckComputeOverlapAreaWith(1.5));
    polygon_points1_ = {{2.0, 1.0}, {2.0, 4.0}, {-1.0, +1.0}};
    ASSERT_NO_FATAL_FAILURE(CheckComputeOverlapAreaWith(3.0));
    polygon_points1_ = {{3.0, 1.0}, {3.0, 5.0}, {-1.0, +1.0}};
    ASSERT_NO_FATAL_FAILURE(CheckComputeOverlapAreaWith(3.5));
    polygon_points1_ = {{4.0, 1.0}, {4.0, 6.0}, {-1.0, +1.0}};
    ASSERT_NO_FATAL_FAILURE(CheckComputeOverlapAreaWith(3.5));
  }
}

TEST_F(Polygon2dTest, ComputeOverlapAreaWith1) {
  {
    polygon_points0_ = {
        {0.0, 0.0},
        {4.0, 0.0},
        {4.0, 4.0},
        {0.0, 4.0},
    };
    polygon_points1_ = {
        {1.0, 1.0},
        {4.0, 1.0},
        {4.0, 2.0},
        {1.0, 2.0},
    };
    ASSERT_NO_FATAL_FAILURE(CheckComputeOverlapAreaWith(3.0));

    polygon_points1_ = {
        {1.0, 1.0},
        {4.0, 1.0},
        {1.0, 2.0},
    };
    ASSERT_NO_FATAL_FAILURE(CheckComputeOverlapAreaWith(1.5));
  }
  {
    polygon_points0_ = {
        {0.0, 1.0},
        {4.0, 1.0},
        {4.0, 4.0},
        {0.0, 4.0},
    };
    polygon_points1_ = {
        {1.0, 0.0},
        {4.0, 0.0},
        {4.0, 3.0},
        {1.0, 3.0},
    };
    const std::vector<Vector2d> expected_overlap_points = {
        {1.0, 1.0},
        {4.0, 1.0},
        {4.0, 3.0},
        {1.0, 3.0},
    };
    ASSERT_NO_FATAL_FAILURE(CheckComputeOverlapAreaWith(6.0));
  }
  {
    polygon_points0_ = {
        {0.0, 1.0},
        {3.0, 1.0},
        {3.0, 2.0},
        {0.0, 2.0},
    };
    polygon_points1_ = {
        {1.0, 0.0},
        {4.0, 0.0},
        {4.0, 1.0},
        {1.0, 1.0},
    };
    ASSERT_NO_FATAL_FAILURE(CheckComputeOverlapAreaWith(0.0));

    polygon_points1_ = {
        {1.0, 2.0},
        {2.0, 2.0},
        {2.0, 4.0},
        {1.0, 4.0},
    };
    ASSERT_NO_FATAL_FAILURE(CheckComputeOverlapAreaWith(0.0));
  }
  {
    polygon_points0_ = {
        {0.0, 0.0},
        {4.0, 0.0},
        {4.0, 4.0},
        {0.0, 4.0},
    };
    polygon_points1_ = {
        {0.0, 2.0},
        {2.0, 0.0},
        {4.0, 2.0},
        {2.0, 4.0},
    };
    ASSERT_NO_FATAL_FAILURE(CheckComputeOverlapAreaWith(8.0));
  }
  {
    polygon_points0_ = {
        {0.0, 0.0}, {4.0, 0.0}, {4.0, 2.0}, {2.0, 4.0}, {0.0, 2.0},
    };
    polygon_points1_ = {
        {2.0, 2.0},
        {6.0, 2.0},
        {6.0, 4.0},
        {2.0, 4.0},
    };
    const std::vector<Vector2d> expected_overlap_points = {
        {2.0, 2.0},
        {4.0, 2.0},
        {2.0, 4.0},
    };
    ASSERT_NO_FATAL_FAILURE(CheckComputeOverlapAreaWith(2.0));
  }
}

// TODO(wangnaizheng): add UT for random-concave polygons.
TEST_F(Polygon2dTest, ComputeOverlapAreaWithRandom) {
  for (int n = 0; n < 1000; ++n) {
    polygon_points0_ = testing::MakeRandomConvexPolygon(testing::RandomInt(3, 20),
                                                        testing::RandomDouble(1.0, 10.0));
    polygon_points1_ = testing::MakeRandomConvexPolygon(testing::RandomInt(3, 20),
                                                        testing::RandomDouble(1.0, 10.0));
    ASSERT_NO_FATAL_FAILURE(CheckComputeOverlapAreaWith());
    polygon_points1_ = testing::ShiftPoints(polygon_points1_, testing::RandomPoint(50.0));
    ASSERT_NO_FATAL_FAILURE(CheckComputeOverlapAreaWith());
  }
}

TEST_F(Polygon2dTest, DistanceToBox) {}

TEST_F(Polygon2dTest, SelfIntersectTest1) {
  // Create a star-like polygon.
  std::vector<math::Vector2d> points = {
      {2.0, 5.0}, {8.0, 5.0}, {3.0, 1.0}, {5.0, 7.0}, {7.0, 1.0},
  };
  const Polygon2d polygon(points);
  EXPECT_FALSE(polygon.is_convex());
}

TEST_F(Polygon2dTest, SelfIntersectTest2) {
  // Create a star-like polygon.
  std::vector<math::Vector2d> points = {
      {8.0, 5.0}, {2.0, 5.0}, {7.0, 1.0}, {5.0, 4.0}, {3.0, 1.0},
  };
  const Polygon2d polygon(points);
  EXPECT_FALSE(polygon.is_convex());
}

TEST_F(Polygon2dTest, DistanceToPolygon) {
  const Polygon2d poly1(OrientedBox2d::FromAABox({0, 0}, {1, 1}));
  const Polygon2d poly2({{0, 1}, {1, 0}, {0, -1}, {-1, 0}});
  const Polygon2d poly3(OrientedBox2d::FromAABox({2, 2}, {3, 3}));
  const Polygon2d poly4(OrientedBox2d::FromAABox({-10, -10}, {10, 10}));

  EXPECT_NEAR(poly1.DistanceTo(poly2), 0.0, 1e-5);
  EXPECT_NEAR(poly1.DistanceTo(poly3), sqrt(2.0), 1e-5);
  EXPECT_NEAR(poly2.DistanceTo(poly3), 1.5 * sqrt(2.0), 1e-5);
  EXPECT_NEAR(poly4.DistanceTo(poly1), 0.0, 1e-5);
  EXPECT_NEAR(poly4.DistanceTo(poly2), 0.0, 1e-5);
  EXPECT_NEAR(poly4.DistanceTo(poly3), 0.0, 1e-5);
}

TEST_F(Polygon2dTest, DistanceToPolygonInDirection) {
  const Polygon2d polygon1({{0.0, 0.0}, {2.0, 0.0}, {2.0, 2.0}, {0.0, 2.0}});
  const Polygon2d polygon2({{0.0, 5.0}, {2.0, 5.0}, {2.0, 7.0}, {0.0, 7.0}});
  const Vector2d direction1 = {0, 1};
  const Vector2d direction2 = {0, 1};
  const Vector2d direction3 = {1, 0};
  const Vector2d direction4 = {1, 1};
  const Vector2d direction5 = {-1, -1};

  const double distance1 = polygon1.DistanceTo(polygon2, direction1.Normalized());
  const double distance2 = polygon1.DistanceTo(polygon2, direction2.Normalized());
  const double distance3 = polygon1.DistanceTo(polygon2, direction3.Normalized());
  const double distance4 = polygon1.DistanceTo(polygon2, direction4.Normalized());
  const double distance5 = polygon1.DistanceTo(polygon2, direction5.Normalized());
  EXPECT_DOUBLE_EQ(distance1, 3.0);
  EXPECT_DOUBLE_EQ(distance2, 3.0);
  EXPECT_DOUBLE_EQ(distance3, 0.0);
  EXPECT_DOUBLE_EQ(distance4, sqrt(12.5) - 2 * sqrt(2));
  EXPECT_DOUBLE_EQ(distance5, sqrt(12.5) - 2 * sqrt(2));
}

TEST_F(Polygon2dTest, ConvexHull) {
  Polygon2d polygon;
  EXPECT_FALSE(Polygon2d::ComputeConvexHull({}, &polygon));
  EXPECT_FALSE(Polygon2d::ComputeConvexHull({{1, 2}}, &polygon));
  EXPECT_FALSE(Polygon2d::ComputeConvexHull({{3, 4}, {5, 6}}, &polygon));
  EXPECT_FALSE(Polygon2d::ComputeConvexHull({{3, 4}, {3, 4}, {5, 6}, {5, 6}}, &polygon));

  EXPECT_TRUE(Polygon2d::ComputeConvexHull({{0, 0}, {0, 4}, {3, 0}}, &polygon));
  EXPECT_TRUE(polygon.is_convex());
  EXPECT_NEAR(6.0, polygon.area(), 1e-5);

  EXPECT_TRUE(Polygon2d::ComputeConvexHull({{0, 0}, {0, 4}, {3, 0}, {3, 4}}, &polygon));
  EXPECT_TRUE(polygon.is_convex());
  EXPECT_NEAR(12.0, polygon.area(), 1e-5);

  EXPECT_TRUE(
      Polygon2d::ComputeConvexHull({{0, 0}, {2, 2}, {1, 1}, {0, 4}, {3, 0}, {3, 4}}, &polygon));
  EXPECT_TRUE(polygon.is_convex());
  EXPECT_NEAR(12.0, polygon.area(), 1e-5);

  EXPECT_TRUE(Polygon2d::ComputeConvexHull(
      {{0, 0}, {0, 4}, {0, 1}, {0, 3}, {0, 2}, {1, 0}, {3, 0}, {2, 0}}, &polygon));
  EXPECT_TRUE(polygon.is_convex());
  EXPECT_NEAR(6.0, polygon.area(), 1e-5);

  for (int iter = 0; iter < 10000; ++iter) {
    const int kRange = 10;
    const int n = testing::RandomInt(3, 10);
    std::vector<Vector2d> points;
    for (int i = 0; i < n; ++i) {
      points.emplace_back(testing::RandomInt(0, kRange), testing::RandomInt(0, kRange));
    }
    double area = 0;
    for (int x0 = 0; x0 < kRange; ++x0) {
      double min_y = 0.0;
      double max_y = 0.0;
      if (ProjectByXSlow(points, x0 + 0.5, &min_y, &max_y)) {
        area += max_y - min_y;
      }
    }
    Polygon2d polygon;
    if (area < 1e-3) {
      EXPECT_FALSE(Polygon2d::ComputeConvexHull(points, &polygon));
    } else {
      EXPECT_TRUE(Polygon2d::ComputeConvexHull(points, &polygon));
      EXPECT_NEAR(area, polygon.area(), 1e-5);
    }
  }

  {
    const double testing_y = 10.0;
    const int random_min_x = -10;
    const int random_max_x = 10;
    const int scale_x = 10;
    for (int iter = 0; iter < 10000; ++iter) {
      std::vector<Vector2d> points;
      points.emplace_back(0.0, 0.0);
      points.emplace_back(0.0, 1.0);
      for (int i = 0; i < 20; ++i) {
        const double random_delta = testing::RandomDouble(-kEpsilon * 2, kEpsilon * 2);
        const double random_x = testing::RandomInt(random_min_x, random_max_x) * scale_x;
        points.emplace_back(random_x + random_delta, testing_y);
      }
      Polygon2d polygon;
      EXPECT_TRUE(Polygon2d::ComputeConvexHull(points, &polygon));
      EXPECT_TRUE(polygon.is_convex());
    }
  }
  {
    const std::vector<Vector2d> non_convex_points = {
        {-34408.838686, 1065.364674},
        {-161098.780652, 97293.359560},
        {-746.890868, 615.096625},
        {-1841.960143, 2405.407876},
        {-342.613373, 620.408155},
        {-475.523278, 1601.840046},
        {53.877591, 650.366991},
        {135.496911, 1393.610926},
        {195.935736, 656.534572},
        {272.597520, 1414.650355},
        {209.539875, 655.590033},
        {288.855528, 1434.592128},
        {210.836712, 653.331662},
        {295.617233, 1463.108987},
        {209.887886, 652.901476},
        {295.678260, 1467.815878},
        {204.767878, 651.775773},
        {293.141397, 1478.614705},
        {200.398212, 650.945177},
        {290.645632, 1486.228352},
        {148.473455, 642.156575},
        {256.074856, 1564.213986},
        {104.514476, 634.586593},
        {220.345083, 1638.114222},
        {-6752.079820, 229.305429},
        {-1428464.498125, 2540901.815660},
        {-18976.342837, -404.683220},
        {-6423251.586435, 11338121.005083},
        {-2055821.030123, -146259.063907},
        {-3360489294394881536.000000, 5745897548043231232.000000},
        {-2446252.785000, -19591.711111},
        {-39283504.902015, 23244640.970605},
    };
    Polygon2d result_polygon;
    EXPECT_FALSE(Polygon2d::ComputeConvexHull(non_convex_points, &result_polygon));
    EXPECT_FALSE(result_polygon.is_convex());
  }
}

TEST_F(Polygon2dTest, Overlap) {
  const Polygon2d poly1(OrientedBox2d::FromAABox({0, 0}, {2, 2}));
  const Polygon2d poly2(OrientedBox2d::FromAABox({1, 1}, {3, 3}));
  const Polygon2d poly3(OrientedBox2d::FromAABox({2, 0}, {4, 2}));
  const Polygon2d poly4(OrientedBox2d({2, 2}, M_PI_4, sqrt(2.0), sqrt(2.0)));
  Polygon2d overlap_polygon;

  EXPECT_TRUE(poly1.ComputeOverlap(poly2, &overlap_polygon));
  EXPECT_NEAR(overlap_polygon.area(), 1.0, 1e-5);
  EXPECT_TRUE(poly2.ComputeOverlap(poly1, &overlap_polygon));
  EXPECT_NEAR(overlap_polygon.area(), 1.0, 1e-5);

  EXPECT_TRUE(poly2.ComputeOverlap(poly3, &overlap_polygon));
  EXPECT_NEAR(overlap_polygon.area(), 1.0, 1e-5);
  EXPECT_TRUE(poly3.ComputeOverlap(poly2, &overlap_polygon));
  EXPECT_NEAR(overlap_polygon.area(), 1.0, 1e-5);

  EXPECT_FALSE(poly1.ComputeOverlap(poly3, &overlap_polygon));
  EXPECT_FALSE(poly3.ComputeOverlap(poly1, &overlap_polygon));

  EXPECT_TRUE(poly1.ComputeOverlap(poly4, &overlap_polygon));
  EXPECT_NEAR(overlap_polygon.area(), 0.5, 1e-5);
  EXPECT_TRUE(poly4.ComputeOverlap(poly1, &overlap_polygon));
  EXPECT_NEAR(overlap_polygon.area(), 0.5, 1e-5);

  EXPECT_TRUE(poly2.ComputeOverlap(poly4, &overlap_polygon));
  EXPECT_NEAR(overlap_polygon.area(), 2.0, 1e-5);
  EXPECT_TRUE(poly4.ComputeOverlap(poly2, &overlap_polygon));
  EXPECT_NEAR(overlap_polygon.area(), 2.0, 1e-5);

  EXPECT_TRUE(poly3.ComputeOverlap(poly4, &overlap_polygon));
  EXPECT_NEAR(overlap_polygon.area(), 0.5, 1e-5);
  EXPECT_TRUE(poly4.ComputeOverlap(poly3, &overlap_polygon));
  EXPECT_NEAR(overlap_polygon.area(), 0.5, 1e-5);

  const double radius = 10.0;
  for (int i = 0; i < 100; i++) {
    Polygon2d poly1 = GetRandomConvexPolygon(radius);
    Polygon2d poly2 = GetRandomConvexPolygon(radius).Shifted({5.0, 5.0});
    std::vector<Vector2d> points1 = poly1.points();
    std::vector<Vector2d> points2 = poly2.points();
    Polygon2d overlap_polygon1;
    double area1;
    if (poly1.ComputeOverlap(poly2, &overlap_polygon1)) {
      area1 = overlap_polygon1.area();
    } else {
      area1 = 0.0;
    }
    double area2;
    Polygon2d overlap_polygon2;
    if (poly1.ComputeOverlap(poly2, &overlap_polygon2)) {
      area2 = overlap_polygon2.area();
    } else {
      area2 = 0.0;
    }
    EXPECT_NEAR(area1, area2, 1e-5);
  }

  EXPECT_TRUE(Polygon2d({{0, 0}, {0, 4}, {4, 0}})
                  .ComputeOverlap(Polygon2d({{1, 1}, {1, 3}, {3, 1}}), &overlap_polygon));
  EXPECT_NEAR(overlap_polygon.area(), 2.0, 1e-5);

  EXPECT_TRUE(Polygon2d({{0, 0}, {0, 4}, {4, 0}})
                  .ComputeOverlap(Polygon2d({{1, 1}, {-1, 1}, {1, 3}}), &overlap_polygon));
  EXPECT_NEAR(overlap_polygon.area(), 1.5, 1e-5);
  EXPECT_TRUE(Polygon2d({{0, 0}, {0, 4}, {4, 0}})
                  .ComputeOverlap(Polygon2d({{2, 1}, {-1, 1}, {2, 4}}), &overlap_polygon));
  EXPECT_NEAR(overlap_polygon.area(), 3.0, 1e-5);
  EXPECT_TRUE(Polygon2d({{0, 0}, {0, 4}, {4, 0}})
                  .ComputeOverlap(Polygon2d({{3, 1}, {-1, 1}, {3, 5}}), &overlap_polygon));
  EXPECT_NEAR(overlap_polygon.area(), 3.5, 1e-5);
  EXPECT_TRUE(Polygon2d({{0, 0}, {0, 4}, {4, 0}})
                  .ComputeOverlap(Polygon2d({{4, 1}, {-1, 1}, {4, 6}}), &overlap_polygon));
  EXPECT_NEAR(overlap_polygon.area(), 3.5, 1e-5);

  for (int iter = 0; iter < 10000; ++iter) {
    const double x1 = testing::RandomDouble(-10, 10);
    const double y1 = testing::RandomDouble(-10, 10);
    const double x2 = testing::RandomDouble(-10, 10);
    const double y2 = testing::RandomDouble(-10, 10);
    const double heading1 = testing::RandomDouble(0, M_PI * 2.0);
    const double heading2 = testing::RandomDouble(0, M_PI * 2.0);
    const double l1 = testing::RandomDouble(1, 5);
    const double l2 = testing::RandomDouble(1, 5);
    const double w1 = testing::RandomDouble(1, 5);
    const double w2 = testing::RandomDouble(1, 5);
    OrientedBox2d box1({x1, y1}, heading1, l1, w1);
    OrientedBox2d box2({x2, y2}, heading2, l2, w2);
    OrientedBox2d shrinked_box2({x2, y2}, heading2, l2 - 0.2, w2 - 0.2);
    OrientedBox2d extended_box2({x2, y2}, heading2, l2 + 0.2, w2 + 0.2);
    if (!box1.HasOverlap(extended_box2)) {
      EXPECT_FALSE(Polygon2d(box1).ComputeOverlap(Polygon2d(box2), &overlap_polygon));
    } else if (box1.HasOverlap(shrinked_box2)) {
      EXPECT_TRUE(Polygon2d(box1).ComputeOverlap(Polygon2d(box2), &overlap_polygon));
    }
  }

  for (int iter = 0; iter < 10000; ++iter) {
    const int kRange = 10;
    const int n1 = testing::RandomInt(3, 10);
    const int n2 = testing::RandomInt(3, 10);
    std::vector<Vector2d> points1, points2;
    for (int i = 0; i < n1; ++i) {
      points1.emplace_back(testing::RandomInt(0, kRange), testing::RandomInt(0, kRange));
    }
    for (int i = 0; i < n2; ++i) {
      points2.emplace_back(testing::RandomInt(0, kRange), testing::RandomInt(0, kRange));
    }
    Polygon2d polygon1;
    Polygon2d polygon2;
    if (!Polygon2d::ComputeConvexHull(points1, &polygon1) ||
        !Polygon2d::ComputeConvexHull(points2, &polygon2)) {
      continue;
    }
    std::vector<double> key_points;
    for (int x0 = 0; x0 <= kRange; ++x0) {
      key_points.push_back(x0);
    }
    const std::vector<LineSegment2d> line_segments1 = polygon1.GetLineSegments();
    const std::vector<LineSegment2d> line_segments2 = polygon2.GetLineSegments();
    for (const auto& line_segment1 : line_segments1) {
      for (const auto& line_segment2 : line_segments2) {
        Vector2d pt;
        if (line_segment1.GetIntersect(line_segment2, &pt)) {
          key_points.push_back(pt.x);
        }
      }
    }
    double area = 0;
    std::sort(key_points.begin(), key_points.end());
    for (size_t i = 0; i + 1 < key_points.size(); ++i) {
      const double width = key_points[i + 1] - key_points[i];
      if (width < 1e-6) {
        continue;
      }
      const double x = (key_points[i] + key_points[i + 1]) / 2.0;
      double min_y1 = 0.0;
      double max_y1 = 0.0;
      double min_y2 = 0.0;
      double max_y2 = 0.0;
      if (ProjectByXSlow(points1, x, &min_y1, &max_y1) &&
          ProjectByXSlow(points2, x, &min_y2, &max_y2)) {
        area += std::max(0.0, std::min(max_y1, max_y2) - std::max(min_y1, min_y2)) * width;
      }
    }
    Polygon2d overlap_polygon;
    if (area < 1e-3) {
      EXPECT_FALSE(polygon1.ComputeOverlap(polygon2, &overlap_polygon));
    } else {
      EXPECT_TRUE(Polygon2d::ComputeConvexHull(points1, &polygon1));
      EXPECT_TRUE(Polygon2d::ComputeConvexHull(points2, &polygon2));
      EXPECT_TRUE(polygon1.ComputeOverlap(polygon2, &overlap_polygon));
      EXPECT_NEAR(area, overlap_polygon.area(), 1e-5);
    }
  }

  // corner case, not coredump
  const Polygon2d polygon3({{4.779958, 6.014309},
                            {5.117553, 5.382054},
                            {5.457890, 5.221255},
                            {5.778805, 6.389028},
                            {5.638636, 6.469351},
                            {5.528915, 6.495750},
                            {5.484512, 6.488453}});
  const Polygon2d polygon4(
      {{4.183147, 5.713207}, {4.749878, 5.663364}, {4.782589, 6.035303}, {4.215858, 6.085146}});
  EXPECT_FALSE(polygon3.ComputeOverlap(polygon4, &overlap_polygon));
}

TEST_F(Polygon2dTest, BoundingBox) {
  {
    const std::vector<Vector2d> points = {{0.0, 0.0}, {2.0, 0.0}, {2.0, 2.0}, {0.0, 2.0}};
    const Polygon2d polygon(points);
    const OrientedBox2d box0 = Polygon2d::BoundingBoxWithHeading(points, 0.0);
    ASSERT_TRUE(box0.center().IsNear({1.0, 1.0}));
    ASSERT_NEAR(box0.area(), 4.0, math::kEpsilon);
    ASSERT_TRUE(Polygon2d(box0).Contains(polygon));

    const OrientedBox2d box1 = Polygon2d::BoundingBoxWithHeading(points, M_PI_4);
    ASSERT_TRUE(box1.center().IsNear({1.0, 1.0}));
    ASSERT_NEAR(box1.area(), 8.0, math::kEpsilon);
    ASSERT_TRUE(Polygon2d(box1).Contains(polygon));

    const OrientedBox2d box2 = polygon.MinAreaBoundingBox();
    ASSERT_TRUE(box2.center().IsNear({1.0, 1.0}));
    ASSERT_NEAR(box2.area(), 4.0, math::kEpsilon);
    ASSERT_TRUE(Polygon2d(box2).Contains(polygon));
  }
  {
    const std::vector<Vector2d> points = {{1.0, 0.0}, {0.0, 1.0}, {-1.0, 0.0}, {0.0, -1.0}};
    const Polygon2d polygon(points);
    const OrientedBox2d box0 = Polygon2d::BoundingBoxWithHeading(points, 0.0);
    ASSERT_TRUE(box0.center().IsNear({0.0, 0.0}));
    ASSERT_NEAR(box0.area(), 4.0, math::kEpsilon);
    ASSERT_TRUE(Polygon2d(box0).Contains(polygon));

    const OrientedBox2d box1 = Polygon2d::BoundingBoxWithHeading(points, M_PI_4);
    ASSERT_TRUE(box1.center().IsNear({0.0, 0.0}));
    ASSERT_NEAR(box1.area(), 2.0, math::kEpsilon);
    ASSERT_TRUE(Polygon2d(box1).Contains(polygon));

    const OrientedBox2d box2 = polygon.MinAreaBoundingBox();
    ASSERT_TRUE(box2.center().IsNear({0.0, 0.0}));
    ASSERT_NEAR(box2.area(), 2.0, math::kEpsilon);
    ASSERT_TRUE(Polygon2d(box2).Contains(polygon));
  }
  {
    const std::vector<Vector2d> points = {
        {0.0, 0.0}, {2.0, 0.0}, {2.0, 2.0}, {1.0, 1.0}, {0.0, 2.0}};
    const Polygon2d polygon(points);
    const OrientedBox2d box0 = Polygon2d::BoundingBoxWithHeading(points, 0.0);
    ASSERT_TRUE(box0.center().IsNear({1.0, 1.0}));
    ASSERT_NEAR(box0.area(), 4.0, math::kEpsilon);
    ASSERT_TRUE(Polygon2d(box0).Contains(polygon));

    const OrientedBox2d box1 = Polygon2d::BoundingBoxWithHeading(points, M_PI_4);
    ASSERT_TRUE(box1.center().IsNear({1.0, 1.0}));
    ASSERT_NEAR(box1.area(), 8.0, math::kEpsilon);
    ASSERT_TRUE(Polygon2d(box1).Contains(polygon));

    const OrientedBox2d box2 = polygon.MinAreaBoundingBox();
    ASSERT_TRUE(box2.center().IsNear({1.0, 1.0}));
    ASSERT_NEAR(box2.area(), 4.0, math::kEpsilon);
    ASSERT_TRUE(Polygon2d(box2).Contains(polygon));
  }
  {
    const std::vector<Vector2d> points = {
        {50.0, -1e-8}, {100.0, 0.0}, {100.0, 100.0}, {0.0, 100.0}, {0.0, 0.0}};
    const Polygon2d polygon(points);
    ASSERT_NEAR(polygon.MinAreaBoundingBox().area(), 10000.0, 1e-3);
  }
  {
    for (int i = 0; i < 1000; ++i) {
      const int num_points = testing::RandomInt(3, 10);
      const std::vector<Vector2d> points = testing::RandomPoints(num_points, 10.0);
      const OrientedBox2d box0 = Polygon2d(points).MinAreaBoundingBox();
      for (int j = 0; j < num_points; ++j) {
        const int k = Next(j, num_points);
        ASSERT_LE(box0.DistanceTo(points[j]), 1e-3);
        ASSERT_LE(box0.DistanceTo(points[j], points[k]), 1e-3);
      }
      for (int j = 0; j < num_points; ++j) {
        const int k = Next(j, num_points);
        if (points[k].IsNear(points[j])) {
          continue;
        }
        const double heading = (points[k] - points[j]).Angle();
        const OrientedBox2d box1 = Polygon2d::BoundingBoxWithHeading(points, heading);
        ASSERT_LE(box1.DistanceTo(points[j]), 1e-3);
        ASSERT_LE(box1.DistanceTo(points[j], points[k]), 1e-3);
        ASSERT_GE(box1.area(), box0.area() - 1e-3);
      }
    }
  }
}

TEST_F(Polygon2dTest, Expand) {
  {
    const Polygon2d poly(OrientedBox2d::FromAABox({0, 0}, {2, 2}));
    const Polygon2d exp_poly = poly.ExpandByDistance(1.0, 0.1);
    EXPECT_TRUE(exp_poly.is_convex());
    const OrientedBox2d box = exp_poly.BoundingBoxWithHeading(0.0);
    EXPECT_NEAR(box.center().x, 1.0, 1e-6);
    EXPECT_NEAR(box.center().y, 1.0, 1e-6);
    EXPECT_NEAR(box.width(), 4.0, 1e-6);
    EXPECT_NEAR(box.length(), 4.0, 1e-6);
    EXPECT_NEAR(exp_poly.area(), 12 + M_PI, 0.1);
  }
  {
    const std::vector<Vector2d> points{{0, 0}, {2, 0}, {2, 2}, {0, 2}, {1, 1}};
    const Polygon2d poly(points);
    const Polygon2d exp_poly = poly.ExpandByDistance(1.0, 0.1);
    EXPECT_TRUE(exp_poly.is_convex());
    const OrientedBox2d box = exp_poly.BoundingBoxWithHeading(0.0);
    EXPECT_NEAR(box.center().x, 1.0, 1e-6);
    EXPECT_NEAR(box.center().y, 1.0, 1e-6);
    EXPECT_NEAR(box.width(), 4.0, 1e-6);
    EXPECT_NEAR(box.length(), 4.0, 1e-6);
    EXPECT_NEAR(exp_poly.area(), 12 + M_PI, 0.1);
  }
}

TEST_F(Polygon2dTest, Transform) {
  const Vector2d offset(3.8, -9.4);
  const std::vector<Vector2d> points0 = {
      {9.3, 5.8}, {9.6, 3.4}, {3.2, 2.3}, {1.8, 6.3}, {5.6, 2.9}};
  const Polygon2d polygon0(points0);
  std::vector<Polygon2d> polygons;
  polygons.push_back(polygon0);
  polygons[0].Shift(offset);
  polygons.push_back(polygon0.Shifted(offset));
  const std::vector<LineSegment2d> polygon_0_segments = polygon0.GetLineSegments();
  for (int i = 0; i < static_cast<int>(polygons.size()); ++i) {
    ASSERT_EQ(polygons[i].num_points(), polygon0.num_points());
    ASSERT_EQ(polygons[i].is_convex(), polygon0.is_convex());
    ASSERT_NEAR(polygons[i].area(), polygon0.area(), 1e-6);
    ASSERT_NEAR(polygons[i].aabox().min_x(), polygon0.aabox().min_x() + offset.x, 1e-6);
    ASSERT_NEAR(polygons[i].aabox().min_y(), polygon0.aabox().min_y() + offset.y, 1e-6);
    ASSERT_NEAR(polygons[i].aabox().max_x(), polygon0.aabox().max_x() + offset.x, 1e-6);
    ASSERT_NEAR(polygons[i].aabox().max_y(), polygon0.aabox().max_y() + offset.y, 1e-6);
    const std::vector<LineSegment2d> polygon_i_segments = polygons[i].GetLineSegments();
    for (int j = 0; j < polygon0.num_points(); ++j) {
      ASSERT_LE(polygons[i].points()[j].DistanceTo(polygon0.points()[j] + offset), 1e-6);
      const LineSegment2d& segments_lhs = polygon_i_segments[j];
      const LineSegment2d& segments_rhs = polygon_0_segments[j];
      ASSERT_LE(segments_lhs.endpoints()[0].DistanceTo(segments_rhs.endpoints()[0] + offset), 1e-6);
      ASSERT_LE(segments_lhs.endpoints()[1].DistanceTo(segments_rhs.endpoints()[1] + offset), 1e-6);
    }
  }

  const Polygon2d polygon1({{0.0, 0.0}, {10.0, 0.0}, {0.0, 10.0}});
  const Polygon2d polygon1a = polygon1.Shifted({5.0, 3.0});
  Polygon2d polygon1b = polygon1;
  polygon1b.Shift({5.0, 3.0});

  const Polygon2d polygon2({{5.0, 3.0}, {15.0, 3.0}, {5.0, 13.0}});
  ASSERT_EQ(polygon1a.num_points(), polygon2.num_points());
  ASSERT_EQ(polygon1b.num_points(), polygon2.num_points());
  for (int i = 0; i < polygon2.num_points(); ++i) {
    ASSERT_TRUE(polygon1a.points()[i].IsNear(polygon2.points()[i]));
    ASSERT_TRUE(polygon1b.points()[i].IsNear(polygon2.points()[i]));
  }

  const Polygon2d polygon3({{-2.0, -2.0}, {-12.0, -2.0}, {-2.0, -12.0}});
  const Polygon2d polygon3a = polygon1.Rotated({-1.0, -1.0}, M_PI);
  Polygon2d polygon3b = polygon1;
  polygon3b.Rotate({-1.0, -1.0}, M_PI);
  ASSERT_EQ(polygon3a.num_points(), polygon3.num_points());
  ASSERT_EQ(polygon3b.num_points(), polygon3.num_points());
  for (int i = 0; i < polygon3.num_points(); ++i) {
    ASSERT_TRUE(polygon3a.points()[i].IsNear(polygon3.points()[i]));
    ASSERT_TRUE(polygon3b.points()[i].IsNear(polygon3.points()[i]));
  }

  const Polygon2d polygon4({{0.0, 0.0}, {1.0, 0.0}, {1.0, 1.0}});
  const Polygon2d polygon4a =
      polygon4.Shifted({3.0, -2.0}).Rotated({0.0, 0.0}, M_PI / 7).Shifted({5.0, 3.5});
  Polygon2d polygon4b = polygon4;
  polygon4b.ShiftAndRotate({-3.0, 2.0}, {5.0, 3.5}, M_PI / 7);
  ASSERT_EQ(polygon4a.num_points(), polygon4.num_points());
  ASSERT_EQ(polygon4b.num_points(), polygon4.num_points());
  for (int i = 0; i < polygon4.num_points(); ++i) {
    ASSERT_TRUE(polygon4b.points()[i].IsNear(polygon4a.points()[i]));
  }
}

TEST_F(Polygon2dTest, ShiftAndShifted) {}

TEST_F(Polygon2dTest, GetInteriorGridsCoreDumpFixTest) {
  // Convex polygon.
  std::vector<math::Vector2d> points1 = {
      {463158.26767289441, 4446732.8952431036}, {463158.31029306265, 4446732.8152658539},
      {463158.55760496965, 4446732.7999999998}, {463158.62176333566, 4446732.886280695},
      {463158.4892607074, 4446732.9139664769},
  };
  std::vector<math::Vector2i> coords = math::Polygon2d(points1).GetInteriorGrids(0.2);
  EXPECT_EQ(coords.size(), 1);
  EXPECT_EQ(coords[0].x, 2315792);
  EXPECT_EQ(coords[0].y, 22233664);
}

TEST_F(Polygon2dTest, GetInteriorGrids) {
  // Convex polygon.
  std::vector<math::Vector2d> points1 = {
      {3.0, 6.0},
      {4.0, 8.0},
      {6.0, 8.0},
      {5.0, 5.0},
  };
  EXPECT_THAT(math::Polygon2d(points1).GetInteriorGrids(1.0),
              testing::UnorderedElementsAre(math::Vector2i(3, 6), math::Vector2i(4, 5),
                                            math::Vector2i(4, 6), math::Vector2i(4, 7),
                                            math::Vector2i(5, 6), math::Vector2i(5, 7)));

  // 45 degree rotation.
  std::vector<math::Vector2d> points2 = {
      {-1.0, 0.0},
      {0.0, 1.0},
      {1.0, 0.0},
      {0.0, -1.0},
  };
  EXPECT_THAT(math::Polygon2d(points2).GetInteriorGrids(1.0),
              testing::UnorderedElementsAre(math::Vector2i(-1, 0), math::Vector2i(0, 0),
                                            math::Vector2i(-1, -1), math::Vector2i(0, -1)));

  // 45 degree rotation with smaller Y.
  std::vector<math::Vector2d> points3 = {
      {-1.0, 0.0},
      {0.0, 1.0 - 0.01},
      {1.0, 0.0},
      {0.0, -1.0 + 0.01},
  };
  EXPECT_TRUE(math::Polygon2d(points3).GetInteriorGrids(1.0).empty());
  EXPECT_THAT(math::Polygon2d(points3).GetInteriorGrids(0.5),
              testing::UnorderedElementsAre(math::Vector2i(-1, 0), math::Vector2i(0, 0),
                                            math::Vector2i(-1, -1), math::Vector2i(0, -1)));

  // 45 degree rotation with smaller X.
  std::vector<math::Vector2d> points4 = {
      {-1.0 + 0.01, 0.0},
      {0.0, 1.0},
      {1.0 - 0.01, 0.0},
      {0.0, -1.0},
  };
  EXPECT_TRUE(math::Polygon2d(points4).GetInteriorGrids(1.0).empty());
  EXPECT_THAT(math::Polygon2d(points4).GetInteriorGrids(0.5),
              testing::UnorderedElementsAre(math::Vector2i(-1, 0), math::Vector2i(0, 0),
                                            math::Vector2i(-1, -1), math::Vector2i(0, -1)));

  // Rectangle.
  std::vector<math::Vector2d> points5 = {
      {0.0, 0.0},
      {0.0, 2.0},
      {2.0, 2.0},
      {2.0, 0.0},
  };
  EXPECT_THAT(math::Polygon2d(points5).GetInteriorGrids(1.0),
              testing::UnorderedElementsAre(math::Vector2i(0, 0), math::Vector2i(0, 1),
                                            math::Vector2i(1, 0), math::Vector2i(1, 1)));
  EXPECT_THAT(math::Polygon2d(points5).GetInteriorGrids(4.0),
              testing::UnorderedElementsAre(math::Vector2i(0, 0)));
  EXPECT_TRUE(math::Polygon2d(points5).GetInteriorGrids(4.1).empty());

  // Zig-zag concave polygon.
  std::vector<math::Vector2d> points6 = {
      {0.0, 0.0}, {0.0, 3.0}, {3.0, 3.0}, {3.0, 1.0}, {4.0, 1.0}, {4.0, 2.0},
      {5.0, 2.0}, {5.0, 0.0}, {2.0, 0.0}, {2.0, 2.0}, {1.0, 2.0}, {1.0, 0.0},
  };
  EXPECT_THAT(math::Polygon2d(points6).GetInteriorGrids(1.0),
              testing::UnorderedElementsAre(math::Vector2i(0, 0), math::Vector2i(0, 1),
                                            math::Vector2i(0, 2), math::Vector2i(1, 2),
                                            math::Vector2i(2, 2), math::Vector2i(2, 1),
                                            math::Vector2i(2, 0), math::Vector2i(3, 0),
                                            math::Vector2i(4, 0), math::Vector2i(4, 1)));
}

TEST_F(Polygon2dTest, GetTouchedGrids) {
  const auto get_touched_grids = [](const std::vector<Vector2d>& points,
                                    const Vector2d& base_point) {
    return Polygon2d(points).GetTouchedGrids(1.0, base_point);
  };
  {
    const std::vector<Vector2d> points = {
        {0.0, 0.0},
        {1.0, 0.0},
        {1.0, 1.0},
        {0.0, 1.0},
    };
    ASSERT_THAT(get_touched_grids(points, {0.0, 0.0}),
                testing::UnorderedElementsAreArray(std::vector<Vector2i>{
                    {0, 0}, {1, 0}, {1, 1}, {0, 1}, {-1, -1}, {-1, 0}, {-1, 1}, {0, -1}, {1, -1}}));
    ASSERT_THAT(
        get_touched_grids(points, {0.0, 1.0}),
        testing::UnorderedElementsAreArray(std::vector<Vector2i>{
            {0, -1}, {1, -1}, {1, 0}, {0, 0}, {-1, 0}, {-1, -1}, {-1, -2}, {0, -2}, {1, -2}}));
    ASSERT_THAT(get_touched_grids(points, {0.0, 0.1}),
                testing::UnorderedElementsAreArray(
                    std::vector<Vector2i>{{0, -1}, {1, -1}, {1, 0}, {0, 0}, {-1, 0}, {-1, -1}}));
    ASSERT_THAT(get_touched_grids(points, {0.1, 0.1}),
                testing::UnorderedElementsAreArray(
                    std::vector<Vector2i>{{-1, -1}, {0, -1}, {0, 0}, {-1, 0}}));
  }
  {
    const std::vector<Vector2d> points = {
        {+1.6, +1.6},
        {+1.6, -0.7},
        {-0.7, +1.6},
    };
    ASSERT_THAT(
        get_touched_grids(points, {0.0, 0.0}),
        testing::UnorderedElementsAreArray(std::vector<Vector2i>{
            {+0, -1}, {+1, -1}, {-1, +0}, {+0, +0}, {+1, +0}, {-1, +1}, {+0, +1}, {+1, +1}}));
    ASSERT_THAT(
        get_touched_grids(points, {-1.0, -1.0}),
        testing::UnorderedElementsAreArray(std::vector<Vector2i>{
            {+1, +0}, {+2, +0}, {+0, +1}, {+1, +1}, {+2, +1}, {+0, +2}, {+1, +2}, {+2, +2}}));
  }
  {
    const std::vector<Vector2d> points = {
        {+0.5, +0.0},
        {+0.5, +1.0},
        {+1.5, +1.0},
        {+1.5, +0.0},
    };
    ASSERT_THAT(get_touched_grids(points, {0.0, 0.0}),
                testing::UnorderedElementsAreArray(std::vector<Vector2i>{
                    {+0, +0}, {+1, +0}, {+0, +1}, {+1, +1}, {0, -1}, {1, -1}}));
    ASSERT_THAT(
        get_touched_grids(points, {0.5, 0.0}),
        testing::UnorderedElementsAreArray(std::vector<Vector2i>{
            {+0, +0}, {+1, +0}, {+0, +1}, {+1, +1}, {-1, 1}, {-1, 0}, {-1, -1}, {0, -1}, {1, -1}}));
  }
  {
    const std::vector<Vector2d> points = {
        {+1.6, +0.6}, {+1.6, -1.7}, {+0.0, -0.5}, {-1.6, -1.7}, {-1.6, +0.6},
    };
    ASSERT_THAT(get_touched_grids(points, {0.0, 0.0}),
                testing::UnorderedElementsAreArray(std::vector<Vector2i>{
                    {-2, -2},
                    {-1, -2},
                    {+0, -2},
                    {+1, -2},
                    {-2, -1},
                    {-1, -1},
                    {+0, -1},
                    {+1, -1},
                    {-2, +0},
                    {-1, +0},
                    {+0, +0},
                    {+1, +0},
                }));
  }
  {
    const std::vector<Vector2d> points = {
        {8.5, 0.1},
        {8.5, 1.6},
        {10.0, 1.6},
        {10.0, 0.1},
    };
    ASSERT_THAT(get_touched_grids(points, {0.0, 0.0}),
                testing::UnorderedElementsAreArray(std::vector<Vector2i>{
                    {+8, +0},
                    {+8, +1},
                    {+9, +0},
                    {+9, +1},
                    {+10, +0},
                    {+10, +1},
                }));
  }
  {
    const std::vector<Vector2d> points = {
        {0.0, 1.0},
        {1.0, 1.0},
        {2.0, 1.0},
        {3.0, 1.0},
    };
    ASSERT_THAT(
        get_touched_grids(points, {0.0, 0.0}),
        testing::UnorderedElementsAreArray(std::vector<Vector2i>{
            {-1, 0}, {-1, 1}, {0, 0}, {0, 1}, {1, 0}, {1, 1}, {2, 0}, {2, 1}, {3, 0}, {3, 1}}));
  }
  {
    const std::vector<Vector2d> points = {
        {1.0, 1.0},
        {2.0, 2.0},
        {3.0, 3.0},
    };
    ASSERT_THAT(
        get_touched_grids(points, {0.0, 0.0}),
        testing::UnorderedElementsAreArray(std::vector<Vector2i>{
            {0, 0}, {1, 0}, {0, 1}, {1, 1}, {2, 1}, {1, 2}, {2, 2}, {3, 2}, {2, 3}, {3, 3}}));
  }
  {
    const std::vector<Vector2d> points = {
        {0.0, 0.0},
        {1.0, 1.0},
        {2.0, 0.0},
    };
    ASSERT_THAT(
        get_touched_grids(points, {0.0, 0.0}),
        testing::UnorderedElementsAreArray(std::vector<Vector2i>{
            {-1, -1}, {-1, 0}, {0, -1}, {0, 0}, {0, 1}, {1, -1}, {1, 0}, {1, 1}, {2, -1}, {2, 0}}));
    ASSERT_THAT(get_touched_grids(points, {math::kEpsilon, 0.0}),
                testing::UnorderedElementsAreArray(std::vector<Vector2i>{
                    {-1, -1}, {-1, 0}, {0, -1}, {0, 0}, {1, -1}, {1, 0}, {0, 1}}));
  }
}

TEST_F(Polygon2dTest, HasOverlapPolygon) {
  const double kRadius = 10.0;
  std::mt19937 random_gen;
  std::uniform_real_distribution<double> x_distribution(-kRadius * 2.0, kRadius * 2.0);
  std::uniform_real_distribution<double> y_distribution(-kRadius * 2.0, kRadius * 2.0);
  constexpr int kNumIterations = 100;
  constexpr int kNumPolygons = 300;
  for (int i = 0; i < kNumPolygons; ++i) {
    const Polygon2d polygon1 = GetRandomConvexPolygon(kRadius);
    const Polygon2d polygon2 = GetRandomConvexPolygon(kRadius);
    for (int j = 0; j < kNumIterations; ++j) {
      const math::Vector2d offset = {x_distribution(random_gen), y_distribution(random_gen)};
      const Polygon2d polygon_shifted = polygon2.Shifted(offset);
      const double distance = polygon1.DistanceTo(polygon_shifted);
      EXPECT_EQ(polygon1.HasOverlap(polygon_shifted), distance < kEpsilon);
    }
  }

  const OrientedBox2d box1({1.0, 0.0}, 0.0, 2.0, 1.0);
  const Polygon2d polygon1(
      std::vector<Vector2d>{{5.0, -0.5}, {10.0, -1.0}, {10.0, 3.0}, {5.0, 0.5}});
  ASSERT_FALSE(polygon1.HasOverlap(box1));
  ASSERT_TRUE(polygon1.Shifted({-4.0, 0.0}).HasOverlap(box1));
}

TEST_F(Polygon2dTest, ComputeIoUWith) {
  const math::Polygon2d polygon1(math::AxisAlignBox2d(math::Vector2d(0., 0.), 2., 2.));
  const math::Polygon2d polygon2(math::AxisAlignBox2d(math::Vector2d(1., 1.), 2., 2.));
  const double iou2 = polygon1.ComputeIoUWith(polygon2);
  EXPECT_FLOAT_EQ(1. / 7., iou2);
  const math::Polygon2d polygon3(math::AxisAlignBox2d(math::Vector2d(10., 10.), 2., 2.));
  const double iou3 = polygon1.ComputeIoUWith(polygon3);
  EXPECT_FLOAT_EQ(0., iou3);
  const std::vector<math::Vector2d> points = {
      {2.0, 5.0}, {8.0, 5.0}, {3.0, 1.0}, {5.0, 7.0}, {7.0, 1.0},
  };
  const math::Polygon2d polygon4(points);
  const double iou4 = polygon1.ComputeIoUWith(polygon4);
  EXPECT_FLOAT_EQ(0., iou4);

  // Coredump data. see https://km.sankuai.com/page/1268318046
  {
    const std::vector<Vector2d> points1 = {
        {470116.23196699674, 4446645.1316624591}, {470116.46756207233, 4446645.1215051413},
        {470117.94728714513, 4446645.1121197594}, {470118.1194837332, 4446645.1117906198},
        {470118.4095155024, 4446645.1115087317},  {470120.99200583599, 4446645.1122494712},
        {470124.35673073324, 4446645.1139550544}, {470129.46379679797, 4446645.122752226},
        {470133.88352547481, 4446645.1393845323}, {470134.12939485093, 4446645.1456219601},
        {470134.21576075931, 4446645.1985338256}, {470133.86114417948, 4446645.1997741433},
        {470133.42673201842, 4446645.1999929259}, {470125.93288966041, 4446645.1999996714},
        {470118.20710028568, 4446645.1999350898}, {470117.71189158538, 4446645.1999023296},
        {470116.32617260632, 4446645.1939878734}, {470116.24942267139, 4446645.1796367439}};
    const std::vector<Vector2d> points2 = {
        {470116.23999270465, 4446645.1245851424}, {470116.46184425172, 4446645.1225926401},
        {470118.32851924101, 4446645.1088085258}, {470124.33782804274, 4446645.1081435075},
        {470134.08299967513, 4446645.1351171043}, {470134.13427028968, 4446645.1469630105},
        {470134.20433789433, 4446645.1885089651}, {470134.20243395655, 4446645.1996946791},
        {470132.95133537642, 4446645.199924659},  {470132.02277769527, 4446645.1999873035},
        {470131.08339568507, 4446645.1999971299}, {470130.83369482827, 4446645.1999973496},
        {470118.34847433912, 4446645.1999994162}, {470118.32564757485, 4446645.1999963783},
        {470116.29912126123, 4446645.1991082979}, {470116.24377285206, 4446645.1807353981}};
    const math::Polygon2d polygon1(points1);
    const math::Polygon2d polygon2(points2);
    EXPECT_TRUE(polygon1.is_convex());
    EXPECT_TRUE(polygon2.is_convex());
    EXPECT_GT(polygon2.ComputeIoUWith(polygon1), 0.0);
  }
}

TEST_F(Polygon2dTest, ComputeOverlapRatioWith) {
  const math::Polygon2d polygon1(math::AxisAlignBox2d(math::Vector2d(0., 0.), 2., 2.));
  const math::Polygon2d polygon2(math::AxisAlignBox2d(math::Vector2d(1., 1.), 2., 2.));
  EXPECT_FLOAT_EQ(1. / 4., polygon1.ComputeOverlapRatioWith(polygon2));
  const math::Polygon2d polygon3(math::AxisAlignBox2d(math::Vector2d(10., 10.), 2., 2.));
  EXPECT_FLOAT_EQ(0., polygon1.ComputeOverlapRatioWith(polygon3));
  const std::vector<math::Vector2d> points = {
      {2.0, 5.0}, {8.0, 5.0}, {3.0, 1.0}, {5.0, 7.0}, {7.0, 1.0},
  };
  const math::Polygon2d polygon4(points);
  EXPECT_FLOAT_EQ(0., polygon1.ComputeOverlapRatioWith(polygon4));
  const math::Polygon2d polygon5(math::AxisAlignBox2d(math::Vector2d(0., 0.), 1., 1.));
  EXPECT_FLOAT_EQ(1. / 4., polygon1.ComputeOverlapRatioWith(polygon5));
  const math::Polygon2d polygon6(math::AxisAlignBox2d(math::Vector2d(0., 0.), 5., 5.));
  EXPECT_FLOAT_EQ(1., polygon1.ComputeOverlapRatioWith(polygon6));
}

TEST_F(Polygon2dTest, ********************************) {
  const math::Polygon2d polygon1(math::AxisAlignBox2d(math::Vector2d(0., 0.), 2., 2.));
  const math::Polygon2d polygon2(math::AxisAlignBox2d(math::Vector2d(4., 4.), 8., 8.));
  EXPECT_FLOAT_EQ(1. / 4., polygon1.********************************(polygon2));
  EXPECT_FLOAT_EQ(1. / 4., polygon2.********************************(polygon1));
  const math::Polygon2d polygon3(math::AxisAlignBox2d(math::Vector2d(10., 10.), 2., 2.));
  EXPECT_FLOAT_EQ(0., polygon1.********************************(polygon3));
  const std::vector<math::Vector2d> points = {
      {2.0, 5.0}, {8.0, 5.0}, {3.0, 1.0}, {5.0, 7.0}, {7.0, 1.0},
  };
  const math::Polygon2d polygon4(points);
  EXPECT_FLOAT_EQ(0., polygon1.********************************(polygon4));
  const math::Polygon2d polygon5(math::AxisAlignBox2d(math::Vector2d(0., 0.), 1., 1.));
  EXPECT_FLOAT_EQ(1., polygon1.********************************(polygon5));
  EXPECT_FLOAT_EQ(1., polygon5.********************************(polygon1));
  const math::Polygon2d polygon6(math::AxisAlignBox2d(math::Vector2d(0., 0.), 5., 5.));
  EXPECT_FLOAT_EQ(1., polygon1.********************************(polygon6));
  EXPECT_FLOAT_EQ(1., polygon6.********************************(polygon1));
}

TEST_F(Polygon2dTest, ExtremeProjections) {
  const std::vector<Vector2d> points = {{0.0, 0.0}, {1.0, 0.0}, {1.0, 1.0}, {0.0, 1.0}};
  double min_projection = 0.0;
  double max_projection = 0.0;
  Polygon2d::ExtremeProjections(points, Vector2d::FromAngle(0.0), &min_projection, &max_projection);
  EXPECT_NEAR(0.0, min_projection, math::kEpsilon);
  EXPECT_NEAR(1.0, max_projection, math::kEpsilon);
}

TEST_F(Polygon2dTest, ExtremeProjections2) {
  const std::vector<Vector2d> point_set1({{0.0, 0.0}, {2.0, 0.0}, {2.0, 2.0}, {0.0, 2.0}});
  const std::vector<Vector2d> point_set2({{0.0, 5.0}, {2.0, 5.0}, {2.0, 7.0}, {0.0, 7.0}});
  const Vector2d direction1 = {0, 1};
  const Vector2d direction2 = {0, 2};
  const Vector2d direction3 = {1, 0};
  const Vector2d direction4 = {1, 1};
  double min_projection1_1 = 0.0;
  double max_projection1_1 = 0.0;
  double min_projection1_2 = 0.0;
  double max_projection1_2 = 0.0;
  double min_projection1_3 = 0.0;
  double max_projection1_3 = 0.0;
  double min_projection1_4 = 0.0;
  double max_projection1_4 = 0.0;
  double min_projection2_1 = 0.0;
  double max_projection2_1 = 0.0;
  double min_projection2_2 = 0.0;
  double max_projection2_2 = 0.0;
  double min_projection2_3 = 0.0;
  double max_projection2_3 = 0.0;
  double min_projection2_4 = 0.0;
  double max_projection2_4 = 0.0;

  Polygon2d::ExtremeProjections(
      point_set1, direction1.Normalized(), &min_projection1_1, &max_projection1_1);
  Polygon2d::ExtremeProjections(
      point_set1, direction2.Normalized(), &min_projection1_2, &max_projection1_2);
  Polygon2d::ExtremeProjections(
      point_set1, direction3.Normalized(), &min_projection1_3, &max_projection1_3);
  Polygon2d::ExtremeProjections(
      point_set1, direction4.Normalized(), &min_projection1_4, &max_projection1_4);
  Polygon2d::ExtremeProjections(
      point_set2, direction1.Normalized(), &min_projection2_1, &max_projection2_1);
  Polygon2d::ExtremeProjections(
      point_set2, direction2.Normalized(), &min_projection2_2, &max_projection2_2);
  Polygon2d::ExtremeProjections(
      point_set2, direction3.Normalized(), &min_projection2_3, &max_projection2_3);
  Polygon2d::ExtremeProjections(
      point_set2, direction4.Normalized(), &min_projection2_4, &max_projection2_4);

  EXPECT_DOUBLE_EQ(min_projection1_1, 0.0);
  EXPECT_DOUBLE_EQ(max_projection1_1, 2.0);
  EXPECT_DOUBLE_EQ(min_projection1_2, 0.0);
  EXPECT_DOUBLE_EQ(max_projection1_2, 2.0);
  EXPECT_DOUBLE_EQ(min_projection1_3, 0.0);
  EXPECT_DOUBLE_EQ(max_projection1_3, 2.0);
  EXPECT_DOUBLE_EQ(min_projection1_4, 0.0);
  EXPECT_DOUBLE_EQ(max_projection1_4, 2 * std::sqrt(2.0));

  EXPECT_DOUBLE_EQ(min_projection2_1, 5.0);
  EXPECT_DOUBLE_EQ(max_projection2_1, 7.0);
  EXPECT_DOUBLE_EQ(min_projection2_2, 5.0);
  EXPECT_DOUBLE_EQ(max_projection2_2, 7.0);
  EXPECT_DOUBLE_EQ(min_projection2_3, 0.0);
  EXPECT_DOUBLE_EQ(max_projection2_3, 2.0);
  EXPECT_DOUBLE_EQ(min_projection2_4, std::sqrt(12.5));
  EXPECT_DOUBLE_EQ(max_projection2_4, std::sqrt(2.0) + std::sqrt(24.5));
}

TEST_F(Polygon2dTest, BuildFromPoints) {
  std::vector<math::Vector2d> points1 = {{10.0, 10.0}, {11.0, 11.0}, {10.5, 13.0}};
  const math::Polygon2d polygon1(std::move(points1));
  const math::AxisAlignBox2d& aabox1 = polygon1.aabox();
  EXPECT_NEAR(10.0, aabox1.min_x(), 1e-3);
  EXPECT_NEAR(11.0, aabox1.max_x(), 1e-3);
  EXPECT_NEAR(10.0, aabox1.min_y(), 1e-3);
  EXPECT_NEAR(13.0, aabox1.max_y(), 1e-3);

  std::vector<math::Vector2d> points2 = {{-10.0, -10.0}, {-11.0, -11.0}, {-10.5, -13.0}};
  const math::Polygon2d polygon2(std::move(points2));
  const math::AxisAlignBox2d& aabox2 = polygon2.aabox();
  EXPECT_NEAR(-10.0, aabox2.max_x(), 1e-3);
  EXPECT_NEAR(-11.0, aabox2.min_x(), 1e-3);
  EXPECT_NEAR(-10.0, aabox2.max_y(), 1e-3);
  EXPECT_NEAR(-13.0, aabox2.min_y(), 1e-3);
}

TEST_F(Polygon2dTest, GetPerimeter) {
  std::vector<math::Vector2d> points = {{0.0, 0.0}, {-6.0, 0.0}, {0.0, 8.0}};
  const math::Polygon2d polygon(std::move(points));
  EXPECT_NEAR(24.0, polygon.GetPerimeter(), 1e-6);
}

TEST_F(Polygon2dTest, ToProtoTest) {
  std::vector<math::Vector2d> points0 = {{10.0, 10.0}, {11.0, 11.0}, {10.5, 13.0}};
  const math::Polygon2d polygon0(std::move(points0));
  const Polygon2d polygon1 = Polygon2d(polygon0.ToProto());
  ASSERT_EQ(polygon0.num_points(), polygon1.num_points());
  ASSERT_EQ(polygon0.is_convex(), polygon1.is_convex());
  ASSERT_EQ(polygon0.area(), polygon1.area());
  const std::vector<math::Vector2d> polygon_points0 = polygon0.points();
  const std::vector<math::Vector2d> polygon_points1 = polygon1.points();
  for (int i = 0; i < polygon0.num_points(); ++i) {
    ASSERT_NEAR(polygon_points0[i].DistanceTo(polygon_points1[i]), 0.0, kEpsilon);
  }
}

TEST_F(Polygon2dTest, GetSamplePointsOnPolygonEdgeTest) {
  const std::vector<math::Vector2d> polygon_points{
      {0.0, 0.0},
      {0.0, 0.0},
      {10.0, 0.0},
      {10.001, 0.0},
      {10.0, 10.0},
      {10.0, 10.0001},
      {0.0, 10.0},
      {0.0, 10.001},
  };
  const math::Polygon2d polygon(std::move(polygon_points));
  {
    const std::vector<math::Vector2d> sample_points = polygon.GetSamplePointsOnPolygonEdge(1);
    EXPECT_EQ(1, sample_points.size());
    EXPECT_NEAR(0.0, sample_points[0].x, 1e-6);
    EXPECT_NEAR(0.0, sample_points[0].y, 1e-6);
  }
  {
    const std::vector<math::Vector2d> sample_points = polygon.GetSamplePointsOnPolygonEdge(20);
    EXPECT_EQ(20, sample_points.size());
    EXPECT_NEAR(2.0, sample_points[1].x, 1e-3);
    EXPECT_NEAR(0.0, sample_points[1].y, 1e-3);
  }
  {
    const std::vector<math::Vector2d> sample_points = polygon.GetSamplePointsOnPolygonEdge(100);
    EXPECT_EQ(100, sample_points.size());
    EXPECT_NEAR(4.0, sample_points[10].x, 1e-3);
    EXPECT_NEAR(0.0, sample_points[10].y, 1e-3);
  }
  {
    const std::vector<math::Vector2d> sample_points =
        polygon.GetSamplePointsOnPolygonEdge(100, 10);
    EXPECT_EQ(5, sample_points.size());
    EXPECT_NEAR(10.0, sample_points[1].x, 1e-3);
    EXPECT_NEAR(0.0, sample_points[1].y, 1e-3);
  }
}

}  // namespace math
