// Copyright @2022 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/horizon/horizon_inference_engine.h"

#include <utility>

namespace base {
namespace {

constexpr int kDefaultHorizonModelInputDim = 4;
constexpr int kDefaultHorizonModelInputBatch = 1;
constexpr int kDefaultImageChannelNum = 3;

#define HB_CHECK_SUCCESS(value, errmsg) CHECK(!value) << errmsg << ", Error Code:" << value;

}  // namespace

HorizonInferenceEngine::HorizonInferenceEngine(const std::string& engine_file) {
  const char* model_file_name = engine_file.c_str();
  const char** model_name_list;
  int model_count;
  HB_CHECK_SUCCESS(hbDNNInitializeFromFiles(&packed_dnn_handle_, &model_file_name, 1),
                   "Horizon hbDNNInitializeFromFiles failed");
  H<PERSON>_CHECK_SUCCESS(hbDNNGetModelNameList(&model_name_list, &model_count, packed_dnn_handle_),
                   "Horizon hbDNNGetModelNameList failed");
  HB_CHECK_SUCCESS(hbDNNGetModelHandle(&dnn_handle_, packed_dnn_handle_, model_name_list[0]),
                   "Horizon hbDNNGetModelHandle failed");
  HB_DNN_INITIALIZE_INFER_CTRL_PARAM(&infer_ctrl_param_);
  InitializeBuffers();
}

void HorizonInferenceEngine::Run() {
  task_handle_ = nullptr;
  hbDNNTensor* output_buffer_ptr = output_buffers_.data();
  // make sure that memory data is flushed into BPU DDR before inference
  for (int i = 0; i < input_node_number_; i++) {
    hbSysFlushMem(&input_buffers_[i].sysMem[0], HB_SYS_MEM_CACHE_CLEAN);
  }
  HB_CHECK_SUCCESS(hbDNNInfer(&task_handle_,
                              &output_buffer_ptr,
                              input_buffers_.data(),
                              dnn_handle_,
                              &infer_ctrl_param_),
                   "Horizon hbDNNInfer failed");
  HB_CHECK_SUCCESS(hbDNNWaitTaskDone(task_handle_, 0), "Horizon hbDNNWaitTaskDone failed");
}

void HorizonInferenceEngine::ReleaseTaskHandler() {
  // make sure that CPU read data from BPU DDR before using output tensor data
  for (int i = 0; i < output_node_number_; i++) {
    hbSysFlushMem(&output_buffers_[i].sysMem[0], HB_SYS_MEM_CACHE_INVALIDATE);
  }
  HB_CHECK_SUCCESS(hbDNNReleaseTask(task_handle_), "Horizon hbDNNReleaseTask failed");
}

void HorizonInferenceEngine::InitializeBuffers() {
  HB_CHECK_SUCCESS(hbDNNGetInputCount(&input_node_number_, dnn_handle_),
                   "Horizon hbDNNGetInputCount failed");
  HB_CHECK_SUCCESS(hbDNNGetOutputCount(&output_node_number_, dnn_handle_),
                   "Horizon hbDNNGetInputCount failed");
  input_buffers_.resize(input_node_number_);
  output_buffers_.resize(output_node_number_);
  for (int i = 0; i < input_node_number_; i++) {
    HB_CHECK_SUCCESS(hbDNNGetInputTensorProperties(&input_buffers_[i].properties, dnn_handle_, i),
                     "Horizon hbDNNGetInputTensorProperties failed");
    int input_memory_size = input_buffers_[i].properties.alignedByteSize;
    HB_CHECK_SUCCESS(hbSysAllocCachedMem(&input_buffers_[i].sysMem[0], input_memory_size),
                     "Horizon hbSysAllocCachedMem failed");
    input_buffers_[i].properties.alignedShape = input_buffers_[i].properties.validShape;
    input_buffers_[i].properties.tensorType = HB_DNN_IMG_TYPE_RGB;
  }
  for (int i = 0; i < output_node_number_; i++) {
    HB_CHECK_SUCCESS(hbDNNGetOutputTensorProperties(&output_buffers_[i].properties, dnn_handle_, i),
                     "Horizon hbDNNGetOutputTensorProperties failed");
    int output_memory_size = output_buffers_[i].properties.alignedByteSize;
    HB_CHECK_SUCCESS(hbSysAllocCachedMem(&output_buffers_[i].sysMem[0], output_memory_size),
                     "Horizon hbSysAllocCachedMem failed");
  }
}

void HorizonInferenceEngine::ReleaseBuffers() {
  for (int i = 0; i < input_node_number_; i++) {
    HB_CHECK_SUCCESS(hbSysFreeMem(&input_buffers_[i].sysMem[0]), "Horizon hbSysFreeMem failed");
  }
  for (int i = 0; i < output_node_number_; i++) {
    HB_CHECK_SUCCESS(hbSysFreeMem(&output_buffers_[i].sysMem[0]), "Horizon hbSysFreeMem failed");
  }
  HB_CHECK_SUCCESS(hbDNNRelease(packed_dnn_handle_), "Horizon hbDNNRelease failed");
  if (!image_buffers_.empty()) {
    ReleaseImageBuffers();
  }
}

void HorizonInferenceEngine::InitializeImageBuffers(
    const std::vector<std::pair<int, int>>& input_image_shapes) {
  CHECK_EQ(input_node_number_, input_image_shapes.size());
  image_buffers_.resize(input_node_number_);
  HB_DNN_INITIALIZE_RESIZE_CTRL_PARAM(&resize_ctrl_param_);
  for (int i = 0; i < input_node_number_; i++) {
    const int image_height = input_image_shapes[i].first;
    const int image_width = input_image_shapes[i].second;
    image_buffers_[i].properties.tensorType = HB_DNN_IMG_TYPE_RGB;
    image_buffers_[i].properties.tensorLayout = HB_DNN_LAYOUT_NHWC;
    image_buffers_[i].properties.validShape.numDimensions = kDefaultHorizonModelInputDim;
    image_buffers_[i].properties.validShape.dimensionSize[0] = kDefaultHorizonModelInputBatch;
    image_buffers_[i].properties.validShape.dimensionSize[1] = image_height;
    image_buffers_[i].properties.validShape.dimensionSize[2] = image_width;
    image_buffers_[i].properties.validShape.dimensionSize[3] = kDefaultImageChannelNum;
    image_buffers_[i].properties.alignedShape = image_buffers_[i].properties.validShape;
    hbDNNTensorShape aligned_shape = image_buffers_[i].properties.alignedShape;
    const int image_memory_size = aligned_shape.dimensionSize[1] * aligned_shape.dimensionSize[2] *
                                  aligned_shape.dimensionSize[3];
    HB_CHECK_SUCCESS(hbSysAllocCachedMem(&image_buffers_[i].sysMem[0], image_memory_size),
                     "Horizon hbSysAllocCachedMem failed");
  }
}

void HorizonInferenceEngine::ResizeInputImage() {
  for (int i = 0; i < input_node_number_; i++) {
    image_resize_handle_ = nullptr;
    hbSysFlushMem(&image_buffers_[i].sysMem[0], HB_SYS_MEM_CACHE_CLEAN);
    HB_CHECK_SUCCESS(hbDNNResize(&image_resize_handle_,
                                 &input_buffers_[i],
                                 &image_buffers_[i],
                                 nullptr,
                                 &resize_ctrl_param_),
                     "Horizon hbDNNResize failed");
    HB_CHECK_SUCCESS(hbDNNWaitTaskDone(image_resize_handle_, 0),
                     "Horizon hbDNNWaitTaskDone failed");
    HB_CHECK_SUCCESS(hbDNNReleaseTask(image_resize_handle_), "Horizon hbDNNReleaseTask failed");
  }
}

void HorizonInferenceEngine::ReleaseImageBuffers() {
  for (int i = 0; i < input_node_number_; i++) {
    HB_CHECK_SUCCESS(hbSysFreeMem(&image_buffers_[i].sysMem[0]), "Horizon hbSysFreeMem failed");
  }
}

}  // namespace base
