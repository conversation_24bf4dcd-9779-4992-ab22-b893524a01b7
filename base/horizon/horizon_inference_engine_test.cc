// Copyright @2022 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "gtest/gtest.h"

#include "base/horizon/horizon_inference_engine.h"

namespace base {
namespace {

constexpr char kEngineFile[] = "common/test/resources/horizon_models/dummy_yolo.bin";
constexpr int kInputNodeNum = 1;
constexpr int kOutputNodeNum = 3;
constexpr int kInputBatchsize = 1;
constexpr int kInputChannelNum = 3;
constexpr int kInputHeight = 512;
constexpr int kInputWidth = 1024;
constexpr int kOutputChannelNum = 45;
constexpr int kOutputHeight = kInputHeight >> 3;
constexpr int kOutputWidth = kInputWidth >> 3;

}  // namespace

TEST(HorizonInferenceEngineTest, BasicTest) {
  HorizonInferenceEngine horizon_inference_engine(kEngineFile);
  horizon_inference_engine.InitializeImageBuffers({{kInputHeight, kInputWidth}});
  const int input_node_num = horizon_inference_engine.GetInputNodeNumber();
  const int output_node_num = horizon_inference_engine.GetOutputNodeNumber();
  EXPECT_EQ(input_node_num, kInputNodeNum);
  EXPECT_EQ(output_node_num, kOutputNodeNum);
  const hbDNNTensor* image_buff = horizon_inference_engine.GetMutableImageBuffer(0);
  const hbDNNTensor* input_buff = horizon_inference_engine.GetMutableInputBuffer(0);
  const hbDNNTensor* output_buff = horizon_inference_engine.GetMutableOutputBuffer(0);
  const int* image_buff_shape = image_buff->properties.validShape.dimensionSize;
  const int* input_buff_shape = input_buff->properties.validShape.dimensionSize;
  const int* output_buff_shape = output_buff->properties.validShape.dimensionSize;
  EXPECT_EQ(image_buff_shape[0], kInputBatchsize);
  EXPECT_EQ(image_buff_shape[1], kInputHeight);
  EXPECT_EQ(image_buff_shape[2], kInputWidth);
  EXPECT_EQ(image_buff_shape[3], kInputChannelNum);
  EXPECT_EQ(input_buff_shape[0], kInputBatchsize);
  EXPECT_EQ(input_buff_shape[1], kInputHeight);
  EXPECT_EQ(input_buff_shape[2], kInputWidth);
  EXPECT_EQ(input_buff_shape[3], kInputChannelNum);
  EXPECT_EQ(output_buff_shape[1], kOutputHeight);
  EXPECT_EQ(output_buff_shape[2], kOutputWidth);
  EXPECT_EQ(output_buff_shape[3], kOutputChannelNum);
  EXPECT_NO_THROW(horizon_inference_engine.Run());
  EXPECT_NO_THROW(horizon_inference_engine.ReleaseTaskHandler());
  EXPECT_NO_THROW(horizon_inference_engine.ResizeInputImage());
}

}  // namespace base
