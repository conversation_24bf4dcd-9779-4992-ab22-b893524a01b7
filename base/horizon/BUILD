package(default_visibility = ["//visibility:public"])

cc_library(
    name = "horizon_inference_engine",
    srcs = ["horizon_inference_engine.cc"],
    hdrs = ["horizon_inference_engine.h"],
    deps = [
        "//base/common:field_macros",
        "//base/common:macros",
        "//base/common:optional",
        "//base/container:utils",
        "//base/math",
        "@horizon",
    ],
)

cc_test(
    name = "horizon_inference_engine_test",
    size = "small",
    srcs = ["horizon_inference_engine_test.cc"],
    data = [
        "//common/test:resources",
    ],
    tags = [
        "ci",
        "ci_cpu",
    ],
    deps = [
        ":horizon_inference_engine",
        "//base/testing:test_main",
    ],
)
