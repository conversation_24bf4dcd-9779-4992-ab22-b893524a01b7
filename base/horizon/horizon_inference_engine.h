// Copyright @2022 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <cmath>
#include <memory>
#include <string>
#include <unordered_map>
#include <utility>
#include <vector>

#include "base/common/macros.h"
#include "glog/logging.h"
#include "hb_dnn/hb_dnn.h"

namespace base {

class HorizonInferenceEngine {
 public:
  explicit HorizonInferenceEngine(const std::string& engine_file);
  ~HorizonInferenceEngine() { ReleaseBuffers(); }

  void Run();
  void ReleaseTaskHandler();
  void InitializeImageBuffers(const std::vector<std::pair<int, int>>& input_image_shapes);
  void ResizeInputImage();
  void ReleaseImageBuffers();

  const hbDNNTensor& GetInputBuffer(int input_buffer_index) const {
    CHECK_LT(input_buffer_index, input_buffers_.size());
    return input_buffers_[input_buffer_index];
  }

  hbDNNTensor* GetMutableInputBuffer(int input_buffer_index) {
    CHECK_LT(input_buffer_index, input_buffers_.size());
    return &input_buffers_[input_buffer_index];
  }

  const hbDNNTensor& GetImageBuffer(int input_buffer_index) const {
    CHECK_LT(input_buffer_index, image_buffers_.size());
    return image_buffers_[input_buffer_index];
  }

  hbDNNTensor* GetMutableImageBuffer(int input_buffer_index) {
    CHECK_LT(input_buffer_index, image_buffers_.size());
    return &image_buffers_[input_buffer_index];
  }

  const hbDNNTensor& GetOutputBuffer(int output_buffer_index) const {
    CHECK_LT(output_buffer_index, output_buffers_.size());
    return output_buffers_[output_buffer_index];
  }

  hbDNNTensor* GetMutableOutputBuffer(int output_buffer_index) {
    CHECK_LT(output_buffer_index, output_buffers_.size());
    return &output_buffers_[output_buffer_index];
  }

  int GetInputNodeNumber() const { return input_node_number_; }

  int GetOutputNodeNumber() const { return output_node_number_; }

 private:
  void InitializeBuffers();
  void ReleaseBuffers();
  // Horizon official pre-defined variables
  hbPackedDNNHandle_t packed_dnn_handle_;
  hbDNNInferCtrlParam infer_ctrl_param_;
  hbDNNResizeCtrlParam resize_ctrl_param_;
  hbDNNTaskHandle_t task_handle_ = nullptr;
  hbDNNTaskHandle_t image_resize_handle_ = nullptr;
  hbDNNHandle_t dnn_handle_;

  std::vector<hbDNNTensor> image_buffers_;
  std::vector<hbDNNTensor> input_buffers_;
  std::vector<hbDNNTensor> output_buffers_;
  int input_node_number_ = 0;
  int output_node_number_ = 0;

  DISALLOW_COPY_AND_ASSIGN(HorizonInferenceEngine);
};

}  // namespace base
