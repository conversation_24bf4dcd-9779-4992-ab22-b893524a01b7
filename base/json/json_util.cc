// Copyright @2020 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#include "base/json/json_util.h"

#include <fstream>
#include <utility>

#include "base/file/file_path_util.h"

namespace base {
namespace {

using Json = nlohmann::json;
using google::protobuf::util::MessageToJsonString;

google::protobuf::util::JsonOptions JsonOption() {
  google::protobuf::util::JsonOptions json_option;
  json_option.always_print_primitive_fields = true;
  return json_option;
}

}  // namespace

const google::protobuf::util::JsonOptions JsonUtil::kDefaultJsonOption = JsonOption();

nlohmann::json JsonUtil::ProtoToTypedJson(
    const std::string &json_type, const google::protobuf::Message &proto) {
  static const auto kJsonOption = JsonOption();
  std::string json_string;
  const auto status = MessageToJsonString(proto, &json_string, kJsonOption);
  CHECK(status.ok()) << "Cannot convert proto to json:" << proto.DebugString();

  Json json_obj;
  json_obj["type"] = json_type;
  json_obj["data"] = Json::parse(json_string);
  return json_obj;
}

nlohmann::json JsonUtil::ProtoToJsonObj(const google::protobuf::Message& proto,
                                        const google::protobuf::util::JsonOptions& option) {
  std::string json_string;
  const google::protobuf::util::Status status = MessageToJsonString(proto, &json_string, option);
  CHECK(status.ok()) << "Cannot convert proto to json:" << proto.DebugString();
  return Json::parse(json_string);
}

bool JsonUtil::JsonObjToProto(const nlohmann::json& json, google::protobuf::Message* proto) {
  google::protobuf::util::JsonParseOptions options;
  options.case_insensitive_enum_parsing = true;
  options.ignore_unknown_fields = true;
  google::protobuf::util::Status status = google::protobuf::util::JsonStringToMessage(
      json.dump(), proto, options);
  if (!status.ok()) {
    return false;
  }
  return true;
}

bool JsonUtil::GetObjectFromJson(const Json &json, const std::string &key,
                                 Json *value) {
  const auto iter = json.find(key);
  if (iter == json.end()) {
    LOG(ERROR) << "The json has no such key: " << key;
    return false;
  }
  if (!iter->is_object()) {
    LOG(ERROR) << "The value of json[" << key << "] is not a object";
    return false;
  }
  *value = *iter;
  return true;
}

bool JsonUtil::GetStringFromJson(const Json &json, const std::string &key,
                                 std::string *value) {
  const auto iter = json.find(key);
  if (iter == json.end()) {
    LOG(ERROR) << "The json has no such key: " << key;
    return false;
  }
  if (!iter->is_string()) {
    LOG(ERROR) << "The value of json[" << key << "] is not a string";
    return false;
  }
  *value = iter->get<std::string>();
  return true;
}

bool JsonUtil::GetIntFromJson(const Json &json, const std::string &key, int *value) {
  const auto iter = json.find(key);
  if (iter == json.end()) {
    LOG(ERROR) << "The json has no such key: " << key;
    return false;
  }
  if (!iter->is_number_integer()) {
    LOG(ERROR) << "The value of json[" << key << "] is not int";
    return false;
  }
  *value = *iter;
  return true;
}

bool JsonUtil::GetIntVectorFromJson(const Json &json, const std::string &key,
                                       std::vector<int32_t> *value) {
  const auto iter = json.find(key);
  if (iter == json.end()) {
    LOG(ERROR) << "The json has no such key: " << key;
    return false;
  }
  if (!iter->is_array()) {
    LOG(ERROR) << "The value of json[" << key << "] is not an array";
    return false;
  }

  bool ret = true;
  value->clear();
  value->reserve(iter->size());
  for (const auto &elem : *iter) {
    // Note that we still try to get all string values though there are invalid
    // elements.
    if (!elem.is_number_integer()) {
      LOG(WARNING) << "The value of json[" << key << "] contains non-string element";
      ret = false;
    } else {
      value->push_back(elem);
    }
  }
  return ret;
}

bool JsonUtil::GetStringVectorFromJson(const Json &json, const std::string &key,
                                       std::vector<std::string> *value) {
  const auto iter = json.find(key);
  if (iter == json.end()) {
    LOG(ERROR) << "The json has no such key: " << key;
    return false;
  }
  if (!iter->is_array()) {
    LOG(ERROR) << "The value of json[" << key << "] is not an array";
    return false;
  }

  bool ret = true;
  value->clear();
  value->reserve(iter->size());
  for (const auto &elem : *iter) {
    // Note that we still try to get all string values though there are invalid
    // elements.
    if (!elem.is_string()) {
      LOG(WARNING) << "The value of json[" << key << "] contains non-string element";
      ret = false;
    } else {
      value->push_back(elem);
    }
  }
  return ret;
}

bool JsonUtil::GetObjectVectorFromJson(const Json &json, const std::string &key,
                                      std::vector<Json> *value) {
  const auto iter = json.find(key);
  if (iter == json.end()) {
    LOG(ERROR) << "The json has no such key: " << key;
    return false;
  }
  if (!iter->is_array()) {
    LOG(ERROR) << "The value of json[" << key << "] is not an array";
    return false;
  }

  bool ret = true;
  value->clear();
  value->reserve(iter->size());
  for (const auto &elem : *iter) {
    // Note that we still try to get all string values though there are invalid
    // elements.
    if (!elem.is_object()) {
      LOG(WARNING) << "The value of json[" << key << "] contains non-string element";
      ret = false;
    } else {
      value->push_back(elem);
    }
  }
  return ret;
}

bool JsonUtil::AddJsonObjectIntoJson(nlohmann::json* json_data, const std::string& key) {
  nlohmann::json json_object;
  const auto iter = json_data->find(key);
  if (iter != json_data->end()) {
    LOG(WARNING) << "The json has already has the key " << key;
    return false;
  }
  (*json_data)[key] = std::move(json_object);
  return true;
}

bool JsonUtil::ReadJsonFile(const std::string& path, nlohmann::json* json_data) {
  if (!file_path::Exists(path)) {
    LOG(ERROR) << "The json file " << path << " does not exist";
    return false;
  }
  std::ifstream file(path);
  *json_data = nlohmann::json::parse(file);
  if (json_data == nullptr) {
    LOG(ERROR) << "Failed to parse json file " << path;
    return false;
  }
  return true;
}

}  // namespace base
