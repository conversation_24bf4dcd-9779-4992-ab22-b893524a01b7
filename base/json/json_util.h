// Copyright @2020 Sankuai Technology Inc. All rights reserved.
// Authors: <AUTHORS>

#pragma once

#include <string>
#include <vector>

#include "glog/logging.h"
#include "google/protobuf/message.h"
#include "google/protobuf/util/json_util.h"
#include "third_party/json/json.hpp"

namespace base {

class JsonUtil {
 public:
  static const google::protobuf::util::JsonOptions kDefaultJsonOption;

  /**
   * @brief Convert proto to a json string.
   * @return A json with two fields: {type:<json_type>, data:<proto_to_json>}.
   */
  static nlohmann::json ProtoToTypedJson(const std::string& json_type,
                                         const google::protobuf::Message& proto);
  static nlohmann::json ProtoToJsonObj(
      const google::protobuf::Message& proto,
      const google::protobuf::util::JsonOptions& option = kDefaultJsonOption);
  static bool GetObjectFromJson(const nlohmann::json& json,
                                const std::string& key,
                                nlohmann::json* value);
  static bool JsonObjToProto(const nlohmann::json& json, google::protobuf::Message* proto);

  /**
   * @brief Get a string value from the given json[key].
   * @return Whether the field exists and is a valid string.
   */
  static bool GetStringFromJson(const nlohmann::json &json,
                                const std::string &key, std::string *value);

  /**
   * @brief Get an int value from the given json[key].
   * @return Whether the field exists and is a valid int.
   */
  static bool GetIntFromJson(const nlohmann::json &json,
                             const std::string &key, int *value);

  /**
   * @brief Get a number value from the given json[key].
   * @return Whether the field exists and is a valid number.
   */
  template <class T>
  static bool GetNumberFromJson(const nlohmann::json &json,
                                const std::string &key, T *value) {
    const auto iter = json.find(key);
    if (iter == json.end()) {
      LOG(ERROR) << "The json has no such key: " << key;
      return false;
    }
    if (!iter->is_number()) {
      LOG(ERROR) << "The value of json[" << key << "] is not a number";
      return false;
    }
    *value = *iter;
    return true;
  }

  /**
   * @brief Get a number value from the given json[key].
   * @return Whether the field exists and is a valid number.
   */
  template <class T>
  static bool GetNumberVectorFromJson(const nlohmann::json &json,
                                      const std::string &key, std::vector<T> *value) {
    const auto iter = json.find(key);
    if (iter == json.end()) {
      LOG(ERROR) << "The json has no such key: " << key;
      return false;
    }
    if (!iter->is_array()) {
      LOG(ERROR) << "The value of json[" << key << "] is not an array";
      return false;
    }

    bool ret = true;
    value->clear();
    value->reserve(iter->size());
    for (const auto &elem : *iter) {
      // Note that we still try to get all string values though there are invalid
      // elements.
      if (!elem.is_number()) {
        LOG(ERROR) << "The value of json[" << key << "] contains non-string element";
        ret = false;
      } else {
        value->push_back(elem);
      }
    }
    return ret;
  }

  /**
   * @brief Get a string vector from the given json[key].
   * @return Whether the field exists and is a valid string vector.
   */
  static bool GetStringVectorFromJson(const nlohmann::json &json,
                                      const std::string &key,
                                      std::vector<std::string> *value);

  static bool GetIntVectorFromJson(const nlohmann::json &json,
                                   const std::string &key,
                                   std::vector<int32_t> *value);

  static bool GetObjectVectorFromJson(const nlohmann::json &json,
                                      const std::string &key,
                                      std::vector<nlohmann::json> *value);

  static bool AddJsonObjectIntoJson(nlohmann::json* json_data, const std::string& key);

  static bool ReadJsonFile(const std::string& path, nlohmann::json* json_data);
};

}  // namespace base
